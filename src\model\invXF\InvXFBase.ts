import { InvXFState } from './InvXFState'
import { InvXFType } from './InvXFType'

export default class InvXFBase {
  // 单据标识
  billId: string = ''
  // 单号
  num: string = ''
  // 申请单号
  applyNum: Nullable<string> = null
  // 发起方数据标识
  starterId: string = ''
  // 发起方名称
  starterName: string = ''
  // 调拨类型，取值范围：increase-调入，decrease-调出
  type: Nullable<InvXFType> = null
  // 状态,取值范围：initial-未提交，shipped-已发货，receipted-已收货
  state: Nullable<InvXFState> = null
  // 门店标识,调拨类型为调入时，它为调入门店；调拨类型为调出时，它为调出门店
  shopId: string = ''
  // 门店代码
  shopNo: string = ''
  // 门店名称
  shopName: string = ''
  // 相对方门店标识,调拨类型为调入时，它为调出门店；调拨类型为调出时，它为调入门店
  counterShopId: string = ''
  // 相对方门店代码
  counterShopNo: string = ''
  // 相对方门店名称
  counterShopName: string = ''
  // 品项数
  recCnt: number = 0
  // 申请总数
  qty: number = 0
  // 申请总规格数
  qpcQty: number = 0
  // 申请总额
  total: number = 0
  // 批准总数
  approvalQty: Nullable<number> = null
  // 批准总额
  approvalTotal: Nullable<number> = null
  // 批准总规格数
  approvalQpcQty: Nullable<number> = null
  // 实发总数
  shipQty: Nullable<number> = null
  // 实发总额
  shipTotal: Nullable<number> = null
  // 实发总规格数
  shipQpcQty: Nullable<number> = null
  // 实收总数
  receiptQty: Nullable<number> = null
  // 实收总额
  receiptTotal: Nullable<number> = null
  // 实收总规格数
  receiptQpcQty: Nullable<number> = null
  // 创建时间
  created: Date = new Date()
  // 创建人代码
  creatorId: Nullable<string> = null
  // 创建人名称
  creatorName: Nullable<string> = null
  // 最后修改时间
  lastModified: Date = new Date()
  // 最后修改人代码
  lastModifierId: Nullable<string> = null
  // 最后修改人名称
  lastModifierName: Nullable<string> = null
  // 发货时间
  shipTime: Nullable<Date> = null
  // 发货人代码
  shipperId: Nullable<string> = null
  // 发货人名称
  shipperName: Nullable<string> = null
  // 收货时间
  receiveTime: Nullable<Date> = null
  // 收货人代码
  consigneeId: Nullable<string> = null
  // 收货人名称
  consigneeName: Nullable<string> = null
  // 备注
  note: Nullable<string> = null
  // 配送模式
  deliveryType: Nullable<number> = null
  // 零售金额
  rtlTotal: Nullable<number> = null
  // 批准零售金额
  approvalRtlTotal: Nullable<number> = null
  // 发货零售金额
  shipRtlTotal: Nullable<number> = null
  // 收货零售金额
  receiptRtlTotal: Nullable<number> = null
  // 包材金额
  packTotal: Nullable<number> = null
  // 批准包材金额
  approvalPackTotal: Nullable<number> = null
  // 实发包材金额
  shipPackTotal: Nullable<number> = null
  // 实收包材金额
  receiptPackTotal: Nullable<number> = null
}
