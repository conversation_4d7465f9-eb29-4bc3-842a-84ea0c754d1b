/*
 * @Author: weish<PERSON>
 * @Date: 2025-04-01 10:00:00
 * @LastEditTime: 2025-04-24 15:37:44
 * @LastEditors: weisheng
 * @Description: 特殊品登记/移出详情
 * @FilePath: /soa/src/pagesSG/specGdRegist/SpecGdRegistDetail.ts
 * 记得注释
 */
import { Component, Vue } from 'vue-property-decorator'
import SpecGdRegistApi from '@/network/AppSpecGdRegistApi/AppSpecGdRegistApi'
import QueryRequest from '@/model/base/QueryRequest'
import AppSpecGdRegistDTO from '@/model/AppSpecGdRegist/AppSpecGdRegistDTO'
import AppSpecGdRegistLineDTO from '@/model/AppSpecGdRegist/AppSpecGdRegistLineDTO'
import AppSpecGdRegistLogDTO from '@/model/AppSpecGdRegist/AppSpecGdRegistLogDTO'
import { SpecGdRegistState } from '@/model/AppSpecGdRegist/SpecGdRegistState'
import DateUtil from '@/utils/DateUtil'
import SpecGdRegistGoodsList from './cmp/SpecGdRegistGoodsList.vue'
import config from '@/config'
import FlowDialog from '../cmp/FlowDialog.vue'

@Component({
  components: { FlowDialog, SpecGdRegistGoodsList }
})
export default class SpecGdRegistDetail extends Vue {
  billId: string = '' // 单据ID
  billDetail: AppSpecGdRegistDTO = new AppSpecGdRegistDTO() // 单据详情
  billLines: AppSpecGdRegistLineDTO[] = [] // 单据明细
  billLogs: AppSpecGdRegistLogDTO[] = [] // 单据日志
  $refs: any
  // 分页相关
  pageSize: number = 5 // 每页大小
  pageNum: number = 0 // 页码
  isLoading: boolean = false // 是否在加载
  finished: boolean = false // 是否加载完成

  // 状态枚举
  SpecGdRegistState = SpecGdRegistState

  // 状态文本映射
  stateTextMap = {
    [SpecGdRegistState.initial]: '待处理',
    [SpecGdRegistState.submitted]: '已提交',
    [SpecGdRegistState.approved]: '已批准',
    [SpecGdRegistState.rejected]: '已拒绝'
  }

  // 状态样式映射
  stateStyleMap = {
    [SpecGdRegistState.initial]: 'warning',
    [SpecGdRegistState.submitted]: 'primary',
    [SpecGdRegistState.approved]: 'success',
    [SpecGdRegistState.rejected]: 'danger'
  }
  // 状态图标映射
  stateImgMap = {
    [SpecGdRegistState.initial]: config.sourceUrl + 'icon/ic_apply_2x.png',
    [SpecGdRegistState.submitted]: config.sourceUrl + 'icon/ic_apply_2x.png',
    [SpecGdRegistState.approved]: config.sourceUrl + 'icon/ic_approval_2x.png',
    [SpecGdRegistState.rejected]: config.sourceUrl + 'icon/ic_refuse_2x.png'
  }

  // 类型文本映射
  typeTextMap = {
    0: '登记',
    1: '移除'
  }

  // 是否显示"查看更多"按钮
  get showMore() {
    return this.billLines.length > 4
  }

  // 是显示流程
  get processAble() {
    return this.billDetail.state && this.billDetail.state !== SpecGdRegistState.initial
  }

  // 商品总数量
  get count() {
    let count: number = 0
    for (let index = 0; index < this.billLines.length; index++) {
      count += this.billLines[index].qty || 0
    }
    return Number(count).scale(2)
  }

  // 页面标题
  get pageTitle() {
    return this.billDetail.type === 0 ? '特殊品登记详情' : '特殊品移除详情'
  }

  onLoad(option: { id: string }) {
    if (option.id) {
      this.billId = option.id
      this.$nextTick(async () => {
        await this.getBillDetail()
        // 设置页面标题
        uni.setNavigationBarTitle({ title: this.pageTitle })
        await this.getBillLines()
        await this.getBillLogs()
      })
    }
  }

  // 获取单据详情
  async getBillDetail() {
    try {
      this.$showLoading()
      const resp = await SpecGdRegistApi.get({ id: this.billId, fetchParts: [] })
      this.billDetail = resp.data || new AppSpecGdRegistDTO()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: (error as any).msg })
    }
  }

  // 获取单据明细
  async getBillLines() {
    if (this.isLoading || this.finished) {
      return
    }

    try {
      this.isLoading = true
      this.$showLoading()

      const queryRequest = new QueryRequest()
      queryRequest.page = this.pageNum
      queryRequest.pageSize = this.pageSize
      queryRequest.conditions = [
        {
          operation: 'billId:=',
          parameters: [this.billDetail.billId]
        }
      ]

      const resp = await SpecGdRegistApi.queryLine(queryRequest)

      if (this.pageNum === 0) {
        this.billLines = resp.data || []
      } else {
        this.billLines.push(...(resp.data || []))
      }

      this.pageNum++
      this.finished = !resp.more
      this.isLoading = false
      this.$hideLoading()
    } catch (error) {
      this.isLoading = false
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: (error as any).msg })
    }
  }

  // 获取单据日志
  async getBillLogs() {
    try {
      this.$showLoading()
      const resp = await SpecGdRegistApi.getLog(this.billId)
      this.billLogs = resp.data || []
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: (error as any).msg })
    }
  }

  // 加载更多明细
  handleLoadMore() {
    this.getBillLines()
  }

  // 查看全部商品
  doViewMore() {
    this.$Router.push({
      name: 'specGdRegistLineTotal',
      params: {
        id: this.billDetail.billId
      }
    })
  }

  // 格式化日期
  formatDate(date: Date | null | undefined) {
    if (!date) return ''
    return DateUtil.format(date, 'yyyy-MM-dd HH:mm:ss')
  }

  // 返回列表页
  handleBack() {
    uni.navigateBack({})
  }

  // 显示流程
  async doShowProcess() {
    if (!this.processAble) {
      return
    }

    try {
      this.$showLoading({ delayTime: 200 })
      const resp = await SpecGdRegistApi.getLog(this.billId)
      this.$hideLoading()
      const logs: AppSpecGdRegistLogDTO[] = resp.data || []
      const items: any[] = []

      for (let index = 0; index < logs.length; index++) {
        const log = logs[index]
        const title = this.stateTextMap[log.state!] || ''
        const desc = log.modifierName || ''
        let type = ''

        if (log.state! === 'approved') {
          type = 'success'
        } else if (log.state! === 'rejected') {
          type = 'error'
        }

        const date = log.modified ? DateUtil.format(new Date(log.modified.toString().replace(/-/g, '/')), 'MM-dd') : ''
        const time = log.modified ? DateUtil.format(new Date(log.modified.toString().replace(/-/g, '/')), 'HH:mm:ss') : ''

        items.unshift({ title: title, desc: desc, type: type, date: date, time: time })
      }

      this.$showPopup({
        ref: this.$refs.flowDialog,
        title: '特殊品登记流程',
        items: items
      })
    } catch (error) {
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: (error as any).msg })
    }
  }
}
