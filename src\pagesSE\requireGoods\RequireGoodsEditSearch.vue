<template>
  <view class="content require-goods-edit" @click="doCloseOutside()">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <hd-date-picker></hd-date-picker>
    <view>
      <weather-search-bar
        v-if="type === 'advance'"
        :curActive="true"
        :pageKeyWord="searchPage.keyWord"
        :weather="weatherList[0]"
        @doSearch="doSearch"
        @doCancel="doCancel"
        @doWeatherShow="doWeatherShow"
      >
        <template slot="date">
          <view class="expire-date" @click="doPickExpireDate">
            <text class="expire-date-title">期望到货日期:</text>
            <view class="expire-date-value">
              {{ expireDate | date('yyyy/MM/dd') }}
              <image class="expire-date-value-img" :src="'/static/icon/ic_chevron_white.png' | oss"></image>
            </view>
          </view>
        </template>
      </weather-search-bar>
      <weather-search-bar
        v-else
        :weather="weatherList[0]"
        :curActive="true"
        :pageKeyWord="searchPage.keyWord"
        @doSearch="doSearch"
        @doCancel="doCancel"
        @doWeatherShow="doWeatherShow"
      ></weather-search-bar>
    </view>
    <view>
      <scroll-view class="search-scroll" @scrolltolower="doLoadMore" scroll-y>
        <template>
          <goods-list-item
            v-for="goodsSearch in goodsSearchExtList"
            :accessoryGoodsCategoryNamePrefix="accessoryGoodsCategoryNamePrefix"
            :gtyLowLimitlevel="gtyLowLimitlevel"
            :gtyHighLimitlevel="gtyHighLimitlevel"
            :goodsLine="goodsSearch"
            :key="goodsSearch.goods.code"
            :accessoryManage="accessoryManage"
            @change="doNumberChange"
            @doAvgShow="doAvgShow"
            @doGoodsNoteShow="doGoodsNoteShow"
            @doShowShelveNote="doShowShelveNote"
            :hideInvQty="hideInvQty"
            :showGdImage="showGdImage"
            :validateLevel4CaseQty="validateLevel4CaseQty"
          ></goods-list-item>
        </template>
        <view v-if="!searchPage.finished && pageStatus === 'resultNormal'" class="loading">加载中···</view>
        <view
          v-if="searchPage.finished && goodsSearchExtList && goodsSearchExtList.length > searchPage.pageSize && pageStatus !== 'resultNone'"
          class="loading"
        >
          加载完毕
        </view>
        <view class="result-none" v-if="pageStatus === 'resultNone'">
          <image class="result-none-img" :src="'/static/img/img_empty_query.png' | oss"></image>
          <text class="result-none-txt">未查询到与之匹配的结果</text>
        </view>
        <check-pagination
          :eachPage="eachPage"
          :hasMore="hasMore"
          :totalNum="total"
          @pageChange="doShow"
          @hide="doHide"
          @showPagePick="doShowPagePick"
          ref="checkpick"
        ></check-pagination>
      </scroll-view>
    </view>
    <goods-action
      v-if="showPageBottom"
      :groupPurchase="requireBody.groupPurchase"
      @groupPurchase="doGroupPurchase"
      :num="goodsNum"
      :price="goodsPrice"
      :totalQpcQty="totalQpcQty"
      :btnPermission="btnPermission"
      :clickAble="clickAble"
      :note="requireBody.note"
      @onClick="doCartClick"
      @submit="doSubmitAfter"
      @save="doSaveAfter"
      @doNoteShow="doNoteShow"
      :showBalance="showBalance"
      :balance="balance"
      :dpsAccountInfo="dpsAccountInfo"
      :isPre="isPre"
      :isPreOrder="isPreOrder"
      :noPickAmt="noPickAmt"
      @closePreOrder="closePreOrder"
      :showSelfPickUp="showSelfPickUp"
      @doSelfPickUp="doSelfPickUp"
      :selfPickUp="requireBody.selfPickUp"
    ></goods-action>
    <page-picker ref="pagepick" :totalPage="totalPage" @confirm="doPageConfirm" @cancel="doPageCancel" :page="curpage"></page-picker>
    <view :class="[searchPage.isLoading && 'visible', 'mask']"></view>
    <uni-popup ref="weather" type="bottom">
      <weather-dialog @close="doWeatherClose"></weather-dialog>
    </uni-popup>
    <uni-popup ref="note" type="bottom">
      <hd-note :value="requireBody.note" @confirm="doNoteConfirm" @close="doNoteClose"></hd-note>
    </uni-popup>
    <uni-popup ref="avg" type="bottom">
      <avg-sale-dialog
        v-if="showAvg"
        @close="doAvgClose"
        :uuid="avgData.goods.uuid"
        :inputCode="avgData.goods.inputCode"
        :skuName="avgData.goods.name"
      ></avg-sale-dialog>
    </uni-popup>
    <!--上架备注详情弹框 -->
    <uni-popup ref="shelveNote" type="bottom">
      <inventory-diff @close="doInventoryClose" title="上架备注">
        <view slot="content">
          <scroll-view scroll-y style="height: 720rpx">
            <view class="content-list-text">{{ shelveNote }}</view>
          </scroll-view>
        </view>
      </inventory-diff>
    </uni-popup>
    <uni-popup ref="goodsNote" type="bottom">
      <hd-note :value="currentNote" @confirm="doGoodsNoteConfirm" @close="doGoodsNoteClose" :maxLength="200" ref="hdNote"></hd-note>
    </uni-popup>
    <!-- 库存不足时弹窗 -->
    <uni-popup ref="Inventory" type="bottom">
      <inventory-diff
        @close="doInventoryClose"
        @confirm="doInventoryConfirm"
        title="以下商品信息发生变化"
        showbutton
        btntext="继续提交"
        :disable="!goodsNum"
      >
        <view slot="content">
          <view class="list-title">
            <view class="name">品名/条码/代码/单位</view>
            <view class="price">单价</view>
            <view class="qty">可订数</view>
            <view class="total">小计</view>
          </view>
          <view class="divide"></view>
          <scroll-view scroll-y style="height: 490rpx">
            <view class="list" v-for="line in showList" :key="line.goods.uuid">
              <view :class="[line.validCode == '1' && 'disableBck']">
                <view class="sku-name">
                  {{ line.goods.name | empty }}
                </view>
                <view class="sku-info">
                  <view class="code-price">
                    <view class="goods-tag-primary" v-if="line.goods.isDisp">散称</view>
                    <view class="info-tag">{{ line.goods.inputCode | empty }}</view>
                    <view class="info-tag">{{ line.goods.code | empty }}</view>
                    <view class="info-tag">{{ line.goods.munit | empty }}</view>
                  </view>
                  <view style="width: 100%; height: 10rpx"></view>
                  <view class="name"></view>
                  <view class="price">
                    <text class="num">{{ line.goods.tempPrice | fmt }}</text>
                    <text class="qpc-src" v-if="line.goods.srcPrice != line.goods.tempPrice">{{ line.goods.srcPrice | fmt }}</text>
                  </view>
                  <!--1/kg-->
                  <view class="qty">
                    <text class="num">{{ line.qpcQty | empty }}</text>
                  </view>
                  <view class="total">
                    <text class="amount">{{ line.total | fmt }}</text>
                  </view>
                </view>
              </view>
              <image :src="'/static/icon/icon_xiajia.png' | oss" class="deleteImg" mode="aspectFit" v-if="line.validCode == '1'"></image>
            </view>
          </scroll-view>
          <view class="red-text">
            <view>
              <image :src="'/static/icon/ic_yiwen_inverted_2x.png' | oss" mode="aspectFill" class="warn-icon"></image>
              若继续提交系统将自动修改价格或订货数
            </view>
            <view class="flex-row">
              <view>
                订货品项数：
                <text class="qty">{{ goodsNum }}</text>
                <text class="qpc-src" v-if="oldGoodsNum != goodsNum">{{ oldGoodsNum }}</text>
              </view>
              <view>
                订货金额：
                <text class="qty">{{ goodsPrice | fmt }}</text>

                <text class="qpc-src" v-if="oldGoodsPrice != goodsPrice">{{ oldGoodsPrice | fmt }}</text>
              </view>
            </view>
          </view>
        </view>
      </inventory-diff>
    </uni-popup>

    <!-- 准备支付 -->
    <uni-popup ref="perparepay" type="bottom">
      <prepare-pay @close="doPerparePayClose" @confirm="doPerparePayConfirm" :paymentSrcContext="paymentSrcContext"></prepare-pay>
    </uni-popup>

    <!-- #ifdef MP-WEIXIN -->
    <hd-privacy-popup id="privacy-popup"></hd-privacy-popup>
    <!-- #endif -->
  </view>
</template>

<script lang="ts" src="./RequireGoodsEditSearch.ts"></script>

<style lang="scss" scoped>
.require-goods-edit {
  position: relative;

  .header-tips {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 $base-padding;
    width: 100%;
    height: 72rpx;
    background: #fdf1e8;
    @include flex(row, flex-start, center);

    .tips-img {
      width: 40rpx;
      height: 40rpx;
    }

    .tips-txt {
      margin-left: 8rpx;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: $color-warning;
      line-height: 40rpx;
    }

    .close-icon {
      margin-left: auto;
      width: 40rpx;
      height: 40rpx;
    }
  }

  .header {
    position: relative;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 $base-padding;
    width: 100%;
    height: 72rpx;
    background: $color-background;

    .header-img {
      width: 32rpx;
      height: 32rpx;
    }

    .header-close {
      position: absolute;
      right: 24rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 36rpx;
      height: 36rpx;
    }

    .header-txt--before {
      text-align: left;
      margin-left: 12rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: rgba(40, 44, 52, 1);
    }

    .header-txt--after {
      text-align: left;
      margin-left: 12rpx;
      font-size: 28rpx;
      font-family: $font-medium;
      font-weight: 500;
      color: $color-primary;
    }
  }
  .expire-date {
    width: 320rpx;
    height: 100%;
    box-sizing: border-box;
    padding: 16rpx 0 20rpx 24rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 26rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;

    &-value {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      &-img {
        width: 32rpx;
        height: 32rpx;
        margin-left: 8rpx;
      }
    }
  }

  .mask {
    position: fixed;
    z-index: 1000;
    top: 0;
    right: 0;
    left: 0;
    bottom: 208rpx;
    visibility: hidden;
    opacity: 1;
  }
  .visible {
    visibility: visible;
  }
  .side-scroll {
    height: calc(100vh - 372rpx);
    flex: 0 0 auto;
    width: 168rpx;
    overflow-x: hidden;
    overflow-y: scroll;
  }

  // 展示订货时间的样式
  .side-scroll--hastime {
    height: calc(100vh - 444rpx);
  }

  .side-scroll--order {
    height: calc(100vh - 556rpx);
  }

  .side-scroll--order--notime {
    height: calc(100vh - 484rpx);
  }

  .main {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    overflow-y: hidden;
  }
  .collapseBar-contain {
    z-index: 3;
    width: 582rpx;
    height: 80rpx;
  }
  .line-box {
    background: #ffffff;
    padding-left: 24rpx;
    .line {
      padding-left: 16rpx;
      width: 518rpx;
      height: 40rpx;
      line-height: 40rpx;
      background: $skeleton-background;
      border-radius: 4rpx;
      @include ellipsis();
      font-size: 22rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $color-text-thirdly;
    }
  }

  .goods-scroll {
    overflow-y: auto;
    z-index: 1;
    width: 582rpx;
    height: calc(100vh - 452rpx);
    background: #ffffff;
    // 展示订货时间的样式
    &-hastime {
      height: calc(100vh - 524rpx);
    }
    &-order {
      height: calc(100vh - 636rpx);
    }
    &-notime {
      height: calc(100vh - 564rpx);
    }
  }
  .goods-scroll--long {
    height: calc(100vh - 392rpx);
    // 展示订货时间的样式
    &-hastime {
      height: calc(100vh - 444rpx) !important;
    }
    &-order {
      height: calc(100vh - 576rpx) !important;

      &-notime {
        height: calc(100vh - 504rpx) !important;
      }
    }
  }

  .search-scroll {
    position: relative;
    z-index: 1;
    width: 750rpx;
    height: calc(100vh - 372rpx);
    background: #ffffff;
  }
  .result-none {
    display: flex;
    align-items: center;
    flex-direction: column;
    height: calc(100vh - 448rpx);
    .result-none-img {
      margin-top: 256rpx;
      width: 218rpx;
      height: 218rpx;
    }
    .result-none-txt {
      margin: 10rpx 0 40rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(148, 150, 154, 1);
      line-height: 40rpx;
    }
  }
  .result-none--long {
    height: calc(100vh - 368rpx);
  }
  .search-scroll--long {
    height: calc(100vh - 120rpx);
  }

  .loading {
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: rgb(148, 150, 154);
  }

  .next-sort {
    padding: 20rpx 0;
    font-size: 22rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #94939f;
    line-height: 30rpx;
    width: 100%;
    text-align: center;
    @include flex(column, center, center);

    &-text {
      @include flex(row, center, center);
    }
  }

  .pre-sort {
    padding: 20rpx 0;
    font-size: 22rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #94939f;
    line-height: 30rpx;
    width: 100%;
    text-align: center;
    @include flex(column, center, center);

    &-text {
      @include flex(row, center, center);
    }
  }

  .next-sort-icon {
    width: 20rpx;
    height: 20rpx;
    margin-left: 8rpx;
  }

  .last-sort-icon {
    width: 20rpx;
    height: 20rpx;
    margin-left: 8rpx;
    transform: rotate(180deg);
  }
}
.content-list-text {
  line-height: 32rpx;
  word-break: break-all;

  text-align: left;
  margin-top: 20rpx;
  font-weight: 400;
  color: $font-color-darklight;
  font-size: $font-size-xsmall;
  width: 702rpx;
  margin: 0 24rpx;
  box-sizing: border-box;
}
.red-text {
  width: 750rpx;
  line-height: 48rpx;
  overflow: hidden;
  background: rgba(253, 155, 28, 0.1);
  padding: 16rpx 24rpx;
  text-align: left;
  box-sizing: border-box;

  font-size: 24rpx;
  color: #ff9100;

  .warn-icon {
    width: 32rpx;
    height: 32rpx;
    vertical-align: middle;
    margin-right: 12rpx;
  }
  .qpc-src {
    margin-left: 4rpx;
    text-decoration: line-through;
    font-size: 22rpx;
    font-family: PingFangSC;
    color: #94969a;
  }
  .flex-row {
    margin-top: 12rpx;
    @include flex(row, space-between, center);
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: $color-text-thirdly;
    .qty {
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: $color-text-primary;
    }
  }
}
.list-title {
  position: relative;
  width: 100%;
  height: 76rpx;
  padding: 0 24rpx;
  display: flex;
  box-sizing: border-box;
  align-items: center;
  justify-content: space-between;
  font-size: 26rpx;
  color: $font-color-darklight;
  background: #ffffff;
  .name {
    flex: 1.5;
  }
  .price {
    flex: 1;
    text-align: center;
  }
  .qty {
    flex: 0.5;
    text-align: center;
  }
  .total {
    flex: 1;
    text-align: right;
  }
}
.divide {
  width: 750rpx;

  border-bottom: $list-border-bottom;
}
.list {
  padding: $base-padding 24rpx;
  border-bottom: $list-border-bottom;
  position: relative;
  .sku-name {
    /*height: 44rpx;*/
    line-height: 44rpx;
    font-size: 30rpx;
    font-weight: 500;
    color: $color-text-primary;
    font-family: PingFangSC-Medium, PingFang SC;
    overflow: hidden;
    text-overflow: ellipsis;
    /*white-space: nowrap;*/
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    .code {
      color: #282c34;
      font-size: 26rpx;
      margin-right: 10rpx;
    }
  }
  .sku-info {
    line-height: 40rpx;
    display: flex;
    align-items: center;
    margin-top: 8rpx;
    justify-content: space-between;
    flex-wrap: wrap;
    color: #585a5e;
    font-size: 26rpx;
    .code-price {
      display: flex;
      align-items: center;

      justify-content: flex-start;
    }
    .qpc-src {
      margin-left: 4rpx;
      text-decoration: line-through;
      font-size: 22rpx;
      font-family: PingFangSC;
      color: #94969a;
    }

    .name {
      // flex: 1.5;
      width: 37.5%;
    }
    .price {
      flex: 1;
      text-align: center;
    }
    .qty {
      flex: 0.5;
      text-align: center;
    }
    .total {
      flex: 1;
      text-align: right;
    }

    .total {
      flex: 1;
      text-align: right;
    }
    .num {
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: $color-text-thirdly;
    }
    .amount {
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: $color-text-primary;
    }
  }
}
.deleteImg {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 128rpx;
  height: 128rpx;
  z-index: 2;
}
.disableBck {
  opacity: 0.2;
}
</style>
