<template>
  <view class="return-apply-edit-card" v-if="value">
    <view class="sku-goods">
      <view class="sku-left">
        <image lazy-load class="sku-img" :src="skuImg" @click.stop="handlePreviewImg" />
        <view class="info__scale">
          <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
        </view>
      </view>
      <view class="title">{{ innerValue.goods.name | empty }}</view>
    </view>
    <view class="qpc" v-if="showMaster.showDisplayLocation">
      <view class="main-exhibit">
        <view :class="[hasMutiple(innerValue.displayLocation) ? 'goods-one' : '']">
          陈列位置：
          <text class="goods-text">{{ innerValue.displayLocation | empty }}</text>
        </view>
        <image
          class="good-img"
          :src="'/static/icon/ic_right_grey.png' | oss"
          v-if="hasMutiple(innerValue.displayLocation)"
          @click="viewExhibit(innerValue)"
        />
      </view>
    </view>
    <view class="code">
      <view class="code-line">
        条码：
        <text class="code-text">{{ (innerValue.goods.code2 || innerValue.goods.inputCode) | empty }}</text>
      </view>
      <view class="code-line">
        代码：
        <text class="code-text">{{ innerValue.goods.code | empty }}</text>
      </view>
    </view>

    <view class="qpc">
      <view class="qpc-line">
        <!-- <view v-if="showAlcPrice" class="price-line">
          <text class="qpc-price" style="font-size: 24rpx">￥</text>
          <text class="qpc-price">{{ innerValue.goods.singlePrice | empty }}</text>
          <text class="qpc-munit">/{{ innerValue.goods.minMunit | empty }}</text>
        </view> -->
        <text class="qpc-text">规格：{{ innerValue.goods.qpcStr | empty }}</text>
      </view>
      <block>
        <view v-if="innerValue.reasons.length > 0 && !isNormal">
          <text class="qpc-detail">最后退仓日期：</text>
          <text class="qpc-munit" style="margin: 0">{{ innerValue.reasons[0].lstBckDate | date('yy/MM/dd') | empty }}</text>
        </view>
        <block v-else>
          <view class="bckalc" v-if="stkoutBckValidLimit || croOrgSaleBckValidLimit">
            退货方式：
            <text class="bckalc-text">{{ innerValue.bckAlc | empty }}</text>
          </view>
        </block>
      </block>
    </view>
    <!-- 添加库存字段展示 -->
    <view class="stock-info">
      <view class="stock-item">
        <text class="stock-label">{{ invCatName ? invCatName + '库存' : '当前库存' }}：</text>
        <text class="stock-value">{{ innerValue.invQty | empty }}{{ innerValue.goods.munit }}</text>
      </view>
    </view>
    <view class="qpc">
      <view class="bckalc" v-if="(stkoutBckValidLimit || croOrgSaleBckValidLimit) && innerValue.reasons.length > 0 && !isNormal">
        退货方式：
        <text class="bckalc-text">{{ innerValue.bckAlc }}</text>
      </view>
    </view>
    <block v-if="(stkoutBckValidLimit && innerValue.bckAlc == '统配') || (croOrgSaleBckValidLimit && innerValue.bckAlc == '跨公司统配')">
      <view class="good-time">
        <view class="time-start" @click="handleSelectDate('start')">
          <view class="time-label must_star">生产日期</view>
          <view class="flex">
            <view class="time-value">{{ innerValue.prdDate ? innerValue.prdDate : '' | date }}</view>
            <image class="time-icon" :src="'/static/icon/ic_right_grey.png' | oss" />
          </view>
        </view>

        <view class="time-start" @click="handleSelectDate('end')">
          <view class="time-label must_star">到效日期</view>
          <view class="flex">
            <view class="time-value">{{ innerValue.validDate ? innerValue.validDate : '' | date }}</view>
            <image class="time-icon" :src="'/static/icon/ic_right_grey.png' | oss" />
          </view>
        </view>
      </view>
      <view v-if="isNormal && innerValue.alwOverdueBck == 0 && validDate" class="out_time">
        <image :src="'/static/icon/ic_error_fill.png' | oss" class="error-image"></image>
        该商品不在可退日期范围内
      </view>
    </block>
    <view class="reason" v-for="(reason, index) in innerValue.reasons" :key="reason.reasonCode">
      <view class="reason-header">
        <view class="flex">
          <view class="reason-txt" :class="{ 'reason-txt--none': !reason.reasonName }" @click="handleSelectReason(reason, index)">
            {{ reason.reasonName || '请选择退货原因' }}
          </view>
          <image v-if="isNormal" class="reason-arrow" lazy-load="true" :src="'/static/icon/ic_noticebar_right_grey.png' | oss"></image>
        </view>
        <view class="reason-number">
          <view v-if="!isNormal">
            <view v-if="reason.limitQty || reason.limitQty === 0" class="limit-qty reason-txt">
              <view class="limit-qty-label">限量</view>
              <view class="limit-qty-number">x{{ reason.limitQty | empty }}</view>
            </view>
            <view v-else class="not-limit-label">不限</view>
          </view>
          <view v-if="!isNormal && !notifyEditable" class="reason-qty">
            <text>x</text>
            {{ Number(reason.qty) | fmt('0.00') }}
          </view>
          <block v-if="(isNormal || notifyEditable) && !isHasTraceCodes && !(innerValue.useVd && enableReasonValidDate)">
            <view class="qty-label" v-if="innerValue.useDoubleMeasure">件数（{{ innerValue.goods.munit }}）</view>
            <block v-if="innerValue.useDoubleMeasure">
              <hd-number-box-test
                :step="1"
                v-model="innerValue.reasons[index].qpcQty"
                :key="reason.reasonCode"
                v-if="reason.limitQpcQty === null || reason.limitQpcQty > 0"
                :max="reason.limitQpcQty !== null ? reason.limitQpcQty : 99999999"
                :scale="0"
                @change="() => hanleNumberChange(index, true)"
              ></hd-number-box-test>
              <view v-else class="reason-qty">
                <text>x</text>
                {{ Number(reason.qpcQty) | fmt('0.00') }}
              </view>
            </block>
            <block v-else>
              <hd-number-box-test
                :step="1"
                v-model="innerValue.reasons[index].qty"
                :key="reason.reasonCode"
                v-if="reason.limitQty === null || reason.limitQty > 0"
                :max="reason.limitQty !== null ? reason.limitQty : 99999999"
                :scale="qtyScale"
                @change="() => hanleNumberChange(index, false)"
              ></hd-number-box-test>
              <view v-else class="reason-qty">
                <text>x</text>
                {{ Number(reason.qty) | fmt('0.00') }}
              </view>
            </block>
          </block>
          <text class="reason-total" v-if="reason.reasonBatchs.length">
            总退货数：
            <text class="amount">{{ handleCalcTotal(reason) }}</text>
          </text>
          <block v-if="isNormal">
            <image
              v-if="index === 0"
              lazy-load="true"
              class="reason-operator"
              :src="'/static/icon/ic_add48.png' | oss"
              @click="handleAddReason"
            ></image>
            <image
              v-else
              lazy-load="true"
              class="reason-operator"
              :src="'/static/icon/ic_delete48.png' | oss"
              @click="handleDeleteReason(index)"
            ></image>
          </block>
        </view>
      </view>
      <view
        class="reason-header justify-content-end"
        v-if="(isNormal || notifyEditable) && !isHasTraceCodes && !(innerValue.useVd && enableReasonValidDate) && innerValue.useDoubleMeasure"
      >
        <view class="qty-label">
          {{ !innerValue.isDisp ? `单品（${innerValue.goods.minMunit}）` : '重量（kg）' }}
        </view>
        <hd-number-box-test
          class="qty-operator"
          :step="1"
          v-model="innerValue.reasons[index].qty"
          :key="reason.reasonCode"
          v-if="reason.limitQty === null || reason.limitQty > 0"
          :max="reason.limitQty !== null ? reason.limitQty : 99999999"
          :scale="qtyScale"
          @change="() => hanleNumberChange(index, false)"
        ></hd-number-box-test>
        <view v-else class="reason-qty">
          <text>x</text>
          {{ Number(reason.qty) | fmt('0.00') }}
        </view>
      </view>
      <view class="reason-header" style="margin-top: 24rpx">
        <view class="flex">
          <view class="flex">
            <image class="reason-arrow" lazy-load="true" :src="'/static/icon/<EMAIL>' | oss"></image>
            <view class="reason-txt">关联原单：</view>
          </view>
          <view class="reason-txt" :class="{ 'reason-txt--none': !reason.receiptNum }" @click="handleSelectBill(reason, index)">
            {{ reason.receiptNum || '暂无关联收货单' }}
          </view>
          <image class="reason-arrow" lazy-load="true" :src="'/static/icon/ic_noticebar_right_grey.png' | oss"></image>
        </view>
        <view class="flex reason-bill" v-if="showAlcPrice">
          <view class="reason-bill-text">退货价：</view>
          <view class="reason-bill-prc">
            ¥ {{ (reason.bckSinglePrice || innerValue.goods.singlePrice) | empty }}/{{ innerValue.goods.minMunit | empty }}
          </view>
        </view>
      </view>
      <view class="upload">
        <hd-up-img-video
          :maxImgCount="3"
          :maxVideoCount="1"
          size="small"
          :videos="
            reason.reasonAttaches
              .filter((img) => {
                return isVideoUrl(img.fileUrl)
              })
              .map((img) => {
                return img.fileUrl
              })
          "
          :images="
            reason.reasonAttaches
              .filter((img) => {
                return isImageUrl(img.fileUrl)
              })
              .map((img) => {
                return img.fileUrl
              })
          "
          @upload="
            (type, data) => {
              doImageUpload(type, data, index)
            }
          "
          @remove="
            (type, data) => {
              doImageRemove(type, data, index)
            }
          "
          @error="doImageError"
          @signInfoKey="getSignInfoKey"
          @showLoading="doShowLoading"
        ></hd-up-img-video>
      </view>
      <view class="reason-tracecode" v-if="isHasTraceCodes">
        <view class="reason-tracecode-header">
          <text class="reason-tracecode-header-left">批次号</text>
          <text class="reason-tracecode-header-middle">溯源码</text>
          <text class="reason-tracecode-header-right">退货数量</text>
        </view>
        <view class="reason-tracecode-placeholder" v-if="!traceCodeBatchs(reason).length">请扫描商品溯源码</view>
        <view class="reason-tracecode-item" v-for="batch in traceCodeBatchs(reason)" :key="batch.vBNum">
          <text class="reason-tracecode-item-left">{{ batch.vBNum }}</text>
          <view class="reason-tracecode-item-middle">
            <text class="txt">{{ batch.traceCode }}</text>
            <text class="value">{{ batch.qty }}</text>
          </view>
          <view class="reason-tracecode-item-right">
            <image
              class="reason-tracecode-item-right-img"
              @click="handleRemoveBatch(index, batch)"
              mode="widthFix"
              :src="'/static/icon/<EMAIL>' | oss"
            ></image>
          </view>
        </view>
      </view>
      <view class="reason-batch" v-if="innerValue.useVd && enableReasonValidDate">
        <view class="reason-batch-header">
          <text class="reason-batch-header-left">批次号</text>
          <text class="reason-batch-header-middle">生产日期</text>
          <text class="reason-batch-header-right">退货数量</text>
        </view>
        <view class="reason-batch-item" v-for="batch in batchs(reason)" :key="batch.vBNum">
          <text class="reason-batch-item-left">{{ batch.vBNum }}</text>
          <view class="reason-batch-item-middle" @click="handleEditBatchDate(index, batch)">
            <text class="txt" v-if="batch.prdDate">{{ batch.prdDate | date }}</text>
            <text class="txt" v-else>生产日期</text>
          </view>
          <view class="reason-batch-item-qty" @click="handleEditBatchQty(index, batch)">{{ batch.qty }}</view>
          <view class="reason-batch-item-right">
            <image
              class="reason-batch-item-right-img"
              mode="widthFix"
              @click="handleRemoveBatch(index, batch)"
              :src="'/static/icon/<EMAIL>' | oss"
            ></image>
          </view>
        </view>

        <view class="reason-batch-add" @click="handleAddBatch(index)" v-if="batchs(reason).length < 5">添加效期</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" src="./ReturnApplyEditCard.ts"></script>

<style lang="scss" scoped>
.return-apply-edit-card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 228rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 24rpx 0;
  background: rgba(255, 255, 255, 1);

  .sku-goods {
    display: flex;
    box-sizing: border-box;
    padding: 0 24rpx;
  }

  .sku-left {
    position: relative;
    width: 120rpx;
    height: 120rpx;
    flex: 0 0 auto;
    margin-right: 16rpx;

    .sku-img {
      width: 120rpx;
      height: 120rpx;
    }
    .info__scale {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 24rpx;
      height: 24rpx;
      background: rgba(0, 0, 0, 0.4);
      border-radius: 8rpx 0px 8rpx 0rpx;
      text-align: center;
      @include flex();
      &-img {
        width: 16rpx;
        height: 16rpx;
      }
    }
  }

  .title {
    width: 100%;
    font-size: 30rpx;
    font-family: $font-medium;
    font-weight: 500;
    color: rgba(40, 44, 52, 1);
    line-height: 40rpx;
  }
  .main-exhibit {
    width: 702rpx;
    background: #f5f5f5;
    border-radius: 8rpx;
    padding: 8rpx 16rpx;
    box-sizing: border-box;
    flex: 0 0 auto;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    display: flex;
    align-items: center;

    .good-img {
      width: 32rpx;
      height: 32rpx;
      display: inline-table;
    }

    .goods-text {
      color: #333333;
      font-size: 26rpx;
      font-weight: 500;
    }

    .goods-one {
      flex: 1;
      @include ellipsis();
    }
  }
  .code {
    max-width: 100%;
    box-sizing: border-box;
    padding: 0 24rpx;
    margin-top: 8rpx;
    font-size: 26rpx;
    color: rgba(148, 150, 154, 1);
    line-height: 37rpx;
    @include flex(row, space-between);
    .code-line {
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $font-color-light;
      .code-text {
        color: $font-color-darklight;
      }
    }
  }
  .qpc {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 24rpx;
    margin-top: 14rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .qpc-price {
      font-size: 32rpx;
      font-weight: 600;
      color: rgba(255, 136, 0, 1);
      line-height: 40rpx;
    }
    .qpc-munit {
      font-size: 24rpx;
      font-weight: 400;
      margin-left: 12rpx;
      color: rgba(88, 90, 94, 1);
      line-height: 40rpx;
    }
    .qpc-line {
      @include flex(row, flex-start, center);
    }
    .price-line {
      margin-right: 20rpx;
    }
    .qpc-text {
      font-size: 24rpx;
      color: rgba(148, 150, 154, 1);
      line-height: 40rpx;
    }
    .qpc-detail {
      margin-left: 20rpx;
      font-size: 24rpx;
      color: rgba(148, 150, 154, 1);
      line-height: 40rpx;
    }
    .bckAlc {
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $font-color-light;

      .bckalc-text {
        color: $font-color-darklight;
      }
    }
  }
  .bckalc {
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: $font-color-light;

    .bckalc-text {
      color: $font-color-darklight;
    }
  }

  /* 库存信息样式 */
  .stock-info {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 24rpx;
    margin-top: 14rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .stock-item {
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $font-color-light;

      .stock-label {
        color: $font-color-light;
      }

      .stock-value {
        color: $font-color-darklight;
      }
    }
  }
  .text-body-text {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: rgb(148, 150, 154);

    .good-location {
      font-size: $font-size-xsmall;
      font-weight: 400;
      color: $color-text-secondary;
      max-width: 179rpx;
      height: 32rpx;
      line-height: 32rpx;
      @include ellipsis();
    }

    .good-img {
      width: 32rpx;
      height: 32rpx;
    }
  }
  .good-time {
    width: 100%;
    margin: 16rpx 0 12rpx;
    padding: 0 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    background: $color-white;

    .time-start {
      height: 64rpx;
      width: 342rpx;
      box-sizing: border-box;
      padding: 16rpx;
      @include flex(row, space-between);
      background: $color-bg-primary;
      border-radius: 8rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $color-text-primary;
      .time-label {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: $color-text-fourth;
      }
      .time-value {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: $color-text-primary;
        line-height: 40px;
      }
    }
    .time-icon {
      width: 28rpx;
      height: 28rpx;
    }
  }

  .out_time {
    margin: 10rpx 24rpx 0 24rpx;
    padding: 0 24rpx;
    box-sizing: border-box;
    width: 702rpx;
    height: 72rpx;
    line-height: 72rpx;
    background: #ffe9eb;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #fa273b;
    .error-image {
      margin-right: 12rpx;
      vertical-align: middle;
      width: 32rpx;
      height: 32rpx;
    }
  }
  .reason {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 0 24rpx;
    margin-top: 20rpx;
    width: 100%;

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .reason-number {
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .reason-operator {
          height: 48rpx;
          width: 48rpx;
          margin-left: 24rpx;
        }
        .reason-total {
          font-weight: 500;
          font-size: 26rpx;
          color: #333333;
          .amount {
            color: $color-primary;
          }
        }
        .limit-qty {
          display: flex;
          border-radius: 4rpx;
          border: 1rpx solid rgba(253, 52, 49, 0.4);
          text-align: right;
          font-size: 24rpx;
          font-weight: 400;
          font-family: PingFangSC-Regular, PingFang SC;
          color: $color-error;
          height: 40rpx;
          margin-right: 12rpx;

          &-label {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 64rpx;
            height: 40rpx;
            background: #ffecec;
          }
          &-number {
            height: 40rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 8rpx;
            background: #ffffff;
          }
        }
        .not-limit-label {
          margin-right: 12rpx;
          width: 64rpx;
          height: 40rpx;
          border-radius: 4rpx;
          border: 1rpx solid rgba(148, 148, 148, 0.4) !important;
          text-align: center;
          font-size: 24rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #a7a7a7;
          line-height: 40rpx;
        }
      }

      .reason-code {
        width: 100%;
        height: 20rpx;
      }

      .reason-txt {
        height: 48rpx;
        max-width: 230rpx;
        line-height: 48rpx;
        font-size: 24rpx;
        color: rgb(127, 132, 143);
        @include ellipsis();
      }
      .reason-qty {
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: $color-text-secondary;
        text {
          font-size: 24rpx;
          margin-right: 4rpx;
        }
      }
      .reason-txt--none {
        color: #cccccc;
      }
      .reason-arrow {
        height: 32rpx;
        width: 32rpx;
      }
      .reason-bill {
        flex: 0 0 auto;
        height: 32rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
        line-height: 32rpx;
        &-prc {
          color: #585a5e;
        }
      }
    }
    .justify-content-end {
      margin-top: 10rpx;
      justify-content: flex-end;
    }
    .qty-label {
      height: 48rpx;
      line-height: 48rpx;
      min-width: 120rpx;
      font-size: 30rpx;
      padding: 0 12rpx;
      font-weight: 400;
      color: rgba(148, 150, 154, 1);
    }
    .qty-operator {
      margin-right: 72rpx;
    }

    &-tracecode {
      width: 100%;
      margin-top: 24rpx;

      &-item {
        position: relative;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        &:not(:last-child) {
          margin-bottom: 12rpx;
        }
        &-left {
          flex: 0 0 auto;
          width: 116rpx;
          font-size: 26rpx;
          color: #666666;
          line-height: 36rpx;
          margin-right: 24rpx;
        }

        &-middle {
          display: flex;
          justify-content: space-between;
          flex: 1 1 auto;
          box-sizing: border-box;
          padding: 16rpx;
          background: #f5f6f7;
          border-radius: 8rpx;
          .txt {
            width: 408rpx;
            @include ellipsis();
            font-size: 24rpx;
            color: #333333;
          }
          .value {
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
          }
        }

        &-right {
          flex: 0 0 auto;
          width: 40rpx;
          height: 40rpx;
          padding: 8rpx;
          margin-left: 8rpx;
          &-img {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }

      &-header {
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding-right: 56rpx;
        margin-bottom: 26rpx;

        &-left {
          flex: 0 0 auto;
          width: 116rpx;
          font-size: 26rpx;
          color: #666666;
          line-height: 36rpx;
          margin-right: 24rpx;
        }

        &-middle {
          flex: 0 0 auto;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
          width: 88rpx;
          height: 36rpx;
          background: rgba(8, 176, 124, 0.05);
          border-radius: 4rpx;
          border: 1rpx solid rgba(8, 176, 124, 0.4);
          font-size: 24rpx;
          color: #08b07c;
        }

        &-right {
          display: flex;
          justify-content: flex-end;
          flex: 1 1 auto;
          width: 116rpx;
          height: 36rpx;
          font-size: 26rpx;
          color: #666666;
          line-height: 36rpx;
        }
      }

      &-placeholder {
        display: flex;
        justify-content: center;
        font-size: 28rpx;
        color: #999999;
      }
    }
    &-batch {
      width: 100%;
      margin-top: 24rpx;

      &-header {
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding-right: 56rpx;
        margin-bottom: 24rpx;

        &-left {
          flex: 0 0 auto;
          width: 116rpx;
          font-size: 26rpx;
          color: #666666;
          line-height: 36rpx;
          margin-right: 24rpx;
        }

        &-middle {
          flex: 0 0 auto;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
          font-size: 24rpx;
          width: 112rpx;
          height: 36rpx;
          background: #f2f6ff;
          border-radius: 4rpx;
          border: 1rpx solid rgba(28, 100, 253, 0.1);
          color: $color-primary;
        }

        &-right {
          display: flex;
          justify-content: flex-end;
          flex: 1 1 auto;
          width: 116rpx;
          height: 36rpx;
          font-size: 26rpx;
          color: #666666;
          line-height: 36rpx;
        }
      }

      &-item {
        position: relative;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        &:not(:last-child) {
          margin-bottom: 12rpx;
        }
        &-left {
          flex: 0 0 auto;
          width: 116rpx;
          font-size: 26rpx;
          color: #666666;
          line-height: 36rpx;
          margin-right: 24rpx;
        }

        &-middle {
          display: flex;
          justify-content: space-between;
          flex: 1 1 auto;
          box-sizing: border-box;
          padding: 16rpx;
          height: 64rpx;
          background: #f5f6f7;
          border-radius: 8rpx;
          margin-right: 12rpx;
          .txt {
            font-size: 24rpx;
            color: #333333;
            width: 264rpx;
            background: #f5f6f7;
            border-radius: 8rpx;
          }
        }

        &-qty {
          display: flex;
          flex: 0 0 auto;
          align-items: center;
          width: 164rpx;
          height: 64rpx;
          background: #f5f6f7;
          border-radius: 8rpx;
          flex: 0 0 auto;
          box-sizing: border-box;
          padding: 16rpx;
        }

        &-right {
          flex: 0 0 auto;
          width: 40rpx;
          height: 40rpx;
          padding: 8rpx;
          margin-left: 8rpx;
          &-img {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }

      &-add {
        width: 100%;
        height: 72rpx;
        background: #f0f5ff;
        border-radius: 8rpx;
        font-size: 28rpx;
        color: $color-primary;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .upload {
      margin-top: 20rpx;
    }
  }
}
.return-apply-edit-card:after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  bottom: 0;
  left: 19rpx;
  width: calc(100% - 38rpx);
  border-bottom: 2rpx solid rgba(227, 228, 232, 1);
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
</style>
