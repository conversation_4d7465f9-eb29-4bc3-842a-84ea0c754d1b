import BaseResponse from '@/model/base/BaseResponse'
import fly from '../fly'
import AppReceiptRecordBoxCheckDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxCheckDTO'
import AppReceiptRecordBoxFullCheckDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxFullCheckDTO'
import AppReceiptRecordLineCheckDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordLineCheckDTO'
import AppReceiptRecordFullCheckDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordFullCheckDTO'
import AppReceiptRecordDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordDTO'
import QueryRequest from '@/model/base/QueryRequest'
import AppReceiptRecordBoxDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxDTO'
import ID from '@/model/default/ID'
import AppReceiptRecordBoxGoodsDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxGoodsDTO'
import AppReceiptRecordLineDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordLineDTO'
import ReceiptRecordBoxSum from '@/model/AppReceiptRecord/default/ReceiptRecordBoxSum'
import AppReceiptRecordInitializer from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordInitializer'
import ReceiptRecordSubmitValidateResult from '@/model/AppReceiptRecord/default/ReceiptRecordSubmitValidateResult'
import JobInstanceDTO from '@/model/default/JobInstanceDTO'
import AppReceiptRecordAttachCreateDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordAttachCreateDTO'
import AppReceiptRecordAttachQueryDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordAttachQueryDTO'
import AppReceiptRecordSubmitterDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordSubmitterDTO'

export default class AppReceiptRecordApi {
  /**
   * 箱码行收货
   *
   * @param body 商品收货对象
   * @param tenant 租户标识
   */
  static boxCheckLine(body: AppReceiptRecordBoxCheckDTO): Promise<BaseResponse<AppReceiptRecordDTO>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/box/line/check`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 箱码行全部收货
   *
   * @param body 商品收货对象
   * @param tenant 租户标识
   */
  static boxFullCheck(body: AppReceiptRecordBoxFullCheckDTO): Promise<BaseResponse<AppReceiptRecordDTO>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/box/full/check`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 取消收货
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static cancel(body: ID): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/cancel`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 商品行收货
   *
   * @param body 商品收货对象
   * @param tenant 租户标识
   */
  static checkLine(body: AppReceiptRecordLineCheckDTO): Promise<BaseResponse<AppReceiptRecordDTO>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/line/check`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 商品行收货
   *
   * @param body 商品收货对象
   * @param tenant 租户标识
   */
  static fullCheck(body: AppReceiptRecordFullCheckDTO): Promise<BaseResponse<AppReceiptRecordDTO>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/full/check`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取指定单据
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static get(body: ID): Promise<BaseResponse<AppReceiptRecordDTO>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/get`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取待处理的收货记录
   *
   * @param body 收货单单据标识
   * @param tenant 租户标识
   */
  static getInitialByReceipt(body: AppReceiptRecordInitializer): Promise<BaseResponse<AppReceiptRecordDTO>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/initial/getByReceipt`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * c
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static saveAttach(body: AppReceiptRecordAttachCreateDTO[]): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/attaches/save`, body, {}).then((res) => {
      return res.data
    })
  }

  /**
   * 单头附件查询
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static listAttach(body: ID): Promise<BaseResponse<AppReceiptRecordAttachQueryDTO[]>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/attaches/list`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询箱码明细
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryBox(body: QueryRequest): Promise<BaseResponse<AppReceiptRecordBoxDTO[]>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/box/lines/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询周转箱商品
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryBoxGoods(body: QueryRequest): Promise<BaseResponse<AppReceiptRecordBoxGoodsDTO[]>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/box/goods/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询单据明细
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryLine(body: QueryRequest): Promise<BaseResponse<AppReceiptRecordLineDTO[]>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/lines/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 提交
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static submit(body: AppReceiptRecordSubmitterDTO): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/submit`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 提交并加工单据
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static submitAndProcess(body: AppReceiptRecordSubmitterDTO): Promise<BaseResponse<JobInstanceDTO>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/submitAndProcess`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 提交校验
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static validate4Submit(body: ID): Promise<BaseResponse<ReceiptRecordSubmitValidateResult>> {
    return fly.post(`sos/v1/{tenant}/receiptrecord/validate4Submit`, body, {}).then((res) => {
      return res
    })
  }
}
