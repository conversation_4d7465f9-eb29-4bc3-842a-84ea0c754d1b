/*
 * @Author: 刘湘
 * @Date: 2022-02-09 16:47:56
 * @LastEditTime: 2022-10-21 14:40:29
 * @LastEditors: 刘湘
 * @Description:
 * @FilePath: \soa\src\model\invXFApply\InvXFApplyModification.ts
 * 记得注释
 */
import InvXFApplyModificationLine from 'model/invXFApply/InvXFApplyModificationLine'
import InvXFApplyModificationPackLine from './InvXFApplyModificationPackLine'

export default class InvXFApplyModification {
  // 单据标识
  billId: string = ''
  // 相对方门店标识,调拨类型为调入时，它为调出门店；调拨类型为调出时，它为调入门店
  counterShopId: string = ''
  // 相对方门店代码
  counterShopNo: string = ''
  // 相对方门店名称
  counterShopName: string = ''
  // 备注
  note: Nullable<string> = null
  // 调拨模式
  invXfMode: Nullable<number> = null
  // 商品明细
  lines: InvXFApplyModificationLine[] = []
  // 配送模式
  deliveryType: Nullable<number> = null
  // 包材明细
  packLines: InvXFApplyModificationPackLine[] = []
}
