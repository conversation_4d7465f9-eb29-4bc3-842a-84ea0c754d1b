/*
 * @Author: weish<PERSON>
 * @Date: 2023-06-12 14:55:21
 * @LastEditTime: 2024-06-25 17:42:23
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: \soa\src\model\requireApply\RequireApplyBaseLine.ts
 * 记得注释
 */
import Goods from './Goods'
import RequireApplyBatchBaseLine from './RequireApplyBatchBaseLine'
import RequireApplySubBaseLine from './RequireApplySubBaseLine'
import Sort from './Sort'
import rand from './rand'

export default class RequireApplyBaseLine {
  // 商品
  goods: Goods = new Goods()
  // 面板分类大类Uuid
  categoryUuid: Nullable<string> = null
  // 面板分类大类代码
  categoryCode: Nullable<string> = null
  // 面板分类大类名称
  categoryName: Nullable<string> = null
  // 建议数量
  suggestQty: Nullable<number> = null
  // 已要货数量
  requiredQty: Nullable<number> = null
  // 数量
  qty: number = 0
  // 金额
  total: number = 0
  // 规格数量
  qpcQty: string = ''
  // 叫货原因代码
  reasonCode: Nullable<string> = null
  // 叫货原因名称
  reasonName: Nullable<string> = null
  // 类别
  sort: Nullable<Sort> = null
  // 品牌
  brand: Nullable<rand> = null
  // 备注
  note: Nullable<string> = null
  // 是否散货
  isDisp: Nullable<boolean> = null
  // 原价
  srcPrice: Nullable<number> = null
  // 来源类型，0-自主叫货，1-建议叫货
  srcType: Nullable<number> = null
  // 上架备注
  shelveNote: Nullable<string> = null
  // 是否限量,0-否，1-是
  isLimitQty: Nullable<number> = null
  // 活动标识
  activityId: Nullable<string> = null
  // 商品类型 ,0-普通商品，4-爆品
  goodsType: Nullable<number> = null
  // 方案编码
  schemeNo: Nullable<string> = null
  // 是否启用效期管理：1-启用，0或其它-不启用
  useVd: Nullable<number> = null
  // 录入商品时的库存数量
  invQty: Nullable<number> = null
  // 含辅料
  hasAccessory: Nullable<number> = null
  // 辅料数量
  accQty: Nullable<number> = null
  // 辅料金额
  accTotal: number = 0
  // 辅料审批金额
  approvalAccTotal: Nullable<number> = null
  // 零售单价
  rtlPrc: Nullable<number> = null
  // 零售规格价
  rtlQpcPrc: Nullable<number> = null
  // 零售金额
  rtlTotal: Nullable<number> = null
  // 配货单价
  alcPrc: Nullable<number> = null
  // 配货规格价
  alcQpcPrc: Nullable<number> = null
  // 配货金额
  alcTotal: Nullable<number> = null
  // 体积
  volume: Nullable<number> = null
  // 体积
  qpcVolume: Nullable<number> = null
  // 溯源码管理：0-否；1-是
  useTraceCode: Nullable<number> = null
  // 溯源码规则：0-标准；1-按肉品；2-酒类源码；3-唯一码
  traceCodeRule: Nullable<number> = null
  // 必订品：0-否；1-是
  reqOrd: Nullable<number> = null
  // 订货倍数
  ordMultiple: Nullable<number> = null
  // 要货下限
  lowOrd: Nullable<number> = null
  // 叫货下限控制
  lowOrdCtrl: Nullable<number> = null
  // 要货上限
  highOrd: Nullable<number> = null
  // 叫货上限控制
  highOrdCtrl: Nullable<number> = null
  // 来源行号
  srcLineNo: Nullable<number> = null
  // 批号明细
  batchLines: RequireApplyBatchBaseLine[] = []
  // 商品子明细
  subLines: RequireApplySubBaseLine[] = []
}
