/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2024-03-21 10:23:11
 * @LastEditTime: 2025-05-15 11:28:42
 * @LastEditors: hanwei
 * @Description:
 * @FilePath: \soa\src\model\gdQuery\GoodsQueryInfo.ts
 * 记得注释
 */
import GoodsExt from './GoodsExt'
import tag from './Tag'

export default class GoodsQueryInfo extends GoodsExt {
  // 保质期天数
  validPeriod: Nullable<number> = null
  // 当前库存(单品数量)
  busQty: Nullable<number> = null
  // 月销量（单品数量）
  monthSaleQty: Nullable<number> = null
  // 日均销量（单品数量）
  avgSQty: Nullable<number> = null
  // 周销量
  weekSaleQty: Nullable<number> = null
  // 商品状态代码
  busGateCode: Nullable<string> = null
  // 商品状态名称
  busGateName: Nullable<string> = null
  // 商品类型：0-普通商品，4-特价商品(爆品)
  goodsType: Nullable<number> = null
  // 当前门店库存(单品数量)
  invQty: Nullable<number> = null
  // 门店库存(包装数量)
  invQpcQtyTitle: Nullable<string> = null
  // 在途库存数量
  inTransitInvQty: Nullable<number> = null
  // 在途库存数量(包装数量)
  inTransitInvQpcQty: Nullable<string> = null
  // 未发货库存数量(单品数量)
  inOrderInvQty: Nullable<number> = null
  // 未发货库存数量(包装数量)
  inOrderInvQpcQty: Nullable<string> = null
  // 库存成本价
  invPrc: Nullable<number> = null
  // 近N天毛利
  grossProfit: Nullable<number> = null
  // 近N天毛利标题
  grossProfitTitle: Nullable<string> = null
  // 商品标题
  friendlyStr: Nullable<string> = null
  // 陈列位置
  displayLocation: Nullable<string> = null
  // 库存下限
  lowInv: Nullable<number> = null
  // 库存上限
  highInv: Nullable<number> = null
  // 商品标签
  tags: tag[] = []
}
