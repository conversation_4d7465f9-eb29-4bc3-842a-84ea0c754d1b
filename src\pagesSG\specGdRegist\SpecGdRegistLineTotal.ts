/*
 * @Author: weish<PERSON>
 * @Date: 2025-04-18 18:43:10
 * @LastEditTime: 2025-04-24 15:28:01
 * @LastEditors: weisheng
 * @LastEditTime: 2025-04-24 10:37:21
 * @LastEditors: weisheng
 * @Description: 特殊品登记明细总列表
 * @FilePath: /soa/src/pagesSG/specGdRegist/SpecGdRegistLineTotal.ts
 * 记得注释
 */
import { Vue, Component } from 'vue-property-decorator'
import SpecGdRegistApi from '@/network/AppSpecGdRegistApi/AppSpecGdRegistApi'
import AppSpecGdRegistLineDTO from '@/model/AppSpecGdRegist/AppSpecGdRegistLineDTO'
import QueryRequest from '@/model/base/QueryRequest'
import SpecGdRegistGoodsList from './cmp/SpecGdRegistGoodsList.vue'

@Component({
  components: {
    SpecGdRegistGoodsList
  }
})
export default class SpecGdRegistLineTotal extends Vue {
  billId: string = '' // 单据ID
  billLines: AppSpecGdRegistLineDTO[] = [] // 单据明细
  pageNum: number = 0 // 当前页码
  pageSize: number = 20 // 每页大小
  isLoading: boolean = false // 是否正在加载
  hasMore: boolean = true // 是否有更多数据
  totalCount: number = 0 // 总数量

  onLoad(option: { id: string }) {
    if (option.id) {
      this.billId = option.id
      this.getBillLines()
    }
  }

  // 上拉加载更多
  onReachBottom() {
    if (!this.isLoading && this.hasMore) {
      this.pageNum++
      this.getBillLines()
    }
  }

  // 获取单据明细
  async getBillLines() {
    if (this.isLoading || !this.hasMore) return

    try {
      this.isLoading = true
      if (this.pageNum === 0) {
        this.$showLoading({ delayTime: 200 })
      }

      const queryRequest = new QueryRequest()
      queryRequest.page = this.pageNum
      queryRequest.pageSize = this.pageSize
      queryRequest.conditions = [
        {
          operation: 'billId:=',
          parameters: [this.billId]
        }
      ]

      const resp = await SpecGdRegistApi.queryLine(queryRequest)

      // 第一页时直接赋值，后续页追加数据
      if (this.pageNum === 0) {
        this.billLines = resp.data || []
      } else {
        this.billLines = [...this.billLines, ...(resp.data || [])]
      }

      // 更新是否有更多数据
      this.hasMore = resp.more || false
      this.totalCount = resp.total || this.billLines.length

      // 设置页面标题
      uni.setNavigationBarTitle({
        title: '全部商品(' + this.totalCount + ')'
      })

      this.isLoading = false
      this.$hideLoading()
    } catch (error) {
      this.isLoading = false
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: (error as any).msg })
    }
  }
}
