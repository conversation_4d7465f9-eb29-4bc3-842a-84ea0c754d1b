{
    "name" : "hd-portal-app",
    "appid" : "__UNI__86A41FA",
    "description" : "",
    "versionName" : "0.0.1",
    "versionCode" : "1",
    "transformPx" : false,
    "app-plus" : {
        "optimization" : {
            "subPackages" : true
        },
        "runmode" : "liberate", // 开启分包优化后，必须配置资源释放模式
        "compatible" : {
            "ignoreVersion" : true
        },
        /* 5+App特有相关 */
        "modules" : {
            "Bluetooth" : {},
            "VideoPlayer" : {},
            "Camera" : {},
            "Barcode" : {},
            "Geolocation" : {},
            "Push" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                "permissionExternalStorage" : {
                    "request" : "always",
                    "prompt" : "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"
                },
                /* android打包配置 */
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />",
                    "<uses-permission android:name=\"android.permission.MANAGE_EXTERNAL_STORAGE\" />",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ]
            },
            "ios" : {
                "idfa" : false,
                "dSYMs" : false
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "push" : {}
            },
            "icons" : {
                "ios" : {
                    "appstore" : "unpackage/res/icons/ios/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/ios/76x76.png",
                        "app@2x" : "unpackage/res/icons/ios/152x152.png",
                        "notification" : "unpackage/res/icons/ios/20x20.png",
                        "notification@2x" : "unpackage/res/icons/ios/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/ios/167x167.png",
                        "settings" : "unpackage/res/icons/ios/29x29.png",
                        "settings@2x" : "unpackage/res/icons/ios/58x58.png",
                        "spotlight" : "unpackage/res/icons/ios/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/ios/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/ios/120x120.png",
                        "app@3x" : "unpackage/res/icons/ios/180x180.png",
                        "notification@2x" : "unpackage/res/icons/ios/40x40.png",
                        "notification@3x" : "unpackage/res/icons/ios/60x60.png",
                        "settings@2x" : "unpackage/res/icons/ios/58x58.png",
                        "settings@3x" : "unpackage/res/icons/ios/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/ios/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/ios/120x120.png"
                    }
                },
                "android" : {
                    "hdpi" : "unpackage/res/icons/android/72x72.png",
                    "xhdpi" : "unpackage/res/icons/android/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/android/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/android/192x192.png"
                }
            },
            "splashscreen" : {
                "iosStyle" : "storyboard",
                "ios" : {
                    "storyboard" : "unpackage/res/splash/CustomStoryboard.zip"
                }
            }
        },
        /* SDK配置 */
        "usingComponents" : true,
        "nativePlugins" : {
            "Aliyun-Push" : {
                "阿里云移动推送Android AppKey" : "",
                "阿里云移动推送Android AppSecret" : "",
                "阿里云移动推送iOS AppKey" : "335190412",
                "阿里云移动推送iOS AppSecret" : "6b8c385da6f94ea49ca4480242cb8943",
                "__plugin_info__" : {
                    "name" : "阿里云移动推送",
                    "description" : "移动推送（Mobile Push）是提供给移动开发者的移动端消息推送服务，通过在App中集成推送功能，进行高效、精准、实时的消息推送，从而使业务及时触达用户，提高用户粘性。",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=7628",
                    "android_package_name" : "com.hd123.hd.portal.dev",
                    "ios_bundle_id" : "com.hd123.hd.portal.dev",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "7628",
                    "parameters" : {
                        "阿里云移动推送Android AppKey" : {
                            "des" : "阿里云EMAS移动应用标识",
                            "key" : "",
                            "value" : ""
                        },
                        "阿里云移动推送Android AppSecret" : {
                            "des" : "阿里云EMAS移动应用密钥",
                            "key" : "",
                            "value" : ""
                        },
                        "阿里云移动推送iOS AppKey" : {
                            "des" : "阿里云EMAS移动应用标识",
                            "key" : "aliyun:push:appKey",
                            "value" : ""
                        },
                        "阿里云移动推送iOS AppSecret" : {
                            "des" : "阿里云EMAS移动应用密钥",
                            "key" : "aliyun:push:appSecret",
                            "value" : ""
                        }
                    }
                }
            }
        }
    },
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "usingComponents" : false,
        "lazyCodeLoading" : "requiredComponents",
        "appid" : "wx5637c5a78f58f352", // 测试环境

        // "appid" : "wx9d007cb9a30a3840", // 川鼎汇uat
        // "appid" : "wx74f92ec633de006a", // 川鼎汇生产
        "setting" : {
            "urlCheck" : false,
            "minified" : true
        },
        "optimization" : {
            "subPackages" : true
        },
        "__usePrivacyCheck__" : true
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "lazyCodeLoading" : "requiredComponents"
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "mp-qq" : {
        "usingComponents" : true
    },
    "h5" : {
        "router" : {
            "mode" : "hash",
            "base" : "./"
        }
    },
    "uniStatistics" : {
        "enable" : false
    }
}
