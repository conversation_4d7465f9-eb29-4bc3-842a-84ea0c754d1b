/*
 * @Author: 庞昭昭
 * @Date: 2021-10-11 13:55:44
 * @LastEditTime: 2025-04-18 11:14:17
 * @LastEditors: yuzhipi
 * @Description: 任务详情
 * @FilePath: /soa/src/pagesTaskCenter/taskDetail/TaskDetail.ts
 * 记得注释
 */
import { Vue, Component, Provide } from 'vue-property-decorator'
import SoptCheckExt from './cmp/SoptCheckExt.vue'
import TaskOpreaRecord from './cmp/TaskOpreaRecord.vue'
import UniPopup from '@/components/uni-popup/uni-popup.vue'
import ShopTaskDetail from '@/model/shopTaskCenter/ShopTaskDetail'
import {
  TaskStateEnum,
  TaskStateChineseEnum,
  TaskPriorityEnum,
  taskTagEnum,
  nowOpreaStateEnum,
  TransferStateEnum,
  fetchPathsEnum,
  taskTypeEnum,
  shopTaskItemTypeEnum,
  PlanType,
  taskItemStateEnum
} from '@/model/shopTaskCenter/TaskEnum'
import ShopTaskApi from '@/network/shopTaskCenter/ShopTaskApi'
import TimeShaft from './cmp/TimeShaft.vue'
import TaskApi from '@/network/shopTaskCenter/TaskApi'
import ShopTaskItem from '@/model/shopTaskCenter/ShopTaskItem'
import AssociatedTask from '@/model/shopTaskCenter/AssociatedTask'
import SoptCheckDetail from './cmp/SoptCheckDetail.vue'
import { Action, Getter, State } from 'vuex-class'
import Store from '@/model/store/Store'
import Shop from '@/model/shopTaskCenter/Shop'
import UserInfo from '@/model/user/UserInfo'
import TaskTag from '@/components/task-tag/task-tag.vue'
import CardItem from './cmp/CardItem.vue'
import AdjustFeedback from './cmp/AdjustFeedback.vue'
import OrdersModule from './cmp/OrdersModule.vue'
import ShopTaskLog from '@/model/shopTaskCenter/ShopTaskLog'
import PriceAdjustReq from '@/model/shopTaskCenter/PriceAdjustTaskBatchAdjustReq'
import ShopTaskFeedbackReq from '@/model/shopTaskCenter/ShopTaskFeedbackReq'
import { feedbackTypeEnum } from '@/model/shopTaskCenter/TaskEnum'
import { Permission } from '@/model/user/Permission'
import PermissionMgr from '@/mgr/PermissionMgr'
import ShopMangeEmployee from '@/model/employee/shopMangeEmployee'
import ShopTaskItemSurveyDetail from '@/model/shopTaskCenter/ShopTaskItemSurveyDetail'
import SurveyTask from './cmp/SurveyTask.vue'
import AiInspectionTask from './cmp/AiInspectionTask.vue'
import Evaluation from './cmp/Evaluation.vue'
import HistoryEval from './cmp/HistoryEval.vue'
import OtrTask from './cmp/OtrTask.vue'
import MediaInfo from '@/model/shopTaskCenter/MediaInfo'
import VmdAccountTemp from './cmp/vmdAccountTemp/VmdAccountTemp.vue'
import ShopTaskItemFeedbackReq from '@/model/shopTaskCenter/ShopTaskItemFeedbackReq'
import ShopTaskItemAuditReq from '@/model/shopTaskCenter/ShopTaskItemAuditReq'
import DingTalkDriveApi from '@/network/dingTalkDriveApi/DingTalkDriveApi'
import LogMgr from '@/Log/LogMgr'
import SoptCheck from './cmp/SoptCheck.vue'
import SaveSeqList from '@/model/shopTaskCenter/SaveSeqList'
import CommonUtil from '@/utils/CommonUtil'
import ShopTaskItemBatchFeedbackReq from '@/model/shopTaskCenter/ShopTaskItemBatchFeedbackReq'
import SaveSeqPatrolList from '@/model/shopTaskCenter/SaveSeqPatrolList'
import ShopTaskItemAuditReqExt from '@/model/shopTaskCenter/ShopTaskItemAuditReqExt'
import ShopTaskItemAuditBatchReq from '@/model/shopTaskCenter/ShopTaskItemAuditBatchReq'
import UniformFeek from './cmp/UniformFeek.vue'
import SurveyItem from '@/model/shopTaskCenter/SurveyItem'
import SurveyContent from '@/model/shopTaskCenter/SurveyContent'
import TaskUtils from '@/components/task-tag/TaskUtils'
import AppPrintLabelApi from '@/network/AppPrintLabelApi/AppPrintLabelApi'
import AppPrintLabelTpGoodsDTO from 'model/AppPrintLabel/AppPrintLabelTpGoodsDTO'
import AppPrintLabelGoodsExpandDTO from 'model/AppPrintLabel/AppPrintLabelGoodsExpandDTO'

// 任务优先级图片枚举
enum TaskPriorityImgEnum {
  P1 = 'ic_tag_jinji',
  P2 = 'ic_tag_zhongyao',
  P3 = 'ic_tag_ciyao'
}
// 价签商品列表类型
class CateGoodsType {
  adjustedNum: number = 0
  allListNum: number = 0
  list: any[] = []
  name: string = ''
  nonAdjustNum: number = 0
}

@Component({
  components: {
    SoptCheck,
    SoptCheckExt,
    UniPopup,
    TaskOpreaRecord,
    TimeShaft,
    SoptCheckDetail,
    TaskTag,
    CardItem,
    AdjustFeedback,
    OrdersModule,
    SurveyTask,
    Evaluation,
    HistoryEval,
    OtrTask,
    VmdAccountTemp,
    UniformFeek,
    AiInspectionTask
  }
})
export default class TaskDetail extends Vue {
  @State('userInfo') userInfo: UserInfo // 登录后返回的信息
  @State('store') store: Nullable<Store> // 门店信息
  @Getter('hasShopManager') hasShopManager: boolean // 是否是店长

  @Action('isVmdTaskSuper') isVmdTaskSuper: any // 超市陈列任务
  $refs: any
  taskUuid: string = '' // 任务uuid

  taskStateEnum = TaskStateEnum // 任务状态枚举
  taskStateChineseEnum = TaskStateChineseEnum // 任务状态中文转义枚举
  taskPriorityEnum = TaskPriorityEnum // 任务优先级枚举
  taskPriorityImgEnum = TaskPriorityImgEnum // 任务优先级图片枚举
  taskTagEnum = taskTagEnum // 任务tag枚举
  nowOpreaStateEnum = nowOpreaStateEnum // 操作流程-动作枚举
  transferStateEnum = TransferStateEnum // 转交过程状态枚举
  fetchPathsEnum = fetchPathsEnum // 详情接口请求附加相关信息枚举
  taskTypeEnum = taskTypeEnum // 任务类型
  shopTaskItemTypeEnum = shopTaskItemTypeEnum // 点检项展示类型
  planType = PlanType // 任务类型

  taskDetail: ShopTaskDetail = new ShopTaskDetail() // 任务详情
  shopTaskItem: ShopTaskItem[] = [] // 点检项集合
  surveyTaskItem: ShopTaskItemSurveyDetail[] = [] // 调整任务集合
  batchTaskItemLog: ShopTaskLog[] = [] // 任务logs集合

  showSoptCheckDetail: boolean = false // 是否显示点检项详情轮播
  currentSoptCheckUuid: string = '' // 查看点检项详情uuid

  deliver: any = '' //选中转交人
  shopList: Shop[] = [] // 可选门店列表

  showTimeShaft: boolean = true // 是否显示时间轴
  isSumbitModal: boolean = false // 是否自动展示提交提示框
  feedbackDetail: ShopTaskLog = new ShopTaskLog() // 反馈详情
  historyDetail: ShopTaskLog[] = [] // 历史评价详情
  cardViewEval: boolean = false // 从卡片点击查看历史
  soptCheckFeedbackDetail: ShopTaskLog = new ShopTaskLog() // 反馈详情
  auditDetail: ShopTaskLog = new ShopTaskLog() // 评价详情
  taskItemStateEnum = taskItemStateEnum // 点检项状态枚举
  showVideo: boolean = false // 是否展示视频预览
  srcVideo: string = '' // 预览视频的url
  goodsCategoryList: any = [new CateGoodsType()] // 调整商品左侧分类
  activeIndex: number = 0 // 调整商品当前选中的index
  goodsList: any = [] // 当前选中分类下的商品
  allSku: any = [] // 所有商品列表
  adjustSkuNum: number = 0 // 所有调整商品数量
  feedbackTypeEnum = feedbackTypeEnum // 反馈类型枚举
  showPrompt: boolean = true // 畅销品待处理时顶部提示是否展示
  openTransfer: boolean = false // 进入后是否直接展开转交弹窗

  surveyTaskFinish: boolean = false // 调查类任务是否可提交
  vmdAccountTaskFinish: boolean = false // 台账类任务单模板反馈内容变动后是否可以提交
  vmdSingleTempBody: ShopTaskItemFeedbackReq = new ShopTaskItemFeedbackReq() // 台账任务反馈内容变化请求体
  vmdAuditFinish: boolean = false // 台账类任务单模板反馈内容变动后是否可以提交
  vmdAuditBody: ShopTaskItemAuditReq = new ShopTaskItemAuditReq() // 台账任务反馈内容变化请求体
  saveSeqList: SaveSeqList[] = [] //准备保存的点检项信息
  allSoptCheckFeedbackList: SaveSeqList[] = [] //所有点击项门店反馈信息
  allSoptCheckEvaList: SaveSeqPatrolList[] = [] //所有评价反馈信息
  items: SurveyItem[] = [] // 展示并绑定的调查类任务项
  @Provide('changeData')
  changeData: any = this.upSaveAeq

  @Provide('changeEvaData')
  changeEvaData: any = this.upSaveEvaAeq
  saveEvaSeqList: SaveSeqPatrolList[] = [] //准备保存的待编辑点检项信息
  curSoptCheck: ShopTaskItem = new ShopTaskItem() //统一反馈陈列项信息
  isExpanded: boolean = false //是否展开
  isOverflow: boolean = false //是否超出

  /**
   * 任务名称
   */
  get taskName() {
    if (this.taskDetail) {
      return this.taskDetail.name
    }
  }

  /**
   * 任务描述去除html标签
   */
  get taskDesc() {
    return this.taskDetail.description
      ? this.taskDetail.description
          .replace(/<[^>]+>/g, '')
          .replace(/[|]*\n/, '')
          .replace(/&nbsp;/gi, '')
      : ''
  }

  /**
   * 任务优先级图片枚举
   */
  get taskPriority() {
    return this.taskDetail.priority ? this.taskPriorityImgEnum[this.taskDetail.priority!] : ''
  }

  /**
   * 任务状态
   */
  get taskState() {
    return this.taskDetail.state ? this.taskStateChineseEnum[this.taskDetail.state!] : '--'
  }

  /**
   * 任务是否可以撤回
   */
  get isRollBack() {
    return (
      (this.taskDetail.state === this.taskStateEnum.SUBMITTED || this.taskDetail.state === this.taskStateEnum.UNEVALUATED) &&
      this.taskDetail.enableRollback
    )
  }

  /**
   * 任务可编辑点检项
   */
  get isAllowEdit() {
    //需要统一反馈的不允许编辑单个陈列项
    if (this.uniformlyFeek && (this.taskDetail.state == this.taskStateEnum.UNDISPOSED || this.taskDetail.state == this.taskStateEnum.RECTIFYING)) {
      return false
    }

    return (
      (this.taskDetail.state == this.taskStateEnum.UNDISPOSED ||
        this.taskDetail.state == this.taskStateEnum.RECTIFYING ||
        this.taskDetail.state == this.taskStateEnum.UNEVALUATED ||
        this.taskDetail.state == this.taskStateEnum.WITHOUT_RE_EVALUATION) &&
      this.canOpreate &&
      !this.surveyTask &&
      !this.isSkuCheckVmdTask &&
      !this.isVmdAccountTask &&
      !this.priceAdjustTask &&
      !this.autoOrderTask &&
      !this.isSkuCheckOtrTask &&
      !this.isAiInspectionTask
    )
  }

  /**
   * 任务状态对应的文案颜色
   */
  get taskStateColor() {
    if (
      this.taskState == this.taskStateChineseEnum.INVALID ||
      this.taskState == this.taskStateChineseEnum.OVERDUE ||
      this.taskState == this.taskStateChineseEnum.REVIEW_OVERDUE
    ) {
      return 'info-state'
    } else if (
      this.taskState == this.taskStateChineseEnum.TERMINATE ||
      this.taskState == this.taskStateChineseEnum.RECTIFYING ||
      this.taskState == this.taskStateChineseEnum.RECTIFICATION_UNFINISHED
    ) {
      return 'error-state'
    } else if (this.taskState == this.taskStateChineseEnum.FINISHED) {
      return 'success-state'
    } else {
      return ''
    }
  }

  /**
   * 点检项总数
   */
  get pointNum() {
    return this.taskDetail.pointNum || 0
  }

  /**
   * 合格点检项
   */
  get eligiblePointNum() {
    return this.taskDetail.eligiblePointNum! || 0
  }

  /**
   * 不合格点检项
   */
  get ineligiblePointNum() {
    return this.taskDetail.ineligiblePointNum! || 0
  }

  /**
   * 未做点检项
   */
  get notDoneNum() {
    return this.pointNum - this.eligiblePointNum - this.ineligiblePointNum
  }

  /**
   * 已完成点检项
   */
  get finishNum() {
    return this.taskDetail.feedbackPointNum || 0
  }

  /**
   * 未完成点检项
   */
  get unFinishNum() {
    return this.pointNum! - this.finishNum!
  }

  /**
   * 是否显示得分内容
   */
  get showScore() {
    return TaskUtils.doShowScore(this.taskDetail.state!)
  }

  /**
   * 关联任务
   */
  get taskRelevance(): AssociatedTask {
    if (this.taskDetail && this.taskDetail.tasks && this.taskDetail.tasks.length > 0) {
      return this.taskDetail.tasks[0]
    }
    return new AssociatedTask()
  }

  // 操作记录集合
  get opreaLogList() {
    return this.taskDetail.flows
  }

  /**
   * 是否显示操作区域
   */
  get showOprea() {
    if (
      this.taskDetail.state == this.taskStateEnum.UNCLAIMED ||
      this.taskDetail.state == this.taskStateEnum.UNDISPOSED ||
      this.taskDetail.state == this.taskStateEnum.RECTIFYING ||
      this.taskDetail.state == this.taskStateEnum.UNEVALUATED ||
      this.taskDetail.state == this.taskStateEnum.WITHOUT_RE_EVALUATION ||
      this.taskState == this.taskStateChineseEnum.REVIEW_OVERDUE
    ) {
      return true
    }
    return false
  }

  /**
   * 转交被拒绝
   */
  get showRefusedTransfer() {
    return this.taskDetail.transferState == this.transferStateEnum.REFUSED
  }

  /**
   * 转交中
   */
  get showTransfering() {
    return this.taskDetail.transferState == this.transferStateEnum.TRANSFER
  }

  /**
   * 取消转交
   */
  get showCancelTransfer() {
    return this.taskDetail.transferState == this.transferStateEnum.CANCELED
  }

  /**
   * 同意转交
   */
  get showAcceptransfer() {
    return this.taskDetail.transferState == this.transferStateEnum.ACCEPTED
  }

  /**
   * 是否显示转交按钮
   */
  get showTransferOpreate() {
    // 非价签调整任务 ((未领取 || (待处理 && 已完成点检项数为0)) && (未有转交记录 || 已拒绝 || 已取消))
    // 价签调整任务 (价签调整任务 && (未领取 || 待处理)&& (未有转交记录 || 已拒绝 || 已取消) && 所有商品未全部调整 && 不存在已经调整商品)
    // 畅销品缺货订货任务 未领取 && (未有转交记录 || 已拒绝 || 已取消)
    return (
      // 下面是非价签非畅销品任务转交按钮显示控制
      ((!this.priceAdjustTask &&
        !this.autoOrderTask &&
        !this.isSkuCheckOtrTask &&
        (this.taskDetail.state == this.taskStateEnum.UNCLAIMED || (this.taskDetail.state == this.taskStateEnum.UNDISPOSED && this.finishNum == 0))) ||
        // 下面是价签调整任务转交按钮显示控制
        (this.priceAdjustTask &&
          (this.taskDetail.state == this.taskStateEnum.UNDISPOSED || this.taskDetail.state == this.taskStateEnum.UNCLAIMED) &&
          this.adjustSkuNum !== this.adjustedNum &&
          !this.adjustedNum) ||
        // 下面是畅销品缺货订货任务或重点商品订货提醒任务转交按钮显示控制
        ((this.autoOrderTask || this.isSkuCheckOtrTask) && this.taskDetail.state == this.taskStateEnum.UNCLAIMED)) &&
      (!this.taskDetail.transferState ||
        this.taskDetail.transferState === this.transferStateEnum.REFUSED ||
        this.taskDetail.transferState === this.transferStateEnum.CANCELED)
    )
  }

  /**
   * 是否显示提交按钮
   */
  get showSubmitOprea() {
    if (
      (this.taskDetail.state == this.taskStateEnum.WITHOUT_RE_EVALUATION || this.taskDetail.state == this.taskStateEnum.UNEVALUATED) &&
      this.canOpreate
    ) {
      // （任务状态为待评价 || 待复评） && 登录用户为执行人
      return true
    }
    return false
  }

  /**
   * 是否可以提交
   */
  get canSubmit() {
    if (this.taskDetail.state == this.taskStateEnum.UNEVALUATED || this.taskDetail.state == this.taskStateEnum.WITHOUT_RE_EVALUATION) {
      if (this.isVmdAccountTask) {
        // 台账任务单模板在任务详情提交判断
        if (this.shopTaskItem.length && this.shopTaskItem[0].lines.length && this.shopTaskItem[0].lines.length === 1) {
          return this.vmdAuditFinish
        } else {
          // 待评价或待复评的没完成项是0时可提交
          return this.notDoneNum == 0
        }
      } else {
        return true
      }
    } else {
      if (this.isAllowEdit) {
        return true
      }
      return (
        (!this.priceAdjustTask &&
          !this.surveyTask &&
          !this.isSkuCheckVmdTask &&
          !this.isSkuCheckOtrTask &&
          !this.isAiInspectionTask &&
          this.finishNum! == this.pointNum!) ||
        (this.priceAdjustTask && this.adjustSkuNum == this.adjustedNum) ||
        (this.surveyTask && this.surveyTaskFinish) ||
        this.isSkuCheckVmdTask ||
        this.isSkuCheckOtrTask ||
        (this.isVmdAccountTask && this.vmdAccountTaskFinish) ||
        (this.isAiInspectionTask && this.vmdAccountTaskFinish)
      )
    }
  }

  /**
   * 非本人创建的任务不显示终止按钮 && 不是整改单（整改子任务）
   */
  get canTermination() {
    return this.userInfo.uuid == this.taskDetail.creatorId && this.taskDetail.type != this.taskTypeEnum.RECTIFY_TASK
  }

  /**
   * 登录用户是否是执行人
   */
  get canOpreate() {
    if (this.taskDetail) {
      if (this.taskDetail.operatorId) {
        return this.taskDetail.operatorId == this.userInfo.uuid
      } else {
        if (
          (this.taskDetail.state === this.taskStateEnum.UNCLAIMED || this.taskDetail.state === this.taskStateEnum.UNDISPOSED) &&
          this.taskDetail.shopManages &&
          this.taskDetail.shopManages.length > 0
        ) {
          // 待领取待处理判断接口的shopManages是否存在当前登陆账号id
          return this.taskDetail.shopManages.some((item) => {
            return item.id === this.userInfo.uuid
          })
        } else if (
          (this.taskDetail.state === this.taskStateEnum.WITHOUT_RE_EVALUATION || this.taskDetail.state === this.taskStateEnum.UNEVALUATED) &&
          this.taskDetail.supervisors &&
          this.taskDetail.supervisors.length > 0
        ) {
          // 待评价待复评判断接口的supervisors是否存在当前登陆账号id
          return this.taskDetail.supervisors.some((item) => {
            return item.id === this.userInfo.uuid
          })
        } else {
          return false
        }
      }
    }
  }

  /**
   * 登录用户是否是接收人
   */
  get isReceiver() {
    if (this.taskDetail) {
      if (this.taskDetail.receiverId) {
        return this.taskDetail.receiverId == this.userInfo.uuid
      } else {
        // 多店长操作
        return this.currentShopManage
      }
    }
  }

  /**
   * 执行人，接受人
   */
  get operator() {
    if (this.taskDetail && this.taskDetail.state == this.taskStateEnum.UNDISPOSED) {
      // 执行人
      if (this.taskDetail.operatorName) {
        return this.taskDetail.operatorName
      } else {
        if (this.currentShopManage) {
          return this.currentShopManage.nickName
        }
      }
    } else {
      // 接收人
      if (this.taskDetail.receiverName) {
        return this.taskDetail.receiverName
      } else {
        if (this.currentShopManage) {
          return this.currentShopManage.nickName
        }
      }
    }
  }

  /**
   * 当前登录用户为店长时信息
   */
  get currentShopManage() {
    if (this.taskDetail && this.taskDetail.shopManages && this.taskDetail.shopManages.length > 0) {
      const currentShopManage = this.taskDetail.shopManages.filter((shopManage: ShopMangeEmployee) => {
        return shopManage.id == this.userInfo.uuid
      })
      if (currentShopManage.length > 0) {
        return currentShopManage[0]
      }
    }
  }

  /**
   * 当前为整改任务时，soureType（源任务单类型是否为巡检单）
   */
  get isPatrolTask() {
    if (this.taskDetail.type == this.taskTypeEnum.RECTIFY_TASK && this.taskDetail.sourceType) {
      return this.taskDetail.sourceType == this.taskTypeEnum.PATROL_TASK
    }
    return false
  }

  /**
   * 是否是陈列任务
   */
  get vmdTask() {
    if (this.taskDetail && this.taskDetail.tags && this.taskDetail.tags.length > 0) {
      return (
        this.taskDetail.tags.includes(this.taskTagEnum.VMD_TASK) ||
        this.taskDetail.tags.includes(this.taskTagEnum.SKU_CHECK_TASK_VMD) ||
        this.taskDetail.tags.includes(this.taskTagEnum.SKU_CHECK_TASK_VMD_RECTIFY) ||
        this.taskDetail.tags.includes(this.taskTagEnum.VMD_ACCOUNT) ||
        this.taskDetail.tags.includes(this.taskTagEnum.VMD_TASK_SUPERMARKET)
      )
    }
  }

  /**
   * 是否为超市陈列任务
   */

  get vmdTaskSuper() {
    if (this.taskDetail && this.taskDetail.tags && this.taskDetail.tags.length > 0) {
      return this.taskDetail.tags.includes(this.taskTagEnum.VMD_TASK_SUPERMARKET)
    }
  }

  /**
   * 是否为统一反馈
   */
  get uniformlyFeek() {
    return this.vmdTaskSuper && this.taskDetail.feedbackType === 'uniformly'
  }

  /**
   * 是否是价签调整任务
   */
  get priceAdjustTask() {
    if (
      this.taskDetail &&
      this.taskDetail.type &&
      this.taskDetail.tags &&
      (this.taskDetail.type == this.taskTypeEnum.ERP_PRICE_ADJUST_TASK || this.taskDetail.tags.includes(this.taskTagEnum.ERP_PRICE_ADJUST_TASK))
    ) {
      return true
    } else {
      return false
    }
  }

  /**
   * 是否是调整类任务
   */
  get surveyTask() {
    if (this.taskDetail && this.taskDetail.tags && this.taskDetail.tags.length > 0) {
      return this.taskDetail.tags.includes(this.taskTagEnum.SURVEY_TASK)
    }
  }

  /**
   * 是否是畅销品任务
   */
  get autoOrderTask() {
    if (
      this.taskDetail &&
      this.taskDetail.type &&
      this.taskDetail.tags &&
      (this.taskDetail.type == this.taskTypeEnum.AUTO_ORDER || this.taskDetail.tags.includes(this.taskTagEnum.AUTO_ORDER_TASK))
    ) {
      return true
    } else {
      return false
    }
  }

  /**
   * 是否是重点商品检查的陈列任务
   */
  get isSkuCheckVmdTask() {
    if (
      this.taskDetail &&
      ((this.taskDetail.type && this.taskDetail.type == this.taskTypeEnum.SKU_CHECK_VMD) ||
        (this.taskDetail.sourceType && this.taskDetail.sourceType === this.taskTypeEnum.SKU_CHECK_VMD))
    ) {
      return true
    } else {
      return false
    }
  }

  /**
   * 是否是重点商品检查的订货提醒任务
   */
  get isSkuCheckOtrTask() {
    if (
      this.taskDetail &&
      ((this.taskDetail.type && this.taskDetail.type == this.taskTypeEnum.SKU_CHECK_OTR) ||
        (this.taskDetail.sourceType && this.taskDetail.sourceType === this.taskTypeEnum.SKU_CHECK_OTR))
    ) {
      return true
    } else {
      return false
    }
  }

  /**
   * 是否是台账陈列类任务
   */
  get isVmdAccountTask() {
    if (
      this.taskDetail &&
      ((this.taskDetail.type && this.taskDetail.type == this.taskTypeEnum.VMD_ACCOUNT) ||
        (this.taskDetail.sourceType && this.taskDetail.sourceType === this.taskTypeEnum.VMD_ACCOUNT))
    ) {
      return true
    } else {
      return false
    }
  }

  /**
   * 是否是AI巡检任务
   */
  get isAiInspectionTask() {
    if (
      this.taskDetail &&
      this.taskDetail.type &&
      (this.taskDetail.type == this.taskTypeEnum.QUEUE_UP ||
        this.taskDetail.type == this.taskTypeEnum.MADE_TO_ORDER_NO_EMPLOYEE ||
        this.taskDetail.type == this.taskTypeEnum.MADE_TO_ORDER_PRODUCT_LESS ||
        this.taskDetail.type == this.taskTypeEnum.BACK_STAY_LONG ||
        this.taskDetail.type == this.taskTypeEnum.FRONT_DURATION_NO_BODY)
    ) {
      return true
    } else {
      return false
    }
  }

  /**
   * 价签调整任务详情paddingbottom为0
   */
  get bottomZero() {
    if (this.priceAdjustTask) {
      if (this.taskDetail.state === this.taskStateEnum.UNCLAIMED || this.taskDetail.state == this.taskStateEnum.UNDISPOSED) {
        return true
      } else {
        return false
      }
    } else {
      return true
    }
  }
  /**
   * 评价item的总标题
   */
  get itemTitle() {
    if (this.vmdTask) {
      return '陈列项'
    } else {
      return '点检项'
    }
  }

  /**
   * 已调商品总个数
   */
  get adjustedNum() {
    let num: number = 0
    this.allSku.forEach((v) => {
      if (v.adjusted) {
        num++
      }
    })
    return num
  }

  /**
   * 未调商品总个数
   */
  get nonAdjustedNum() {
    return this.adjustSkuNum - this.adjustedNum
  }

  /**
   * 是否显示文本输入
   */
  get showTextarea() {
    if (this.taskDetail && this.taskDetail.feedbackNeeded && this.taskDetail.feedbackNeeded.length > 0) {
      return this.taskDetail.feedbackNeeded.includes(this.feedbackTypeEnum.WORD)
    }
    return false
  }

  /**
   * 是否显示图片上传按钮
   */
  get hiddenImg() {
    if (this.taskDetail && this.taskDetail.feedbackNeeded && this.taskDetail.feedbackNeeded.length > 0) {
      return !this.taskDetail.feedbackNeeded.includes(this.feedbackTypeEnum.IMAGE)
    }
    return true
  }

  /**
   * 是否显示视频上传按钮
   */
  get hiddenVideo() {
    if (this.taskDetail && this.taskDetail.feedbackNeeded && this.taskDetail.feedbackNeeded.length > 0) {
      return !this.taskDetail.feedbackNeeded.includes(this.feedbackTypeEnum.VIDEO)
    }
    return true
  }

  /**
   * 订货提醒任务吸顶位置
   */
  get otrShowPrompt() {
    return this.taskDetail.state == this.taskStateEnum.UNDISPOSED && this.showPrompt
  }

  /**
   * 是否显示历史评价按钮判断
   * 根据auditReject是否等于1
   */
  get showHistoricalEval() {
    if (this.taskDetail && this.taskDetail.auditReject && this.taskDetail.auditReject === 1) {
      return true
    }
    return false
  }

  /**
   * 总项数和历史评价显示位置
   */
  get showType() {
    return !this.showSubmitOprea && this.showHistoricalEval
  }

  /**
   * 展示批量处理
   */
  get isShowBatch() {
    if (this.taskDetail.state !== this.taskStateEnum.UNDISPOSED && this.taskDetail.state !== this.taskStateEnum.RECTIFYING) {
      return false
    }
    const arr = {}
    this.allSoptCheckFeedbackList.forEach((e) => {
      arr[e.uuid] = CommonUtil.copy(e.body.infos)
      if (e.body.notApplicable) {
        arr[e.uuid] = [
          {
            type: 'BUTTON',
            items: []
          }
        ]
      }
    })

    let isShow = false
    const shopTaskItem = CommonUtil.copy(this.shopTaskItem)
    shopTaskItem.forEach((e) => {
      if (e.lines && e.lines.length) {
        e.lines.forEach((item) => {
          if (arr[item.uuid]) {
            item.isFinishBtn = arr[item.uuid].some((j) => {
              return j.type === feedbackTypeEnum.BUTTON
            })
          } else {
            item.isFinishBtn = false
          }

          if (!item.isFinishBtn && item.feedbackNeeded && item.feedbackNeeded.includes(feedbackTypeEnum.BUTTON)) {
            isShow = true
          }
        })
      } else {
        if (arr[e.uuid]) {
          e.isFinishBtn = arr[e.uuid].some((j) => {
            return j.type === feedbackTypeEnum.BUTTON
          })
        } else {
          e.isFinishBtn = false
        }

        if (!e.isFinishBtn && e.feedbackNeeded && e.feedbackNeeded.includes(feedbackTypeEnum.BUTTON)) {
          isShow = true
        }
      }
    })

    return isShow
  }

  /**
   * 展示批量处理待评价
   */
  get isShowBatchEva() {
    if (this.taskDetail.state !== this.taskStateEnum.UNEVALUATED && this.taskDetail.state !== this.taskStateEnum.WITHOUT_RE_EVALUATION) {
      return false
    }

    const arr = {}

    this.allSoptCheckEvaList.forEach((e) => {
      arr[e.uuid] = e.body.state
      if (e.body.notApplicable) {
        arr[e.uuid] = 1
      }
    })

    let isShow = false
    const shopTaskItem = CommonUtil.copy(this.shopTaskItem)

    shopTaskItem.forEach((e) => {
      if (e.lines && e.lines.length) {
        e.lines.forEach((item) => {
          if (arr[item.uuid] || arr[item.uuid] === 0) {
            item.isFinishBtn = true
          } else {
            isShow = true
          }
        })
      } else {
        if (arr[e.uuid] || arr[e.uuid] === 0) {
          e.isFinishBtn = true
        } else {
          isShow = true
        }
      }
    })

    return isShow
  }

  //展示保存按钮
  get showSaveBtn() {
    return !this.priceAdjustTask && !this.autoOrderTask && !this.surveyTask && !this.isSkuCheckOtrTask && !this.isVmdAccountTask
  }

  // 展示去打印按钮（）
  get showPrintBtn() {
    return this.priceAdjustTask && PermissionMgr.hasPermission(Permission.labelPrintingView)
  }

  async onLoad(param: any) {
    this.isVmdTaskSuper(false)
    this.taskUuid = param.uuid
    if (param.openTransfer) {
      this.openTransfer = true
    }
    this.doRefresh()
    uni.$off('taskEvalSubmit')
    uni.$on('taskEvalSubmit', () => {
      this.isSumbitModal = true
    })

    uni.$off('task-refresh')
    uni.$on('task-refresh', () => {
      this.doRefresh()
    })
    // 进入任务详情页面埋点
    LogMgr.taskDetailShow()
  }

  onShow() {
    uni.$emit('onShow')
    uni.getStorage({
      key: 'saveSeqList',
      success: (res) => {
        if (res.data) {
          this.saveSeqList = res.data
          ;(res.data || []).forEach((e) => {
            const json = this.allSoptCheckFeedbackList.find((item) => {
              return e.uuid === item.uuid
            })
            if (json) {
              json.body = e.body
            } else {
              this.allSoptCheckFeedbackList.push({ uuid: e.uuid, body: e.body })
            }
          })
          uni.removeStorageSync('saveSeqList')
        }
      }
    })
    uni.getStorage({
      key: 'saveEvaSeqList',
      success: (res) => {
        if (res.data) {
          this.saveEvaSeqList = res.data
          ;(res.data || []).forEach((e) => {
            const json = this.allSoptCheckEvaList.find((item) => {
              return e.uuid === item.uuid
            })
            if (json) {
              json.body = e.body
            } else {
              this.allSoptCheckEvaList.push({ uuid: e.uuid, body: e.body })
            }
          })
          uni.removeStorageSync('saveEvaSeqList')
        }
      }
    })

    uni.getStorage({
      key: 'batchList',
      success: (res) => {
        if (res.data) {
          const batchList = res.data
          uni.removeStorageSync('batchList')

          batchList.forEach((e: string) => {
            const index = this.saveSeqList.findIndex((item) => {
              return e === item.uuid
            })

            if (index > -1) {
              this.saveSeqList[index].body.infos.push({
                type: 'BUTTON',
                items: []
              })
            } else {
              const json = this.allSoptCheckFeedbackList.find((item) => {
                return e === item.uuid
              })

              const body = new ShopTaskItemFeedbackReq()
              if (json) {
                body.notApplicable = json.body.notApplicable
                body.uuid = json.uuid
                body.infos = json.body.infos
                body.infos.push({
                  type: 'BUTTON',
                  items: []
                })
              } else {
                delete body.uuid
                body.infos.push({
                  type: 'BUTTON',
                  items: []
                })

                this.allSoptCheckFeedbackList.push({ uuid: e, body: body })
              }

              this.saveSeqList.push({ uuid: e, body: body })
            }
          })
        }
      }
    })

    uni.getStorage({
      key: 'batchEvaList',
      success: (res) => {
        if (res.data) {
          const batchList = res.data
          uni.removeStorageSync('batchEvaList')

          batchList.forEach((e: ShopTaskItemAuditReqExt) => {
            const index = this.saveEvaSeqList.findIndex((item) => {
              return e.soptUuid === item.uuid
            })
            const json = this.allSoptCheckEvaList.find((item) => {
              return e.soptUuid === item.uuid
            })

            if (index > -1) {
              this.saveEvaSeqList[index].body.state = e.state
              this.saveEvaSeqList[index].body.score = e.score

              if (json) {
                json.body.state = e.state
                json.body.score = e.score
              }
            } else {
              const body = new ShopTaskItemAuditReq()
              body.state = e.state
              body.score = e.score
              if (json) {
                body.infos = json.body.infos
                body.uuid = json.uuid
              } else {
                this.allSoptCheckEvaList.push({ uuid: e.soptUuid || '', body: body })
              }

              this.saveEvaSeqList.push({ uuid: e.soptUuid || '', body: body })
            }
          })
        }
      }
    })
  }

  onUnload() {
    // 卸载监听
    uni.$off('taskEvalSubmit')
    uni.$off('task-refresh')
  }

  mounted() {
    setTimeout(() => {
      this.handleLineChange()
    }, 500)
  }

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    this.doRefresh()
  }

  /**
   * 刷新页面数据
   */
  async doRefresh() {
    await this.doSearchDetail(this.taskUuid)
    await this.doSearchSoptCheck()
    await this.doGetHistoryItemLog()
    if (this.isSumbitModal) {
      this.doSumbitModal()
      this.isSumbitModal = false
    }
    if (this.openTransfer) {
      await this.doDeliver()
      this.openTransfer = false
    }
    uni.stopPullDownRefresh()
  }

  /**
   * 调查类任务每次编辑后，返回是否可提交状态
   * @param finish 是否完成可提交
   */
  doSurveyTaskIsFinish(finish: boolean, items: SurveyItem[]) {
    this.surveyTaskFinish = finish
    this.items = items
  }

  /**
   * 查看历史评价
   */
  doEvalLook() {
    this.$Router.push({
      name: 'historicalEvaluation',
      params: {
        taskUuid: this.taskDetail.uuid,
        vmdTask: this.vmdTask
      }
    })
  }

  /**
   * 任务详情
   * @param uuid 门店任务ID
   */
  doSearchDetail(uuid: string) {
    this.$showLoading({ delayTime: 200 })
    return ShopTaskApi.get(uuid, [this.fetchPathsEnum.STATEFLOW])
      .then((res) => {
        this.$hideLoading()
        this.taskDetail = res.data!
        this.isVmdTaskSuper(this.vmdTaskSuper)
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ title: error.msg, icon: 'none' })
      })
  }

  /**
   * 查询点检项
   */
  doSearchSoptCheck() {
    this.$showLoading({ delayTime: 200 })
    return ShopTaskApi.items(this.taskDetail.uuid)
      .then((res) => {
        this.$hideLoading()
        this.shopTaskItem = res.data!

        if (this.shopTaskItem && this.shopTaskItem.length > 0) {
          this.doGetItemLog()
        }
        // 调查类任务直接返回
        if (this.surveyTask) {
          this.doBatchTaskItem()
          return
        }
        if (this.priceAdjustTask || this.autoOrderTask || this.isSkuCheckOtrTask) {
          this.doHandlePriceTask()
        }
        this.doDispalyTaskItem()
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ title: error.msg, icon: 'none' })
      })
  }

  /**
   * 批量查询任务log
   */
  doBatchTaskItem() {
    const itemIds: string[] = [] // 任务uuid集合
    this.shopTaskItem.forEach((shopTaskItem: ShopTaskItem) => {
      itemIds.push(shopTaskItem.uuid)
    })
    this.batchTaskItemLog = []
    this.$showLoading({ delayTime: 200 })
    ShopTaskApi.listLogsByItems(itemIds, '', '1')
      .then((res) => {
        this.$hideLoading()
        this.batchTaskItemLog = res.data!
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ title: error.msg, icon: 'none' })
      })
  }

  /**
   * 价签调整任务或畅销品任务模型列表调整
   */
  doHandlePriceTask() {
    this.allSku = []
    if (this.priceAdjustTask) {
      this.shopTaskItem.forEach((item: any) => {
        // 所有价签调整商品数组
        this.allSku.push(Object.assign(item.detail.shopTaskItemPriceAdjustDetail, { itemId: item.uuid }))
      })
    } else if (this.autoOrderTask) {
      this.shopTaskItem.forEach((item: any) => {
        // 所有畅销品任务商品数组
        this.allSku.push(Object.assign(item.detail.shopTaskItemAutoOrderDetail, { itemId: item.uuid }))
      })
    } else if (this.isSkuCheckOtrTask) {
      this.shopTaskItem.forEach((item: any) => {
        // 所有重点商品订货提醒任务商品数组
        this.allSku.push(Object.assign(item.detail.shopTaskItemOtrDetail, { itemId: item.uuid }))
      })
    }
    this.adjustSkuNum = this.allSku.length
    // 初始化分类映射表
    const categoryMap = new Map<string, { name: string; list: any[] }>()
    this.goodsCategoryList = []
    this.allSku.forEach((v) => {
      // 如果分类已存在，直接添加商品到对应分类
      if (categoryMap.has(v.catCode || v.category.code)) {
        categoryMap.get(v.catCode || v.category.code)!.list.push(v)
      } else {
        // 如果分类不存在，创建新分类并添加到映射表和分类列表
        const newCategory = { name: v.catName || v.category.name, list: [v] }
        categoryMap.set(v.catCode || v.category.code, newCategory)
        this.goodsCategoryList.push(newCategory)
      }
    })
    // 默认展示第一个分类的数据
    if (this.activeIndex) {
      this.goodsList = this.goodsCategoryList[this.activeIndex].list
    } else {
      this.goodsList = this.goodsCategoryList[0].list
    }
    // 重点商品订货提醒任务还有个全部分类
    if (this.isSkuCheckOtrTask) {
      this.goodsCategoryList.unshift({
        name: '全部',
        list: this.allSku
      })
    }
    this.goodsCategoryList.forEach((v) => {
      v.allListNum = v.list.length
      v.adjustedNum = 0
      v.nonAdjustNum = 0
      v.list.forEach((item) => {
        item.adjusted ? v.adjustedNum++ : v.nonAdjustNum++
      })
    })
  }

  /**
   * 陈列任务时，调整this.shopTaskItem数据结构
   */
  doDispalyTaskItem() {
    if (this.vmdTask) {
      // 当为陈列任务时
      const skuDisplay: ShopTaskItem = new ShopTaskItem() // 商品陈列
      const popDisplay: ShopTaskItem = new ShopTaskItem() // POP陈列
      const tempDisplay: ShopTaskItem = new ShopTaskItem() // 台账模板陈列
      const skuDisplayList: ShopTaskItem[] = this.shopTaskItem.filter((item: ShopTaskItem) => {
        return item.type == this.shopTaskItemTypeEnum.PUT || item.type == 'SKU_CHECK_VMD'
      }) // 商品陈列集合
      const popDisplayList: ShopTaskItem[] = this.shopTaskItem.filter((item: ShopTaskItem) => {
        return item.type == this.shopTaskItemTypeEnum.POP
      }) // POP陈列集合
      const tempDisplayList: ShopTaskItem[] = this.shopTaskItem.filter((item: ShopTaskItem) => {
        return item.type == this.shopTaskItemTypeEnum.ACCOUNT_ITEM
      }) // 台账模板陈列集合

      const shopTaskItem: ShopTaskItem[] = []
      if (skuDisplayList.length > 0) {
        skuDisplay.type = this.shopTaskItemTypeEnum.PUT
        skuDisplay.name = '商品陈列'
        skuDisplay.lines = skuDisplayList
        shopTaskItem.push(skuDisplay)
      }

      if (popDisplayList.length > 0) {
        popDisplay.type = this.shopTaskItemTypeEnum.POP
        popDisplay.name = 'POP陈列'
        popDisplay.lines = popDisplayList
        shopTaskItem.push(popDisplay)
      }

      if (tempDisplayList.length > 0) {
        tempDisplay.type = this.shopTaskItemTypeEnum.ACCOUNT_ITEM
        tempDisplay.name = '台账陈列'
        tempDisplay.lines = tempDisplayList
        shopTaskItem.push(tempDisplay)
      }

      this.shopTaskItem = shopTaskItem
    }
  }

  /**
   * 获取显示的过期时间，若过期时间为空，则隐藏时间轴
   * @param endTimeValue 过期时间
   */
  doShowTimeShaft(endTimeValue: string | undefined) {
    this.showTimeShaft = endTimeValue ? true : false
  }

  /**
   * 跳转关联任务
   */
  doRelevance() {
    uni.redirectTo({ url: `/pagesTaskCenter/taskDetail/TaskDetail?uuid=${this.taskRelevance.id}` })
  }

  /**
   * 显示操作记录弹框
   */
  doOperaRecord() {
    this.$refs.opreaLog.open()
  }

  // 撤回任务
  doOperaRollback() {
    this.$showLoading()
    ShopTaskApi.rollback(this.taskDetail.uuid, true)
      .then(() => {
        this.$hideLoading()
        this.doRefresh()
        this.$showToast({ icon: 'success', title: '撤回修改成功' })
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  /**
   * 关闭操作记录弹框
   */
  doOpreaLogClose() {
    this.$refs.opreaLog.close()
  }

  /**
   * 点检项点击事件
   * @param item 点检项信息
   */
  doSoptCheck(item: ShopTaskItem) {
    if (
      (this.taskDetail.state == this.taskStateEnum.UNDISPOSED || this.taskDetail.state == this.taskStateEnum.RECTIFYING) &&
      this.canOpreate &&
      !this.isSkuCheckVmdTask &&
      !this.uniformlyFeek
    ) {
      // (待处理 || 整改中) && 登录用户为执行人 && 不是重点商品检查任务
      uni.navigateTo({
        url: `/pagesTaskCenter/taskDetail/SoptCheckEval?taskUuid=${this.taskDetail.uuid}&itemId=${item.uuid}&vmdTask=${this.vmdTask}`
      })
    } else if (
      (this.taskDetail.state == this.taskStateEnum.UNEVALUATED || this.taskDetail.state == this.taskStateEnum.WITHOUT_RE_EVALUATION) &&
      this.canOpreate
    ) {
      // (待评价 || 待复评) && 登录用户为执行人
      uni.$on('send', () => {
        uni.$emit('checkFeedback', this.feedbackDetail)
      })
      this.$Router.push({
        name: 'inspectionEval',
        params: {
          taskUuid: this.taskDetail.uuid,
          itemId: item.uuid,
          vmdTask: this.vmdTask,
          checkVmdParentId: this.isSkuCheckVmdTask ? this.taskDetail.sourceId : ''
        }
      })
    } else {
      this.currentSoptCheckUuid = item.uuid
      this.showSoptCheckDetail = true
    }
  }

  /**
   * 新点检项点击事件
   * @param opt 点检项信息
   */

  doSoptCheckExt(item: ShopTaskItem) {
    if (
      (this.taskDetail.state == this.taskStateEnum.UNDISPOSED || this.taskDetail.state == this.taskStateEnum.RECTIFYING) &&
      this.canOpreate &&
      !this.isSkuCheckVmdTask
    ) {
      uni.setStorage({
        key: 'saveSeqList',
        data: CommonUtil.copy(this.saveSeqList),
        success: () => {
          // (待处理 || 整改中) && 登录用户为执行人 && 不是重点商品检查任务
          uni.navigateTo({
            url: `/pagesTaskCenter/taskDetail/SoptCheckEval?taskUuid=${this.taskDetail.uuid}&itemId=${item.uuid}&vmdTask=${this.vmdTask}`
          })
        }
      })
    } else if (
      (this.taskDetail.state == this.taskStateEnum.UNEVALUATED || this.taskDetail.state == this.taskStateEnum.WITHOUT_RE_EVALUATION) &&
      this.canOpreate
    ) {
      // (待评价 || 待复评) && 登录用户为执行人
      uni.$on('send', () => {
        uni.$emit('checkFeedback', this.feedbackDetail)
      })

      uni.setStorage({
        key: 'saveEvaSeqList',
        data: CommonUtil.copy(this.saveEvaSeqList),
        success: () => {
          this.$Router.push({
            name: 'inspectionEval',
            params: {
              taskUuid: this.taskDetail.uuid,
              itemId: item.uuid,
              vmdTask: this.vmdTask,
              checkVmdParentId: this.isSkuCheckVmdTask ? this.taskDetail.sourceId : ''
            }
          })
        }
      })
    } else {
      this.currentSoptCheckUuid = item.uuid
      this.showSoptCheckDetail = true
    }
  }

  /**
   * 单模板改动是否可以提交
   */
  doSingleFbChange(canSave, body) {
    this.vmdAccountTaskFinish = canSave
    this.vmdSingleTempBody = body
  }

  /**
   * 单模板评价内容改动是否可以提交及提交内容
   */
  doSingleAuditChange(auditCanSave, body) {
    this.vmdAuditFinish = auditCanSave
    this.vmdAuditBody = body
  }

  /**
   * 关闭点检项详情轮播
   */
  doCloseSoptCheckDetail() {
    this.showSoptCheckDetail = false
    this.currentSoptCheckUuid = ''
  }

  /**
   * 转交任务
   */
  doDeliver() {
    this.$Router.push({
      name: 'deliverChoose',
      params: {
        deliver: this.deliver,
        taskId: this.taskDetail.taskId,
        shopId: this.taskDetail.shopId,
        state: this.taskDetail.state,
        taskUuid: this.taskDetail.uuid
      }
    })
    this.doRefresh()
  }

  /**
   * 取消转交
   */
  doCancelDeliver() {
    this.$showLoading()
    TaskApi.cancelTransfer(this.taskDetail.taskId!)
      .then((res) => {
        this.$hideLoading()
        this.doReceive()
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ title: error.msg, icon: 'none' })
      })
  }

  /**
   * 转交人弹窗关闭
   */
  doCloseDeliver() {
    this.$refs.category.close()
  }

  /**
   * 领取任务
   */
  doReceive() {
    if (this.autoOrderTask || this.isSkuCheckOtrTask) {
      if (PermissionMgr.hasPermission(Permission.requireApplyView)) {
        this.$showModal({
          title: '领取',
          content: `领取后不可转交，确定领取吗？`,
          confirmText: '提交',
          cancelText: '取消',
          showCancel: true,
          success: (res) => {
            if (res.confirm) {
              this.doReceiveConfirm()
            } else if (res.cancel) {
              this.doRefresh()
            }
          }
        })
      } else {
        this.$showModal({
          content:
            this.showTransferOpreate && this.canOpreate
              ? `您无订货权限，无法处理该任务，请联系管理员或转交至门店其他人员`
              : `您无订货权限，无法处理该任务，请联系管理员或退回此任务`,
          confirmText: this.showTransferOpreate && this.canOpreate ? `去转交` : `退回`,
          cancelText: '取消',
          showCancel: true,
          success: (res) => {
            if (res.confirm) {
              if (this.showTransferOpreate && this.canOpreate) {
                this.doDeliver()
              } else {
                this.doBackTask()
              }
            } else if (res.cancel) {
              this.doRefresh()
            }
          }
        })
      }
    } else {
      this.doReceiveConfirm()
    }
  }

  /**
   * 领取确认
   */
  doReceiveConfirm() {
    this.$showLoading()
    ShopTaskApi.claim(this.taskDetail.uuid)
      .then((resp) => {
        this.$hideLoading()
        this.doRefresh()
        this.$showToast({ icon: 'success', title: '领取成功' })
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }
  /**
   * 退回弹框
   */
  doBackTaskModal() {
    this.$showModal({
      content: `退回后将无法查看该任务，确定退回该任务吗？`,
      showCancel: true,
      confirmText: '确认',
      success: (action) => {
        if (action.confirm) {
          this.doBackTask()
        }
      }
    })
  }

  /**
   * 退回任务
   */
  doBackTask() {
    this.$showLoading()
    TaskApi.refuseTransfer(this.taskDetail.taskId!)
      .then((res) => {
        this.$hideLoading()
        this.doRefresh()
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ title: error.msg, icon: 'none' })
      })
  }

  /**
   * 终止任务弹框
   */
  doTerminationModal() {
    this.$showModal({
      content: `终止后门店将无法完成该任务，确认终止吗？`,
      showCancel: true,
      confirmText: '确认',
      success: (action) => {
        if (action.confirm) {
          this.doTermination()
        }
      }
    })
  }

  /**
   * 终止任务
   */
  doTermination() {
    this.$showLoading()
    ShopTaskApi.cancel(this.taskDetail.uuid)
      .then((res) => {
        this.$hideLoading()
        this.doRefresh()
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ title: error.msg, icon: 'none' })
      })
  }

  /**
   * 提交任务弹框
   */
  async doSumbitModal() {
    if (this.taskDetail.state == this.taskStateEnum.WITHOUT_RE_EVALUATION || this.taskDetail.state == this.taskStateEnum.UNEVALUATED) {
      if (this.isVmdAccountTask) {
        if (this.shopTaskItem.length && this.shopTaskItem[0].lines.length && this.shopTaskItem[0].lines.length === 1 && this.canSubmit) {
          //台账任务单模板待评价时在任务详情页直接完成评价提交
          await this.doItemAuditFeedback()
          await this.doSumbit()
        } else {
          // 待评价或待复评的没完成项是0时可提交
          if (this.notDoneNum > 0) return
          this.$showModal({
            title: '提交',
            content: `提交后将不可更改，确定提交吗？`,
            showCancel: true,
            confirmText: '提交',
            success: (action) => {
              if (action.confirm) {
                this.doSumbit()
              }
            }
          })
        }
      } else {
        const timer = setTimeout(() => {
          //评价保存并提交
          this.doEvaValite()
          clearTimeout(timer)
        }, 300)
      }
    } else if (this.isAllowEdit) {
      const timer = setTimeout(() => {
        const arr = []
        this.allSoptCheckFeedbackList.forEach((e) => {
          arr[e.uuid] = e
        })

        const shopTaskItem = CommonUtil.copy(this.shopTaskItem)
        let toastText = ''
        shopTaskItem.forEach((e) => {
          if (e.lines && e.lines.length) {
            e.lines.forEach((item) => {
              const name = this.getSoptName(e, item)
              if (arr[item.uuid]) {
                const text = this.doValiteSaveReq(arr[item.uuid], item.feedbackNeeded)

                if (text) {
                  toastText = `${name} ${text}`
                }
              } else {
                toastText = `${name} 还未进行反馈`
              }
            })
          } else {
            const name = this.getSoptName(e, e)
            if (arr[e.uuid]) {
              const text = this.doValiteSaveReq(arr[e.uuid], e.feedbackNeeded)
              if (text) {
                toastText = `${name} ${text}`
              }
            } else {
              toastText = `${name} 还未进行反馈`
            }
          }
        })
        if (toastText) {
          this.$showModal({
            title: '提示',
            content: toastText,
            showCancel: false,
            confirmText: '我知道了',
            success: (action) => {}
          })
        } else {
          this.doItemBatchFeedback().then(() => {
            this.doSumbit()
          })
        }

        clearTimeout(timer)
      }, 300)
    } else if (this.priceAdjustTask) {
      if (this.adjustSkuNum !== this.adjustedNum) {
        // 如果没有全部已调不可以点击弹出反馈
        return
      }
      if (this.showTextarea || !this.hiddenImg || !this.hiddenVideo) {
        this.$refs.feedback.open()
      } else {
        this.doSubmitFeedback()
      }
    } else if (this.autoOrderTask) {
      ShopTaskApi.feedback(this.taskDetail.uuid)
        .then((res) => {
          // 跳转至对应订货界面
          if (PermissionMgr.hasPermission(Permission.requireApplyView)) {
            this.$Router.push({
              name: 'requireGoodsDetail',
              params: {
                id: this.taskDetail.uuid
              }
            })
          }
        })
        .catch((error) => {
          // 跳转至对应新增订货
          if (PermissionMgr.hasPermission(Permission.requireApplyView)) {
            this.$Router.push({
              name: 'requireGoodsEdit',
              params: {
                id: this.taskDetail.uuid,
                create: true
              }
            })
          }
        })
    } else if (this.isSkuCheckOtrTask) {
      this.$showLoading()
      const body: ShopTaskFeedbackReq = new ShopTaskFeedbackReq()
      body.owner = this.taskDetail.uuid
      const infos = [
        { type: 'IMAGE', items: [] },
        { type: 'VIDEO', items: [] },
        { type: 'WORD', items: [] }
      ]
      body.infos = infos
      const categoty = this.goodsCategoryList.find((goodsCategory) => goodsCategory.name === '全部')
      categoty?.list.forEach((good) => {
        if (good.qty) {
          body.skus.push({
            skuId: good.sku?.uuid,
            skuQpcStr: good.skuQpcStr,
            qty: good.qty
          })
        }
      })
      ShopTaskApi.taskTotalFeedback(body)
        .then((res) => {
          // 跳转至对应订货界面
          this.$hideLoading()
          if (PermissionMgr.hasPermission(Permission.requireApplyView)) {
            this.$Router.push({
              name: 'requireGoodsList'
            })
            this.doRefresh()
          }
        })
        .catch((error) => {
          this.$hideLoading()
          this.$showToast({ title: error.msg, icon: 'none' })
        })
    } else if (this.isSkuCheckVmdTask) {
      this.$refs.feedback.open()
    } else if (this.isVmdAccountTask && this.shopTaskItem[0].lines && this.shopTaskItem[0].lines.length === 1 && this.canSubmit) {
      // 只有台账任务且是单模板情况，直接在详情页保存填写内容和提交
      await this.doItemFeedback('vmd')
      await this.doSumbit()
    } else if (this.isAiInspectionTask) {
      if (!this.canSubmit) return
      // ai巡检走此提交逻辑
      await this.doItemFeedback('ai')
      await this.doSumbit()
    } else {
      if (!this.canSubmit) return
      if (this.finishNum! < this.pointNum! && !this.surveyTask) return
      this.$showModal({
        title: '提交',
        content: `提交后将不可更改，确定提交吗？`,
        showCancel: true,
        confirmText: '提交',
        success: (action) => {
          if (action.confirm) {
            if (this.taskDetail.surveyType === 'STATISTICS') {
              this.doItemBatchFirstFeedback().then(() => {
                this.doSumbit()
              })
            } else {
              this.doSumbit()
            }
          }
        }
      })
    }
  }

  /**
   * 保存完成反馈内容
   */
  doItemFeedback(fromModel: string) {
    this.$showLoading()
    return new Promise((resolve, reject) => {
      ShopTaskApi.itemFeedback(this.vmdSingleTempBody, fromModel === 'ai' ? this.shopTaskItem[0].uuid : this.shopTaskItem[0].lines[0].uuid)
        .then((res) => {
          this.$hideLoading()
          resolve({})
        })
        .catch((error) => {
          this.$hideLoading()
          this.$showToast({ title: error.msg, icon: 'none' })
          reject({})
        })
    })
  }

  /**
   * 保存评价反馈内容
   */
  doItemAuditFeedback() {
    this.$showLoading()
    return new Promise((resolve, reject) => {
      ShopTaskApi.itemAudit(this.vmdAuditBody, this.shopTaskItem[0].lines[0].uuid)
        .then((res) => {
          this.$hideLoading()
          resolve({})
        })
        .catch((error) => {
          this.$hideLoading()
          this.$showToast({ title: error.msg, icon: 'none' })
          reject({})
        })
    })
  }
  /**
   * 去评价
   */
  doToEval() {
    this.$refs.evaluation.open()
  }

  /**
   * 关闭评价
   */
  doEvaluationClose() {
    this.$refs.evaluation.close()
  }

  /**
   * 关闭订货模块选择
   */
  doCloseOrdersModule() {
    this.$refs.ordersModule.close()
  }

  /**
   * 选择跳转的订货模块
   */
  doToOrder(item: string) {
    ShopTaskApi.feedback(this.taskDetail.uuid)
      .then((res) => {
        // 如果只有一种订货模块，跳转至对应订货详情界面
        if (item !== 'cdh') {
          this.$Router.push({
            name: 'requireGoodsDetail',
            params: {
              id: this.taskDetail.uuid
            }
          })
        } else if (item === 'cdh') {
          this.$Router.push({
            name: 'requireGoodsDetailCdh',
            params: {
              id: this.taskDetail.uuid
            }
          })
        }
      })
      .catch((error) => {
        // 如果只有一种订货模块，跳转至对应新增订货
        if (item !== 'cdh') {
          this.$Router.push({
            name: 'requireGoodsEdit',
            params: {
              id: this.taskDetail.uuid,
              create: true
            }
          })
        } else if (item === 'cdh') {
          this.$Router.push({
            name: 'requireGoodsEditCdh',
            params: {
              id: this.taskDetail.uuid,
              create: true
            }
          })
        }
      })
  }

  /**
   * 关闭完成反馈弹窗
   */
  doCloseFeedback() {
    this.$refs.feedback.close()
  }

  /**
   * 提交任务
   */
  doSumbit() {
    const requestBody: any =
      this.taskDetail.state == this.taskStateEnum.WITHOUT_RE_EVALUATION || this.taskDetail.state == this.taskStateEnum.UNEVALUATED
        ? ShopTaskApi.audit
        : ShopTaskApi.feedback
    this.$showLoading()
    requestBody(this.taskDetail.uuid)
      .then((res) => {
        this.$hideLoading()
        this.doRefresh()
        this.saveEvaSeqList = []
        this.saveSeqList = []
        this.$showToast({ title: '提交成功', icon: 'success' })
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showModal({
          title: '提交失败',
          content: `${error.msg}`,
          showCancel: false,
          confirmText: '知道了'
        })
      })
  }

  /**
   * 切换选中调整商品分类
   */
  doSelect(item, index) {
    if (index === this.activeIndex) {
      return
    }
    this.goodsList = this.goodsCategoryList[index].list
    this.activeIndex = index
  }

  /**
   * 查看门店任务反馈明细详情
   */
  doGetItemLog() {
    this.$showLoading()
    return ShopTaskApi.getItemLogs(this.isAiInspectionTask ? this.shopTaskItem[0].uuid : this.taskDetail.uuid, '', '1')
      .then((res) => {
        if (res.data && res.data.length > 0) {
          this.feedbackDetail =
            res.data.filter((item: ShopTaskLog) => {
              return item.type == this.taskItemStateEnum.FEEDBACK
            })[0] || new ShopTaskLog()

          this.soptCheckFeedbackDetail = res.data[0]
          this.auditDetail =
            res.data.filter((item: ShopTaskLog) => {
              return item.type == this.taskItemStateEnum.AUDIT
            })[0] || new ShopTaskLog()
        }
        this.$hideLoading()
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ title: error.msg, icon: 'none' })
      })
  }

  /**
   * 查看门店历史反馈明细详情
   */
  doGetHistoryItemLog() {
    this.$showLoading({ delayTime: 200 })
    return ShopTaskApi.getItemLogs(
      this.isVmdAccountTask && this.shopTaskItem[0].lines.length === 1 ? this.shopTaskItem[0].lines[0].uuid : this.taskDetail.uuid,
      'AUDIT',
      '0'
    )
      .then((res) => {
        if (res.data && res.data.length > 0) {
          this.historyDetail = res.data.filter((item: ShopTaskLog) => {
            return item.type == this.taskItemStateEnum.AUDIT
          })
        }
        this.$hideLoading()
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ title: error.msg, icon: 'none' })
      })
  }

  /**
   * 标价签任务评价反馈提交
   */
  doEvalSub(canSave, body) {
    if (!canSave) {
      this.$showModal({
        title: '提示',
        content: `未满足反馈要求，请填写后提交`,
        showCancel: false,
        confirmText: '确认'
      })
      return
    }
    if (body && body.infos && body.infos.length > 0) {
      const feedback = body.infos.filter((item) => {
        return item.type === 'WORD'
      })
      if (feedback[0] && feedback[0].items[0].length > 200) {
        this.$showToast({ title: '不能超过200个字符', icon: 'none' })
        return
      }
    }
    this.$showModal({
      title: '提示',
      content: `提交后不可更改填写内容，确定提交吗？`,
      showCancel: true,
      confirmText: '确认',
      success: (action) => {
        if (action.confirm) {
          this.$showLoading()
          // 保存评价反馈内容
          ShopTaskApi.totalTaskEvaluate(body)
            .then((res) => {
              this.$hideLoading()
              this.doEvaluationClose()
              this.doRefresh()
            })
            .catch((error) => {
              this.$hideLoading()
              this.$showToast({ title: error.msg, icon: 'none' })
            })
        }
      }
    })
  }

  /**
   * 价签调整任务查看历史评价
   */
  doViewHistory() {
    this.doEvaluationClose()
    this.doHistoryEvalOpen()
  }

  /**
   * 价签调整历史评价弹窗打开
   */
  doHistoryEvalOpen() {
    this.$refs.historyEval.open()
    this.cardViewEval = true
  }

  /**
   * 价签调整历史评价弹窗关闭
   */
  doHistoryEvalClose() {
    this.$refs.historyEval.close()
  }

  /**
   * 历史评价关闭且回到评价弹窗
   */
  doBackEval() {
    this.doHistoryEvalClose()
    if (this.cardViewEval) {
      // 从卡片点击查看历史不返回评价弹窗
      this.cardViewEval = false
      return
    }
    this.doToEval()
  }

  /**
   * 播放视频
   * @param videoIndex 微信： 播放视频的id； 钉钉：显示的视频播放地址
   */
  doPlayVideo(videoIndex: string) {
    this.srcVideo = videoIndex
    this.$nextTick(() => {
      this.showVideo = true
    })
    // #ifndef MP-DINGTALK
    const Uni: any = uni
    const wxVideo = Uni.createVideoContext('myVideo', this)
    wxVideo.seek(0)
    wxVideo.requestFullScreen()
    // #endif
  }

  /**
   * 钉钉关闭视频
   */
  doCloseVideo() {
    this.showVideo = false
    this.srcVideo = ''
  }

  /**
   * 预览图片
   * @param previewImgList 预览图片列表
   * @param current 预览图片下标
   */
  doPreviewImg(previewImgList: string[], current: number) {
    uni.previewImage({
      current: current,
      urls: previewImgList
    })
  }

  /**
   * 点击调整已调未调状态
   * batch表示是否批量改变调价状态，当batch为true时表示批量处理，此时index表示当前侧边栏的activeIndex
   * 当batch为true时,index表示当前分类下选中的元素的下标
   */
  doChangeState(index: number, batch: boolean = false) {
    this.$showLoading()
    let param: PriceAdjustReq[] = []
    if (batch) {
      if (this.goodsCategoryList[index].allListNum === this.goodsCategoryList[index].adjustedNum) {
        // 此时当前分类下是全部已调，需要全都切成全部未调
        this.goodsCategoryList[index].list.forEach((item: any) => {
          const body = new PriceAdjustReq()
          body.itemId = item.itemId
          body.adjusted = false
          param.push(body)
        })
      } else {
        // 此时当前分类下是全部未调，需要全都切成全部已调
        this.goodsCategoryList[index].list.forEach((item: any) => {
          const body = new PriceAdjustReq()
          body.itemId = item.itemId
          body.adjusted = true
          param.push(body)
        })
      }
    } else {
      const body = new PriceAdjustReq()
      body.itemId = this.goodsList[index].itemId
      body.adjusted = !this.goodsList[index].adjusted
      param = [body]
    }
    ShopTaskApi.itemBatchPriceAdjustFeedback(param)
      .then((res) => {
        this.$hideLoading()
        this.doSearchSoptCheck()
      })
      .catch((e) => {
        this.$hideLoading()
      })
  }

  /**
   * 价签任务完成反馈点击提交按钮
   */
  doSubmitFeedback(wordFeedback?, upImageList?, upVideoList?, signType?) {
    this.$showModal({
      title: '提示',
      content: `提交后不可更改填写内容，确定提交吗？`,
      showCancel: true,
      confirmText: '确认',
      success: (action) => {
        if (action.confirm) {
          const body: ShopTaskFeedbackReq = new ShopTaskFeedbackReq()
          if (this.taskDetail.uuid) {
            body.owner = this.taskDetail.uuid
          }
          if (signType) {
            body.ossSupplier = signType
          }
          if (wordFeedback) {
            if (wordFeedback.length > 200) {
              this.$showToast({ title: '不能超过200个字符', icon: 'none' })
              return
            }
            body.infos.push({
              type: 'WORD',
              items: [wordFeedback]
            })
          }
          if (upImageList && upImageList.length > 0) {
            body.infos.push({
              type: 'IMAGE',
              items: upImageList
            })
          }
          if (upVideoList && upVideoList.length > 0) {
            body.infos.push({
              type: 'VIDEO',
              items: upVideoList
            })
          }
          this.doTotalFeedback(body)
        }
      }
    })
  }

  /**
   * 完成反馈请求
   */
  doTotalFeedback(body) {
    this.$showLoading()
    ShopTaskApi.taskTotalFeedback(body)
      .then((res) => {
        this.$hideLoading()
        this.doCloseFeedback()
        this.doRefresh()
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({
          title: e.msg,
          icon: 'none'
        })
      })
  }

  /**
   * 畅销品待处理顶部提示点击关闭
   */
  doClosePrompt() {
    this.showPrompt = false
  }

  /**
   * 点击查看钉盘内文件
   */
  doViewFile(filePath: string) {
    // #ifdef MP-DINGTALK
    dd.getAuthCode({
      success: (res) => {
        this.getDingInfo(res.authCode, filePath)
      },
      fail: (err) => {
        this.$showToast({
          title: err,
          icon: 'none'
        })
      }
    })
    // #endif
    // #ifndef MP-DINGTALK
    this.$showLoading()
    this.$openDocument({
      filePath: filePath,
      complete: () => {
        this.$hideLoading()
      }
    })
    // #endif
  }

  /**
   * 获取钉盘文件信息
   */
  getDingInfo(code: string, path: string) {
    this.$showLoading()
    DingTalkDriveApi.getDingTalkUrl(code, path)
      .then((res) => {
        this.$hideLoading()
        const corpId = dd.corpId
        if (res.data) {
          dd.previewFileInDingTalk({
            corpId: corpId,
            spaceId: res.data.spaceId,
            fileId: res.data.fileId,
            fileName: res.data.fileName,
            fileSize: res.data.fileSize,
            fileType: res.data.fileType
          })
        }
      })
      .catch((err) => {
        this.$hideLoading()
        this.$showToast({
          title: err.msg,
          icon: 'none'
        })
      })
  }

  //批量处理待编辑点检项
  doBatchEdit() {
    if (this.isShowBatchEva) {
      this.doBatchEvaEdit()
    } else {
      const arr = {}
      this.allSoptCheckFeedbackList.forEach((e) => {
        arr[e.uuid] = CommonUtil.copy(e.body.infos)
        if (e.body.notApplicable) {
          arr[e.uuid] = [
            {
              type: 'BUTTON',
              items: []
            }
          ]
        }
      })

      const shopTaskItem = CommonUtil.copy(this.shopTaskItem)

      shopTaskItem.forEach((e) => {
        if (e.lines && e.lines.length) {
          e.lines.forEach((item) => {
            if (arr[item.uuid]) {
              item.isFinishBtn = arr[item.uuid].some((j) => {
                return j.type === feedbackTypeEnum.BUTTON
              })
            } else {
              item.isFinishBtn = false
            }
          })
        } else {
          if (arr[e.uuid]) {
            e.isFinishBtn = arr[e.uuid].some((j) => {
              return j.type === feedbackTypeEnum.BUTTON
            })
          } else {
            e.isFinishBtn = false
          }
        }
      })

      uni.setStorage({
        key: 'soptBatchEditInfo',
        data: { line: shopTaskItem, task: this.taskDetail },
        success: (res) => {
          this.$Router.push({
            name: 'SoptBatchEdit'
          })
        },
        fail: function (res) {}
      })
    }
  }

  //批量处理待编辑点检项
  doBatchEvaEdit() {
    const arr = {}
    this.allSoptCheckEvaList.forEach((e) => {
      arr[e.uuid] = e.body.state
      if (e.body.notApplicable) {
        arr[e.uuid] = 1
      }
    })

    const shopTaskItem = CommonUtil.copy(this.shopTaskItem)

    shopTaskItem.forEach((e) => {
      if (e.lines && e.lines.length) {
        e.lines.forEach((item) => {
          if (arr[item.uuid] || arr[item.uuid] === 0) {
            item.isFinishBtn = true
          } else {
            item.isFinishBtn = false
          }
        })
      } else {
        if (arr[e.uuid] || arr[e.uuid] === 0) {
          e.isFinishBtn = true
        } else {
          e.isFinishBtn = false
        }
      }
    })

    uni.setStorage({
      key: 'soptBatchEditInfo',
      data: { line: shopTaskItem, task: this.taskDetail },
      success: (res) => {
        this.$Router.push({
          name: 'SoptBatchEdit',
          params: {
            isEva: true
          }
        })
      },
      fail: function (res) {}
    })
  }

  /**
   * 更新待保存数据
   * @param e
   * @param uuid
   * @param isDetail 详情
   */
  upSaveAeq(e, uuid, isDetail) {
    if (!isDetail) {
      const index = this.saveSeqList.findIndex((item) => {
        return item.uuid === uuid
      })

      if (index === -1) {
        this.saveSeqList.push({ body: e, uuid: uuid })
      } else {
        this.saveSeqList[index] = { body: e, uuid: uuid }
      }
    }

    const curIndex = this.allSoptCheckFeedbackList.findIndex((item) => {
      return item.uuid === uuid
    })

    if (curIndex === -1) {
      this.allSoptCheckFeedbackList.push({ body: e, uuid: uuid })
    } else {
      this.allSoptCheckFeedbackList[curIndex] = { body: e, uuid: uuid }
    }
  }

  //更新待保存评价数据
  upSaveEvaAeq(e, uuid, isDetail) {
    if (!isDetail) {
      const index = this.saveEvaSeqList.findIndex((item) => {
        return item.uuid === uuid
      })

      if (index === -1) {
        this.saveEvaSeqList.push({ body: e, uuid: uuid })
      } else {
        this.saveEvaSeqList[index] = { body: e, uuid: uuid }
      }
    }

    const curIndex = this.allSoptCheckEvaList.findIndex((item) => {
      return item.uuid === uuid
    })

    if (curIndex === -1) {
      this.allSoptCheckEvaList.push({ body: e, uuid: uuid })
    } else {
      this.allSoptCheckEvaList[curIndex] = { body: e, uuid: uuid }
    }
  }

  /**
   * 批量保存完成反馈内容
   */
  doItemBatchFeedback() {
    this.$showLoading()
    let body: ShopTaskItemBatchFeedbackReq[] = []
    body = this.saveSeqList.map((e) => {
      const infos = (e.body.infos || []).filter((item) => (item.type == 'DATE' ? !!(item.items || []).length : true))
      return { itemId: e.uuid, ...e.body, infos }
    })
    return new Promise((resolve, reject) => {
      if (!this.saveSeqList.length) {
        resolve({})
      } else {
        ShopTaskApi.itemBatchFeedback(body)
          .then((res) => {
            this.$hideLoading()
            resolve({})
          })
          .catch((error) => {
            this.$hideLoading()
            this.$showToast({ title: error.msg, icon: 'none' })
            reject({})
          })
      }
    })
  }

  /**
   * 统计类第一次进来批量保存完成反馈内容
   */
  doItemBatchFirstFeedback() {
    this.$showLoading()
    let body: ShopTaskItemBatchFeedbackReq[] = []
    // 将当前对象转为字符串传给服务端
    body = this.items.map((e) => {
      const str: string[] = []
      const infos: any = []
      // 将当前对象转为字符串传给服务端
      e.surveyContents.forEach((surveyContent: SurveyContent) => {
        surveyContent.ivContent = null
        str.push(JSON.stringify(surveyContent))
      })
      infos.push({ type: 'WORD', items: str })
      return { itemId: e.uuid, infos: infos, ossSupplier: null, notApplicable: false }
    })
    return new Promise((resolve, reject) => {
      ShopTaskApi.itemBatchFeedback(body)
        .then((res) => {
          this.$hideLoading()
          resolve({})
        })
        .catch((error) => {
          this.$hideLoading()
          this.$showToast({ title: error.msg, icon: 'none' })
          reject({})
        })
    })
  }

  // 保存前校验
  doValiteSaveReq(item: SaveSeqList, feedbackNeeded: string[] = []) {
    let toastText = ''
    let hasImg = false
    let hasVideo = false
    let hasWord = false
    let hasButton = false
    ;(item.body.infos || []).forEach((e: MediaInfo) => {
      if (e.type == this.feedbackTypeEnum.IMAGE) {
        hasImg = true
      } else if (e.type == this.feedbackTypeEnum.VIDEO) {
        hasVideo = true
      } else if (e.type == this.feedbackTypeEnum.WORD) {
        hasWord = true
      } else {
        hasButton = true
      }
    })

    if (!item.body || !item.body.notApplicable) {
      if (feedbackNeeded.includes(this.feedbackTypeEnum.IMAGE) && !hasImg) {
        toastText = '还未上传图片'
      } else if (feedbackNeeded.includes(this.feedbackTypeEnum.VIDEO) && !hasVideo) {
        toastText = '还未上传视频'
      } else if (feedbackNeeded.includes(this.feedbackTypeEnum.BUTTON) && !hasButton) {
        toastText = '还未点击完成按钮'
      } else if (feedbackNeeded.includes(this.feedbackTypeEnum.WORD) && !hasWord) {
        toastText = '还未描述任务完成情况'
      }
    } else if (item.body.notApplicable && !hasWord) {
      toastText = '还未描述不适用原因'
    }

    // 日期组件是否展示
    const isShowDate = feedbackNeeded.includes(this.feedbackTypeEnum.DATE)
    const dateInfo = item.body.infos.find((item) => item.type === this.feedbackTypeEnum.DATE)
    const dateList = (dateInfo && dateInfo.items) || []
    const needDateValid = isShowDate && (!dateList[0] || !dateList[1])
    // 如果日期组件非必填，但是出现了日期按钮，此时只用校验一个填写时另一个有没有填写
    const notDateValid = !isShowDate && dateList.filter((item) => !!item).length == 1
    if (needDateValid || notDateValid) {
      if (!dateList[0] && !dateList[1] && needDateValid) {
        toastText = '点检项还未选择日期'
      } else if (!dateList[0]) {
        toastText = '请选择开始日期'
      } else if (!dateList[1]) {
        toastText = '请选择结束日期'
      }
    }

    return toastText
  }

  doEvaValite() {
    if (this.surveyTask && this.surveyTaskFinish) {
      this.$showModal({
        title: '提交',
        content: `提交后将不可更改，确定提交吗？`,
        showCancel: true,
        confirmText: '提交',
        success: async (action) => {
          if (action.confirm) {
            this.doSumbit()
          }
        }
      })
    } else {
      const arr = []
      this.allSoptCheckEvaList.forEach((e) => {
        arr[e.uuid] = e
      })

      const shopTaskItem = CommonUtil.copy(this.shopTaskItem)
      let toastText = ''
      shopTaskItem.forEach((e) => {
        if (e.lines && e.lines.length) {
          e.lines.forEach((item) => {
            const name = this.getSoptName(e, item)
            if (arr[item.uuid]) {
              const text = this.doValiteSaveEvaReq(arr[item.uuid])

              if (text) {
                toastText = `${name} ${text}`
              }
            } else {
              toastText = `${name} 还未进行评价`
            }
          })
        } else {
          const name = this.getSoptName(e, e)
          if (arr[e.uuid]) {
            const text = this.doValiteSaveEvaReq(arr[e.uuid])
            if (text) {
              toastText = `${name} ${text}`
            }
          } else {
            toastText = `${name} 还未进行评价`
          }
        }
      })
      if (toastText) {
        this.$showModal({
          title: '提示',
          content: toastText,
          showCancel: false,
          confirmText: '我知道了',
          success: (action) => {}
        })
      } else {
        this.doItemBatchEva().then(() => {
          this.$showModal({
            title: '提交',
            content: `提交后将不可更改，确定提交吗？`,
            showCancel: true,
            confirmText: '提交',
            success: async (action) => {
              if (action.confirm) {
                this.doSumbit()
              }
            }
          })
        })
      }
    }
  }

  // 保存前校验
  doValiteSaveEvaReq(item: SaveSeqPatrolList) {
    let hasWord = false
    ;(item.body.infos || []).forEach((e: MediaInfo) => {
      if (e.type == feedbackTypeEnum.WORD && e.items.length) {
        hasWord = true
      }
    })

    if (item.body.state === null || (item.body.state === 0 && !hasWord)) {
      // 未选择是否合格 || 当前为不合格 && 没有填文本反馈内容，不可以保存 || 开启了自主评分，且不合格未打分
      return '还未进行评价'
    }
  }

  /**
   * 批量保存完成评价内容
   */
  async doItemBatchEva() {
    this.$showLoading()
    let body: ShopTaskItemAuditBatchReq[] = []
    body = this.saveEvaSeqList.map((e) => {
      return { itemId: e.uuid, ...e.body }
    })
    return new Promise((resolve, reject) => {
      if (!this.saveEvaSeqList.length) {
        resolve({})
      } else {
        ShopTaskApi.batchEvaluate(body)
          .then((res) => {
            this.$hideLoading()
            resolve({})
          })
          .catch((error) => {
            this.$hideLoading()
            this.$showToast({ title: error.msg, icon: 'none' })
            reject({})
          })
      }
    })
  }

  //获取点检项名称
  getSoptName(soptCheckDetail, item) {
    if (soptCheckDetail.type == this.shopTaskItemTypeEnum.PUT) {
      // 商品陈列展示
      return item.detail!.item!.name!
    }
    return item.name
  }

  //保存
  doSave(type: string) {
    let body: any[] = []

    if (type === 'edit') {
      body = this.saveSeqList.map((e) => {
        const infos = (e.body.infos || []).filter((item) => (item.type == 'DATE' ? !!(item.items || []).length : true))
        return { itemId: e.uuid, ...e.body, infos }
      })
    } else {
      body = this.saveEvaSeqList.map((e) => {
        return { itemId: e.uuid, ...e.body }
      })
    }
    if (!body.length) {
      this.$showToast({ title: '保存成功', icon: 'success' })
      return
    } else {
      this.$showLoading()
      const requst: any = type === 'edit' ? ShopTaskApi.itemBatchFeedback : ShopTaskApi.batchEvaluate
      requst(body, true)
        .then((res) => {
          this.$hideLoading()
          this.$showToast({ title: '保存成功', icon: 'success' })
          this.doRefresh()
          this.saveEvaSeqList = []
          this.saveSeqList = []
        })
        .catch((error) => {
          this.$hideLoading()
          this.$showToast({ title: error.msg, icon: 'none' })
        })
    }
  }

  //统一反馈
  doUniformFeek() {
    this.curSoptCheck = new ShopTaskItem()
    this.$refs.feedbackPop.open()
    const curSoptCheck = (this.shopTaskItem[0] && this.shopTaskItem[0].lines[0]) || new ShopTaskItem()
    this.curSoptCheck = curSoptCheck
  }

  // 处理统一反馈数据

  doFormatFeek(itemFeedbackReq: ShopTaskItemFeedbackReq) {
    const body: ShopTaskItemBatchFeedbackReq[] = []

    this.shopTaskItem.forEach((e) => {
      if (e.lines && e.lines.length) {
        e.lines.forEach((item) => {
          const obj = new ShopTaskItemBatchFeedbackReq()
          obj.itemId = item.uuid
          obj.infos = itemFeedbackReq.infos
          obj.notApplicable = itemFeedbackReq.notApplicable
          obj.ossSupplier = itemFeedbackReq.ossSupplier
          obj.uuid = item.uuid
          body.push(obj)
        })
      } else {
        const obj = new ShopTaskItemBatchFeedbackReq()
        obj.itemId = e.uuid
        obj.infos = itemFeedbackReq.infos
        obj.notApplicable = itemFeedbackReq.notApplicable
        obj.ossSupplier = itemFeedbackReq.ossSupplier
        obj.uuid = e.uuid
        body.push(obj)
      }
    })

    return body
  }

  // 关闭统一反馈弹窗
  doCloseUniform() {
    this.$refs.feedbackPop.close()
  }

  // 保存统一反馈
  doSaveUniform(itemFeedbackReq: ShopTaskItemFeedbackReq) {
    const body = this.doFormatFeek(itemFeedbackReq)
    this.$refs.feedbackPop.close()
    this.$showLoading()
    ShopTaskApi.itemBatchFeedback(body, true)
      .then((res) => {
        this.$hideLoading()
        this.$showToast({ title: '保存成功', icon: 'success' })
        this.doRefresh()
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ title: error.msg, icon: 'none' })
      })
  }

  // 提交统一反馈
  doUniformSubmit(itemFeedbackReq: ShopTaskItemFeedbackReq) {
    const body = this.doFormatFeek(itemFeedbackReq)
    this.$refs.feedbackPop.close()
    this.$showLoading()
    ShopTaskApi.itemBatchFeedback(body)
      .then((res) => {
        this.doSumbit()
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ title: error.msg, icon: 'none' })
      })
  }

  // 重点商品订货提醒任务 - 需订数量变化
  ortTaskQtyChange(value) {
    const categoty = this.goodsCategoryList.find((goodsCategory) => goodsCategory.name === '全部')
    const result = categoty?.list.find((good) => {
      return good.sku?.uuid === value.sku?.uuid && value.skuQpcStr === good.skuQpcStr
    })
    if (result) {
      result.qty = value.qty
    }
  }

  // 去打印
  async doPrint() {
    try {
      this.$showLoading({ delayTime: 200 })
      const goodList: AppPrintLabelTpGoodsDTO[] = this.goodsCategoryList.reduce((acc, cur) => {
        acc.push(...cur.list)
        return acc
      }, [])
      const { data } = await AppPrintLabelApi.transferTpGoods(goodList)
      const result: AppPrintLabelGoodsExpandDTO[] = (data || []).map((item) => {
        return {
          ...item,
          templateName: '',
          qty: 1,
          checked: false,
          mfgDate: null,
          expDate: null,
          gid: ''
        }
      })
      this.$hideLoading()
      const self = this
      uni.setStorage({
        key: 'labelPrintingList',
        data: result,
        success: function () {
          let isAndroid: boolean = false
          // #ifdef APP-PLUS
          isAndroid = uni.getSystemInfoSync().platform === 'android'
          // #endif
          self.$Router.push({ name: isAndroid ? 'labelPrintingListAndroid' : 'labelPrintingList' })
        }
      })
    } catch (error) {
      this.$hideLoading()
      this.$showToast({ title: error.msg, icon: 'none' })
    }
  }

  // 是否展开
  doExpand() {
    this.isExpanded = !this.isExpanded
  }

  // 溢出宽度比较
  handleLineChange() {
    const query = uni.createSelectorQuery().in(this)
    let textHeight: number = 0
    let wrapperHeight: number = 0
    query.select('#desc-content').boundingClientRect((data) => {
      if (data) {
        wrapperHeight = Number(data.height)
      }
    })
    query.select('#desc-text').boundingClientRect((data) => {
      if (data) {
        textHeight = Number(data.height)
      }
    })
    query.exec((res) => {
      this.isOverflow = textHeight > wrapperHeight
    })
  }
}
