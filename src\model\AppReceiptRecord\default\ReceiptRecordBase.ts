/*
 * @Author: weisheng
 * @Date: 2024-12-13 14:52:11
 * @LastEditTime: 2025-02-17 14:45:24
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: \soa\src\model\AppReceiptRecord\default\ReceiptRecordBase.ts
 * 记得注释
 */
import { ReceiptRecordState } from './ReceiptRecordState'

export default class ReceiptRecordBase {
  // 单据标识
  billId: string = ''
  // 单号
  num: string = ''
  // 门店标识
  shopId: string = ''
  // 门店代码
  shopNo: string = ''
  // 门店名称
  shopName: string = ''
  // 来源：receipt-收货单，express-快递单
  source: string = ''
  // 来源标识
  sourceId: string = ''
  // 来源收货记录billId
  srcBillId: Nullable<string> = null
  // 来源单据的source，如过本记录是快递单收货记录拆单而来，该字段为express
  srcBillSource: Nullable<string> = null
  // 来源单据的sourceId，如过本记录是快递单收货记录拆单而来，该字段取快递单号
  srcBillSourceId: Nullable<string> = null
  // 收货单号
  receiptNum: string = ''
  // 状态,取值范围：initial-待处理，finished-已完成
  state: Nullable<ReceiptRecordState> = null
  // 商品品项数
  recCnt: number = 0
  // 箱码行记录数
  boxRecCnt: number = 0
  // 收货方式：0-正常收货；1-按箱收货
  receiptWay: Nullable<number> = null
  // 来源单据类型,取值范围：direct-直配出货订单,loading-装车单
  datasource: Nullable<string> = null
  // 本次应收总数
  qty: number = 0
  // 本次应收总规格数
  qpcQty: number = 0
  // 本次应收总额
  total: number = 0
  // 本次应收箱数
  packQty: Nullable<number> = null
  // 收货总数
  receiptQty: number = 0
  // 收货库总规格数
  receiptQpcQty: number = 0
  // 收货库总额
  receiptTotal: number = 0
  // 收货箱数
  receiptPackQty: Nullable<number> = null
  // 创建时间
  created: Date = new Date()
  // 创建人代码
  creatorId: Nullable<string> = null
  // 创建人名称
  creatorName: Nullable<string> = null
  // 最后修改时间
  lastModified: Date = new Date()
  // 最后修改人代码
  lastModifierId: Nullable<string> = null
  // 最后修改人名称
  lastModifierName: Nullable<string> = null
  // 提交时间
  submitTime: Nullable<Date> = null
  // 提交人代码
  submitterId: Nullable<string> = null
  // 提交人名称
  submitterName: Nullable<string> = null
  // 所属组织标识
  orgUuid: Nullable<string> = null
  // 数据标识
  uuid: string = ''
  // 所属组织代码
  orgCode: Nullable<string> = null
  // 所属组织名称
  orgName: Nullable<string> = null
  // 来源应用
  appId: string = ''
  // 备注
  note: Nullable<string> = null
}
