import { Vue, Component, Prop, Emit, Watch } from 'vue-property-decorator'
import { debounce } from 'ts-debounce'
import config from '@/config'
import SysConfigItem from '@/model/sysConfig/SysConfigItem'
import { State } from 'vuex-class'
import AppRequireApplyDraftLineQueryDTO from '@/model/requireApply/draft/AppRequireApplyDraftLineQueryDTO'
import { RoundType } from '@/model/requireApply/RoundType'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import AcclineItem from './AcclineItem.vue'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'
import IosVerisonMgr from '@/mgr/IosVerisonMgr'
// eslint-disable-next-line @typescript-eslint/ban-ts-ignore
// @ts-ignore
@Component({
  components: { AcclineItem },
  options: {
    virtualHost: true
  }
})
export default class GoodsListItem extends Vue {
  @State('sysConfig') sysConfig: SysConfigItem // 获取全局配置

  focusStatus: boolean = false // 聚焦状态

  imageHeight: number = uni.upx2px(120) //商品图片高度
  $refs: any
  // 购物车相关
  @Prop() value: number
  @Prop({ type: Number, default: 1 }) step: number
  @Prop({ type: Boolean, default: false }) disabled: boolean
  @Prop({ type: Number, default: 0 }) scale: number
  @Prop({ type: Boolean, default: false }) allowNull: boolean
  @Prop({ type: Boolean, default: true }) showAvgArrow: boolean
  @Prop({ type: Boolean, default: false }) hideInvQty: boolean //隐藏库存数量
  @Prop({ type: Boolean, default: false }) showGdImage: boolean //是否显示图片
  @Prop({ type: Boolean, default: true }) showNote: boolean //是否显示备注图片
  @Prop({ type: Number, default: 0 }) validateLevel4CaseQty: number // 叫货包装数整规格校验级别:0-不提示(默认)，1-仅提示，2-不允许保存
  @Prop({ type: Boolean, default: false }) isShopCart: boolean // 是否为购物车商品行
  // 门店辅料管理 为1时自动计算辅料，并将辅料数据计算入主商品的价格中，为2时直接展示辅料商品，用户自行添加数量，不计算入主商品的价格金额中
  @Prop({ type: Number, default: 0 }) accessoryManage: number
  @Prop({ type: String, default: '-' }) accessoryGoodsCategoryNamePrefix: string // 当前商品是否为辅料
  oldVal: number = 0 // 用户编辑输入框时保留用户旧数据
  beforeVal: number = 0
  debounceChange = debounce(this.doChangeValue, 200, { isImmediate: false }) // 防抖

  goods: AppRequireApplyDraftLineQueryDTO = new AppRequireApplyDraftLineQueryDTO() // 商品信息
  @Prop({ type: Object, default: new AppRequireApplyDraftLineQueryDTO() }) goodsLine: AppRequireApplyDraftLineQueryDTO // 商品信息
  @Watch('goodsLine', { immediate: true, deep: true })
  onChange(newGood: AppRequireApplyDraftLineQueryDTO) {
    if (newGood) {
      newGood.qpcQty = `${Number(newGood.qpcQty)}`
      this.goods = JSON.parse(JSON.stringify(newGood))
    }
  }
  @Watch('goods.qpcQty')
  onChangeInputValue(newVal, oldVal) {
    this.goods.qpcQty = `${Number(newVal)}`
    this.debounceChange(newVal, oldVal)
  }

  // 获取系统信息
  get iosVerison() {
    return IosVerisonMgr.hasIosVerison()
  }

  /**
   * 是否展示辅料商品tag
   */
  get showAccessoryTag() {
    return (
      this.accessoryManage === 2 &&
      this.goods.goods.firstCategory &&
      this.goods.goods.firstCategory.name.startsWith(this.accessoryGoodsCategoryNamePrefix)
    )
  }

  /**
   * 是否自动计算辅料价格
   * 当accessoryManage为1时，自动计算辅料价格，并将辅料数据计算入主商品的价格中，为2时直接展示辅料商品，用户自行添加数量，不计算入主商品的价格金额中
   */
  get autoCalcAcc() {
    return this.accessoryManage === 1
  }

  // 商品名
  get name() {
    return this.goods.goods && this.goods.goods.name ? `${this.goods.goods.name}` : null
  }
  // 商品名+含量
  get friendlyStr() {
    return this.goodsLine.goods && this.goodsLine.goods.friendlyStr ? `${this.goodsLine.goods.friendlyStr}` : null
  }

  // 商品code
  get code() {
    return this.goods.goods && this.goods.goods.code ? `${this.goods.goods.code}` : null
  }

  // 商品inputCode

  get inputCode() {
    return this.goods.goods && this.goods.goods.inputCode ? `${this.goods.goods.inputCode}` : null
  }
  // 单位
  get munit() {
    return this.goods.goods && this.goods.goods.munit ? `${this.goods.goods.munit}` : null
  }
  // 最小规格单位
  get minMunit() {
    return this.goods.goods && this.goods.goods.minMunit ? `${this.goods.goods.minMunit}` : null
  }
  // 规格价
  get price() {
    if (this.goods.accLines && this.goods.accLines.length) {
      return this.isShopCart ? this.calcCartPrice(this.goods) : this.calcPrice(this.goods)
    } else {
      if (!this.goods.goods) {
        return null
      } else {
        return this.isShopCart ? this.calcCartPrice(this.goods) : this.goods.goods.price
      }
    }
  }
  // 规格
  get qpcStr() {
    return this.goods.goods && this.goods.goods.qpcStr ? `${this.goods.goods.qpcStr}` : null
  }

  // 零售价
  get retailPrice() {
    return `¥ ${Number.isFinite(this.goods.goods.rtlPrc) ? this.goods.goods.rtlPrc : '--'}/${
      this.goods.goods.minMunit ? this.goods.goods.minMunit : '--'
    }`
  }

  // uuid
  get uuid() {
    return this.goods.goods && this.goods.goods.uuid ? `${this.goods.goods.uuid}` : null
  }

  // 是否显示减号和输入框
  get showMinus() {
    return typeof this.goods.qpcQty == 'string' || (this.goods && this.goods.qpcQty > 0)
  }

  // 商品图片
  // get img() {
  //   return this.goodsLine.goods && this.goodsLine.goods.images && this.goodsLine.goods.images.length
  //     ? `${this.goodsLine.goods.images[0]}?x-oss-process=image/resize,l_${this.imageHeight}`
  //     : `${config.sourceUrl}icon/pic_goods.png`
  // }

  // 商品图片
  get img() {
    if (this.goodsLine.goods) {
      if (this.goodsLine.goods.mainImage) {
        return `${this.goodsLine.goods.mainImage}?x-oss-process=image/resize,l_${this.imageHeight}`
      } else {
        if (this.goodsLine.goods.images && this.goodsLine.goods.images.length) {
          const firstImg = this.goodsLine.goods.images.find((item) => !this.isVideoType(item))
          return firstImg ? `${firstImg}?x-oss-process=image/resize,l_${this.imageHeight}` : `${config.sourceUrl}icon/pic_goods.png`
        }
        return `${config.sourceUrl}icon/pic_goods.png`
      }
    }
    return `${config.sourceUrl}icon/pic_goods.png`
  }

  // get ImageList() {
  //   return this.goodsLine && this.goodsLine.goods && this.goodsLine.goods.images && this.goodsLine.goods.images.length
  //     ? this.goodsLine.goods.images
  //     : [`${config.sourceUrl}icon/pic_goods.png`]
  // }

  get ImageList() {
    if (this.goodsLine.goods) {
      if (this.goodsLine.goods.mainImage) {
        return [this.goodsLine.goods.mainImage]
      } else {
        if (this.goodsLine.goods.images && this.goodsLine.goods.images.length) {
          const firstImg = this.goodsLine.goods.images.find((item) => !this.isVideoType(item))
          return firstImg ? [firstImg] : [`${config.sourceUrl}icon/pic_goods.png`]
        }
        return [`${config.sourceUrl}icon/pic_goods.png`]
      }
    }
    return [`${config.sourceUrl}icon/pic_goods.png`]
  }

  // 推介类型
  get recommandType() {
    return this.goodsLine.goods && this.goodsLine.goods.recommandType ? `${this.goodsLine.goods.recommandType}` : null
  }

  // 是否含料包
  get hasLiaobao() {
    if (!Number(this.goodsLine.qpcQty)) {
      return false
    }
    return this.goodsLine && this.goodsLine.accLines && this.goodsLine.accLines.length ? true : false
  }

  // 是否为爆品
  get isHot() {
    if (this.goodsLine.goods) {
      const hotType = this.goodsLine.goods.goodsType && this.goodsLine.goods.goodsType == 4
      const actQty = this.goodsLine.goods.allowActQpcQty
      return hotType && actQty
    } else {
      return false
    }
  }

  // 是否限量
  get isLimit() {
    return this.goodsLine.goods && this.goodsLine.goods.isLimit ? true : false
  }

  // 配货方式
  get alcType() {
    return this.goodsLine.goods && this.goodsLine.goods.alc ? `${this.goodsLine.goods.alc}` : null
  }

  // 是否新品
  get isNewList() {
    return this.goodsLine.goods && this.goodsLine.goods.isNewList ? true : false
  }

  get reqQtyDay1() {
    return this.goodsLine && this.goodsLine.goods ? this.goodsLine.goods.reqQtyDay1 : null
  }

  // 是否散货
  get isDisp() {
    return this.goodsLine && this.goodsLine.goods.isDisp ? true : false
  }

  //商品备注
  get note() {
    return this.goodsLine && this.goodsLine.note ? true : false
  }

  //上架备注
  get shelveNote() {
    return this.goodsLine && this.goodsLine.goods && this.goodsLine.goods.shelveNote ? this.goodsLine.goods.shelveNote : ''
  }

  //报名数（规格数）
  get signUpQty() {
    return this.goodsLine && this.goodsLine.goods && Number((this.goodsLine.goods.signUpQty || 0) / this.goodsLine.goods.qpc).scale(4)
  }

  //可订数（规格数）
  get allowQpcQty() {
    return this.goodsLine && this.goodsLine.goods && Number((this.goodsLine.goods.allowQty || 0) / this.goodsLine.goods.qpc).floorScale(4)
  }

  // 爆品可订货数
  get allowActQpcQty() {
    return this.goodsLine && this.goodsLine.goods && Number(this.goodsLine.goods.allowActQpcQty)
  }

  //已订数（规格数）
  get orderQpcQty() {
    return this.goodsLine && this.goodsLine.goods && Number((this.goodsLine.goods.orderQty || 0) / this.goodsLine.goods.qpc).scale(4)
  }

  // 保质期天数
  get validPeriod() {
    return this.goodsLine && this.goodsLine.goods ? this.goodsLine.goods.validPeriod : null
  }

  // 到效期
  get recentValidDate() {
    return this.goodsLine && this.goodsLine.goods ? this.goodsLine.goods.recentValidDate : null
  }

  //最大可订量
  get max() {
    let max: number = 99999999

    if (this.isLimit) {
      if (this.goodsLine.goods.allowQty) {
        max = Math.min(max, (Number(this.goodsLine.goods.allowQty) / this.goodsLine.goods.qpc).floorScale(4))
      }
    } else {
      if (this.goodsLine.highOrd && this.goodsLine.highOrdCtrl === 1) {
        max = Math.min(max, (Number(this.goodsLine.highOrd) / this.goodsLine.goods.qpc).floorScale(4))
      }
    }
    return max
  }

  get min() {
    let min: number = 0

    if (this.goodsLine.lowOrd && this.goodsLine.lowOrdCtrl === 1) {
      min = Math.max(min, (Number(this.goodsLine.lowOrd) / this.goodsLine.goods.qpc).floorScale(4))
    }
    return min
  }

  //展示代码
  get showCode() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.PLACEORDERCODE)) {
      return true
    }
    return false
  }

  //展示条码
  get showInputCode() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.PLACEORDERBARCODE)) {
      return true
    }
    return false
  }

  //展示在途库存
  get enableStandardShowTransportationInventory() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.ENABLESTANDARDSHOWTRANSPORTATIONINVENTORY)) {
      return true
    }
    return false
  }

  //展示未发货库存
  get enableStandardShowUnshippedInventory() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.ENABLESTANDARDSHOWUNSHIPPEDINVENTORY)) {
      return true
    }
    return false
  }

  //展示保质期天数
  get showValidPeriod() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWVALIDPERIOD)) {
      return true
    }
    return false
  }

  //展示到效期
  get showRecentValidDate() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWRECENTVALIDDATE)) {
      return true
    }
    return false
  }

  // 展示标准陈列量
  get showStdShow() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.STDSHOW)) {
      return true
    }
    return false
  }

  // 展示活动陈列量
  get showActiveShowQty() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.ACTIVESHOWQTY)) {
      return true
    }
    return false
  }

  get warnText() {
    let warnText: string = ''
    if (!this.goods) {
      warnText = ''
    } else {
      if (
        typeof this.goods.lowOrd === 'number' &&
        this.goods.lowOrdCtrl === 0 &&
        Number(this.goods.qpcQty) &&
        Number(this.goods.qpcQty) < Number(this.goods.lowOrd) / this.goods.goods.qpc
      ) {
        return '低于建议数量'
      } else if (
        typeof this.goods.highOrd === 'number' &&
        this.goods.highOrdCtrl === 0 &&
        this.goods.qpcQty &&
        Number(this.goods.qpcQty) > Number(this.goods.highOrd) / this.goods.goods.qpc
      ) {
        return '超出建议数量'
      }
    }

    return warnText
  }

  // 是否显示昨日叫货
  get yestedayPremission() {
    return PermissionMgr.hasPermission(Permission.requireApplyYestedayShow)
  }

  /**
   * 辅料数量变化
   * @param qty 辅料数量
   * @param index 辅料下标
   */
  handleAccChange(qpcQty: number, index: number) {
    if (qpcQty !== Number(this.goods.accLines[index].qpcQty)) {
      this.goods.accLines[index].qpcQty = `${qpcQty}`
      this.$emit('change', Number(this.goods.qpcQty), this.goods)
    }
  }

  // 平均日销点击事件
  doAvgShow() {
    this.$emit('doAvgShow', this.goodsLine)
  }
  // 输入框的值改变触发之事件
  doChangeValue(newVal, oldVal) {
    if (newVal !== '' && newVal >= 0) {
      if (!this.focusStatus) {
        this.$emit('change', +newVal, this.goods)
      }
    } else {
      if (oldVal) {
        this.oldVal = +oldVal
      }
    }
  }
  _calcValue(type: string) {
    const scale: number = Number(this._getDecimalScale())
    let value: number = Number(this.goods.qpcQty) * scale
    const step: number = this.step * scale
    if (type === 'minus') {
      value = (value - step).scale(4)
      if (value < this.min) {
        // if (value > 0) {
        //   value = this.min
        // } else {
        //   value = 0
        // }
        value = 0
      }
      if (value > this.max) {
        value = this.max
      }
    } else if (type === 'plus') {
      value = (value + step).scale(4)
      if (value > this.max) {
        value = this.max
      }
      if (value < this.min) {
        value = this.min

        // if (value > 0) {
        //   value = this.min
        // } else {
        //   value = 0
        // }
      }
      // 爆品订货超出提示
      if (this.isHot && value > this.allowActQpcQty && Number(this.goods.qpcQty) <= this.allowActQpcQty) {
        uni.showToast({ icon: 'none', title: '超出爆品可订数的商品将按正常订货价结算～' })
      }
    }
    this.goods.qpcQty = `${value.scale(4)}`
    this.beforeVal = +this.goods.qpcQty
    if (this.validateLevel4CaseQty == 1 && String(value).indexOf('.') > -1) {
      uni.showToast({ icon: 'none', title: '订货数不是配货规格的整数倍' })
    } else if (this.validateLevel4CaseQty == 2) {
      this.goods.qpcQty = `${value.floorScale(0) || 0}`
      this.beforeVal = +this.goods.qpcQty
    }
  }

  _getDecimalScale() {
    let scale = 1
    // 浮点型
    if (~~this.step !== this.step) {
      scale = Math.pow(10, (this.step + '').split('.')[1].length)
    }
    return scale
  }

  onFocus() {
    this.focusStatus = true
    this.$emit('focusChange', this.focusStatus)
  }
  _onBlur(event) {
    this.focusStatus = false
    this.$emit('focusChange', this.focusStatus)

    let value = event.detail.value.match(/^\d*(\.?\d{0,4})/g)[0]

    if (value === '') {
      if (this.oldVal === this.min) {
        this.goods.qpcQty = '-1'
        this.$nextTick(() => {
          this.goods.qpcQty = `${this.min}`
          uni.showToast({ icon: 'none', title: `请输入大于${this.min}的数字哦～` })
        })
      } else {
        uni.showToast({ icon: 'none', title: `请输入大于${this.min}的数字哦～` })
        this.goods.qpcQty = `${+this.oldVal}`
        this.oldVal = this.min
        this.beforeVal = +this.goods.qpcQty
      }
      return
    }

    value = +value

    if (value > this.max) {
      uni.showToast({ icon: 'none', title: `请输入小于${this.max}的数字哦～` })
      value = this.max
    } else if (value < this.min) {
      if (value > 0) {
        value = this.min
        uni.showToast({ icon: 'none', title: `请输入大于${this.min}的数字哦～` })
      } else {
        value = 0
      }
    }
    this.oldVal = this.min

    // 爆品订货超出提示
    if (this.isHot && value > this.allowActQpcQty && Number(this.beforeVal) <= this.allowActQpcQty) {
      uni.showToast({ icon: 'none', title: '超出爆品可订数的商品将按正常订货价结算～' })
    }
    this.goods.qpcQty = value.scale(4)
    this.beforeVal = +this.goods.qpcQty

    if (this.validateLevel4CaseQty == 1 && String(value).indexOf('.') > -1) {
      uni.showToast({ icon: 'none', title: '订货数不是配货规格的整数倍' })
    } else if (this.validateLevel4CaseQty == 2) {
      this.goods.qpcQty = value.floorScale(0)
      this.beforeVal = +this.goods.qpcQty
    }

    // this.$emit('change', this.goods.qpcQty, this.goods)
  }

  /**
   * 预览图片
   * @param imageList 预览图片列表
   * @param current 预览图片下标
   */
  previewImg() {
    uni.previewImage({
      current: String(0),
      urls: this.ImageList
    })
  }

  /**
   * 打开商品备注弹窗
   */
  doNoteShow() {
    this.$emit('doGoodsNoteShow', this.goodsLine)
  }

  /**
   * 打开上架备注弹窗
   */
  doShowShelveNote() {
    this.$emit('doShowShelveNote', this.shelveNote)
  }

  /**
   * 计算价格
   */
  calcPrice(goods: AppRequireApplyDraftLineQueryDTO) {
    const qpcQty = Number(this.goods.qpcQty) || 1
    const sku = JSON.parse(JSON.stringify(goods))
    sku.qty = Number(qpcQty.multiply(Number(sku.goods.qpc))).scale(4)
    let skuAccTotal: number = 0

    this.autoCalcAcc &&
      sku.accLines &&
      sku.accLines.forEach((line) => {
        const qty: number = (sku.qty * line.accessoryRate) / (line.goodsRate || 1)
        if (line.roundType === RoundType.ceil) {
          line.qty = Math.ceil(qty)
          line.total = (line.qty * line.price) / line.qpc
        } else {
          line.qty = qty.scale(4)
          line.total = (line.qty * line.price) / line.qpc
        }
        skuAccTotal = Number(skuAccTotal) + line.total
      })
    const total = Number(sku.goods.price).multiply(Number(qpcQty))
    const accTotal = Number(skuAccTotal).add(total)

    return (accTotal / qpcQty).scale(4)
  }

  /**
   * 计算购物车商品价格
   * 有爆品活动时，购物车商品价格 = 总
   */
  calcCartPrice(goods: AppRequireApplyDraftLineQueryDTO) {
    const qpcQty = Number(this.goods.qpcQty) || 1
    const sku = JSON.parse(JSON.stringify(goods))
    sku.qty = Number(qpcQty.multiply(Number(sku.goods.qpc))).scale(4)
    let skuAccTotal: number = 0
    this.autoCalcAcc &&
      sku.accLines &&
      sku.accLines.forEach((line) => {
        const qty: number = (sku.qty * line.accessoryRate) / (line.goodsRate || 1)
        if (line.roundType === RoundType.ceil) {
          line.qty = Math.ceil(qty)
          line.total = ((line.qty * line.price) / line.qpc).scale(2)
        } else {
          line.qty = qty.scale(4)
          line.total = ((line.qty * line.price) / line.qpc).scale(2)
        }
        skuAccTotal = Number(skuAccTotal) + line.total
      })

    let total = Number(sku.goods.price).multiply(Number(qpcQty))
    if (sku.goods.goodsType == 4 && sku.goods.allowActQpcQty) {
      total = 0
      const actPrice = sku.goods.actPrice || 0
      if (qpcQty > this.allowActQpcQty) {
        // 加购数量大于爆品活动限购数量时，多出的数量需要按照原价进行金额计算
        const diffQty = Number(qpcQty) - this.allowActQpcQty
        const activeTotal = actPrice.multiply(Number(this.allowActQpcQty))
        const diffTotal = sku.goods.price.multiply(Number(diffQty))
        total = activeTotal.add(diffTotal)
      } else {
        // 正常情况下按照爆品活动价进行价格计算
        total = actPrice.multiply(Number(qpcQty))
      }
    }
    const accTotal = Number(skuAccTotal).add(total)

    return (accTotal / qpcQty).scale(4)
  }

  /**
   * 判断一个资源是否为视频格式
   * @param url 资源链接
   */
  isVideoType(url) {
    const videoExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    const fileExtension = url.substring(url.lastIndexOf('.')).toLowerCase()
    return videoExtensions.includes(fileExtension)
  }
}
