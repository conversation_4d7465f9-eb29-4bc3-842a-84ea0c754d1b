import fly from '@/network/fly'
import BaseResponse from '@/model/base/BaseResponse'
import QueryRequest from '@/model/base/QueryRequest'
import RequireApplyPrepareResponse from '@/model/requireApply/RequireApplyPrepareResponse'
import RequireApplyModification from '@/model/requireApply/RequireApplyModification'
import RequireApplySummary from '@/model/requireApply/RequireApplySummary'
import RequireApplyGdSearchResult from '@/model/requireApply/RequireApplyGdSearchResult'
import RequireApplyStoreInfo from '@/model/requireApply/RequireApplyStoreInfo'
import RequireApplyOption from '@/model/requireApply/RequireApplyOption'
import AllocDir from '@/model/requireApply/AllocDir'
import RequireApplyAllocDirImportResult from '@/model/requireApply/RequireApplyAllocDirImportResult'
import RequireApplyAllocDirImporter from '@/model/requireApply/RequireApplyAllocDirImporter'
import AppRequireApplyDTO from '@/model/requireApply/AppRequireApplyDTO'
import RequireApplyCopyResult from '@/model/requireApply/RequireApplyCopyResult'
import AppRequireApplyCreationDTO from '@/model/requireApply/AppRequireApplyCreationDTO'
import RequireApplyCreationByDraft from '@/model/requireApply/draft/RequireApplyCreationByDraft'
import RequireApplySubmitResponse from '@/model/requireApply/draft/RequireApplySubmitResponse'
import illDraftKey from '@/model/default/illDraftKey'
import AppRequireApplyDraftLineQueryDTO from '@/model/requireApply/draft/AppRequireApplyDraftLineQueryDTO'
import RequirePeriod from '@/model/requireApply/RequirePeriod'
import GoodsSaleRefRequest from '@/model/default/GoodsSaleRefRequest'
import AppRequireApplySaleRefDTO from '@/model/requireApply/AppRequireApplySaleRefDTO'
import AppRequireApplyTabCategoryFilterDTO from '@/model/requireApply/draft/AppRequireApplyTabCategoryFilterDTO'
import BaseTabCategoryDTO from '@/model/requireApply/BaseTabCategoryDTO'
import CodeName from '@/model/base/CodeName'
import RequireApplyModificationByDraft from '@/model/requireApply/draft/RequireApplyModificationByDraft'
import AppRequireApplyModifyDraftLineDTO from '@/model/requireApply/draft/AppRequireApplyModifyDraftLineDTO'
import AppRequireApplyAddGdSearchItemDTO from '@/model/requireApply/AppRequireApplyAddGdSearchItemDTO'
import AppRequireApplyDraftLineSumDTO from '@/model/requireApply/draft/AppRequireApplyDraftLineSumDTO'
import AppRequireApplyLineDTO from '@/model/requireApply/AppRequireApplyLineDTO'
import DraftID from '@/model/default/DraftID'
import illDraftLineKey from '@/model/default/illDraftLineKey'
import illDraftLineKeys from '@/model/default/illDraftLineKeys'
import AppRequireApplyDraftDTO from '@/model/requireApply/draft/AppRequireApplyDraftDTO'
import AppRequireApplyDraftSaveResultDTO from '@/model/requireApply/AppRequireApplyDraftSaveResultDTO'
import AppRequireApplyDraftLineDTO from '@/model/requireApply/draft/AppRequireApplyDraftLineDTO'
import DraftLineID from '@/model/requireApply/DraftLineID'
import GoodsExt from '@/model/requireApply/GoodsExt'
import ID from '@/model/default/ID'
import AppRequireTemplateDTO from '@/model/requireApply/AppRequireTemplateDTO'
import AppRequireApplyDraftGoodsFilter from '@/model/requireApply/draft/AppRequireApplyDraftGoodsFilter'
import AppRequireApplyReducibleFilter from '@/model/requireApply/AppRequireApplyReducibleFilter'
import AppRequireApplyDraftLineKeys from '@/model/requireApply/AppRequireApplyDraftLineKeys'
import RequireApplyWithdrawResult from '@/model/requireApply/draft/RequireApplyWithdrawResult'
import CMSPageTemplate from '@/model/delivery/CMSPageTemplate'
import CMSContentTemplate from '@/model/delivery/CMSContentTemplate'
import RequireApplySuggestOrderRule from '@/model/requireApply/RequireApplySuggestOrderRule'
import PreReqOrdUnpickInfo from '@/model/requireApply/PreReqOrdUnpickInfo'
import RequireApplyBeforeSubmitValResult from '@/model/requireApply/RequireApplyBeforeSubmitValResult'
import RequireApplyBeforeSubmitValidator from '@/model/requireApply/RequireApplyBeforeSubmitValidator'

export default class RequireApplyApi {
  /**
   * 复制指定单据
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static copy(id: string): Promise<BaseResponse<AppRequireApplyDTO>> {
    return fly
      .post(`sos/v1/{tenant}/require/apply/copy`, {
        id: id
      })
      .then((res) => {
        return res
      })
  }

  /**
   * 复制单据数据到草稿
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static copy2Draft(id: string): Promise<BaseResponse<RequireApplyCopyResult>> {
    return fly
      .post(`sos/v1/{tenant}/require/apply/copy2Draft`, {
        id: id
      })
      .then((res) => {
        return res
      })
  }

  /**
   * 创建单据
   *
   * @param body 创建对象
   * @param tenant 租户标识
   */
  static create(body: AppRequireApplyCreationDTO): Promise<BaseResponse<string>> {
    return fly.post(`sos/v1/{tenant}/require/apply/create`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 创建并提交单据
   *
   * @param body 创建对象
   * @param tenant 租户标识
   */
  static createAndSubmit(body: AppRequireApplyCreationDTO): Promise<BaseResponse<string>> {
    return fly.post(`sos/v1/{tenant}/require/apply/createAndSubmit`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 从草稿创建并提交单据
   *
   * @param body 创建对象
   * @param tenant 租户标识
   */
  static createAndSubmitByDraft(body: RequireApplyCreationByDraft): Promise<BaseResponse<RequireApplySubmitResponse>> {
    return fly.post(`sos/v1/{tenant}/require/apply/createAndSubmitByDraft`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 从草稿创建单据
   *
   * @param body 创建对象
   * @param tenant 租户标识
   */
  static createByDraft(body: RequireApplyCreationByDraft): Promise<BaseResponse<string>> {
    return fly.post(`sos/v1/{tenant}/require/apply/createByDraft`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询商品
   *
   * @param body 查询条件
   */
  static queryGoods(body: QueryRequest): Promise<BaseResponse<Nullable<GoodsExt[]>>> {
    return fly.post('sos/v1/{tenant}/require/apply/goods/query', body, {}).then((res) => {
      return res
    })
  }

  /**
   * 准备编辑数据
   *
   * @param body 单据缓存主键
   * @param tenant 租户标识
   */
  static doBeforeEdit(body: illDraftKey): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/require/apply/edit/before`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取指定单据
   *
   * @param tenant 租户标识
   * @param id 单据标识
   * @param fetchParts 需要返回的部分信息，多个以逗号分隔
   */
  static get(id: string, fetchParts?: string): Promise<BaseResponse<AppRequireApplyDTO>> {
    return fly
      .get(
        `sos/v1/{tenant}/require/apply/${id}`,
        {},
        {
          params: {
            fetchParts: fetchParts
          }
        }
      )
      .then((res) => {
        return res
      })
  }

  /**
   * 查询自动配货规则选项
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static getAutoDistRuleOption(body: ID): Promise<BaseResponse<RequireApplySuggestOrderRule>> {
    return fly.post(`sos/v1/{tenant}/require/apply/suggest/order/rule`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询指定商品
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static getDraftGoods(body: AppRequireApplyDraftGoodsFilter): Promise<BaseResponse<AppRequireApplyDraftLineQueryDTO>> {
    return fly.post(`sos/v1/{tenant}/require/apply/draft/goods/get`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取模块配置
   *
   * @param tenant 租户标识
   */
  static getOption(): Promise<BaseResponse<RequireApplyOption>> {
    return fly.post(`sos/v1/{tenant}/require/apply/option/get`, {}, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取要货时段
   *
   * @param tenant 租户标识
   */
  static getPeriod(): Promise<BaseResponse<RequirePeriod>> {
    return fly.get(`sos/v1/{tenant}/require/apply/period`, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询商品销售参考信息
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static getSaleRef(body: GoodsSaleRefRequest): Promise<BaseResponse<AppRequireApplySaleRefDTO>> {
    return fly.post(`sos/v1/{tenant}/require/apply/goods/reference`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取门店叫货规则信息
   *
   * @param tenant 租户标识
   */
  static getStoreInfo(): Promise<BaseResponse<RequireApplyStoreInfo>> {
    return fly.get(`sos/v1/{tenant}/require/apply/store/info`, {}).then((res) => {
      return res
    })
  }

  /**
   * 从叫货目录导入商品
   *
   * @param body 租户标识
   * @param tenant 租户标识
   */
  static importGoodsByAllocDir(body: RequireApplyAllocDirImporter): Promise<BaseResponse<RequireApplyAllocDirImportResult>> {
    return fly.post(`sos/v1/{tenant}/require/apply/allocdirgoods/import`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 初始化减量单
   *
   * @param body 要货申请单标识
   * @param tenant 租户标识
   */
  static initReduce(body: ID): Promise<BaseResponse<illDraftKey>> {
    return fly.post(`sos/v1/{tenant}/require/apply/reduce/init`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取商品分类。（两级分类）
   *
   * @param body 草稿标识
   * @param tenant 租户标识
   */
  static listCategory(body: AppRequireApplyTabCategoryFilterDTO): Promise<BaseResponse<BaseTabCategoryDTO[]>> {
    return fly.post(`sos/v1/{tenant}/require/apply/category/list`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取原因列表
   *
   * @param tenant 租户标识
   * @param type 原因类型：normal-叫货原因,add-加单原因
   */
  static listReason(type: string): Promise<BaseResponse<CodeName[]>> {
    return fly
      .get(
        `sos/v1/{tenant}/require/apply/reason/list`,
        {},
        {
          params: {
            type: type
          }
        }
      )
      .then((res) => {
        return res
      })
  }

  /**
   * 获取可减量单据
   *
   * @param tenant 租户标识
   */
  static listReducible(body: AppRequireApplyReducibleFilter): Promise<BaseResponse<AppRequireApplyDTO[]>> {
    return fly.post(`sos/v1/{tenant}/require/apply/reducible/list`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询叫货模板
   *
   * @param body 查询请求
   * @param tenant 租户标识
   */
  static queryTemplate(body: QueryRequest): Promise<BaseResponse<AppRequireTemplateDTO[]>> {
    return fly.post(`sos/v1/{tenant}/require/apply/template/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 编辑单据
   *
   * @param body 编辑对象
   * @param tenant 租户标识
   */
  static modify(body: RequireApplyModification): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/require/apply/modify`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 从草稿编辑并提交单据
   *
   * @param body 编辑对象
   * @param tenant 租户标识
   */
  static modifyAndSubmitByDraft(body: RequireApplyModificationByDraft): Promise<BaseResponse<RequireApplySubmitResponse>> {
    return fly.post(`sos/v1/{tenant}/require/apply/modifyAndSubmitByDraft`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 从草稿编辑单据
   *
   * @param body 编辑对象
   * @param tenant 租户标识
   */
  static modifyByDraft(body: RequireApplyModificationByDraft): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/require/apply/modifyByDraft`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 编辑草稿行
   *
   * @param body 草稿行编辑对象
   * @param tenant 租户标识
   */
  static modifyDraftLine(body: AppRequireApplyModifyDraftLineDTO): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/require/apply/draft/line/modify`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 准备创建单据
   *
   * @param tenant 租户标识
   */
  static prepare4create(): Promise<BaseResponse<RequireApplyPrepareResponse>> {
    return fly.post(`sos/v1/{tenant}/require/apply/create/prepare`, {}, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询单据
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static query(body: QueryRequest): Promise<BaseResponse<AppRequireApplyDTO[]>> {
    return fly.post(`sos/v1/{tenant}/require/apply/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 加单商品搜索
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryAddGoods(body: QueryRequest): Promise<BaseResponse<AppRequireApplyAddGdSearchItemDTO[]>> {
    return fly.post(`sos/v1/{tenant}/require/apply/add/goods/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询叫货目录
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryAllocDir(body: QueryRequest): Promise<BaseResponse<AllocDir[]>> {
    return fly.post(`sos/v1/{tenant}/require/apply/allocdir/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询商品
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryDraftGoods(body: QueryRequest): Promise<BaseResponse<AppRequireApplyDraftLineQueryDTO[]>> {
    return fly.post(`sos/v1/{tenant}/require/apply/draft/goods/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询单据草稿明细
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryDraftLine(body: QueryRequest): Promise<BaseResponse<AppRequireApplyDraftLineSumDTO>> {
    return fly.post(`sos/v1/{tenant}/require/apply/draft/lines/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询单据明细
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryLine(body: QueryRequest): Promise<BaseResponse<AppRequireApplyLineDTO[]>> {
    return fly.post(`sos/v1/{tenant}/require/apply/lines/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询未添加的爆品商品
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryUnOrderActGoods(body: DraftID): Promise<BaseResponse<AppRequireApplyDraftLineQueryDTO[]>> {
    return fly.post(`sos/v1/{tenant}/require/apply/unorder/actgoods/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 删除指定单据
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static remove(id: string): Promise<BaseResponse<void>> {
    return fly
      .post(
        `sos/v1/{tenant}/require/apply/remove`,
        {
          id: id
        },
        {}
      )
      .then((res) => {
        return res
      })
  }

  /**
   * 删除草稿
   *
   * @param body 草稿标识
   * @param tenant 租户标识
   */
  static removeDraft(body: DraftID): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/require/apply/draft/remove`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 删除指定单据草稿明细
   *
   * @param body 草稿明细标识
   * @param tenant 租户标识
   */
  static removeDraftLine(body: illDraftLineKey): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/require/apply/draft/line/remove`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 批量删除单据草稿明细
   *
   * @param body 草稿行批量删除对象
   * @param tenant 租户标识
   */
  static removeDraftLines(body: AppRequireApplyDraftLineKeys): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/require/apply/draft/lines/remove`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 保存草稿
   *
   * @param body 草稿对象
   * @param tenant 租户标识
   */
  static saveDraft(body: AppRequireApplyDraftDTO): Promise<BaseResponse<AppRequireApplyDraftSaveResultDTO>> {
    return fly.post(`sos/v1/{tenant}/require/apply/draft/save`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 保存草稿行
   *
   * @param body 草稿行对象
   * @param tenant 租户标识
   */
  static saveDraftLine(body: AppRequireApplyDraftLineDTO): Promise<BaseResponse<DraftLineID>> {
    return fly.post(`sos/v1/{tenant}/require/apply/draft/line/save`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 单据和商品模糊搜索
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static search(body: QueryRequest): Promise<BaseResponse<RequireApplyGdSearchResult[]>> {
    return fly.post(`sos/v1/{tenant}/require/apply/search`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 提交单据
   *
   * @param body 编辑对象
   * @param tenant 租户标识
   */
  static submit(body: RequireApplyModification): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/require/apply/submit`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询单据总数
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static sum(body: QueryRequest): Promise<BaseResponse<RequireApplySummary>> {
    return fly.post(`sos/v1/{tenant}/require/apply/sum`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 重试提交接口
   *
   * @param body 编辑对象
   * @param tenant 租户标识
   */
  static retrySubmit(body: ID): Promise<BaseResponse<RequireApplySubmitResponse>> {
    return fly.post(`sos/v2/{tenant}/require/apply/retrysubmit`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 重新生成吱口令
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static regenZiCommand(body: ID): Promise<BaseResponse<string>> {
    return fly.post(`sos/v1/{tenant}/require/apply/zicommand/regen`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 撤回
   *
   * @param body 要货申请单标识
   * @param tenant 租户标识
   */
  static withdraw(body: ID): Promise<BaseResponse<RequireApplyWithdrawResult>> {
    return fly.post(`sos/v1/{tenant}/require/apply/withdraw`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取首页模板
   *
   * @param tenant 租户标识
   */
  static getHomePageTemplate(): Promise<BaseResponse<CMSPageTemplate>> {
    return fly.post(`sos/v1/{tenant}/require/apply/home/<USER>/template`, {}, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取内容模板
   *
   * @param tenant 租户标识
   * @param templateId 模板标识
   */
  static getContentTemplate(templateId: string): Promise<BaseResponse<CMSContentTemplate>> {
    return fly.post(`sos/v1/{tenant}/require/apply/home/<USER>/template/${templateId}`, {}, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询未提货完成的预订货订单
   *
   * @param tenant 租户标识
   */
  static getUnpickBill(): Promise<BaseResponse<PreReqOrdUnpickInfo>> {
    return fly.get(`sos/v1/{tenant}/require/apply/unpickbill/get`, {}).then((res) => {
      return res
    })
  }

  /**
   * 提交前校验
   *
   * @param body 要货申请单据标识
   * @param tenant 租户标识
   */
  static validateBeforeSubmit(body: RequireApplyBeforeSubmitValidator): Promise<BaseResponse<RequireApplyBeforeSubmitValResult>> {
    return fly.post(`sos/v1/{tenant}/require/apply/beforeSubmit/validate`, body, {}).then((res) => {
      return res
    })
  }
}
