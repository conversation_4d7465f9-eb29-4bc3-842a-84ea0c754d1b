<template>
  <view class="record-box-detail-card" :style="customStyle" @click.stop="box.type === 'split' ? handleClick() : ''">
    <view class="record-box-detail-card-header">
      <template v-if="box.type === 'split'">
        <text class="record-box-detail-card-header-tag">周转箱</text>
        <text>{{ box.boxNo }}</text>
        <image class="record-box-detail-card-header-arrow" :src="'/static/icon/ic_noticebar_right_grey.png' | oss" mode="aspectFill"></image>
      </template>
      <template v-if="box.type === 'whole'">
        <text class="record-box-detail-card-header-disp" v-if="box.boxGoodss[0].goods.isDisp">散称</text>

        <text class="record-box-detail-card-header-text">{{ box.boxGoodss[0].goods.name }}</text>
      </template>
    </view>
    <view class="record-box-detail-card-main">
      <block v-if="box.type === 'split'">
        <view class="record-box-detail-card-main-count">共包含{{ box.boxGoodss.length }}种商品</view>
      </block>
      <block v-if="box.type === 'whole'">
        <view class="record-box-detail-card-main-exhibit" v-if="showMaster.showDisplayLocation">
          <view :class="[hasMutiple ? 'goods-one' : '']">
            陈列位置：
            <text class="goods-text">{{ box.boxGoodss[0].displayLocation | empty }}</text>
          </view>
          <image class="good-img" :src="'/static/icon/ic_right_grey.png' | oss" v-if="hasMutiple" @click="viewExhibit" />
        </view>
        <view class="record-box-detail-card-main-half">箱码：{{ box.boxNo }}</view>
        <view class="record-box-detail-card-main-half">代码：{{ box.boxGoodss[0].goods.code }}</view>
        <view class="record-box-detail-card-main-half">价格：￥{{ box.boxGoodss[0].goods.price }}/{{ box.boxGoodss[0].goods.munit }}</view>
        <view class="record-box-detail-card-main-half">规格：{{ box.boxGoodss[0].goods.qpcStr }}/{{ box.boxGoodss[0].goods.munit }}</view>

        <template v-if="box.boxGoodss[0].goods.isDisp">
          <view class="record-box-detail-card-main-disp" v-if="doubleMeasureGoodsEnterQpcQty">
            <view class="record-box-detail-card-main-disp-txt">件数</view>
            <view class="record-box-detail-card-main-disp-number">
              <text>{{ box.boxGoodss[0].receiptQpcQty | empty }}{{ box.boxGoodss[0].goods.munit }}</text>
            </view>
          </view>
          <view class="record-box-detail-card-main-disp">
            <view class="record-box-detail-card-main-disp-txt">重量</view>
            <view class="record-box-detail-card-main-disp-number">
              <text>{{ box.boxGoodss[0].receiptQty | empty }}{{ box.boxGoodss[0].goods.minMunit }}</text>
            </view>
          </view>
        </template>
        <view class="record-box-detail-card-main-operation" v-else>
          <text>本次收</text>
          <view class="record-box-detail-card-main-operation-right">{{ box.receiptQty }}箱</view>
        </view>
      </block>
    </view>
  </view>
</template>

<script lang="ts" src="./RecordBoxDetailCard.ts"></script>

<style lang="scss" scoped>
.record-box-detail-card {
  position: relative;
  width: 100%;
  background: #ffffff;
  box-sizing: border-box;
  padding: 24rpx;
  border-radius: 16rpx;

  .record-box-detail-card-header {
    display: flex;
    align-items: center;
    width: 100%;
    height: 40rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 16rpx;

    &-gift {
      flex: 0 0 auto;
      display: inline-flex;
      align-items: center;
      padding: 0 10rpx;
      box-sizing: border-box;
      margin-right: 8rpx;
      width: 68rpx;
      height: 40rpx;
      background: linear-gradient(313deg, #ff9a00 0%, #ffd05b 100%);
      border-radius: 8rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #ffffff;
    }

    &-disp {
      flex: 0 0 auto;
      display: inline-flex;
      align-items: center;
      padding: 0 10rpx;
      box-sizing: border-box;
      margin-right: 8rpx;
      width: 68rpx;
      height: 40rpx;
      background: #e9f0ff;
      border-radius: 8rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #1c64fd;
    }

    &-text {
      flex: 1 1 auto;
      @include ellipsis();
    }

    &-arrow,
    &-img {
      position: relative;
      flex: 0 0 auto;
      width: 32rpx;
      height: 32rpx;
      margin-left: 8rpx;
    }
    &-img {
      &-tip {
        position: absolute;
        z-index: 11;
        right: -24rpx;
        top: -60rpx;
        display: inline-flex;
        align-items: center;
        padding: 0 12rpx;
        height: 44rpx;
        background: #000000;
        border-radius: 8rpx;
        font-size: 20rpx;
        color: #ffffff;
        white-space: nowrap;
        // 顶点朝下的底边宽20rpx 高12rpx的等边三角形
        &::after {
          content: '';
          position: absolute;
          z-index: 10;
          right: 24rpx;
          bottom: -12rpx;
          width: 0;
          height: 0;
          border-left: 12rpx solid transparent;
          border-right: 12rpx solid transparent;
          border-top: 12rpx solid #000000;
        }
      }
    }

    &-tag {
      flex: 0 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 88rpx;
      height: 36rpx;
      background: rgba(8, 176, 124, 0.08);
      border-radius: 4rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #08b07c;
      margin-right: 12rpx;
    }
  }

  .record-box-detail-card-main {
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
    &-count {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 32rpx;
    }

    &-half {
      width: 50%;
      flex: 0 0 auto;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      &:not(:last-child) {
        margin-bottom: 16rpx;
      }
    }

    &-exhibit {
      width: 100%;
      background: #f5f5f5;
      border-radius: 8rpx;
      padding: 8rpx 16rpx;
      box-sizing: border-box;
      flex: 0 0 auto;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .good-img {
        width: 32rpx;
        height: 32rpx;
        display: inline-table;
      }

      .goods-text {
        color: #333333;
        font-size: 26rpx;
        font-weight: 500;
      }

      .goods-one {
        flex: 1;
        @include ellipsis();
      }
    }

    &-half--important {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
    }

    &-operation {
      width: 100%;
      flex: 0 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 24rpx;
      color: #333333;

      &:not(:last-child) {
        margin-bottom: 16rpx;
      }

      &--end {
        justify-content: flex-end;
      }

      &-left {
        flex: 1 1 auto;
        @include ellipsis();
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        display: flex;
        align-items: center;
        &-icon {
          width: 40rpx;
          height: 40rpx;
        }
      }

      &-right {
        flex: 0 0 auto;
        display: flex;
        justify-content: flex-end;
        font-size: 24rpx;
        color: #333333;
      }

      &-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &-label {
          font-size: 26rpx;
          color: #666666;
        }
      }
    }

    .record-box-detail-card-main-disp {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 48rpx;
      box-sizing: border-box;
      margin-bottom: 16rpx;
      .record-box-detail-card-main-disp-number {
        display: flex;
        justify-content: flex-end;
        font-size: 24rpx;
        color: #666666;
      }
      .record-box-detail-card-main-disp-txt {
        height: 48rpx;
        max-width: 386rpx;
        line-height: 48rpx;
        font-size: 24rpx;
        color: #585a5e;
        @include ellipsis();
      }
      .record-box-detail-card-main-disp-arrow {
        height: 32rpx;
        width: 32rpx;
      }
    }
  }

  &:not(:last-child) {
    margin-bottom: 16rpx;
  }
}
</style>
