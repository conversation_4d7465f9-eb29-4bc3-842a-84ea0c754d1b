/*
 * @Author: 刘湘
 * @Date: 2021-05-06 16:22:21
 * @LastEditTime: 2025-03-18 14:02:09
 * @LastEditors: hanwei
 * @Description:
 * @FilePath: \soa\src\pagesUtil\receiptRecord\cmp\EditCardDialog.ts
 * 记得注释
 */
import { Vue, Component } from 'vue-property-decorator'
import CommonUtil from '@/utils/CommonUtil'
import ModuleOption from '@/model/default/ModuleOption'
import { Getter, State } from 'vuex-class'
import { ModuleId } from '@/model/common/OptionListModuleId'
import AppReceiptRecordLineDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordLineDTO'
import DateUtil from '@/utils/DateUtil'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'

// eslint-disable-next-line @typescript-eslint/ban-ts-ignore
// @ts-ignore
@Component({
  components: {},
  options: {
    virtualHost: true
  }
})
export default class EditCardDialog extends Vue {
  @State('optionList') optionList: ModuleOption[]
  @Getter('qtyScale') qtyScale: number

  $refs: any
  sku: AppReceiptRecordLineDTO = new AppReceiptRecordLineDTO() // 商品信息

  wholeQty: number = 0 // 件数 对应商品的qpcQty
  splitQty: number = 0 // 重量 对应商品的qty

  //直配收货是否录入生产日期和到效期
  get directFillPrddateAndValidate() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.directFillPrddateAndValidate == '1'
    }
    return false
  }

  // 散称双计量是否需要录入件数（包装数）
  get doubleMeasureGoodsEnterQpcQty() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.doubleMeasureGoodsEnterQpcQty == '1'
    }
    return false
  }

  //获取字段展示权限
  get showMaster() {
    const masterPermission = {
      showDisplayLocation: false // 陈列位置
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDISPLAYLOCATION)) {
      masterPermission.showDisplayLocation = true
    }
    return masterPermission
  }

  /**
   * 整件
   */
  get wholeMax() {
    if (this.sku) {
      return this.sku.isDisp ? Number(this.sku.qpcQty) : Math.floor((this.sku.qty - this.splitQty) / this.sku.goods.qpc)
    } else {
      return 9999999
    }
  }

  /**
   * 单品
   */
  get splitMax() {
    if (this.sku) {
      return this.sku.isDisp ? this.sku.qty : (this.sku.qty - this.wholeQty * this.sku.goods.qpc).scale(this.qtyScale)
    } else {
      return 9999999
    }
  }

  /**
   * 是否有多个陈列位置
   */
  get hasMutiple() {
    return this.sku && this.sku.displayLocation && this.sku.displayLocation.split(',').length > 1
  }

  open(sku: AppReceiptRecordLineDTO) {
    this.sku = CommonUtil.deepClone(sku)
    if (this.sku.isDisp) {
      const qtyArr = (this.sku.receiptQpcQty || '').split('+')
      if (qtyArr && qtyArr.length > 1) {
        this.wholeQty = (Number(this.sku.receiptQty) / this.sku.goods.qpc).scale(this.qtyScale)
      } else {
        this.wholeQty = Number(this.sku.receiptQpcQty)
      }
      this.splitQty = this.sku.receiptQty
    } else {
      const qtyArr = (this.sku.receiptQpcQty || '').split('+')
      if (qtyArr && qtyArr.length > 1) {
        this.wholeQty = Number(qtyArr[0])
        this.splitQty = Number(qtyArr[1])
      } else if (this.sku.receiptQpcQty) {
        this.wholeQty = Number(this.sku.receiptQpcQty)
        this.splitQty = 0
      } else {
        this.wholeQty = Math.floor(this.sku.qty / this.sku.goods.qpc)
        this.splitQty = (this.sku.qty - Number(this.wholeQty) * this.sku.goods.qpc).scale(this.qtyScale)
      }
    }
    this.$refs.edit.open()
  }

  close() {
    this.$refs.edit.close()
    setTimeout(() => {
      this.sku = new AppReceiptRecordLineDTO()
      this.wholeQty = 0
      this.splitQty = 0
    }, 300)
  }

  // 时间选择
  // 开始日期选择点击事件
  doStartClick() {
    const currentDate: string = this.sku.mfgDate ? this.sku.mfgDate : DateUtil.format(new Date(), 'yyyy-MM-dd')
    this.$showPicker({
      currentDate: currentDate,
      startDate: '2010-10-11',
      success: (res) => {
        if (res.date) {
          if (this.doTimeValidate(this.sku.expDate, res.date)) {
            return this.$showToast({ icon: 'error', title: '到效日期不能早于生产日期～' })
          }
          this.sku.mfgDate = res.date
          if (this.sku.validPeriod) {
            this.sku.expDate = this.doDateChange(this.sku.mfgDate, this.sku.validPeriod)
          }
        }
      }
    })
  }
  // 结束日期选择点击事件
  doEndClick() {
    const currentDate: string = this.sku.expDate ? this.sku.expDate : DateUtil.format(new Date(), 'yyyy-MM-dd')
    this.$showPicker({
      currentDate: currentDate,
      startDate: '2010-10-11',
      success: (res) => {
        if (res.date) {
          if (this.doTimeValidate(res.date, this.sku.mfgDate)) {
            return this.$showToast({ icon: 'error', title: '到效日期不能早于生产日期～' })
          }
          this.sku.expDate = res.date
          if (this.sku.validPeriod) {
            this.sku.mfgDate = this.doDateChange(this.sku.expDate, 0 - this.sku.validPeriod)
          }
        }
      }
    })
  }

  //日期计算
  doDateChange(date, num) {
    date = date.replace(/-/g, '/') //IOS不支持解析YYYY-MM格式

    date = new Date(date).getTime() / 1000 //转换为时间戳
    date += 86400 * num //修改后的时间戳
    const newDate = new Date(parseInt(date) * 1000) //转换为时间
    return newDate.getFullYear() + '-' + (newDate.getMonth() + 1) + '-' + newDate.getDate()
  }

  // 检验时间，到效日期不满足时返回true
  doTimeValidate(date1, date2) {
    if (!date1 || !date2) {
      return false
    }
    date1 = date1.replace(/-/g, '/')
    date2 = date2.replace(/-/g, '/')
    const oDate1 = new Date(date1)
    const oDate2 = new Date(date2)
    if (oDate1.getTime() >= oDate2.getTime()) {
      return false
    } else {
      return true
    }
  }

  /**
   * 取消
   */
  handleCancel() {
    this.$emit('cancel')
    this.close()
  }

  handleConfirm() {
    const sku: AppReceiptRecordLineDTO = CommonUtil.deepClone(this.sku)

    if (sku.isDisp) {
      sku.receiptQpcQty = this.doubleMeasureGoodsEnterQpcQty ? `${this.wholeQty}` : `${sku.qpcQty}`
      sku.receiptQty = this.splitQty
    } else {
      sku.receiptQpcQty = `${this.wholeQty}+${this.splitQty}`
      sku.receiptQty = Number(this.wholeQty) * this.sku.goods.qpc + Number(this.splitQty)
    }

    this.$emit('confirm', sku)
    this.close()
  }

  handleClick() {
    this.$emit('click', this.sku)
  }

  handleWholeQtyChange(qty: number) {
    this.wholeQty = qty
    this.splitQty = Math.min(this.splitMax, (this.wholeQty * this.sku.goods.qpc).scale(this.qtyScale))
  }

  handleSplitQtyChange(qty: number) {
    if (this.sku.goods.qpcStr === '1*1' && this.wholeQty !== qty) {
      this.wholeQty = qty
    }
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit() {
    this.$emit('viewExhibit', this.sku)
  }
}
