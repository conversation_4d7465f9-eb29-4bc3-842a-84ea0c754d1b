import Category from 'model/default/Category'
import Sort from 'model/default/Sort'
import rand from 'model/default/rand'
import BaseGoodsQueryDTO from './BaseGoodsQueryDTO'

export default class GoodsExtDTO extends BaseGoodsQueryDTO {
  // 规格价
  price: number = 0
  // 单价
  singlePrice: number = 0
  // 门店管理生产日期
  storeUseMfg: number = 0
  // 门店管理到效期
  storeUseExp: number = 0
  // 一级面板分类
  firstCategory: Nullable<Category> = null
  // 二级面板分类
  secondCategory: Nullable<Category> = null
  // 类别
  sort: Nullable<Sort> = null
  // 品牌
  brand: Nullable<rand> = null
  // 是否新品
  isNewList: Nullable<boolean> = null
  // 是否散货
  isDisp: Nullable<boolean> = null
  // 推介类型
  recommandType: Nullable<string> = null
  // 规格零售价
  rtlPrc: Nullable<number> = null
  // 图片url外部可访问的地址列表
  images: string[] = []
}
