<template>
  <uni-popup type="center" ref="edit" :maskClick="false">
    <view class="box-edit-card-dialog" v-if="box.boxGoodss[0]">
      <view class="header">
        <text class="header-gift" v-if="box.boxGoodss[0].aggGiftQty">含赠</text>
        <text>{{ box.boxGoodss[0].goods.name | empty }}</text>
      </view>
      <view class="main">
        <view class="main-exhibit" v-if="showMaster.showDisplayLocation">
          <view :class="[hasMutiple ? 'goods-one' : '']">
            陈列位置：
            <text class="goods-text">{{ box.boxGoodss[0].displayLocation | empty }}</text>
          </view>
          <image class="good-img" :src="'/static/icon/ic_right_grey.png' | oss" v-if="hasMutiple" @click="viewExhibit" />
        </view>
        <view class="main-half">箱码：{{ box.boxNo | empty }}</view>
        <view class="main-half">代码：{{ box.boxGoodss[0].goods.code | empty }}</view>
        <view class="main-half">价格：￥{{ box.boxGoodss[0].goods.price }}/{{ box.boxGoodss[0].goods.munit }}</view>
        <view class="main-half">规格：{{ box.boxGoodss[0].goods.qpcStr }}/{{ box.boxGoodss[0].goods.munit }}</view>
        <view class="exhibit" v-if="slotSource && isShowExhibitBtn">
          <view class="exhibit-top">
            <view class="exhibit-top-left">
              <text class="exhibit-top-left-text">绑定陈列位置</text>
            </view>
            <view class="exhibit-top-right" v-if="allowOneGoodsMultipleSlot" @click="resetExhibit">陈列位置调整</view>
            <!-- <view class="exhibit-top-right" @click="bindExhibit">手动选择</view> -->
          </view>
          <view class="exhibit-input">
            <input class="exhibit-input-content" v-model="exhibitValue" placeholder="输入陈列位置编号 例：A-1-1-1" @blur="getGoodsInfo" />
          </view>
          <view class="exhibit-scan" @click="doScanExhibit">
            <image class="exhibit-scan-img" :src="'/static/icon/ic_scan_blue.png' | oss" />
            <text class="exhibit-scan-text">扫码识别</text>
          </view>
        </view>

        <view class="main-total">
          <view class="main-total-qty">
            应收：
            <text v-if="!wholeBoxGoodsReceiptByMinQpc" class="main-total-value">{{ box.shipQty | empty }}箱</text>
            <text v-if="wholeBoxGoodsReceiptByMinQpc" class="main-total-value">
              {{ box.boxGoodss[0].shipQty }}{{ box.boxGoodss[0].goods.minMunit | empty }}
            </text>
          </view>
          <view class="main-total-qty">
            待收：
            <text v-if="!wholeBoxGoodsReceiptByMinQpc" class="main-total-value">{{ box.qty | empty }}箱</text>
            <text v-if="wholeBoxGoodsReceiptByMinQpc" class="main-total-value">
              {{ box.boxGoodss[0].qty }}{{ box.boxGoodss[0].goods.minMunit | empty }}
            </text>
          </view>
        </view>
        <view class="main-qty" v-if="!box.boxGoodss[0].goods.isDisp">
          <text>实收</text>
          <hd-number-box-test v-if="!wholeBoxGoodsReceiptByMinQpc" v-model="box.receiptQty" :scale="0" :max="box.qty"></hd-number-box-test>
          <hd-number-box-test
            v-if="wholeBoxGoodsReceiptByMinQpc"
            v-model="box.boxGoodss[0].receiptQty"
            @change="handleMinQpcQtyChange"
            :scale="qtyScale"
            :max="Number(box.boxGoodss[0].qty)"
          ></hd-number-box-test>
        </view>
        <template v-else>
          <view class="main-qty" v-if="doubleMeasureGoodsEnterQpcQty" style="margin-bottom: 16rpx">
            <text>件数</text>
            <hd-number-box-test
              v-model="box.boxGoodss[0].receiptQpcQty"
              @change="handleWholeQtyChange"
              :scale="qtyScale"
              :max="Number(box.boxGoodss[0].qpcQty)"
            ></hd-number-box-test>
          </view>
          <view class="main-qty">
            <text>重量{{ box.boxGoodss[0].goods.minMunit ? `(${box.boxGoodss[0].goods.minMunit})` : '' }}</text>
            <hd-number-box-test
              v-model="box.boxGoodss[0].receiptQty"
              @change="handleSplitQtyChange"
              :scale="qtyScale"
              :max="Number(box.boxGoodss[0].qty)"
            ></hd-number-box-test>
          </view>
        </template>
      </view>

      <view class="footer">
        <view class="footer-btn" @click="handleCancel">取消</view>
        <view class="footer-btn footer-btn--confirm" @click="handleConfirm">确定修改</view>
      </view>
    </view>
  </uni-popup>
</template>

<script lang="ts" src="./BoxEditCardDialog.ts"></script>

<style lang="scss" scoped>
.box-edit-card-dialog {
  position: relative;
  box-sizing: border-box;
  width: 574rpx;
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;

  .tip {
    width: 100%;
    box-sizing: border-box;
    padding: 20rpx 32rpx;
    background: rgba(253, 155, 28, 0.21);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 26rpx;
    color: #fd9b1c;
  }

  .header {
    display: flex;
    align-items: center;
    width: 100%;
    font-family: PingFangSC, PingFang SC;
    font-weight: 550;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 16rpx;
    padding: 32rpx 32rpx 0 32rpx;
    box-sizing: border-box;

    &-gift {
      flex: 0 0 auto;
      display: inline-flex;
      align-items: center;
      padding: 0 10rpx;
      box-sizing: border-box;
      margin-right: 8rpx;
      width: 68rpx;
      height: 40rpx;
      background: linear-gradient(313deg, #ff9a00 0%, #ffd05b 100%);
      border-radius: 8rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #ffffff;
    }
  }

  .main {
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
    box-sizing: border-box;
    padding: 0 32rpx 40rpx 32rpx;
    &-exhibit {
      width: 100%;
      background: #f5f5f5;
      border-radius: 8rpx;
      padding: 8rpx 16rpx;
      box-sizing: border-box;
      flex: 0 0 auto;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;

      .good-img {
        width: 32rpx;
        height: 32rpx;
        display: inline-table;
      }

      .goods-text {
        color: #333333;
        font-size: 26rpx;
        font-weight: 500;
      }

      .goods-one {
        flex: 1;
        @include ellipsis();
      }
    }
    &-count {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 32rpx;
    }

    &-half {
      width: 50%;
      flex: 0 0 auto;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      display: flex;
      align-items: center;
      &:not(:last-child) {
        margin-bottom: 16rpx;
      }
    }

    .good-location {
      max-width: 285rpx;
      @include ellipsis();
    }

    .good-img {
      width: 32rpx;
      height: 32rpx;
    }

    .goods_one {
      @include ellipsis();
    }

    &-total {
      width: 100%;
      height: 80rpx;
      background: #f5f6f7;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      padding: 0 24rpx;
      box-sizing: border-box;
      margin-bottom: 20rpx;

      &-qty {
        margin-right: 32rpx;
      }
      &-value {
        color: #333333;
      }
    }

    &-qty {
      width: 100%;
      display: flex;
      justify-content: space-between;
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
    }
  }

  .exhibit {
    width: 100%;
    margin-bottom: 16rpx;
    @include flex(column);

    &-top {
      width: 100%;
      margin-bottom: 16rpx;
      @include flex(row, space-between, center);

      &-left {
        @include flex(row, flex-start, center);

        &-text {
          height: 32rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          line-height: 32rpx;
          text-align: left;
          font-style: normal;
        }

        &-img {
          width: 32rpx;
          height: 32rpx;
        }
      }

      &-right {
        height: 32rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #1c64fd;
        line-height: 32rpx;
        text-align: center;
        font-style: normal;
      }
    }

    &-input {
      width: 100%;
      height: 72rpx;
      background: #f5f5f5;
      border-radius: 8rpx;
      // border: 1rpx solid #cccccc;
      padding: 0 16rpx;
      box-sizing: border-box;
      @include flex(row, space-between, center);

      &-content {
        width: 342rpx;
        height: 40rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
      }
    }

    &-scan {
      width: 100%;
      height: 72rpx;
      background: #e9f0ff;
      border-radius: 8rpx;
      margin-top: 12rpx;
      @include flex(row);

      &-img {
        width: 36rpx;
        height: 36rpx;
        margin-right: 12rpx;
      }

      &-text {
        width: 104rpx;
        height: 32rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26rpx;
        color: #1c64fd;
        line-height: 32rpx;
        text-align: left;
        font-style: normal;
      }
    }
  }

  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100rpx;
    background: #ffffff;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 2rpx;
      transform: scaleY(0.5);
      background: #e5e5e5;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      width: 2rpx;
      height: 100rpx;
      background: #e5e5e5;
      transform: translateX(-50%) scaleX(0.5);
    }

    &-btn {
      flex: 0 0 auto;
      width: 50%;
      height: 100rpx;
      font-family: PingFangSC, PingFang SC;
      font-size: 36rpx;
      font-weight: 400;
      font-size: 36rpx;
      color: #666666;
      display: flex;
      justify-content: center;
      align-items: center;

      &--confirm {
        color: #1c64fd;
      }
    }
  }
}
</style>
