/*
 * @Author: weisheng
 * @Date: 2024-09-26 16:19:34
 * @LastEditTime: 2024-12-25 15:02:43
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: \soa\src\pagesUtil\receiptRecord\RecordBoxMore.ts
 * 记得注释
 */
import { Component } from 'vue-property-decorator'
import swipeMix from '@/components/hd-swipe-action/swipeMix'
import { mixins } from 'vue-class-component'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import BoxEditSplitCard from './cmp/BoxEditSplitCard.vue'
import AppReceiptBoxGoodsDTO from '@/model/receipt/AppReceiptBoxGoodsDTO'
import UseQueues from '@/components/hd-popover/UseQueues'
import AppReceiptRecordApi from '@/network/AppReceiptRecordApi/AppReceiptRecordApi'
import QueryRequest from '@/model/base/QueryRequest'
import AppReceiptRecordBoxDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxDTO'
import AppReceiptRecordBoxGoodsDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxGoodsDTO'
import SkuRecordDialog from '@/pages/cmp/SkuRecordDialog.vue'
import AppReceiptMultipleRcvRecordQueryer from '@/model/receipt/AppReceiptMultipleRcvRecordQueryer'
import ReceiptApi from '@/network/receipt/ReceiptApi'
import AppReceiptMultipleRcvGdRecordDTO from '@/model/receipt/AppReceiptMultipleRcvGdRecordDTO'

@Component({
  components: { BoxEditSplitCard, SkuRecordDialog }
})
export default class RecordBoxMore extends mixins(swipeMix, BroadCast, UseQueues) {
  $refs: any
  keyWord: string = '' // 搜索关键字
  boxNo: string = '' // 箱码
  uuid: string = '' // 箱码uuid
  billId: string = '' // 单据id
  box: AppReceiptRecordBoxDTO = new AppReceiptRecordBoxDTO() // 箱数据
  from: 'search' | 'edit' = 'edit' // 来源页面
  receiptBillId: string = '' // 收货单单号

  /**
   * 商品种数
   */
  get count() {
    if (this.box && this.box.boxGoodss) {
      return this.box.boxGoodss.length
    } else {
      return 0
    }
  }

  /**
   * 差异金额
   */
  get diffTotal() {
    let diffTotal: number = 0
    if (this.box && this.box.boxGoodss) {
      this.box.boxGoodss.forEach((sku) => {
        diffTotal = diffTotal.add(Number(sku.total)).minus(Number(sku.receiptTotal))
      })
    }

    return diffTotal
  }

  /**
   * 展示的商品列表
   */
  get showList() {
    if (this.box && this.box.boxGoodss) {
      return this.box.boxGoodss.filter((sku) => {
        return (
          !this.keyWord ||
          sku.goods.name.indexOf(this.keyWord) > -1 ||
          sku.goods.inputCode.indexOf(this.keyWord) > -1 ||
          sku.goods.code.indexOf(this.keyWord) > -1 ||
          (sku.goods.inputCodes && sku.goods.inputCodes.indexOf(this.keyWord) > -1)
        )
      })
    } else {
      return []
    }
  }

  async onLoad(option) {
    if (option) {
      this.boxNo = option.boxNo || ''
      this.uuid = option.uuid || ''
      this.billId = option.id || ''
      this.from = option.from || 'edit'
      this.receiptBillId = option.receiptBillId || ''
      try {
        this.$showLoading()
        this.box = await this.getBox(this.uuid)
        this.$hideLoading()
      } catch (error) {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: error.msg })
      }
    }
  }

  /**
   * 关闭所有气泡
   */
  doCloseOutside() {
    if (this.queueObj && this.queueObj.closeOutside) {
      this.queueObj.closeOutside()
    }
  }

  /**
   * 商品变化
   * @param sku 商品
   */
  handleSkuChange(sku: AppReceiptBoxGoodsDTO) {
    const index = this.box.boxGoodss.findIndex((item) => {
      return item.uuid === sku.uuid
    })
    if (index > -1) {
      this.$set(this.box.boxGoodss, index, sku)
    }
  }

  // 搜索框清空事件
  doClear() {
    this.keyWord = ''
  }

  // 搜索框搜索事件
  doSearch(keyWord: string = '') {
    if (keyWord.trim() === '') {
      this.$nextTick(() => {
        uni.pageScrollTo({
          scrollTop: 0
        })
      })
    } else {
      this.doScanAfter(keyWord)
    }
  }

  handleEditReason(sku: AppReceiptBoxGoodsDTO) {
    this.$refs.reason.open(sku)
  }

  /**
   * 晚点再收
   */
  doBack() {
    this.$Router.back(1)
  }

  /**
   * 获取指定的箱
   * @param body
   * @returns
   */
  getBox(uuid: string) {
    const body = new QueryRequest()
    body.page = 0
    body.pageSize = 1
    body.conditions = [{ operation: 'uuid:=', parameters: [uuid] }]
    return new Promise<AppReceiptRecordBoxDTO>((resolve, reject) => {
      AppReceiptRecordApi.queryBox(body)
        .then((resp) => {
          if (resp.data[0]) {
            resolve(resp.data[0])
          } else {
            reject({ msg: `未找到指定箱${uuid}` })
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查看商品行收货记录
   * @param sku
   */
  async handleViewDetail(sku: AppReceiptRecordBoxGoodsDTO) {
    try {
      this.$showLoading()
      const recordList = await this.listMultipleRcvRecord({
        billId: this.receiptBillId,
        gdInputCode: sku.goods.inputCode
      })
      this.$hideLoading()

      if (recordList.length === 0) {
        this.$showToast({
          title: '暂无收货记录'
        })
      } else {
        this.$refs.recordDialog.open(recordList)
      }
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 查询商品行多次收货记录
   * @param body
   * @returns
   */
  listMultipleRcvRecord(body: AppReceiptMultipleRcvRecordQueryer) {
    return new Promise<AppReceiptMultipleRcvGdRecordDTO[]>((resolve, reject) => {
      ReceiptApi.listMultipleRcvRecord(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  //PDA扫码回调事件
  async doScanAfter(scanWord) {
    this.keyWord = scanWord
    this.$nextTick(() => {
      uni.pageScrollTo({
        scrollTop: 0
      })
    })
  }
}
