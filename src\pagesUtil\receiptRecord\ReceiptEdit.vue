<template>
  <view class="content receipt-edit">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <hd-date-picker></hd-date-picker>
    <view class="header">
      <view class="header-num" v-if="isExpress">
        <image :src="'/static/icon/ic_odd_numbers.png' | oss" class="header-num-img"></image>
        <text class="header-num-txt">单号：{{ receipt.num }}</text>
      </view>

      <view class="header-count" v-if="isExpress">
        待收共
        <text>{{ receipt.qty }}</text>
        件
      </view>

      <view class="header-search">
        <view class="header-search-body" @click="doSearch">
          <image class="header-search-img" :src="'/static/icon/ic_search.png' | oss"></image>
          <text class="header-search-txt">商品名称/代码/条码</text>
        </view>
        <view class="header-search-scan" @click.stop="doScan">
          <image class="header-search-img" :src="'/static/icon/ic_saoma_green.png' | oss"></image>
          <text class="header-search-scan-txt">扫一扫</text>
        </view>
      </view>
    </view>
    <view class="tool">
      <view class="tool-switch">
        <view @click="handleShowUnconfirmed" :class="['tool-switch-option', !showConfirmed ? 'tool-switch-option--active' : '']">
          待收{{ unConfirmLineCnt || '' }}
        </view>
        <view @click="handleShowConfirmed" :class="['tool-switch-option', showConfirmed ? 'tool-switch-option--active' : '']">
          已确认{{ confirmedLineCnt || '' }}
        </view>
        <view class="tool-switch-cachet" :style="!showConfirmed ? 'transform: translateX(0)' : 'transform: translateX(182rpx)'"></view>
      </view>
      <view class="tool-filter" v-if="showConfirmed">
        <view class="tool-filter-sort" @click="handleViewMine">
          <hd-radio :checked="viewMine" :preventClick="true" :clearable="false"></hd-radio>
          <text class="tool-filter-sort-txt">仅看自己</text>
        </view>
      </view>
    </view>

    <view v-if="skuList.length > 0" class="receipt-body">
      <template v-if="showConfirmed">
        <hd-swipe-action
          v-for="(item, index) in skuList"
          :key="index"
          @onDelete="handleCancel(item)"
          :index="index"
          :swipe-able="item.confirmed"
          :moveIndex="moveIndex"
          @updateIndex="onUpdateIndex"
          operatorText="取消收货"
        >
          <receipt-edit-card
            :sku="item"
            :showPrice="showPrice"
            :trust="trust"
            :showConfirmed="showConfirmed"
            @confirm="handleBeforeConfirm"
            @detail="handleViewDetail"
            @edit="handleEdit"
            @over="handelOver"
            @getRequestBody="getRequestBody"
            @bindExhibit="(info) => bindExhibit(info, index)"
            @resetExhibit="(info) => resetExhibit(info, index)"
            @viewExhibit="viewExhibit"
          ></receipt-edit-card>
        </hd-swipe-action>
      </template>
      <template v-else>
        <receipt-edit-card
          v-for="(item, index) in skuList"
          :key="index"
          :sku="item"
          :showPrice="showPrice"
          :trust="trust"
          :showConfirmed="showConfirmed"
          @confirm="handleBeforeConfirm"
          @detail="handleViewDetail"
          @edit="handleEdit"
          @over="handelOver"
          @getRequestBody="getRequestBody"
          @bindExhibit="(info) => bindExhibit(info, index)"
          @resetExhibit="(info) => resetExhibit(info, index)"
          @viewExhibit="viewExhibit"
        ></receipt-edit-card>
      </template>

      <view class="loading" v-if="!finished && isLoading">
        <view class="load-more"></view>
        <text>拼命加载中~~~</text>
      </view>
    </view>
    <hd-empty v-else :img="'/static/img/img_empty_goods.png' | oss" title="暂无相关商品 ヽ(・×・ )"></hd-empty>

    <check-pagination
      ref="checkpick"
      v-if="showPagination"
      :each-page="eachPage"
      :cur-page="currentPage"
      @page-change="handlePageChange"
      :totalNum="total"
    ></check-pagination>

    <view class="receipt-footer">
      <template v-if="!showConfirmed">
        <hd-button :type="btnPermission.finish && !isExpress ? 'white' : 'primary'" v-if="btnPermission.save && !trust" @click="handlePreFullCheck">
          一键确认
        </hd-button>
        <hd-button type="primary" v-if="btnPermission.finish && !isExpress" @click="handlePreFinish">结束收货</hd-button>
      </template>
      <template v-else>
        <hd-button @click="handleSave" :type="btnPermission.submit ? 'white' : 'primary'" v-if="!isExpress && !trust">保存</hd-button>
        <hd-button type="primary" v-if="btnPermission.submit" @click="doSubmit">提交</hd-button>
      </template>
    </view>

    <sku-record-dialog ref="recordDialog"></sku-record-dialog>
    <edit-card-dialog ref="edit" @confirm="handleConfirm" @viewExhibit="viewExhibit"></edit-card-dialog>

    <!-- 选择陈列位置 -->
    <select-exhibit ref="exhibit" @addExhibit="addExhibit" @success="confirmExhibit"></select-exhibit>

    <!-- 陈列位置调整 -->
    <reset-exhibit ref="resetExhibit" @addExhibit="addExhibit" @success="confirmExhibit"></reset-exhibit>

    <!-- 页码选择 -->
    <page-picker ref="pagepick" :totalPage="totalPage" :page="currentPage" @confirm="handlePagePickerConfirm"></page-picker>

    <!-- 查看多个陈列位置 -->
    <uni-popup ref="viewExhibit" type="bottom">
      <view-exhibit :name="viewExhibitInfo.goods.name" :displayLocation="viewExhibitInfo.displayLocation" @doClose="closeViewExhibit"></view-exhibit>
    </uni-popup>

    <!-- 重复收货提醒 -->
    <duplicate-receipt-reminder ref="reminder" @confirm="submit"></duplicate-receipt-reminder>

    <!-- 上传附件 -->
    <uni-popup type="bottom" ref="feedback" :maskClick="false">
      <adjust-feedback :hiddenVideo="false" @doClose="doCloseFeedback" @doSubmit="doSubmitFeedback"></adjust-feedback>
    </uni-popup>
  </view>
</template>

<script lang="ts" src="./ReceiptEdit.ts"></script>

<style lang="scss" scoped>
.receipt-edit {
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: 100rpx !important;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 100rpx) !important;
  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx) !important;

  .header {
    width: 100%;
    box-sizing: border-box;
    background: #ffffff;
    padding: 0 24rpx;
    &-num {
      display: flex;
      align-items: center;
      width: 100%;
      height: 44rpx;
      align-items: center;
      font-size: 32rpx;
      color: $color-text-secondary;
      margin-bottom: 24rpx;

      &-img {
        width: 44rpx;
        height: 44rpx;
      }

      &-txt {
        margin-left: 4rpx;
      }
    }

    &-count {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 32rpx;
      text {
        color: #333333;
        display: inline-block;
        margin: 0 4rpx;
      }
    }

    &-search {
      width: 100%;
      height: 120rpx;
      background: #ffffff;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-body {
        flex: 0 0 auto;
        width: 510rpx;
        height: 72rpx;
        background: #ffffff;
        border-radius: 36rpx;
        border: 2rpx solid #e9eefb;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding: 0 20rpx;
      }
      &-scan {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        min-width: 172rpx;
        height: 72rpx;
        border-radius: 46rpx;
        border: 1rpx solid #1c64fd;
        box-sizing: border-box;
        padding: 0 20rpx;
        &-txt {
          font-size: 28rpx;
          color: #1c64fd;
        }
      }
      &-img {
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
      }

      &-txt {
        font-size: 26rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #cccccc;
      }
    }
    ::v-deep .hd-search-bar {
      padding-bottom: 0 0 24rpx 0;
    }
  }

  .tool {
    position: sticky;
    z-index: 10;
    top: -1rpx;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 18rpx 24rpx;
    background: #f5f6f7;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-switch {
      position: relative;
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 384rpx;
      height: 60rpx;
      background: #ffffff;
      border-radius: 8rpx;

      &-option {
        position: relative;
        height: 60rpx;
        width: 50%;
        box-sizing: border-box;
        z-index: 2;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        color: $color-text-thirdly;
        display: flex;
        align-items: center;
        justify-content: center;
        &--active {
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: $color-white;
        }
      }

      &-cachet {
        transition-timing-function: ease;
        transition-duration: 300ms;
        position: absolute;
        z-index: 1;
        left: 8rpx;
        top: 8rpx;
        width: 186rpx;
        height: 48rpx;
        background: #1c64fd;
        border-radius: 8rpx;
      }
    }

    &-filter {
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 24rpx;
      box-sizing: border-box;

      &-sort {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        font-family: PingFangSC;
        color: $color-text-thirdly;
        &-txt {
          margin-left: 8rpx;
        }
      }
    }
  }

  .receipt-body {
    background: #ffffff;
    .loading {
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: rgb(148, 150, 154);
    }
  }

  .receipt-footer {
    width: 100vw;
    position: fixed;
    bottom: 0;
    display: flex;
    padding-bottom: 0 !important;
    padding-bottom: constant(safe-area-inset-bottom) !important;
    padding-bottom: env(safe-area-inset-bottom) !important;
    background: #ffffff;
    z-index: 2;
  }
}
</style>
