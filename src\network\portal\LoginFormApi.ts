import ResponseVo from '@/model/portalModel/commons/ResponseVo'
import LoginFormVo from '@/model/portalModel/login/LoginFormVo'
// import ApiClient from '../flyPortal'
import ApiClient from '../flyGeneral'
import CredentialVo from '@/model/portalModel/login/CredentialVo'
import LoginResultVo from '@/model/portalModel/login/LoginResultVo'
import TokenLoginRequestVo from '@/model/portalModel/login/TokenLoginRequestVo'
import store from '@/store'

export default class LoginFormApi {
  /**
   * 加载登录表单。
   * 加载登录表单。
   *
   */
  static load(language?: string): Promise<ResponseVo<LoginFormVo>> {
    return ApiClient.get(
      `/v1/login-form/load`,
      {
        terminalType: 'MOBILE',
        tenant: store.state.tenantInfo.id
      },
      {
        headers: {
          language: language
        }
      }
    ).then((res) => {
      return res
    })
  }

  /**
   * 登录。
   * 登录。
   *
   */
  static login(body: CredentialVo[], timestamp?: number, token?: string, language?: string): Promise<ResponseVo<LoginResultVo>> {
    return ApiClient.post(`/v1/login-form/login`, body, {
      params: {
        terminalType: 'MOBILE',
        tenant: store.state.tenantInfo.id,
        timestamp: timestamp,
        token: token
      },
      headers: {
        language: language
      }
    }).then((res) => {
      return res
    })
  }

  /**
   * 通过OAuth登录。
   * 通过OAuth登录。
   *
   */
  static loginWithOAuth(
    oauthCorpAccount: string,
    oauthIdentifyingCode: string,
    timestamp?: number,
    token?: string,
    language?: string
  ): Promise<ResponseVo<LoginResultVo>> {
    return ApiClient.post(
      `/v1/login-form/login-with-oauth`,
      {
        terminalType: 'MOBILE',
        tenant: store.state.tenantInfo.id,
        oauthCorpAccount: oauthCorpAccount,
        oauthIdentifyingCode: oauthIdentifyingCode,
        timestamp: timestamp,
        token: token
      },
      {
        headers: {
          language: language
        }
      }
    ).then((res) => {
      return res
    })
  }

  /**
   * 通过手机短信验证码登录。
   * 通过手机短信验证码登录。
   *
   */
  static loginWithSmsCode(body: CredentialVo[], timestamp?: number, token?: string, language?: string): Promise<ResponseVo<LoginResultVo>> {
    return ApiClient.post(`/v1/login-form/login-with-sms-code`, body, {
      params: {
        terminalType: 'MOBILE',
        tenant: store.state.tenantInfo.id,
        timestamp: timestamp,
        token: token
      },
      headers: {
        language: language
      }
    }).then((res) => {
      return res
    })
  }

  /**
   * 通过令牌登录。
   * 通过令牌登录。
   *
   */
  static loginWithToken(body: TokenLoginRequestVo, language?: string): Promise<ResponseVo<LoginResultVo>> {
    return ApiClient.post(`/v1/login-form/login-with-token`, body, {
      headers: {
        language: language
      }
    }).then((res) => {
      return res
    })
  }
}
