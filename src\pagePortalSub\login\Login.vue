<template>
  <view class="content login">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <view class="login-main">
      <view class="login-layout">
        <image :src="'/static/img/img_logo.png' | oss" class="login-title-img"></image>
        <text class="login-title">海鼎零售</text>
      </view>
      <view class="login-subtitle">让商业更有效率</view>
      <view class="login-body">
        <view class="login-main-tabs">
          <view class="login-main-tabs-account" v-if="showTab('account')" @click="onTab('account')">
            <view class="login-main-tabs-mobile-txt" :style="{ 'font-weight': selectedTab('account') ? '800' : '400' }">账号登录</view>
            <view class="login-main-tabs-mobile-line" v-if="selectedTab('account')"></view>
          </view>
          <view class="login-main-tabs-mobile" v-if="showTab('mobile')" @click="onTab('mobile')">
            <view class="login-main-tabs-mobile-txt" :style="{ 'font-weight': selectedTab('mobile') ? '800' : '400' }">手机号登录</view>
            <view class="login-main-tabs-mobile-line" v-if="selectedTab('mobile')"></view>
          </view>
        </view>

        <hd-field
          class="input"
          v-if="selectedTab('account')"
          v-model="inputElement('LOGIN_NAME').inputText"
          :placeholder="inputElement('LOGIN_NAME').placeHolder"
          @confirm="next('LOGIN_NAME')"
          :maxlength="32"
          hideTitle
          clearable
        ></hd-field>

        <hd-field
          class="input"
          v-if="selectedTab('account') && inputElement('PASSWORD').placeHolder"
          v-model="inputElement('PASSWORD').inputText"
          :placeholder="inputElement('PASSWORD').placeHolder"
          @confirm="next('PASSWORD')"
          ref="pwd"
          :pwd="32"
          hideTitle
          clearable
          password
        ></hd-field>
        <view class="login-main-captcha" v-if="selectedTab('account') && inputElement('CAPTCHA').placeHolder">
          <hd-field
            class="login-main-captcha-input"
            v-if="selectedTab('account') && inputElement('CAPTCHA').placeHolder"
            v-model="inputElement('CAPTCHA').inputText"
            :placeholder="inputElement('CAPTCHA').placeHolder"
            @confirm="next('CAPTCHA')"
            ref="captcha"
            :maxlength="10"
            hideTitle
            clearable
          ></hd-field>
          <view class="login-main-captcha-code" @click="refreshCaptcha">
            <image class="login-main-captcha-code-image" :src="inputElement('CAPTCHA').imageDataUrl" />
            <text class="login-main-captcha-code-refresh">点击切换</text>
          </view>
        </view>

        <hd-field
          class="input"
          v-if="selectedTab('mobile')"
          v-model="inputElement('MOBILE').inputText"
          :placeholder="inputElement('MOBILE').placeHolder"
          @confirm="next('MOBILE')"
          :maxlength="11"
          hideTitle
          clearable
        ></hd-field>
        <view class="login-main-smsCode" v-if="selectedTab('mobile')">
          <hd-field
            class="login-main-smsCode-input"
            v-model="inputElement('SMSCODE').inputText"
            :placeholder="inputElement('SMSCODE').placeHolder"
            @confirm="next('SMSCODE')"
            ref="smsCode"
            :maxlength="10"
            hideTitle
            clearable
          ></hd-field>
          <view class="login-main-smsCode-split"></view>
          <text
            class="login-main-smsCode-send"
            @click="snedSms"
            :style="{
              color: enableSendSms ? '#1C64FD' : '#8E8E8E',
              'font-weight': enableSendSms ? '800' : '400'
            }"
          >
            {{ smsSendHint }}
          </text>
        </view>
        <view class="login-main-btn" :style="{ opacity: disabled ? '0.19' : '1' }" @click="login()">
          <text class="login-main-btn-txt">确认</text>
        </view>
      </view>
    </view>
    <view class="login-empty"></view>
    <view class="login-footer">
      <view class="login-bindinfo" @click="doBindinfoView" v-if="active">
        <image :src="'/static/icon/ic_binding.png' | oss" class="login-bindinfo-icon"></image>
        绑定信息
      </view>

      <view class="login-layout">
        <image :src="'/static/icon/ic_login_logo.png' | oss" class="login-footer-icon"></image>
        <text>海鼎零售 {{ version }}</text>
      </view>
      <view>海鼎提供技术支持</view>
    </view>
  </view>
</template>

<script lang="ts" src="./Login.ts"></script>

<style lang="scss" scoped>
.login {
  background-color: $color-white;
  height: 100vh;
  overflow: hidden;
  .login-layout {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .login-main {
    padding: 112rpx 75rpx 0 75rpx;
    position: relative;
    .login-title {
      height: 64rpx;
      font-size: 48rpx;
      font-weight: 600;
      color: $color-text-secondary;
      line-height: 64rpx;
      letter-spacing: 8rpx;
    }
    .login-title-img {
      width: 92rpx;
      height: 92rpx;
      margin-right: 8rpx;
    }
    .login-subtitle {
      height: 40rpx;
      font-size: 28rpx;
      color: $color-text-thirdly;
      line-height: 40rpx;
      letter-spacing: 12rpx;
      text-align: center;
      margin: 8rpx 0 104rpx;
    }
    .login-body {
      position: relative;
      box-sizing: border-box;
      width: 600rpx;
      .login-password {
        position: relative;
        padding: 40rpx 0 104rpx;
        .login-password-forget {
          position: absolute;
          height: 40rpx;
          font-size: 28rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: $color-text-fourth;
          line-height: 40rpx;
          bottom: 32rpx;
          right: 0;
        }
      }
      .login-btn {
        height: 96rpx;
        line-height: 96rpx;
        background-color: $color-primary;
        font-size: 32rpx;
        font-weight: 500;
        color: $color-white;
        box-shadow: $box-shadow-btn;
        border-radius: 48rpx;
        outline: none;
        border: none;
      }
      .login-btn-disabled {
        opacity: 0.3;
      }
    }
    &-tabs {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      margin: 64rpx 0 30rpx 0;
      &-mobile {
        width: 180rpx;
        height: 48rpx;
        text-align: center;
        &-txt {
          width: 180rpx;
          height: 48rpx;
          font-size: 32rpx;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 800;
          color: #1f2324;
          line-height: 45rpx;
          margin-bottom: -18rpx;
        }
        &-line {
          width: 112rpx;
          height: 12rpx;
          background: #1c64fd;
          border-radius: 6rpx;
          margin: auto;
        }
      }
      &-account {
        width: 180rpx;
        height: 48rpx;
        text-align: center;
      }
    }

    &-smsCode {
      height: auto;
      width: 100%;
      margin-top: 32rpx;
      background: #ffffff;
      border-radius: 16rpx;
      border: 2rpx solid #e8e8e8;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-left: 17rpx;
      box-sizing: border-box;
      &-input {
        height: auto;
        flex: 1;
      }
      &-clear {
        width: 40rpx;
        height: 40rpx;
      }
      &-split {
        width: 1rpx;
        height: 40rpx;
        background: #c4c4c4;
        opacity: 0.6;
        margin-left: 12rpx;
      }
      &-send {
        width: 200rpx;
        height: 40rpx;
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8e8e8e;
        line-height: 40rpx;
        text-align: center;
      }
    }

    &-captcha {
      height: 140rpx;
      width: 100%;
      margin-top: 32rpx;
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      justify-content: flex-start;
      &-input {
        height: 102rpx;
        border-radius: 16rpx;
        border: 2rpx solid #e8e8e8;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-left: 41rpx;
        box-sizing: border-box;
        flex: 1;
      }
      &-code {
        width: 214rpx;
        height: 140rpx;
        margin-left: 24rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        &-image {
          width: 214rpx;
          height: 102rpx;
        }
        &-refresh {
          // width: 214rpx;
          height: 33rpx;
          font-size: 24rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #c4c4c4;
          line-height: 33rpx;
          margin-top: 4rpx;
        }
      }
    }

    &-btn {
      height: 102rpx;
      width: 100%;
      margin-top: 64rpx;
      background: #1c64fd;
      border-radius: 16rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      &-txt {
        font-size: 32rpx;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        text-align: center;
        color: #ffffff;
      }
    }
  }
  .input {
    height: 102rpx;
    margin-top: 32rpx;
    border-radius: 16rpx;
    border: 2rpx solid #e8e8e8;
    padding-left: 41rpx;
    flex: 1;
  }
  .login-empty {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    height: 35rpx;
  }
  .login-footer {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 300rpx;
    text-align: center;
    line-height: 28rpx;
    font-size: 20rpx;
    font-weight: 500;
    color: $color-text-fifth;

    .login-bindinfo {
      position: relative;
      margin: 0 auto;
      margin-bottom: 104rpx;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      width: 236rpx;
      height: 64rpx;
      box-sizing: border-box;
      padding: 12rpx 40rpx;
      border-radius: 46rpx;
      border: 1rpx solid #cccccc;
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $color-text-fourth;
      &-icon {
        width: 40rpx;
        height: 40rpx;
      }
    }
    .login-footer-icon {
      width: 22rpx;
      height: 22rpx;
      margin-right: 8rpx;
    }
  }
}
</style>
