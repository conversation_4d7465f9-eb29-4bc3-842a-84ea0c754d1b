import { Component } from 'vue-property-decorator'
import swipeMix from '@/components/hd-swipe-action/swipeMix'
import { mixins } from 'vue-class-component'
import HdCell from '@/components/hd-cell/hd-cell.vue'
import HdNote from '@/components/hd-note/hd-note.vue'
import HdButton from '@/components/hd-button/hd-button.vue'
import HdNumberBoxTest from '@/components/hd-number-box-test/hd-number-box-test.vue'
import HdSwipeAction from '@/components/hd-swipe-action/hd-swipe-action.vue'
import HdTabs from '@/components/hd-tabs/hd-tabs.vue'
import OperatorCell from './cmp/OperatorCell.vue'
import ProcessBillApi from '@/network/processBill/ProcessBillApi'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import { State, Mutation, Action } from 'vuex-class'
import ProcessBillCreation from '@/model/processBill/ProcessBillCreation'
import ProcessBillCreationLine from '@/model/processBill/ProcessBillCreationLine'
import { ProcessGoodsState } from '@/model/processBill/ProcessGoodsState'
import Ucn from '@/model/base/Ucn'
import PscpDetail from '@/model/processBill/PscpDetail'
import { Filter } from '@/pagesSC/processBill/cmd/Filter'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import config from '@/config'
import CommonUtil from '@/utils/CommonUtil'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import QueryRequest from '@/model/base/QueryRequest'
import ProcessBillGoods from '@/model/processBill/ProcessBillGoods'
import AbstractProcessBillLine from '@/model/processBill/AbstractProcessBillLine'

@Component({
  components: { HdCell, HdNote, HdButton, HdNumberBoxTest, HdSwipeAction, HdTabs, OperatorCell },
  filters: Filter
})
export default class ProcessBillEdit extends mixins(swipeMix, BroadCast) {
  @Mutation('goodsList') mutationGoodsList // 提交到vuex
  @Action('isGoods') actionIsGoods // 是否为商品存到vuex中
  @State('isGoods') isGoods: boolean // 是否为选择商品
  @State('optionList') optionList: ModuleOption[] //店务配置列表
  $refs: any
  requestBody: ProcessBillCreation = new ProcessBillCreation() // 创建单据的请求体
  isEdit: boolean = false // 是否为编辑
  tabList = [{ name: '成品' }, { name: '原料' }] // 选项卡选项
  current: number = 0 // 激活tab
  scaleArr: number[] = [] // 计算比例数组
  pdtsqty: number = 0 // 成品的数量
  pdtsLength: boolean = false // 初始成品数组的长度是否满足联动
  optionListModuleId = ModuleId //店务配置枚举
  isRenovote: boolean = false // 是否需要刷新

  // 是否可以新增成品或原料
  get clickable() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosProcessBill
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options) {
      return moduleConfig[0].options.validatePSCPGoodsScope === '0'
    }
    return false
  }

  // 商品总类别数
  get count() {
    let count: number = 0
    if (this.current === 0) {
      count = this.requestBody.pdts.length
    } else if (this.current === 1) {
      count = this.requestBody.raws.length
    }
    return Number(count).scale(4)
  }

  // 商品总金额
  get total() {
    let total: number = 0
    let dataList: ProcessBillCreationLine[] = []
    if (this.current === 0) {
      dataList = this.requestBody.pdts
    } else if (this.current === 1) {
      dataList = this.requestBody.raws
    }
    for (let index = 0; index < dataList.length; index++) {
      total = Number(total).add(dataList[index].total)
    }
    return Number(total).scale(2)
  }

  // 标题
  get type() {
    let type: string = ''
    if (this.current === 0) {
      type = ProcessGoodsState.product
    } else if (this.current === 1) {
      type = ProcessGoodsState.material
    }
    return type
  }

  // 按钮是否可以点击
  get btnClickable() {
    return this.validate()
  }

  // 获取按钮权限
  get btnPermission() {
    const btnPermission = {
      save: false,
      submit: false,
      globalPriceView: false
    }
    if (PermissionMgr.hasPermission(Permission.processBillEdit)) {
      btnPermission.save = true
    }
    if (PermissionMgr.hasPermission(Permission.processBillSubmit)) {
      btnPermission.submit = true
    }
    if (PermissionMgr.hasPermission(Permission.globalPriceView)) {
      btnPermission.globalPriceView = false
    }
    return btnPermission
  }

  /**
   * 是否联动
   */
  get linkageable() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == this.optionListModuleId.sosProcessBill
        })
      : []
    // 是否联动（0-否，保持联动，1-是，不联动）
    let isLink: boolean = false
    if (moduleConfig.length > 0 && moduleConfig[0].options) {
      isLink = moduleConfig[0].options.allowModPscpProportion === '1'
    }
    if (isLink) {
      return false
    }
    return !this.isEdit && !this.isGoods && this.pdtsLength
  }

  // 是否开启无配方加工
  get enableNoneFormule() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == this.optionListModuleId.sosProcessBill
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options) {
      return moduleConfig[0].options.enableNoneFormule === 'true' ? true : false
    }

    return true
  }

  // 商品图片
  get skuImg() {
    return (sku: ProcessBillCreationLine) => {
      return sku && sku.goodsImages && sku.goodsImages.length && CommonUtil.isImageUrl(sku.goodsImages[0])
        ? `${sku.goodsImages[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
        : `${config.sourceUrl}icon/pic_goods.png`
    }
  }

  get imageList() {
    return (sku: ProcessBillCreationLine) => {
      return sku && sku.goodsImages && sku.goodsImages.filter((item) => CommonUtil.isImageUrl(item)).length
        ? sku.goodsImages.filter((item) => CommonUtil.isImageUrl(item))
        : [`${config.sourceUrl}icon/pic_goods.png`]
    }
  }

  onShow() {
    const list: ProcessBillCreationLine[] = uni.getStorageSync('processGoodsList')
    if (list) {
      if (this.current === 0) {
        this.requestBody.pdts = list
      } else if (this.current === 1) {
        this.requestBody.raws = list
      }
      uni.removeStorage({ key: 'processGoodsList' })
    }
    const formulaInfo = uni.getStorageSync('formulaInfo')
    if (formulaInfo) {
      if (formulaInfo.formula.uuid) {
        if (formulaInfo.formula.uuid !== this.requestBody.pscpUuid || this.requestBody.multiple !== Number(formulaInfo.multiple)) {
          this.requestBody.multiple = Number(formulaInfo.multiple)
          this.requestBody.pscpCode = formulaInfo.formula.code
          this.requestBody.pscpName = formulaInfo.formula.name
          this.requestBody.pscpUuid = formulaInfo.formula.uuid
          if (this.requestBody.pscpUuid) {
            this.doGetPscp(this.requestBody.pscpUuid)
          }
        }
      } else if (this.requestBody.pscpUuid) {
        this.requestBody.pscpCode = null
        this.requestBody.pscpName = null
        this.requestBody.pscpUuid = null
        this.requestBody.pdts = []
        this.requestBody.raws = []
      }
      uni.removeStorage({ key: 'formulaInfo' })
    }
  }

  onLoad(option) {
    this.actionIsGoods(false)
    // 获取订单
    if (option && option.id) {
      if (option.isEdit && option.isEdit === 'true') {
        this.isEdit = true
      }
      this.requestBody.billId = option.id
      if (!option.create) {
        this.getrequestBody()
      }
    }
  }

  onUnload() {
    uni.$emit('PATH', this.isRenovote)
  }

  // 获取单据详情
  getrequestBody() {
    this.$showLoading()
    ProcessBillApi.get(this.requestBody.billId, 'details')
      .then((resp) => {
        this.$hideLoading()
        if (resp.data) {
          const data = resp.data
          this.requestBody = { ...data }
        }
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  /**
   * 获取指定配方定义
   * @param id 配方数据标识
   */
  doGetPscp(id: string) {
    this.$showLoading()
    ProcessBillApi.getPscp(id)
      .then((resp) => {
        this.$hideLoading()
        if (resp.data) {
          this.requestBody.raws = this.doExchangeModel(resp.data.raws, Number(this.requestBody.multiple) > 0 ? Number(this.requestBody.multiple) : 1)
          this.requestBody.pdts = this.doExchangeModel(resp.data.pdts, Number(this.requestBody.multiple) > 0 ? Number(this.requestBody.multiple) : 1)
          if (resp.data.pdts.length === 1) {
            this.pdtsLength = true
          } else {
            this.pdtsLength = false
          }
          this.doScale()
        }
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  /**
   * PscpDetail模型转换为ProcessBillCreationLine
   * @param before 待转换数组
   */
  doExchangeModel(before: PscpDetail[] = [], multiple: number) {
    const after: ProcessBillCreationLine[] = []
    for (let index = 0; index < before.length; index++) {
      const temp: ProcessBillCreationLine = new ProcessBillCreationLine()
      temp.goods = before[index].goods
      temp.goodsImages = before[index].goodsImages
      temp.total = Number(before[index].total).multiply(multiple)
      temp.qty = Number(before[index].qty).multiply(multiple)
      temp.qpcQty = (temp.qty / temp.goods.qpc).scale(4)
      if (before[index].firstCategory !== null) {
        temp.categoryCode = before[index].firstCategory!.code
        temp.categoryName = before[index].firstCategory!.name
        temp.categoryUuid = before[index].firstCategory!.uuid
      }
      after.push(temp)
    }
    return after
  }

  // tab点击事件
  doHandler(tab, index) {
    this.current = index
  }

  // 选择配方
  doSelectFormula() {
    let url = '/pagesSC/processBill/ProcessBillFormula'
    const formula: Ucn = new Ucn()
    formula.code = this.requestBody.pscpCode
    formula.uuid = this.requestBody.pscpUuid
    formula.name = this.requestBody.pscpName
    if (this.requestBody.pscpUuid) {
      url = `${url}?formula=${JSON.stringify(formula)}`
      if (this.requestBody.multiple) {
        url = `${url}&multiple=${this.requestBody.multiple}`
      } else {
        url = `${url}&multiple=1`
      }
      if (this.requestBody.raws.length > 0 || this.requestBody.pdts.length > 0) {
        url = `${url}&exist=true`
      }
    } else {
      if (this.requestBody.raws.length > 0 || this.requestBody.pdts.length > 0) {
        url = `${url}?exist=true`
      }
    }
    uni.navigateTo({ url: url })
  }

  // 保存按钮事件
  doSave() {
    if (this.validate()) {
      const request: any = this.isEdit ? ProcessBillApi.modify : ProcessBillApi.create
      this.$showLoading()
      const requestBody = this.doAssemble()
      request(requestBody)
        .then((resp) => {
          if (!this.isEdit) {
            this.isEdit = true
          }
          this.$hideLoading()
          this.$showToast({ icon: 'success', title: '保存成功' })
          this.isRenovote = true // 是否需要刷新
        })
        .catch((e) => {
          this.$hideLoading()
          this.$showModal({
            title: '保存失败',
            content: `失败原因:${e.msg}`,
            showCancel: true,
            confirmText: '重试',
            success: (action) => {
              if (action.confirm) {
                this.doSave()
              }
            }
          })
        })
    }
  }

  // 提交按钮事件
  doSubmit() {
    if (this.validate()) {
      this.$showModal({
        title: '提示',
        content: `确定提交加工申请吗`,
        showCancel: true,
        confirmText: '提交',
        cancelText: '取消',
        success: (action) => {
          if (action.confirm) {
            this.submit()
          }
        }
      })
    }
  }

  // 提交
  submit() {
    const request: any = this.isEdit ? ProcessBillApi.modifyAndSubmit : ProcessBillApi.createAndSubmit
    this.$showLoading()
    const requestBody = this.doAssemble()
    request(requestBody)
      .then(() => {
        this.$hideLoading()
        this.$showToast({ icon: 'success', title: '提交成功' })
        this.isRenovote = true // 是否需要刷新
        setTimeout(() => {
          uni.redirectTo({ url: '/pagesSC/processBill/ProcessBillDetail?id=' + this.requestBody.billId })
        }, 1500)
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showModal({
          title: '提交失败',
          content: `失败原因:${e.msg}`,
          showCancel: true,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.submit()
            }
          }
        })
      })
  }

  // 校验请求体
  validate() {
    if (
      this.requestBody.raws.length <= 0 ||
      this.requestBody.pdts.length <= 0 ||
      this.requestBody.raws.filter((line) => line.qty === 0).length > 0 ||
      this.requestBody.pdts.filter((line) => line.qty === 0).length > 0
    ) {
      return false
    }
    return true
  }

  /**
   * 组装请求体
   * @returns 请求体
   */
  doAssemble() {
    const request: ProcessBillCreation = this.requestBody
    for (let index = 0; index < request.pdts.length; index++) {
      const qpcQty: number = Number(request.pdts[index].qpcQty)

      request.pdts[index].qty = qpcQty.multiply(request.pdts[index].goods.qpc).scale(4)
      request.pdts[index].total = request.pdts[index].goods.price.multiply(qpcQty)
    }
    for (let index = 0; index < request.raws.length; index++) {
      const qpcQty: number = Number(request.raws[index].qpcQty)
      request.raws[index].qty = qpcQty.multiply(request.raws[index].goods.qpc).scale(4)
      request.raws[index].total = request.raws[index].goods.price.multiply(qpcQty)
    }
    return request
  }

  // 查询商品
  doSearchGoods() {
    let selectedList: ProcessBillCreationLine[] = []
    let title: string = '搜索成品'
    if (this.current === 0) {
      selectedList = this.requestBody.pdts
    } else if (this.current === 1) {
      title = '搜索原料'
      selectedList = this.requestBody.raws
    }
    uni.setStorage({
      key: 'processGoodsList',
      data: selectedList,
      success: () => {
        uni.navigateTo({ url: `/pagesSC/processBill/ProcessBillGoodSearch?from=edit&title=${title}` })
      },
      fail: (e) => {
        console.log(e)
      }
    })
  }

  // PDA扫码回调事件
  doScanAfter(scanWord) {
    if (scanWord) {
      this.doSearch(true, scanWord)
    }
  }

  // 搜索事件
  doSearch(showLoading: boolean = true, keyWord: string = '') {
    const query = new QueryRequest()
    query.page = 0
    query.pageSize = 20
    query.conditions = [{ operation: 'keyword:%=%', parameters: [keyWord] }]
    query.fetchParts = ['category', 'image']
    if (showLoading) {
      this.$showLoading({ delayTime: 200 })
    }
    ProcessBillApi.queryGoods(query)
      .then((resp) => {
        if (showLoading) {
          this.$hideLoading()
        }
        if (resp.data && resp.data.length === 1) {
          if (this.current === 0) {
            const index = this.requestBody.pdts.findIndex((item) => {
              return item.goods.inputCode === resp.data![0].inputCode && item.goods.uuid === resp.data![0].uuid
            })
            if (index > -1) {
              this.$set(this.requestBody.pdts[index], 'qpcQty', Number(this.requestBody.pdts[index].qpcQty) + 1)
            } else {
              this.$set(resp.data[0], 'qpcQty', 1)
              const item = this.doReturnModel(resp.data[0])
              this.requestBody.pdts.push(item)
            }
          } else if (this.current === 1) {
            const index = this.requestBody.raws.findIndex((item) => {
              return item.goods.inputCode === resp.data![0].inputCode && item.goods.uuid === resp.data![0].uuid
            })
            if (index > -1) {
              this.$set(this.requestBody.raws[index], 'qpcQty', Number(this.requestBody.raws[index].qpcQty) + 1)
            } else {
              this.$set(resp.data[0], 'qpcQty', 1)
              const item = this.doReturnModel(resp.data[0])
              this.requestBody.raws.push(item)
            }
          }
        }
        // 搜索查出多条跳转搜索页
        if (resp.data && resp.data.length > 1) {
          let selectedList: ProcessBillCreationLine[] = []
          let title: string = '搜索成品'
          if (this.current === 0) {
            selectedList = this.requestBody.pdts
          } else if (this.current === 1) {
            title = '搜索原料'
            selectedList = this.requestBody.raws
          }
          uni.setStorage({
            key: 'processGoodsList',
            data: selectedList,
            success: () => {
              uni.navigateTo({ url: `/pagesSC/processBill/ProcessBillGoodSearch?from=edit&title=${title}&value=${keyWord}` })
            },
            fail: (e) => {
              console.log(e)
            }
          })
        }
      })
      .catch((e) => {
        if (showLoading) {
          this.$hideLoading()
        }
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  /**
   * 模型还原
   * @param before 待还原模型
   */
  doReturnModel(before: ProcessBillGoods) {
    const temp: AbstractProcessBillLine = new AbstractProcessBillLine()
    temp.goods.code = before.code
    temp.goods.inputCode = before.inputCode
    temp.goods.minMunit = before.minMunit
    temp.goods.munit = before.munit
    temp.goods.name = before.name
    temp.goods.price = before.price
    temp.goods.qpc = before.qpc
    temp.goods.qpcStr = before.qpcStr
    temp.goods.singlePrice = before.singlePrice
    temp.goods.uuid = before.uuid
    temp.qpcQty = before.qpcQty
    temp.total = before.price.multiply(temp.qpcQty)
    temp.qty = temp.qpcQty.multiply(before.qpc).scale(4)
    temp.goodsImages = before.images
    if (before.firstCategory) {
      temp.categoryCode = before.firstCategory!.code || ''
      temp.categoryName = before.firstCategory!.name || ''
      temp.categoryUuid = before.firstCategory!.uuid || ''
    }
    return temp
  }

  // 删除商品
  doDelete(item: ProcessBillCreationLine) {
    if (this.current === 0) {
      this.requestBody.pdts = this.requestBody.pdts.filter((line) => {
        return line.goods.uuid !== item.goods.uuid || line.goods.inputCode !== item.goods.inputCode || line.goods.qpcStr !== item.goods.qpcStr
      })
    } else if (this.current === 1) {
      this.requestBody.raws = this.requestBody.raws.filter((line) => {
        return line.goods.uuid !== item.goods.uuid || line.goods.inputCode !== item.goods.inputCode || line.goods.qpcStr !== item.goods.qpcStr
      })
    }
  }

  // 显示备注弹出框
  doNoteShow() {
    this.$refs.note.open()
  }

  // 关闭备注弹出框
  doNoteClose() {
    this.$refs.note.close()
  }

  // 备注弹框确认事件
  doNoteConfirm(note: string) {
    this.requestBody.note = note
  }

  // 选择商品
  doSelectGoods() {
    let selectedList: ProcessBillCreationLine[] = []
    let title: string = '选择成品'
    if (this.current === 0) {
      selectedList = this.requestBody.pdts
    } else if (this.current === 1) {
      selectedList = this.requestBody.raws
      title = '选择原料'
    }
    uni.setStorage({
      key: 'processGoodsList',
      data: selectedList,
      success: () => {
        uni.navigateTo({ url: `/pagesSC/processBill/ProcessBillSkuSelect?title=${title}` })
      },
      fail: (e) => {
        console.log(e)
      }
    })
  }

  /**
   *
   * 计算成品原料的比例
   */
  doScale() {
    // 当成品只有一个时计算比例= 原料/成品(qpcQty)
    if (this.linkageable) {
      // 原料列表
      for (let j = 0; j < this.requestBody.raws.length; j++) {
        const item = this.requestBody.raws[j]
        if (item.qpcQty !== 0 && this.requestBody.pdts[0].qpcQty !== 0) {
          // 比例 = 每个原料的规格数量/第一个成品的规格数量
          const singleScale: number = item.qpcQty / this.requestBody.pdts[0].qpcQty
          this.scaleArr.push(singleScale)
        } else {
          this.scaleArr.push(0)
        }
      }
    }
  }

  /**
   * 步进器change事件
   * @param qpcQty 数量
   * @param index 对应商品下标
   */
  doNumberChange(qpcQty: number, index: number) {
    let dataList: ProcessBillCreationLine[] = []
    if (this.current === 0) {
      // 成品列表数据
      dataList = this.requestBody.pdts
      this.pdtsqty = qpcQty
      // 当不是编辑状态且不是选择商品时
      if (this.linkageable) {
        const raws: ProcessBillCreationLine[] = JSON.parse(JSON.stringify(this.requestBody.raws))
        for (let i = 0; i < raws.length; i++) {
          const scale: number = this.scaleArr[i]
          raws[i].qpcQty = this.pdtsqty * scale
          raws[i].qty = qpcQty.multiply(raws[i].goods.qpc).scale(4)
          raws[i].total = raws[i].goods.price.multiply(qpcQty)
        }
        this.requestBody.raws = raws
      }
    } else if (this.current === 1) {
      // 原料列表数据
      dataList = this.requestBody.raws
      if (qpcQty === 0) {
        this.actionIsGoods(true)
      }
    }
    dataList[index].qty = qpcQty.multiply(dataList[index].goods.qpc).scale(4)
    dataList[index].total = dataList[index].goods.price.multiply(qpcQty)
    if (qpcQty === 0) {
      this.$nextTick(() => {
        dataList.splice(index, 1)
        if (this.linkageable) {
          this.requestBody.raws = []
        }
      })
    } else {
      dataList[index].qty = qpcQty.multiply(dataList[index].goods.qpc).scale(4)
      dataList[index].total = dataList[index].goods.price.multiply(qpcQty)
    }
  }

  /**
   * 预览图片
   */
  handlePreviewImg(sku: ProcessBillCreationLine) {
    uni.previewImage({
      current: String(0),
      urls: this.imageList(sku)
    })
  }
}
