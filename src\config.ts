/*
 * @LastEditors: weisheng
 */
type Config = {
  [key: string]: any
  env: 'dev' | 'uat' | 'prod'
  source: string
  tenant: string
  appId: string
  baseUrl: string
  BIURL?: string // 报表地址
  AzkWebUrl?: string // 爱折扣软折平台地址
  AzkSJKBUrl?: string // 爱折扣数据看板地址
  orderGoodUrl?: string
  skipPermission?: boolean
  sourceUrl: string
  logBaseUrl: string // 日志地址
  orderSearchUrl?: string //订单查询服务端地址
  wxappid?: string
  //大数据绩效提升H5页面
  commissionUrl?: string
  UNI_APPID?: string
  // 订货投放logo
  homeLogo?: string
  // 订货投放标题
  homeTitle?: string
  portalBaseUrl?: string
  // 履约助手
  lyzsBff?: string // 履约助手bff地址
  lyzsPortalAppid?: string // 履约助手portalAppid
  // 门店助手
  soaBff?: string // 门店助手bff地址
  soaPortalAppid?: string // 门店助手portalAppid
  jyzsPortalAppid?: string // 经营助手portalAppid
  jyzsBff?: string // 经营助手bff地址
  jyzsH5Url?: string // 经营助手H5地址
}

const baseSourceUrl = 'https://download.qianfan123.com/soa-static/'
const devConfig: Config = {
  env: 'dev',
  tenant: process.env.VUE_APP_TENANT || 'mkhtest',
  source: 'static',
  appId: 'sopDingTalk',
  baseUrl: process.env.VUE_APP_BASEURL || 'https://baas-test.hd123.com/',
  sourceUrl: `${baseSourceUrl}static/`,
  logBaseUrl: process.env.VUE_APP_LOGBASEURL || 'https://track.qianfan123.com:9999/track/test/', // 日志地址
  BIURL: process.env.VUE_APP_BIURL || 'https://portal.hd123.com/report-mobile/test/index.html#/', // 报表地址
  wxappid: 'wx5637c5a78f58f352',
  orderSearchUrl: 'https://8180-baas-gateway-service-test.hd123.com/', //订单查询服务端地址
  commissionUrl:
    'https://dcproxy.qianfan123.com/webroot/decision/view/form?viewlet=Mobile%252FPerformance%252F%25E6%258F%2590%25E6%2588%2590%25E6%2596%25B9%25E6%25A1%2588.frm&op=h5',
  homeLogo: `${baseSourceUrl}static/logo/<EMAIL>`,
  homeTitle: '诚信志远',
  AzkWebUrl: 'https://btp-h5-daily.3songshu.com/#/?tenantId=600001',
  AzkSJKBUrl: 'https://sdc-ocs.3songshu.com/api/web/dataCenter/azk/getUrl',
  portalBaseUrl: 'https://baas-test.hd123.com',
  soaPortalAppid: 'sop',
  soaBff: 'https://baas-test.hd123.com/sop_bff',
  lyzsPortalAppid: 'oas-osp',
  lyzsBff: 'https://mas-test-api.hd123.com',
  jyzsPortalAppid: '经营助手',
  jyzsBff: 'https://dcproxy.qianfan123.com:38430',
  jyzsH5Url: 'https://portal.hd123.com/jyzs-h5/test/index.html#'
}

const config = devConfig
// #ifdef APP-PLUS
// #ifndef HD-UNI
config.appId = 'StoreHelperPda' // pda版本
// #endif
// #endif

export default config
