import { Component } from 'vue-property-decorator'
import swipeMix from '@/components/hd-swipe-action/swipeMix'
import ReceiptApi from '@/network/receipt/ReceiptApi'

import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import { mixins } from 'vue-class-component'
import QueryRequest from '@/model/base/QueryRequest'
import { UIStyleEnum } from '@/model/common/UIEmue'
import { Mutation, State, Getter } from 'vuex-class'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import ModuleFieldRights from '@/model/default/ModuleFieldRights'
import SysConfigItem from '@/model/sysConfig/SysConfigItem'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'
import AppReceiptRecordApi from '@/network/AppReceiptRecordApi/AppReceiptRecordApi'
import AppReceiptRecordDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordDTO'
import AppReceiptRecordLineDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordLineDTO'
import ReceiptEditCard from './cmp/ReceiptEditCard.vue'
import AppReceiptRecordLineCheckDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordLineCheckDTO'
import AppReceiptRecordFullCheckDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordFullCheckDTO'
import ID from '@/model/default/ID'
import AppReceiptMultipleRcvRecordQueryer from '@/model/receipt/AppReceiptMultipleRcvRecordQueryer'
import AppReceiptMultipleRcvGdRecordDTO from '@/model/receipt/AppReceiptMultipleRcvGdRecordDTO'
import EditCardDialog from './cmp/EditCardDialog.vue'
import AppReceiptMultipleRcvCloser from '@/model/receipt/AppReceiptMultipleRcvCloser'
import AppReceiptMultipleRcvCloseResult from '@/model/receipt/AppReceiptMultipleRcvCloseResult'
import AppReceiptRecordInitializer from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordInitializer'
import UserInfo from '@/model/user/UserInfo'
import AppReceiptDiffSumDTO from '@/model/receipt/AppReceiptDiffSumDTO'
import CommonUtil from '@/utils/CommonUtil'
import SkuRecordDialog from '@/pages/cmp/SkuRecordDialog.vue'
import SelectExhibit from '@/components/select-exhibit/SelectExhibit.vue'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import ResetExhibit from '@/components/select-exhibit/ResetExhibit.vue'
import Slot from '@/model/data/Slot'
import GoodsSlotBinder from '@/model/data/GoodsSlotBinder'
import Receipt from '@/model/receipt/Receipt'
import ReceiptApiV2 from '@/network/receipt/ReceiptApiV2'
import CheckPagination from '../cmp/CheckPagination.vue'
import PagePicker from '../cmp/PagePicker.vue'
import { runJob } from '@/common/Job'
import DuplicateReceiptReminder from './cmp/DuplicateReceiptReminder.vue'
import ReceiptRecordSubmitValidateResult from '@/model/AppReceiptRecord/default/ReceiptRecordSubmitValidateResult'
import AdjustFeedback from './cmp/AdjustFeedback.vue'
import AppReceiptRecordAttachCreateDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordAttachCreateDTO'
import { FileType } from '@/model/AppReceiptRecord/default/FileType'
import AppReceiptRecordSubmitterDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordSubmitterDTO'

enum ReceiptStatusEnum {
  Y = '已确认',
  N = '未确认'
}

@Component({
  components: {
    ReceiptEditCard,
    SkuRecordDialog,
    EditCardDialog,
    SelectExhibit,
    ViewExhibit,
    ResetExhibit,
    CheckPagination,
    PagePicker,
    DuplicateReceiptReminder,
    AdjustFeedback
  }
})
export default class ReceiptEdit extends mixins(swipeMix, BroadCast) {
  @State('userInfo') userInfo: UserInfo // 当前用户信息
  @Getter('qtyScale') qtyScale: number
  @State('sysConfig') sysConfig: SysConfigItem // 获取全局配置
  @State('optionList') optionList: ModuleOption[]
  @State('moduleFieldRightsList') moduleFieldRightsList: ModuleFieldRights[] // 获取模块字段权限
  @Mutation('moduleFieldRightsList') setModuleFieldRightsList // 提交模块字段权限
  $refs: any
  receipt: AppReceiptRecordDTO = new AppReceiptRecordDTO() // 创建单据的请求体
  skuList: AppReceiptRecordLineDTO[] = [] // 商品列表
  unConfirmLineCnt: Nullable<number> = null // 未清点行数
  confirmedLineCnt: Nullable<number> = null // 已清点行数
  // 分页相关
  pageSize: number = 10 // 每页大小
  pageNum: number = 0 // 页码
  finished: boolean = false // 是否加载完成
  isLoading: boolean = false // 是否在加载

  total: number = 0 // 总数
  eachPage: number = 100 // 每页条数（分页器分页每页条数）
  currentPage: number = 0 // 当前页码(分页器分页的页码)
  // 分页器全部页数
  get totalPage() {
    return Math.ceil(this.total / this.eachPage)
  }
  /**
   * 是否展示分页器
   * 当finished时显示
   * 或者当前列表数据大于等于每页条数时展示分页器
   */
  get showPagination() {
    return this.totalPage > 1 && (this.skuList.length >= this.eachPage || this.finished)
  }

  disabled: boolean = true // 是否禁用按钮
  ReceiptStatus = ReceiptStatusEnum
  styleEnum = UIStyleEnum // 样式枚举
  optionListModuleId = ModuleId
  wrhNote: string = '' //当前拣货备注
  note: string = '' //当前出货备注
  isRenovote: boolean = false // 列表页是否需要刷新
  billId: string = '' // 单据id
  showConfirmed: boolean = false // 是否展示已收
  viewMine: boolean = false // 仅看自己
  scaning: boolean = false // 是否扫码

  hasSelelcExhibit: Slot = new Slot() // 已选中的陈列位置
  viewExhibitInfo: AppReceiptRecordLineDTO = new AppReceiptRecordLineDTO() // 陈列位置弹窗数据
  exhibitIndex: number = 0 // 陈列位置下标

  tabList = [
    { name: '未确认', value: this.ReceiptStatus.N },
    { name: '已确认', value: this.ReceiptStatus.Y }
  ]

  // 是否快递单收货
  get isExpress() {
    return this.receipt.source == 'express'
  }

  /**
   * 提交加工方式：0-异步；1-同步
   */
  get submitProcessMode() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []

    if (moduleConfig.length > 0) {
      return moduleConfig.findIndex((item) => item.options && item.options.submitProcessMode === '1') > -1
    }
    return false
  }

  // 控制收货记录是否支持上传附件，0-否，1-是
  get uploadRecordAttach() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []

    if (moduleConfig.length > 0) {
      return moduleConfig.findIndex((item) => item.options && item.options.uploadRecordAttach === '1') > -1
    }
    return false
  }

  // 启用单品多人收货，0-否，1-是
  get enableSingleGoodsMulRcver() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []

    if (moduleConfig.length > 0) {
      return moduleConfig.findIndex((item) => item.options && item.options.enableSingleGoodsMulRcver === '1') > -1
    }
    return false
  }

  // 是否展示散称商品件数
  get showWeightSkuReceivedQty() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWWEIGHTSKURECEIVEDQTY)) {
      return true
    }
    return false
  }

  // 获取按钮权限
  get btnPermission() {
    const btnPermission = {
      submit: PermissionMgr.hasPermission(Permission.receiptSubmit),
      save: PermissionMgr.hasPermission(Permission.receiptOneClick),
      finish: PermissionMgr.hasPermission(Permission.receiptFinish)
    }
    return btnPermission
  }

  get showPrice() {
    //店务配置，是否显示配货价
    if (!this.showAlcPrice) {
      return false
    }
    if (PermissionMgr.hasPermission(Permission.receiptShowprice)) {
      return true
    }
    return false
  }

  // 是否信任模式
  get trust() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == this.optionListModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.receiptMode == 'trust'
    }
    return false
  }

  //提交按钮置灰

  get submitDisabled() {
    return this.disabled
  }

  /**
   * 是否展示配货价配货金额 - 默认展示
   */
  get showAlcPrice() {
    const moduleConfig = (this.moduleFieldRightsList || []).find((option: ModuleFieldRights) => option.moduleId == 'sosReceipt')
    if (moduleConfig && moduleConfig.fieldRights && moduleConfig.fieldRights.price) {
      return moduleConfig.fieldRights.price === '1'
    }
    return true
  }

  onLoad(option) {
    if (this.enableSingleGoodsMulRcver) {
      this.viewMine = true
    }
    if (option && option.id) {
      this.billId = option.id
      if (option.isExpress) {
        this.receipt.source = 'express'
      }
      this.$nextTick(async () => {
        await this.handleInit(option.id)
        if (option.keyword) {
          let keyword: string = ''
          try {
            keyword = decodeURIComponent(option.keyword)
          } catch (error) {
            keyword = option.keyWord
          }
          this.doScanAfter(keyword)
        }
        if (option.searchword) {
          let searchword: string = ''
          try {
            searchword = decodeURIComponent(option.searchword)
          } catch (error) {
            searchword = option.searchword
          }
          this.$Router.push({
            name: 'receiptRecordSkuSearch',
            params: {
              id: this.receipt.billId,
              receiptBillId: this.billId,
              searchword: searchword
            }
          })
        }
      })
    }

    // 卸载
    uni.$off('receipt-edit-query')
    // 重新监听
    uni.$on('receipt-edit-query', () => {
      this.doResetPage()
      this.loadSkuList()
    })

    // 卸载
    uni.$off('update-un-confirm-sku')
    // 重新监听
    uni.$on('update-un-confirm-sku', (res) => {
      this.isRenovote = true
      if (res) {
        this.unConfirmLineCnt = res.unConfirmLineCnt
        this.confirmedLineCnt = res.confirmedLineCnt
      }
    })
    uni.$off('refreshExhibit')
    uni.$on('refreshExhibit', () => {
      this.$refs.exhibit.refresh()
      this.$refs.resetExhibit.refresh()
    })
  }

  onUnload() {
    uni.$emit('PATH', this.isRenovote)
    // 卸载
    uni.$off('receipt-edit-query')
    uni.$off('refreshExhibit')
  }

  /**
   * 切换页码
   * @param curentPage
   */
  handlePageChange(curentPage: number) {
    this.currentPage = curentPage
    this.finished = false
    this.pageNum = ((curentPage - 1) * (this.eachPage / this.pageSize)).scale(0)
    this.skuList = []
    this.loadSkuList()
    this.$nextTick(() => {
      uni.pageScrollTo({
        scrollTop: 0
      })
    })
  }

  //分页器弹窗确认
  handlePagePickerConfirm(page: number) {
    this.$refs.checkpick.doConfirm(page)
  }

  async handleInit(billId: string) {
    try {
      this.$showLoading({ delayTime: 200 })
      const bill = await this.getBill(billId)
      if (bill && bill.state !== 'initial' && bill.state !== 'receiving') {
        this.$hideLoading()
        this.$showModal({
          title: '提示',
          content: '该单据已被处理',
          showCancel: false,
          confirmText: '我知道了',
          success: (action) => {
            if (action.confirm) {
              this.$Router.replace({
                name: 'receiptDetail',
                params: { id: billId }
              })
            }
          }
        })
        this.isRenovote = true
        return
      }
      this.receipt = await this.getReceipt(billId)
      this.unConfirmLineCnt = this.receipt.unConfirmLineCnt
      this.confirmedLineCnt = this.receipt.confirmedLineCnt
      if (this.receipt.recCnt === 0) {
        this.$hideLoading()
        this.$showModal({
          title: '提示',
          content: '该单据没有商品可以继续收货',
          confirmText: '结束收货',
          cancelText: '返回',
          success: async (action) => {
            if (action.confirm) {
              this.handleFinishDirect(billId)
            } else {
              this.$Router.back(1)
            }
          }
        })
        return
      }
      await this.queryLine()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: error.msg })
    }
  }

  /**
   * 直接关单
   */
  async handleFinishDirect(billId: string, force: boolean = false) {
    try {
      this.$showLoading()
      const result = await this.finish({
        billId: billId,
        confirmSubmitReceiptRecord: force
      })
      this.$hideLoading()
      // code为0时，表示结束收货成功
      if (result.code === '0') {
        this.isRenovote = true
        this.$showToast({ icon: 'success', title: '操作成功' })
        const timer = setTimeout(() => {
          clearTimeout(timer)
          this.$Router.replace({
            name: 'receiptDetail',
            params: {
              id: billId
            }
          })
        }, 1500)
      } else if (result.code === '1') {
        // code为1时，表示结束时存在其他未提交但是已清点的商品，需要用户确认
        this.$showModal({
          title: '提醒',
          content: `${result.errMsg}`,
          showCancel: true,
          confirmText: '继续结束',
          success: (action) => {
            if (action.confirm) {
              this.handleFinishDirect(billId, true)
            }
          }
        })
      } else {
        this.$showModal({
          title: '结束收货失败',
          content: `失败原因:${result.errMsg}`,
          showCancel: true,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.handleFinishDirect(billId)
            }
          }
        })
      }
    } catch (error) {
      this.$hideLoading()
      this.$showModal({
        title: '结束收货失败',
        content: `失败原因:${error.msg}`,
        showCancel: true,
        confirmText: '重试',
        cancelText: '返回',
        success: (action) => {
          if (action.confirm) {
            this.handleFinishDirect(billId)
          } else {
            this.$Router.back(1)
          }
        }
      })
    }
  }

  // 获取收货单单据详情
  getBill(billId: string) {
    return new Promise<Receipt>((resolve, reject) => {
      ReceiptApiV2.get(billId)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  // 获取单据详情
  getReceipt(billId: string) {
    return new Promise<AppReceiptRecordDTO>((resolve, reject) => {
      const params = new AppReceiptRecordInitializer()
      params.id = billId
      params.source = this.receipt.source
      AppReceiptRecordApi.getInitialByReceipt(params)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查询箱码列表
   * @param body
   * @returns
   */
  queryLine(isScan: boolean = false, keyWord?: string) {
    this.isLoading = true
    const body = new QueryRequest()
    body.page = this.pageNum
    body.pageSize = this.pageSize
    body.sorts = [
      { asc: false, field: 'type' },
      { asc: true, field: 'lineNo' }
    ]
    body.conditions = [{ operation: 'billId:=', parameters: [this.receipt.billId] }]
    if (this.showConfirmed) {
      body.sorts.push({ field: 'defSlotCode', asc: true })
      body.conditions.push({ operation: 'confirmed:=', parameters: ['true'] })
      if (this.viewMine) {
        body.conditions.push({ operation: 'consigneeId:=', parameters: [this.userInfo.loginId || ''] })
      }
    } else {
      body.conditions.push({ operation: 'confirmed:=', parameters: ['false'] })
    }

    if (keyWord) {
      body.conditions.push({ operation: 'keyword:%=%', parameters: [keyWord] })
    }
    if (isScan) {
      body.page = 0
      body.pageSize = 2
      body.conditions.push({ operation: 'isScan:=', parameters: ['true'] })
    }

    if (!isScan && !keyWord && body.page === 0) {
      body.fetchParts = ['lineTotal']
    }

    return new Promise<AppReceiptRecordLineDTO[]>((resolve, reject) => {
      AppReceiptRecordApi.queryLine(body)
        .then((resp) => {
          if (body.fetchParts.includes('lineTotal')) {
            this.total = resp.total || 0
          }
          if (!isScan) {
            this.pageNum++
            if (!resp.more) {
              this.finished = true
            }
            if (body.page === 0) {
              this.skuList = resp.data
            } else {
              this.skuList.push(...resp.data)
            }
          }
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
        .finally(() => {
          this.isLoading = false
        })
    })
  }

  /**
   * 商品行确认收货
   * @param sku
   */
  handleBeforeConfirm(sku: AppReceiptRecordLineDTO) {
    this.handleConfirm(sku)
  }

  /**
   * 商品行结束收货
   * @param sku
   */
  handelOver(sku: AppReceiptRecordLineDTO) {
    this.$showModal({
      title: '结束收货确认',
      content: '确认后该商品将按本次收货数量确认到【已确认】，后续无法对该商品继续收货',
      success: async (action) => {
        if (action.confirm) {
          this.handleSkuOver(sku)
        }
      }
    })
  }

  /**
   * 结束收货
   * @param sku
   */
  async handleSkuOver(sku: AppReceiptRecordLineDTO, confirm: boolean = true, finished: boolean = true) {
    try {
      this.$showLoading()
      const body: AppReceiptRecordLineCheckDTO = {
        // 单据标识
        billId: sku.billId,
        // 商品行标识
        lineUuid: sku.uuid!,
        // 商品GID
        gdUuid: sku.goods.uuid,
        // 收货数量
        receiptQty: sku.receiptQty,
        // 收货金额
        receiptTotal: Number(sku.receiptQty).multiply(sku.goods.price).divide(sku.goods.qpc).scale(2),
        // 收货规格数
        receiptQpcQty: sku.receiptQpcQty,
        // 实际收货数量
        realReceiptQty: sku.receiptQty,
        // 生产日期
        mfgDate: sku.mfgDate,
        // 到效期
        expDate: sku.expDate,
        // 是否已清点
        confirmed: confirm,
        // 是否已结束
        finished: finished
      }
      const summary = await this.modifySku(body)
      this.unConfirmLineCnt = summary.unConfirmLineCnt
      this.confirmedLineCnt = summary.confirmedLineCnt
      this.isRenovote = true
      this.$hideLoading()
      this.doResetPage()
      this.loadSkuList()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 查看商品行收货记录
   * @param sku
   */
  async handleViewDetail(sku: AppReceiptRecordLineDTO) {
    try {
      this.$showLoading()
      const recordList = await this.listMultipleRcvRecord({
        billId: this.billId,
        gdInputCode: sku.goods.inputCode,
        source: this.isExpress ? 'express' : 'receipt'
      })
      this.$hideLoading()

      if (recordList.length === 0) {
        this.$showToast({
          title: '暂无收货记录'
        })
      } else {
        this.$refs.recordDialog.open(recordList)
      }
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败'
      })
    }
  }

  handleEdit(sku: AppReceiptRecordLineDTO) {
    this.$refs.edit.open(sku)
  }

  /**
   * 查看未确认
   */
  handleShowUnconfirmed() {
    if (this.showConfirmed) {
      this.showConfirmed = false
      this.doResetPage()
      this.loadSkuList()
      this.onUpdateIndex(null)
    }
  }

  /**
   * 查看已确认
   */
  async handleShowConfirmed() {
    if (!this.showConfirmed) {
      this.showConfirmed = true
      this.doResetPage()
      this.loadSkuList()
    }
  }

  /**
   * 待收货的商品确认收货
   * @param box
   */
  async handleConfirm(sku: AppReceiptRecordLineDTO, confirm: boolean = true, isCancel: boolean = false) {
    try {
      this.$showLoading()
      const body: AppReceiptRecordLineCheckDTO = {
        // 单据标识
        billId: sku.billId,
        // 商品行标识
        lineUuid: sku.uuid!,
        // 商品GID
        gdUuid: sku.goods.uuid,
        // 收货数量
        receiptQty: sku.receiptQty,
        // 收货金额
        receiptTotal: Number(sku.receiptQty).multiply(sku.goods.price).divide(sku.goods.qpc).scale(2),
        // 收货规格数
        receiptQpcQty: sku.receiptQpcQty,
        // 实际收货数量
        realReceiptQty: sku.receiptQty,
        // 生产日期
        mfgDate: sku.mfgDate,
        // 到效期
        expDate: sku.expDate,
        // 是否已清点
        confirmed: confirm,
        // 是否已结束
        finished: isCancel ? false : sku.finished
      }
      const summary = await this.modifySku(body)
      this.unConfirmLineCnt = summary.unConfirmLineCnt
      this.confirmedLineCnt = summary.confirmedLineCnt
      this.isRenovote = true
      this.$hideLoading()
      this.doResetPage()
      this.loadSkuList()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 取消收货
   * @param sku 商品
   */
  handleCancel(sku: AppReceiptRecordLineDTO) {
    const target: AppReceiptRecordLineDTO = CommonUtil.deepClone(sku)
    this.handleConfirm(target, false, true)
  }

  /**
   * 商品行收货
   * @param body
   * @returns
   */
  modifySku(body: AppReceiptRecordLineCheckDTO) {
    return new Promise<AppReceiptRecordDTO>((resolve, reject) => {
      AppReceiptRecordApi.checkLine(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查询商品行多次收货记录
   * @param body
   * @returns
   */
  listMultipleRcvRecord(body: AppReceiptMultipleRcvRecordQueryer) {
    return new Promise<AppReceiptMultipleRcvGdRecordDTO[]>((resolve, reject) => {
      ReceiptApi.listMultipleRcvRecord(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 保存按钮事件 无实际逻辑，自动保存
   */
  handleSave() {
    this.$showToast({ icon: 'success', title: '保存成功' })
  }

  // 提交按钮事件
  doSubmit() {
    this.$showModal({
      title: this.isExpress ? '提交收货确认' : '提示',
      content: this.isExpress ? `提交后，将按本次收货数量确认到所属【收货单】，后续仍可对该快递单继续收货` : `确定提交收货吗`,
      showCancel: true,
      confirmText: this.uploadRecordAttach && this.receipt.datasource === 'direct' ? '上传附件' : '确定',
      cancelText: this.uploadRecordAttach && this.receipt.datasource === 'direct' ? '直接提交' : '取消',
      success: (action) => {
        if (action.cancel) {
          if (this.uploadRecordAttach && this.receipt.datasource === 'direct' && this.isExpress) {
            this.validateBeforeSubmit()
          } else if (this.uploadRecordAttach && this.receipt.datasource === 'direct' && !this.isExpress) {
            this.submit()
          }
        } else if (action.confirm) {
          // 开启上传附件配置
          if (this.uploadRecordAttach && this.receipt.datasource === 'direct') {
            // 打开上传附件弹窗
            this.$refs.feedback.open()
          } else {
            if (this.isExpress) {
              this.validateBeforeSubmit()
            } else {
              this.submit()
            }
          }
        }
      }
    })
  }

  // 提交
  async submit(note?: string) {
    try {
      if (this.submitProcessMode) {
        const success = await this.submitAndProcess(note) // 进行加工处理
        if (success) {
          this.isRenovote = true
          uni.$emit('PATH', true)
          this.$hideLoading()
          this.$showToast({ icon: 'success', title: '提交成功' })
          setTimeout(() => {
            this.$Router.replace({
              name: 'receiptRecordDetail',
              params: {
                id: this.receipt.billId,
                receiptBillId: this.billId
              }
            })
          }, 1500)
        }
      } else {
        this.$showLoading()
        const params = new AppReceiptRecordSubmitterDTO()
        params.billId = this.receipt.billId
        if (this.enableSingleGoodsMulRcver) {
          params.scope = this.viewMine ? '0' : '1'
        }
        if (note) params.note = note
        await this.submitReceipt(params)
        this.isRenovote = true
        uni.$emit('PATH', true)
        this.$hideLoading()
        this.$showToast({ icon: 'success', title: '提交成功' })
        setTimeout(() => {
          this.$Router.replace({
            name: 'receiptRecordDetail',
            params: {
              id: this.receipt.billId,
              receiptBillId: this.billId
            }
          })
        }, 1500)
      }
    } catch (error) {
      this.$hideLoading()
      this.$showModal({
        title: '提交失败',
        content: `失败原因:${error.msg}`,
        showCancel: true,
        confirmText: '重试',
        success: (action) => {
          if (action.confirm) {
            this.submit()
          }
        }
      })
    }
  }

  // 提交前校验
  async validateBeforeSubmit(note?: string) {
    const body = new ID()
    body.id = this.receipt.billId
    AppReceiptRecordApi.validate4Submit(body)
      .then((res) => {
        this.$hideLoading()
        const data: ReceiptRecordSubmitValidateResult = res.data
        if (data.code === '0') {
          this.submit(note)
        } else {
          this.$refs.reminder.open(data.lines)
        }
      })
      .catch((err) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: err.msg || '校验失败' })
      })
  }

  // 异步加工接口
  async submitAndProcess(note?: string) {
    return new Promise<boolean>((resolve, reject) => {
      const body = new AppReceiptRecordSubmitterDTO()
      body.billId = this.receipt.billId
      if (this.enableSingleGoodsMulRcver) {
        body.scope = this.viewMine ? '0' : '1'
      }
      if (note) body.note = note
      AppReceiptRecordApi.submitAndProcess(body)
        .then((res) => {
          if (res.data.state === 'executing') {
            runJob(
              this,
              res.data.id,
              () => {
                resolve(true)
              },
              (fail) => {
                resolve(false)
                this.$showToast({ icon: 'error', title: fail.message || '提交失败' })
              }
            )
          } else if (res.data.state === 'completed') {
            resolve(true)
          } else {
            resolve(false)
            this.$showToast({ icon: 'error', title: res.data.message || '提交失败' })
          }
        })
        .catch((err) => {
          resolve(false)
          this.$showToast({ icon: 'error', title: err.msg || '提交失败' })
        })
        .finally(() => {
          this.$hideLoading()
        })
    })
  }

  // 显示更多商品
  async onReachBottom() {
    if (this.finished || this.isLoading || this.skuList.length === this.eachPage) {
      return
    }
    try {
      this.$showLoading()
      await this.queryLine()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '查询失败',
        icon: 'error'
      })
    }
  }

  /**
   * 一键确认
   */
  handlePreFullCheck() {
    this.$showModal({
      title: '提示',
      content: '一键确认后，将对当前单据上的所有商品，按待收数确认到已确认页签',
      success: async (action) => {
        if (action.confirm) {
          try {
            this.$showLoading()
            const summary = await this.boxFullCheck({ billId: this.receipt.billId, lines: [] })
            this.unConfirmLineCnt = summary.unConfirmLineCnt
            this.confirmedLineCnt = summary.confirmedLineCnt
            this.isRenovote = true
            this.showConfirmed = true
            this.$hideLoading()
            this.doResetPage()
            this.loadSkuList()
          } catch (error) {
            this.$hideLoading()
            this.$showToast({ icon: 'error', title: error.msg })
          }
        }
      }
    })
  }

  /**
   * 一键确认
   * @param body
   * @returns
   */
  boxFullCheck(body: AppReceiptRecordFullCheckDTO) {
    return new Promise<AppReceiptRecordDTO>((resolve, reject) => {
      AppReceiptRecordApi.fullCheck(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  handlePreFinish() {
    this.$showModal({
      title: '提示',
      content: '确认关闭收货后，该单后续将无法继续收货，不影响已收货商品',
      success: async (action) => {
        if (action.confirm) {
          this.handleFinish()
        }
      }
    })
  }

  /**
   * 结束收货
   * 先submit
   * 再查询差异
   * 最后finish
   */
  async handleFinish(force: boolean = false) {
    try {
      this.$showLoading()
      if (this.submitProcessMode) {
        const success = await this.submitAndProcess() // 进行加工处理
        if (!success) return
      } else {
        const body = new AppReceiptRecordSubmitterDTO()
        body.billId = this.receipt.billId
        if (this.enableSingleGoodsMulRcver) {
          body.scope = this.viewMine ? '0' : '1'
        }
        await this.submitReceipt(body)
      }
      const result = await this.finish({
        billId: this.billId,
        confirmSubmitReceiptRecord: force
      })
      this.$hideLoading()
      // code为0时，表示结束收货成功
      if (result.code === '0') {
        this.$showToast({ icon: 'success', title: '操作成功' })
        const timer = setTimeout(() => {
          clearTimeout(timer)
          this.$Router.replace({
            name: 'receiptDetail',
            params: {
              id: this.billId
            }
          })
        }, 1500)
        this.isRenovote = true
      } else if (result.code === '1') {
        // code为1时，表示结束时存在其他未提交但是已清点的商品，需要用户确认
        this.$showModal({
          title: '提醒',
          content: `${result.errMsg}`,
          showCancel: true,
          confirmText: '继续结束',
          success: (action) => {
            if (action.confirm) {
              this.handleFinishDirect(this.billId, true)
            }
          }
        })
      } else {
        this.$showModal({
          title: '结束收货失败',
          content: `失败原因:${result.errMsg}`,
          showCancel: true,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.handleFinish()
            }
          }
        })
      }
    } catch (error) {
      this.$hideLoading()
      this.$showModal({
        title: '结束收货失败',
        content: `失败原因:${error.msg}`,
        showCancel: true,
        confirmText: '重试',
        success: (action) => {
          if (action.confirm) {
            this.handleFinish()
          }
        }
      })
    }
  }

  /**
   * 提交
   * @param body
   * @returns
   */
  submitReceipt(body: AppReceiptRecordSubmitterDTO) {
    return new Promise<void>((resolve, reject) => {
      AppReceiptRecordApi.submit(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查询单据差异明细
   * @param body
   * @returns
   */
  queryDiffLine(body: QueryRequest) {
    return new Promise<AppReceiptDiffSumDTO>((resolve, reject) => {
      ReceiptApi.queryDiffLine(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 结束收货
   * @param body
   * @returns
   */
  finish(body: AppReceiptMultipleRcvCloser) {
    return new Promise<AppReceiptMultipleRcvCloseResult>((resolve, reject) => {
      ReceiptApi.closeMultipleRcv(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  handleViewMine() {
    this.viewMine = !this.viewMine
    this.doResetPage()
    this.loadSkuList()
  }

  // 搜索框搜索事件
  doSearch() {
    this.$Router.push({
      name: 'receiptRecordSkuSearch',
      params: {
        id: this.receipt.billId,
        receiptBillId: this.billId
      }
    })
  }

  // 扫码按钮点击事件
  doScan() {
    uni.scanCode({
      success: (res) => {
        const scanWord = res.result || ''
        this.doScanAfter(scanWord)
      },
      fail: () => {
        this.scaning = false
      }
    })
  }

  //PDA扫码回调事件
  async doScanAfter(scanWord: string, isScan: boolean = true) {
    if (this.isLoading) {
      this.$showToast({ title: '正在加载，请稍后重试~' })
      return
    }
    try {
      this.$showLoading()
      this.scaning = true
      const skuList = await this.queryLine(isScan, scanWord)
      this.$hideLoading()
      if (!skuList || !skuList.length) {
        // 无搜索结果跳转至搜索页面
        this.$Router.push({
          name: 'receiptRecordSkuSearch',
          params: {
            id: this.receipt.billId,
            receiptBillId: this.billId,
            keyWord: scanWord
          }
        })
      } else if (skuList.length === 1) {
        this.handleEdit(skuList[0])
      } else {
        this.$Router.push({
          name: 'receiptRecordSkuSearch',
          params: {
            id: this.receipt.billId,
            receiptBillId: this.billId,
            keyWord: scanWord
          }
        })
      }
    } catch (error) {
      this.scaning = false
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 重置分页参数
   */
  doResetPage() {
    this.pageNum = 0
    this.currentPage = 0
    this.total = 0
    this.isLoading = false
    this.finished = false
  }

  async loadSkuList() {
    try {
      this.$showLoading()
      await this.queryLine()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '查询失败',
        icon: 'error'
      })
    }
  }

  /**
   * 绑定陈列位置
   */
  bindExhibit(info: AppReceiptRecordLineDTO, index: number) {
    this.exhibitIndex = index
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.open(this.hasSelelcExhibit, slotGoods, 'bindExhibit')
  }

  /**
   * 陈列位置调整
   */
  resetExhibit(info: AppReceiptRecordLineDTO, index: number) {
    this.hasSelelcExhibit = new Slot()
    this.exhibitIndex = index
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.$refs.resetExhibit.open(slotGoods)
  }

  /**
   * 获取该商品信息
   */
  getRequestBody(info: AppReceiptRecordLineDTO) {
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 目标货位
    slotGoods.targetSlotCode = info.exhibitValue
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.success(this.hasSelelcExhibit, slotGoods)
  }

  /**
   * 输入框的值组装
   */
  inputExhibit(code) {
    this.hasSelelcExhibit.code = code
    this.hasSelelcExhibit.name = code
    this.hasSelelcExhibit.uuid = ''
  }

  /**
   * 陈列位置绑定成功
   */
  confirmExhibit() {
    this.doResetPage()
    this.loadSkuList()
    this.hasSelelcExhibit = new Slot()
  }

  /**
   * 添加陈列位置
   */
  addExhibit() {
    this.$Router.push({
      name: 'addExhibitLocation'
    })
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info: AppReceiptRecordLineDTO) {
    this.viewExhibitInfo = { ...info }
    this.$refs.viewExhibit.open()
  }

  /**
   * 取消收货（本次不做）
   */
  cancelReceipt() {
    this.$showModal({
      title: '提示',
      content: '取消收货后，将退出当前快递单号的收货，对应数据会被清除',
      showCancel: true,
      confirmText: '确定',
      success: (action) => {
        if (action.confirm) {
          this.$showLoading({ delayTime: 200 })
          AppReceiptRecordApi.cancel({ id: this.billId })
            .then((resp) => {
              uni.navigateBack({})
            })
            .catch((error) => {
              this.$showToast({
                title: error.message,
                icon: 'none'
              })
            })
            .finally(() => {
              this.$hideLoading()
            })
        }
      }
    })
  }

  /**
   * 关闭上传附件弹窗
   */
  doCloseFeedback() {
    this.$refs.feedback.close()
  }

  /**
   * 上传附件弹窗点击提交按钮
   */
  async doSubmitFeedback(wordFeedback?, upImageList?, upVideoList?, signType?, signInfoKey?) {
    console.log(wordFeedback, upImageList, upVideoList, signType)
    try {
      this.$showLoading({ delayTime: 200 })
      const data: AppReceiptRecordAttachCreateDTO[] = []
      upImageList.forEach((item: string, index: number) => {
        const ImageInfo = new AppReceiptRecordAttachCreateDTO()
        ImageInfo.billId = this.receipt.billId
        ImageInfo.fileType = FileType.img
        ImageInfo.fileUrl = item
        ImageInfo.lineNo = index
        const strIndex = item.lastIndexOf('/')
        ImageInfo.fileName = item.substring(strIndex + 1)
        ImageInfo.fileId = signInfoKey + ImageInfo.fileName
        data.push(ImageInfo)
      })
      await this.saveAttach(data)
      if (this.isExpress) {
        this.validateBeforeSubmit(wordFeedback)
      } else {
        this.submit(wordFeedback)
      }
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: error.msg })
    }
  }

  // 单头附件保存
  saveAttach(body: AppReceiptRecordAttachCreateDTO[]) {
    return new Promise<void>((resolve, reject) => {
      AppReceiptRecordApi.saveAttach(body)
        .then(() => {
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
}
