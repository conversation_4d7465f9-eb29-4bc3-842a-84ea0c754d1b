<template>
  <view class="sel-tag-wraper">
    <view class="header">
      <text class="header-title">标签选择</text>
      <image @click="doCancel()" class="ic-close" :src="'/static/icon/ic_closegrey.png' | oss" />
    </view>
    <scroll-view class="scroll-wrapper" scroll-y="true">
      <view class="sel-type-item" :class="{ active: !curTag }" @click.stop="doSelectItem(null)">
        <view class="title ellipsis">全部标签</view>
        <image v-if="!curTag" class="ic-sel" :src="'/static/icon/ic_choice.png' | oss" />
      </view>
      <view
        class="sel-type-item"
        :class="{ active: curTag && curTag.tagname === item.tagname }"
        v-for="(item, index) in tagList"
        :key="index"
        @click="doSelectItem(item)"
      >
        <view class="title ellipsis">{{ item.tagname }}</view>
        <image v-if="curTag && curTag.tagname === item.tagname" class="ic-sel" :src="'/static/icon/ic_choice.png' | oss" />
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts">
import ConfigResult from '@/model/jyzsData/ConfigResult'
import GoodsTag from '@/model/jyzsData/GoodsTag'
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { State } from 'vuex-class'
import JyzsUser from '@/model/jyzsData/JyzsUser'
import store from '@/store'
import CommonRequest from '../goodsSearch/CommonRequest'
import UserLoginApi from '@/network/jyzsData/jyzsData'

@Component({
  components: {}
})
export default class HdSelTagPopup extends Vue {
  @State('configResult') configResult: ConfigResult
  @State('userInfo') user: JyzsUser
  @Prop({ type: Object, default: {} }) tag // 当前tag

  showPop: boolean = false
  tagList: GoodsTag[] = []
  curTag: Nullable<any> = null
  shopId = 1000020 || store!.state!.store!.id //门店id

  // 监听事件
  @Watch('tag', { deep: true, immediate: true })
  onShowChange(newVal: boolean, oldVal: boolean) {
    this.curTag = this.tag
  }
  mounted() {
    this.getAllTags()
  }

  // 获取所有标签
  getAllTags() {
    const commonRequest: CommonRequest = new CommonRequest()
    console.log('this.user', this.user)
    commonRequest.userId = (this.user.uuid as any).toString()
    commonRequest.groupId = '4'
    commonRequest.orgId = Number(this.shopId)
    commonRequest.orgType = '4'

    UserLoginApi.getAllTags(commonRequest).then((res) => {
      if (res.data) {
        this.tagList = res.data
      }
    })
  }

  doCancel() {
    this.$emit('cancel')
  }
  // 确定选择
  doSelectItem(tag) {
    console.log(22222)
    this.doCancel()
    this.$set(this, 'curTag', tag)
    this.$emit('onConfirm', tag)
  }
}
</script>
<style lang="scss" scoped>
::v-deep  .hd-popup-content {
  width: 100%;
  border-radius: 0;
}

.sel-tag-wraper {
  width: 100vw;
  height: 100vh;
  border-radius: 24rpx 24rpx 0px 0px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
}

.header {
  height: 100rpx;
  width: 750rpx;
  border-radius: 24rpx 24rpx 0px 0px;
  box-sizing: border-box;
  padding: 0 24rpx;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  overflow: hidden;
  border: none;
  background-color: #ffffff;
  position: relative;

  .header-title {
    height: 100rpx;
    line-height: 100rpx;
    width: 200rpx;
    position: absolute;
    left: 50%;
    margin-left: -100rpx;
    font-size: 36rpx;
    font-weight: 500;
    color: #32353a;
    font-family: PingFangSC-Medium, PingFang SC;
  }

  .ic-close {
    width: 32rpx;
    height: 32rpx;
  }
}

.scroll-wrapper {
  width: 100%;
  height: calc(100% - 100rpx);
  .sel-type-item {
    display: flex;
    height: 100rpx;
    background: linear-gradient(360deg, #ffffff 0%, #ffffff 100%);
    /*border-radius: 12rpx;*/
    padding: 0 24rpx;
    align-items: center;
    /*margin: 16rpx 0 0 0;*/
    color: #65686f;
    font-size: 28rpx;
    font-weight: 500;
    position: relative;
    &:after {
      content: ' ';
      width: 100%;
      height: 2rpx;
      background: #d8d8d8;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
    }
    &:first-child {
      margin-top: 24rpx;
    }
    &:last-child {
      margin-bottom: 24rpx;
    }
    .title {
      flex: 1;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
    }
    .ic-sel {
      width: 44rpx;
      height: 44rpx;
      margin-left: 24rpx;
    }

    &.active {
      color: #1c64fd;
    }
  }
}
</style>
