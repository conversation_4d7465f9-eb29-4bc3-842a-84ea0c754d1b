<template>
  <view class="receipt-edit-card" :key="sku.goods.uuid">
    <text class="receipt-title">
      <text class="receipt-title-over" v-if="sku.finished">已结束</text>
      <text class="receipt-title-tag" v-if="sku.isDisp">散称</text>
      <text class="receipt-title-gift" v-if="sku.type === 'gift'">赠品</text>
      {{ sku.goods.name | empty }}
    </text>

    <view class="info">
      <view class="info__img">
        <image lazy-load class="info__img-sku" :src="img" @click.stop="handlePreviewImg" />
        <view class="info__scale">
          <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
        </view>
      </view>
      <view class="info__value">
        <view class="info__value-half">代码：{{ sku.goods.code | empty }}</view>
        <view class="info__value-half">条码：{{ sku.goods.inputCode | empty }}</view>
        <view class="info__value-half">规格：{{ sku.goods.qpcStr | empty }}</view>
        <view class="info__value-half" v-if="showCurInvQty">可售库存：{{ sku.invQty | empty }}{{ sku.goods.minMunit | empty }}</view>
      </view>
    </view>

    <view class="receipt-code">
      <text class="receipt-code-txt">
        应收数：
        <text class="receipt-code-txt-qty">{{ sku.shipQty | empty }}{{ sku.goods.minMunit | empty }}</text>
      </text>
      <text class="receipt-code-txt" v-if="showPrice">
        应收金额：
        <text style="font-size: 24rpx">￥</text>
        <text class="text">{{ sku.shipTotal | empty }}</text>
      </text>
    </view>

    <view class="receipt-code">
      <text class="receipt-code-txt">
        待收数：
        <text class="receipt-code-txt-qty">{{ pendingReceipts | empty }}{{ sku.goods.minMunit | empty }}</text>
      </text>
    </view>

    <view class="receipt-code" v-if="showMaster.showDisplayLocation">
      <view class="main-exhibit">
        <view :class="[hasMutiple(sku.displayLocation) ? 'goods-one' : '']">
          陈列位置：
          <text class="goods-text">{{ sku.displayLocation | empty }}</text>
        </view>
        <image class="good-img" :src="'/static/icon/ic_right_grey.png' | oss" v-if="hasMutiple(sku.displayLocation)" @click="viewExhibit" />
      </view>
    </view>

    <!-- 绑定陈列位置 -->
    <view class="bind-exhibit" v-if="isShowExhibitBtn && slotSource">
      <view class="bind-exhibit-select">
        <view class="bind-exhibit-block-left">
          <text class="left-text">绑定陈列位置</text>
        </view>
        <view>
          <text @click="resetExhibit" v-if="allowOneGoodsMultipleSlot">陈列位置调整</text>
          <!-- <text @click="bindExhibit" style="margin-left: 32rpx">手动选择陈列位置</text> -->
        </view>
      </view>
      <view class="bind-exhibit-block">
        <view class="bind-exhibit-block-right">
          <input class="right-input" v-model="exhibitValue" placeholder="输入商品陈列位置编号 例：A-1-1-1" @blur="getGoodsInfo" />
        </view>
        <view class="right-scan" @click="doScanExhibit">
          <image class="right-img" :src="'/static/icon/ic_scan_blue.png' | oss" />
          <text class="right-text">扫码识别</text>
        </view>
      </view>
    </view>

    <view class="receipt-code" v-if="showPrice && sku.confirmed">
      <text class="receipt-code-txt" v-if="showPrice">
        单价：
        <text class="text">
          <text class="qpc-price" style="font-size: 24rpx">￥</text>
          <text class="qpc-price">{{ sku.goods.singlePrice | empty }}</text>
          <text class="qpc-munit">/{{ sku.goods.minMunit | empty }}</text>
        </text>
      </text>
      <text class="receipt-code-txt" v-if="sku.confirmed">
        本次收：
        <text class="receipt-code-txt-qty">{{ sku.receiptQty | empty }}{{ sku.goods.minMunit | empty }}</text>
      </text>
    </view>
    <view class="receipt-code" v-if="sku.confirmed && directFillPrddateAndValidate && (sku.goods.storeUseExp || sku.goods.storeUseMfg)">
      <text class="receipt-code-txt">
        生产日期：
        <text class="receipt-code-txt-qty">{{ mfgDate | date | empty }}</text>
      </text>
      <text class="receipt-code-txt">
        到效日期：
        <text class="receipt-code-txt-qty">{{ expDate | date | empty }}</text>
      </text>
    </view>
    <view class="receipt-operation" v-if="sku.confirmed">
      <view class="receipt-operation-left">
        <img :src="'/static/icon/ic_user.png' | oss" class="receipt-operation-left-icon" />
        {{ sku.consigneeName | empty }}
      </view>
      <view class="receipt-operation-right">
        <view class="receipt-operation-detail" @click="handleViewDetail">收货明细</view>
        <view class="receipt-operation-right-btn" @click="handleEdit">修改</view>
      </view>
    </view>

    <template v-else>
      <block v-if="!sku.isDisp">
        <view class="receipt-reason">
          <view class="receipt-reason-txt">整件数（即规格的整倍数）</view>
          <view class="receipt-reason-number">
            <view class="receipt-reason-operator" v-if="sku.goods.qpc">{{ sku.goods.qpc | empty }} ×</view>
            <hd-number-box-test v-model="wholeQty" :max="wholeMax" :scale="0" :min="0" :disabled="isSkuQtyDisabeld"></hd-number-box-test>
          </view>
        </view>
        <view class="receipt-reason">
          <view class="receipt-reason-txt">单品</view>
          <view class="receipt-reason-number">
            <hd-number-box-test v-model="splitQty" :max="splitMax" :min="0" :scale="qtyScale" :disabled="isSkuQtyDisabeld"></hd-number-box-test>
          </view>
        </view>
      </block>
      <block v-else>
        <view class="receipt-reason" v-if="doubleMeasureGoodsEnterQpcQty">
          <view class="receipt-reason-txt">件数({{ sku.goods.munit }})</view>
          <view class="receipt-reason-number">
            <hd-number-box-test
              v-model="wholeQty"
              :max="wholeMax"
              :min="0"
              :scale="qtyScale"
              :disabled="isSkuQtyDisabeld"
              @change="handleWholeQtyChange"
            ></hd-number-box-test>
          </view>
        </view>
        <view class="receipt-reason">
          <view class="receipt-reason-txt">重量({{ sku.goods.minMunit }})</view>
          <view class="receipt-reason-number">
            <hd-number-box-test
              v-model="splitQty"
              @change="handleSplitQtyChange"
              :max="splitMax"
              :min="0"
              :scale="qtyScale"
              :disabled="isSkuQtyDisabeld"
            ></hd-number-box-test>
          </view>
        </view>
      </block>
      <view class="receipt-operation">
        <view class="receipt-operation-detail" @click="handleViewDetail">收货明细</view>
        <view class="receipt-operation-confirm" @click="handleOver">结束收货</view>
        <view class="receipt-operation-confirm" @click="handleConfirm">确认收货</view>
      </view>
    </template>
    <view class="good-time" v-if="!sku.confirmed && directFillPrddateAndValidate && (sku.goods.storeUseExp || sku.goods.storeUseMfg)">
      <view class="time-start" @click="doStartClick">
        <view class="time-label">生产日期</view>
        <view class="flex">
          <view class="time-value">{{ mfgDate | date | empty }}</view>
          <image class="time-icon" :src="'/static/icon/ic_right_grey.png' | oss" />
        </view>
      </view>
      <view class="time-start" @click="doEndClick">
        <view class="time-label">到效日期</view>
        <view class="flex">
          <view class="time-value">{{ expDate | date | empty }}</view>
          <image class="time-icon" :src="'/static/icon/ic_right_grey.png' | oss" />
        </view>
      </view>
    </view>
    <view v-if="isIntime" class="out-time">
      <image :src="'/static/icon/ic_error_fill.png' | oss" class="error-image"></image>
      该商品已过期
    </view>
  </view>
</template>

<script lang="ts" src="./ReceiptEditCard.ts"></script>

<style lang="scss" scoped>
.receipt-edit-card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 228rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 24rpx 0;
  background: rgba(255, 255, 255, 1);
  .receipt-edit-card-tag {
    position: absolute;
    right: 0;
    top: 0;
    width: 52rpx;
    height: 52rpx;
  }
  .receipt-title {
    width: 100%;
    box-sizing: border-box;
    padding: 0 24rpx;
    font-size: 30rpx;
    font-family: $font-medium;
    font-weight: 550;
    color: #333333;
    line-height: 40rpx;
    margin-bottom: 16rpx;
    &-gift {
      display: inline-flex;
      align-items: center;
      padding: 0 10rpx;
      box-sizing: border-box;
      margin-right: 8rpx;
      width: 68rpx;
      height: 40rpx;
      background: linear-gradient(313deg, #ff9a00 0%, #ffd05b 100%);
      border-radius: 8rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #ffffff;
    }

    &-tag {
      display: inline-flex;
      align-items: center;
      padding: 0 10rpx;
      box-sizing: border-box;
      margin-right: 8rpx;
      width: 68rpx;
      height: 40rpx;
      background: #e9f0ff;
      border-radius: 8rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #1c64fd;
    }

    &-over {
      background: #fff1df;
      border-radius: 4rpx 4rpx 4rpx 4rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #f57f00;
      line-height: 40rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-right: 8rpx;
      padding: 4rpx 10rpx;
      box-sizing: border-box;
    }
  }

  .info {
    width: 100%;
    display: flex;
    margin-bottom: 16rpx;
    padding: 0 24rpx;
    box-sizing: border-box;
    &__img {
      position: relative;
      width: 120rpx;
      height: 120rpx;
      margin-right: 24rpx;
      flex: 0 0 auto;
      &-sku {
        width: 120rpx;
        height: 120rpx;
      }
    }

    &__scale {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 24rpx;
      height: 24rpx;
      background: rgba(0, 0, 0, 0.4);
      border-radius: 8rpx 0px 8rpx 0rpx;
      text-align: center;
      @include flex();
      &-img {
        width: 16rpx;
        height: 16rpx;
      }
    }

    &__value {
      position: relative;
      flex: 1 1 auto;
      &-half {
        width: 50%;
        flex: 0 0 auto;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        display: inline-flex;

        // 如果不是最后两个元素，则有向下16rpx的margin
        &:not(:nth-last-child(-n + 2)) {
          margin-bottom: 16rpx;
        }

        .good-img {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }

  .receipt-code {
    display: flex;
    align-items: center;
    margin-top: 8rpx;
    box-sizing: border-box;
    padding: 0 24rpx;

    .receipt-code-txt {
      width: 50%;
      font-size: 24rpx;
      color: rgba(148, 150, 154, 1);
      display: flex;
      align-items: center;

      .text {
        color: #666666;
      }

      &-qty {
        color: #333333;
        font-weight: 550;
      }

      .receipt-code-tag {
        width: 56rpx;
        height: 28rpx;
        margin-right: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #e9f0ff;
        color: #1c64fd;
        border-radius: 4rpx;
        font-size: 20rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }

    .text-body-text {
      width: 50%;
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: rgba(148, 150, 154, 1);

      .good-location {
        font-size: $font-size-xsmall;
        font-weight: 400;
        color: $color-text-secondary;
        max-width: 179rpx;
        height: 32rpx;
        line-height: 32rpx;
        @include ellipsis();
      }

      .good-img {
        width: 32rpx;
        height: 32rpx;
      }
    }

    .main-half {
      width: 702rpx;
      flex: 0 0 auto;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      display: flex;
      align-items: center;

      .good-img {
        width: 32rpx;
        height: 32rpx;
        display: inline-table;
      }

      .goods-one {
        flex: 1;
        @include ellipsis();
      }
    }
  }
  .main-exhibit {
    width: 702rpx;
    background: #f5f5f5;
    border-radius: 8rpx;
    padding: 8rpx 16rpx;
    box-sizing: border-box;
    flex: 0 0 auto;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    display: flex;
    align-items: center;

    .good-img {
      width: 32rpx;
      height: 32rpx;
      display: inline-table;
    }

    .goods-text {
      color: #333333;
      font-size: 26rpx;
      font-weight: 500;
    }

    .goods-one {
      @include ellipsis();
    }
  }
  .bind-exhibit {
    width: 100%;
    margin-top: 16rpx;
    display: flex;
    flex-direction: column;
    padding: 0 24rpx;
    box-sizing: border-box;
    .bind-exhibit-block {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-left {
        display: flex;
        align-items: center;

        .left-text {
          height: 32rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #585a5e;
          line-height: 32rpx;
          text-align: left;
          font-style: normal;
        }

        .left-img {
          width: 32rpx;
          height: 32rpx;
        }
      }

      &-right {
        flex: 1;
        height: 72rpx;
        background: #f5f5f5;
        border-radius: 4rpx;
        display: flex;
        align-items: center;
        padding: 0 16rpx;
        box-sizing: border-box;

        .right-input {
          flex: 1;
          height: 40rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
        }
      }

      .right-scan {
        width: 188rpx;
        height: 72rpx;
        background: #e9f0ff;
        border-radius: 8rpx;
        margin-left: 16rpx;
        @include flex(row);

        .right-img {
          width: 36rpx;
          height: 36rpx;
          margin-right: 16rpx;
        }

        .right-text {
          width: 104rpx;
          height: 32rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #1c64fd;
          line-height: 32rpx;
          text-align: left;
          font-style: normal;
        }
      }
    }

    .bind-exhibit-select {
      width: 100%;
      height: 32rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #1c64fd;
      line-height: 32rpx;
      text-align: right;
      font-style: normal;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
    }
  }
  .receipt-qpc {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 18rpx;
    margin-top: 14rpx;
    display: flex;
    align-items: center;
    .receipt-qpc-detail {
      width: 316rpx;
      display: flex;
    }
    .qpc-price {
      font-size: 24rpx;
      line-height: 40rpx;
      color: rgba(40, 44, 52, 1);
      line-height: 32rpx;
    }
    .qpc-munit {
      font-size: 24rpx;
      margin-left: 12rpx;
      color: rgba(88, 90, 94, 1);
      line-height: 32rpx;
    }
    .qpc-detail {
      margin-left: 20rpx;
      font-size: 24rpx;
      color: rgba(148, 150, 154, 1);
      line-height: 32rpx;
    }
    .qpc-qty {
      width: 166rpx;
      margin-left: 8rpx;
      text-align: right;
      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: rgba(88, 90, 94, 1);
      line-height: 32rpx;
    }
    .qpc-total {
      width: 200rpx;
      margin-left: 12rpx;
      text-align: right;
      font-size: 32rpx;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: rgba(255, 136, 0, 1);
      line-height: 32rpx;
    }
  }
  .wrhNote {
    margin: 12rpx 24rpx 0rpx;
    width: 702rpx;

    background: #f3f7ff;
    padding: 16rpx 20rpx 20rpx;
    box-sizing: border-box;
    border-radius: 8rpx;

    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: $color-text-thirdly;
    @include flex(column, space-between, flex-start);

    .wrhNote-line {
      line-height: 36rpx;
      @include flex(row, flex-start);
    }

    .data {
      flex: 0 0 auto;
      width: 516rpx;
      @include ellipsis();
      color: $color-text-secondary;
    }
  }
  .gift-info {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    box-sizing: border-box;
    padding: 0 24rpx;
    &-content {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 64rpx;
      background: #f9fafc;
      border-radius: 8rpx;
      padding: 0 18rpx;
      font-size: 28rpx;
      color: #999999;
    }
  }

  .receipt-reason {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 48rpx;
    box-sizing: border-box;
    padding: 0 24rpx;
    margin-top: 20rpx;
    .receipt-reason-number {
      display: flex;
      justify-content: flex-end;
      .receipt-reason-operator {
        height: 48rpx;
        margin-right: 12rpx;
        line-height: 48rpx;
        font-size: 24rpx;
        font-family: HelveticaNeue;
        color: rgba(88, 90, 94, 1);
      }
    }
    .receipt-reason-txt {
      height: 48rpx;
      max-width: 386rpx;
      line-height: 48rpx;
      font-size: 24rpx;
      color: #585a5e;
      @include ellipsis();
    }
  }

  .receipt-operation {
    margin-top: 24rpx;
    padding: 0 24rpx;
    @include flex(row, flex-end, center);

    &-detail {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 48rpx;
      background: #ffffff;
      border: 1rpx solid #cccccc;
      border-radius: 32rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      padding: 0 12rpx;
      margin-left: 12rpx;
      min-width: 112rpx;
    }

    &-confirm {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 48rpx;
      background: #e9f0ff;
      border: 1rpx solid #e9f0ff;
      border-radius: 32rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #1c64fd;
      padding: 0 12rpx;
      margin-left: 12rpx;
      min-width: 112rpx;
    }

    &-left {
      flex: 1 1 auto;
      @include ellipsis();
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      display: flex;
      align-items: center;
      &-icon {
        width: 40rpx;
        height: 40rpx;
      }
    }

    &-right {
      flex: 0 0 auto;
      display: flex;
      justify-content: flex-end;

      &-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 48rpx;
        background: #e9f0ff;
        border: 1rpx solid #e9f0ff;
        border-radius: 32rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #1c64fd;
        padding: 0 12rpx;
        margin-left: 12rpx;
        min-width: 112rpx;
      }
    }
  }

  .good-time {
    width: 100%;
    margin-top: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 24rpx;
    background: $color-white;

    .time-start {
      height: 64rpx;
      width: 342rpx;
      box-sizing: border-box;
      padding: 12rpx;
      @include flex(row, space-between);
      background: $color-bg-primary;
      border-radius: 8rpx;
      font-size: 24rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;

      &:not(:last-child) {
        margin-right: 16rpx;
      }
    }
    .time-icon {
      width: 28rpx;
      height: 28rpx;
    }
  }
  .out-time {
    margin-top: 16rpx;
    padding: 0 24rpx;
    box-sizing: border-box;
    width: 100%;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    font-size: 24rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #fd3431;
    .error-image {
      margin-right: 12rpx;
      vertical-align: middle;
      width: 32rpx;
      height: 32rpx;
    }
  }
}
.receipt-edit-card:not(:last-child)::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  bottom: 0;
  left: 19rpx;
  width: calc(100% - 38rpx);
  border-bottom: 2rpx solid rgba(227, 228, 232, 1);
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
</style>
