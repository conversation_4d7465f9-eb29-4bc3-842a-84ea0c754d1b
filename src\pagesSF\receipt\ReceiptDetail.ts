/*
 * @Author: 徐庆凯
 * @Date: 2020-11-04 13:34:38
 * @LastEditTime: 2025-04-03 14:24:53
 * @LastEditors: weisheng
 * @Description: 收货详情
 * @FilePath: /soa/src/pagesSF/receipt/ReceiptDetail.ts
 * @记得注释
 */
import { Component, Vue } from 'vue-property-decorator'
import SaleList from './cmp/SaleList.vue'
import ReceiptApi from '@/network/receipt/ReceiptApi'
import ReceiptApiV2 from '@/network/receipt/ReceiptApiV2'
import FlowDialog from '@/pagesSF/cmp/FlowDialog.vue'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import BIllIdNum from 'model/data/BillIdNum'
import config from '@/config'
import CommonUtil from '@/utils/CommonUtil'
import CommentApi from '@/network/commentApi/CommentApi'
import ModuleFieldRights from '@/model/default/ModuleFieldRights'
import { Mutation, State } from 'vuex-class'
import SysConfigItem from '@/model/sysConfig/SysConfigItem'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import { ReceiptState } from '@/model/receipt/default/ReceiptState'
import ReceiptCombinedValidLine from '@/model/receipt/default/ReceiptCombinedValidLine'
import QueryRequest from '@/model/receipt/default/QueryRequest'
import AppReceiptBoxGoodsDTO from '@/model/receipt/AppReceiptBoxGoodsDTO'
import ReceiptLine from '@/model/receipt/default/ReceiptLine'
import SaleListBox from './cmp/SaleListBox.vue'
import AppReceiptDTO from '@/model/receipt/dto/AppReceiptDTO'
import AppReceiptMultipleRcvRecordQueryer from '@/model/receipt/AppReceiptMultipleRcvRecordQueryer'
import AppReceiptMultipleRcvGdRecordDTO from '@/model/receipt/AppReceiptMultipleRcvGdRecordDTO'
import SkuRecordDialog from '@/pages/cmp/SkuRecordDialog.vue'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import { ReqOrdType } from '@/model/receipt/default/ReqOrdType'

@Component({
  components: { SaleList, FlowDialog, SaleListBox, SkuRecordDialog, ViewExhibit }
})
export default class ReceiptDetail extends Vue {
  @State('moduleFieldRightsList') moduleFieldRightsList: ModuleFieldRights[] // 获取模块字段权限
  @Mutation('moduleFieldRightsList') setModuleFieldRightsList // 提交模块字段权限
  @State('sysConfig') sysConfig: SysConfigItem // 获取全局配置
  @State('optionList') optionList: ModuleOption[]
  optionListModuleId = ModuleId

  data: AppReceiptDTO = new AppReceiptDTO() // 单据详情
  state: string = '' // 订单状态
  stateImg: string = '' //状态图标
  allowNavReceiptDiff: boolean = true // 收货差异详情跳转到该页面，不允许再次跳转至收货差异页面
  $refs: any
  wrhNote: string = '' //当前拣货备注
  note: string = '' //当前出货备注
  showComment: boolean = false // 是否展示去评论按钮
  receiptLines: ReceiptLine[] = [] // 收货商品列表
  boxGoods: AppReceiptBoxGoodsDTO[] = [] // 按箱收货商品列表
  viewExhibitInfo: ReceiptLine = new ReceiptLine() // 陈列位置弹窗数据
  viewBoxExhibitInfo: AppReceiptBoxGoodsDTO = new AppReceiptBoxGoodsDTO() // 陈列位置弹窗数据
  /**
   * 是否使用诚信志远定制的订货界面
   * sosRequireApply下的homePage为1就是跳转投放订货
   * 使用诚信志远定制的订货界面才可以对单子进行评价
   */
  get jumpNewOrderPage() {
    const moduleConfig: any = this.optionList
      ? this.optionList.find((option: ModuleOption) => {
          return option.moduleId == this.optionListModuleId.sosRequireApply
        })
      : {}
    if (moduleConfig && moduleConfig.options && 'homePage' in moduleConfig.options) {
      return moduleConfig.options.homePage === '1'
    }

    return false
  }

  // 散称双计量是否需要录入件数（包装数）
  get doubleMeasureGoodsEnterQpcQty() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.doubleMeasureGoodsEnterQpcQty == '1'
    }
    return false
  }

  // 是否启用多次收货
  get enableMultipleReceipt() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == this.optionListModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.enableMultipleReceipt == '1'
    }
    return false
  }

  // 获取权限
  get diffPermision() {
    if (PermissionMgr.hasPermission(Permission.receiptDiffView)) {
      return true
    }
    return false
  }

  /**
   * 是否展示配货价配货金额 - 默认展示
   */
  get showAlcPrice() {
    const moduleConfig = (this.moduleFieldRightsList || []).find((option: ModuleFieldRights) => option.moduleId == 'sosReceipt')
    if (moduleConfig && moduleConfig.fieldRights && moduleConfig.fieldRights.price) {
      return moduleConfig.fieldRights.price === '1'
    }
    return true
  }

  get showPrice() {
    //店务配置，是否显示配货价
    if (!this.showAlcPrice) {
      return false
    }
    if (PermissionMgr.hasPermission(Permission.receiptShowprice)) {
      return true
    }
    return false
  }

  /**
   * 是否展示更多
   */
  get showMore() {
    return this.receiptLines.length > 4
  }

  /**
   * 是否展示更多
   */
  get boxShowMore() {
    return this.boxGoods.length > 4
  }

  onLoad(option) {
    this.$nextTick(() => {
      this.data.billId = option.id
      if (option.allowNavReceiptDiff === 'false') {
        this.allowNavReceiptDiff = false
      }
      this.getDetail()
      if (option.keyword) {
        let keyword: string = ''
        try {
          keyword = decodeURIComponent(option.keyword)
        } catch (error) {
          keyword = option.keyWord
        }
        const timer = setTimeout(() => {
          this.$Router.push({
            name: 'receiptLineTotal',
            params: {
              id: this.data.billId,
              keyword: keyword
            }
          })
          clearTimeout(timer)
        }, 200)
      }
      if (option.searchword) {
        let searchword: string = ''
        try {
          searchword = decodeURIComponent(option.searchword)
        } catch (error) {
          searchword = option.searchword
        }
        const timer = setTimeout(() => {
          this.$Router.push({
            name: 'receiptLineTotal',
            params: {
              id: this.data.billId,
              keyword: searchword
            }
          })
          clearTimeout(timer)
        }, 200)
      }
    })
  }

  onShow() {
    // 从评论页回来，需要刷新状态
    if (this.state && this.data.state === ReceiptState.finished && this.jumpNewOrderPage) {
      this.doGetCommentList()
    }
  }

  // 获取单据详情
  getDetail() {
    this.$showLoading({ delayTime: 200 })
    ReceiptApiV2.get(this.data.billId, 'diffs')
      .then(async (resp) => {
        this.data = resp.data || new AppReceiptDTO()
        switch (this.data.state) {
          case ReceiptState.boxReceipted:
          case ReceiptState.finished:
            this.stateImg = config.sourceUrl + 'icon/ic_approval_2x.png'
            break
          case ReceiptState.aborted:
          case ReceiptState.invalided:
            this.stateImg = config.sourceUrl + 'icon/ic_refuse_2x.png'
            break
        }
        this.$nextTick(() => {
          this.state = `${ReceiptState.string(this.data.state as ReceiptState)}` || ''
        })
        // 获取是否可以评论
        if (this.data.state === ReceiptState.finished && this.jumpNewOrderPage) {
          this.doGetCommentList()
        }

        try {
          if (this.data.receiptWay === 1) {
            this.boxGoods = await this.doQueryBoxGoods()
          } else {
            this.receiptLines = await this.doQueryLine()
          }
          this.$hideLoading()
        } catch (error) {
          this.$hideLoading()
          this.$showToast({ icon: 'none', title: error.msg })
        }
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'none', title: e.msg })
      })
  }

  /**
   * 查看商品行收货记录
   * @param sku
   */
  async handleViewDetail(inputCode: string) {
    try {
      this.$showLoading()
      const recordList = await this.listMultipleRcvRecord({
        billId: this.data.billId,
        gdInputCode: inputCode
      })
      this.$hideLoading()

      if (recordList.length === 0) {
        this.$showToast({
          title: '暂无收货记录'
        })
      } else {
        this.$refs.recordDialog.open(recordList)
      }
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败'
      })
    }
  }

  /**
   * 查询商品行多次收货记录
   * @param body
   * @returns
   */
  listMultipleRcvRecord(body: AppReceiptMultipleRcvRecordQueryer) {
    return new Promise<AppReceiptMultipleRcvGdRecordDTO[]>((resolve, reject) => {
      ReceiptApi.listMultipleRcvRecord(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查询箱码商品列表
   */
  async doQueryBoxGoods() {
    return new Promise<AppReceiptBoxGoodsDTO[]>((resolve, reject) => {
      const body: QueryRequest = new QueryRequest()
      body.page = 0
      body.pageSize = 5
      body.conditions = [{ operation: 'billId:=', parameters: [this.data.billId] }]
      ReceiptApi.queryBoxGoods(body)
        .then((resp) => {
          if (resp && resp.total) {
            this.data.recCnt = resp.total
          }
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查询单据商品明细行列表
   */
  async doQueryLine() {
    return new Promise<ReceiptLine[]>((resolve, reject) => {
      const body: QueryRequest = new QueryRequest()
      body.page = 0
      body.pageSize = 5
      body.conditions = [{ operation: 'billId:=', parameters: [this.data.billId] }]
      ReceiptApi.queryLine(body)
        .then((resp) => {
          if (resp && resp.total) {
            this.data.recCnt = resp.total || 0
          }
          const lines = resp.data || []
          for (let index = 0; index < lines.length; index++) {
            if (!lines[index].validLines || !lines[index].validLines.length) {
              lines[index].validLines = (lines[index].receiptvalidDates as any) || []
            }

            lines[index].validLines = lines[index].validLines.filter((e) => {
              return Number(e.receiptQpcQty) > 0
            })

            const addArr: ReceiptCombinedValidLine[] = []
            const deleteArr: string[] = []
            lines[index].validLines.forEach((e) => {
              e.vBNum = e.vBNum || e.vbnum || ''
              if (lines[index].isDisp) {
                if (e.refQty || e.refQty === 0) {
                  e.receiptQty = e.refQty
                } else {
                  e.receiptQty = null
                }
              } else {
                e.receiptQty = lines[index].goods.qpc
              }
              if (Number(e.receiptQpcQty) > 1) {
                for (let j = 0; j < Number(e.receiptQpcQty); j++) {
                  const validObj = CommonUtil.copy(e)
                  validObj.receiptQty = e.receiptQty
                  validObj.receiptQpcQty = '1'
                  addArr.push(validObj)
                }
                deleteArr.push(e.vBNum)
              }
            })

            lines[index].validLines = lines[index].validLines.filter((e) => {
              return !deleteArr.includes(e.vBNum!)
            })
            lines[index].validLines.push(...addArr)
            lines[index].showAllValid = false
          }
          resolve(lines)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 跳转至订货详情
   */
  handleNavRequireApply() {
    this.$Router.push({
      name: this.data.reqOrdType === ReqOrdType.independent ? 'requireGoodsDetailCdh' : 'requireGoodsDetail',
      params: {
        id: this.data.reqOrdNum
      }
    })
  }

  /**
   * 获取订单是否需要评论
   */
  doGetCommentList() {
    this.$showLoading({ delayTime: 200 })
    CommentApi.getUnCommentOrderId([this.data.billId])
      .then((resp) => {
        this.$hideLoading()
        const result = resp.data || []
        this.showComment = !!result.find((item) => item.orderId === this.data.billId && item.orderEvaluateState === 'NEVER_EVALUATE')
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  // 查看全部商品
  doViewMore() {
    this.$Router.push({
      name: 'receiptLineTotal',
      params: {
        id: this.data.billId
      }
    })
  }

  doReceiptDiff(diff: BIllIdNum) {
    uni.navigateTo({ url: `/pagesShopManage/receiptDiff/ReceiptDiffDetail?id=${diff.id}&allowNavReceipt=false` })
  }

  /**
   * 打开仓库(拣货）备注弹窗
   */
  doShowWrhNote(item) {
    this.wrhNote = item.wrhNote
    this.note = item.note
    this.$refs.wrhNote.open()
  }
  /**
   * 关闭仓库(拣货）备注弹窗
   */
  doWrhNoteClose() {
    this.$refs.wrhNote.close()
  }

  /**
   * 展开批次明细
   * @param line 商品行
   */

  doOpenValid(line) {
    const index = this.data.lines.findIndex((e) => {
      return e.goods.uuid === line.goods.uuid && e.goods.inputCode === line.goods.inputCode && e.goods.qpc === line.goods.qpc
    })
    this.data.lines[index].showAllValid = true
  }

  /**
   * 收起批次明细
   * @param line 商品行
   */

  doRetract(line) {
    const index = this.data.lines.findIndex((e) => {
      return e.goods.uuid === line.goods.uuid && e.goods.inputCode === line.goods.inputCode && e.goods.qpc === line.goods.qpc
    })
    this.data.lines[index].showAllValid = false
  }

  /**
   * 商品评价
   */
  doComment() {
    this.$Router.push({
      name: 'requireComment',
      params: { billId: this.data.billId }
    })
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info: ReceiptLine) {
    this.viewExhibitInfo = { ...info }
    this.$refs.viewExhibit.open()
  }

  viewBoxExhibit(info: AppReceiptBoxGoodsDTO) {
    this.viewBoxExhibitInfo = { ...info }
    this.$refs.viewExhibit.open()
  }
}
