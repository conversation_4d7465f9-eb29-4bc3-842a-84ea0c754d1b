import Category from '@/model/default/Category'
import Goods from './Goods'
import Sort from '@/model/default/Sort'
import rand from '@/model/default/rand'
import Attach from '@/model/receipt/default/Attach'

export default class GoodsExt extends Goods {
  // 一级面板分类
  firstCategory: Nullable<Category> = null
  // 二级面板分类
  secondCategory: Nullable<Category> = null
  // 类别
  sort: Nullable<Sort> = null
  // 品牌
  brand: Nullable<rand> = null
  // 是否新品
  isNewList: Nullable<boolean> = null
  // 是否散货
  isDisp: Nullable<boolean> = null
  // 推介类型
  recommandType: Nullable<string> = null
  // 规格零售价
  rtlPrc: Nullable<number> = null
  // 箱规
  bulkPackDesc: Nullable<string> = null
  // 运输状态
  transitStorage: Nullable<string> = null
  // sop商品主图
  mainImage: Nullable<string> = null
  // 图片url外部可访问的地址列表
  images: string[] = []
  // 附件
  attaches: Attach[] = []
  // 条码
  inputCodes: string[] = []
  // 资质图片列表
  quaImages: string[] = []
}
