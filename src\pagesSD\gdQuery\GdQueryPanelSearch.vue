<!--
 * @Author: 徐庆凯
 * @Date: 2022-10-31 10:40:11
 * @LastEditTime: 2024-06-20 15:21:22
 * @LastEditors: weisheng
 * @Description: 
 * @FilePath: \soa\src\pagesSD\gdQuery\GdQueryPanelSearch.vue
 * 记得注释
-->
<template>
  <view class="gd-query-search safe-area-inset-bottom">
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <hd-modal></hd-modal>

    <view style="position: sticky; top: 0; z-index: 99">
      <hd-simple-search-bar
        :showCancel="true"
        v-model="keyWord"
        placeholder="搜索商品"
        @confirm="doSearch"
        @cancel="doCancel"
        @clear="doClear"
      ></hd-simple-search-bar>
    </view>
    <view class="result">
      <sku-card
        v-for="sku in skuList"
        :key="sku.uuid"
        :sku="sku"
        @detail="handleRedirctDetail"
        @showDesc="handleShowDesc"
        @viewExhibit="viewExhibit"
        @resetExhibit="resetExhibit"
        @operateInvQty="operateInvQty"
      ></sku-card>
      <hd-empty v-if="inited && !skuList.length" :img="'/static/icon/img_empty_bill.png' | oss" title="暂无分类商品"></hd-empty>
      <view class="loading" v-if="!finished && inited && skuList.length">
        <view class="load-more"></view>
        <text>拼命加载中~~~</text>
      </view>
    </view>

    <!-- 查看多个陈列位置 -->
    <uni-popup ref="viewExhibit" type="bottom">
      <view-exhibit :name="viewExhibitInfo.friendlyStr" :displayLocation="viewExhibitInfo.displayLocation" @doClose="closeViewExhibit"></view-exhibit>
    </uni-popup>
    <!-- 选择陈列位置 -->
    <select-exhibit ref="exhibit" @deleteExhibit="deleteExhibit" @addExhibit="addExhibit" @success="confirmExhibit"></select-exhibit>

    <!-- 陈列位置调整 -->
    <reset-exhibit
      ref="resetExhibit"
      :showOperate="true"
      :isGoods="true"
      @deleteExhibit="deleteExhibit"
      @addExhibit="addExhibit"
      @success="confirmExhibit"
    ></reset-exhibit>

    <!-- 调整库存 -->
    <operate-inv-qty-dialog ref="operateInvQty" @close="confirmExhibit"></operate-inv-qty-dialog>
  </view>
</template>
<script lang="ts" src="./GdQueryPanelSearch.ts"></script>

<style lang="scss" scoped>
.gd-query-search {
  min-height: 100vh;
  box-sizing: border-box;
  width: 100vw;
  height: auto;
  ::v-deep .search-bar {
    margin: 0;
  }
  .result {
    .loading {
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: rgb(148, 150, 154);
    }
  }
}
</style>
