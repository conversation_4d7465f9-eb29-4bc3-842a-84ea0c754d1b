import GoodsExt from './GoodsExt'
import RequireGoodsAcc from './RequireGoodsAcc'
import RequireGoodsBatch from './RequireGoodsBatch'

export default class RequireGoods extends GoodsExt {
  // 当前门店库存(单品数量)
  invQty: Nullable<number> = null
  // 可销天数
  saleDays: Nullable<number> = null
  // 日均销量（单品数量）
  dayAvgSale: Nullable<number> = null
  // 叫货截止时间
  requireEndTime: Nullable<string> = null
  // 总部库存(单品数量)
  wrhInvQty: Nullable<number> = null
  // 月销量
  monthSaleQty: Nullable<number> = null
  // 总部库存(包装数量)
  wrhInvQpcQtyTitle: Nullable<string> = null
  // 门店库存(包装数量)
  invQpcQtyTitle: Nullable<string> = null
  // 原价
  srcPrice: Nullable<number> = null
  // 是否限量
  isLimit: Nullable<number> = null
  // 仓库维度的最大允许叫货数
  limitQty: Nullable<number> = null
  // 已叫货数，表示当前商品的已叫货数(普通价、活动价的叫货数都包括)
  orderQty: Nullable<number> = null
  // 允许叫货数，表示当前商品还允许叫货的数量(普通价、活动价的叫货数都包括)
  allowQty: Nullable<number> = null
  // 上架备注
  shelveNote: Nullable<string> = null
  // 活动标识
  activityId: Nullable<string> = null
  // 商品类型：0-普通商品，4-特价商品(爆品)
  goodsType: Nullable<number> = null
  // 方案编码
  schemeNo: Nullable<string> = null
  // 是否启用效期管理：1-启用，0或其它-不启用
  useVd: Nullable<number> = null
  // 保质期天数
  validPeriod: Nullable<number> = null
  // 在单量
  inOrdQty: Nullable<number> = null
  // 叫货目录建议包装数量
  allocDirQpcQty: Nullable<number> = null
  // 要货下限
  lowOrd: Nullable<number> = null
  // 要货上限
  highOrd: Nullable<number> = null
  // 昨日报货数
  reqQtyDay1: Nullable<number> = null
  // 叫货上限控制
  highOrdCtrl: Nullable<number> = null
  // 叫货下限控制
  lowOrdCtrl: Nullable<number> = null
  // 允许参与活动价订货的数量（可叫货爆品数）：由活动报名数-活动商品已叫货数计算得到
  allowActQty: Nullable<number> = null
  // 允许参与活动价订货的规格数量（可叫货爆品数）：由活动报名数-活动商品已叫货数计算得到
  allowActQpcQty: Nullable<number> = null
  // 活动规格价字段
  actPrice: Nullable<number> = null
  // 采购规格
  purQpc: Nullable<number> = null
  // 采购包装单位
  purMunit: Nullable<string> = null
  // 活动报名数
  signUpQty: Nullable<number> = null
  // 在途库存数量
  inTransitInvQty: Nullable<number> = null
  // 在途库存数量(包装数量)
  inTransitInvQpcQty: Nullable<string> = null
  // 未发货库存数量(单品数量)
  inOrderInvQty: Nullable<number> = null
  // 未发货库存数量(包装数量)
  inOrderInvQpcQty: Nullable<string> = null
  // 零售规格价
  rtlQpcPrc: Nullable<number> = null
  // 配货规格价
  alcQpcPrc: Nullable<number> = null
  // 配货规格价
  alcPrc: number = 0
  // 价格类型
  priceType: Nullable<number> = null
  // 体积
  qpcVolume: Nullable<number> = null
  // 最近到效期
  recentValidDate: Nullable<string> = null
  // 产地
  origin: Nullable<string> = null
  // 包装类型
  munitType: Nullable<string> = null
  // 质检标准
  qualityInspStd: Nullable<string> = null
  // 销售周期
  salesCycle: Nullable<string> = null
  // 规格描述
  qpcDesc: Nullable<string> = null
  // 重量
  weight: Nullable<string> = null
  // 净重
  netWeight: Nullable<string> = null
  // 仅7日销售数量
  weekSaleQty: Nullable<number> = null
  // 标题
  title: string = ''
  // 溯源码管理：0-否；1-是
  useTraceCode: Nullable<number> = null
  // 溯源码规则：0-标准；1-按肉品；2-酒类源码；3-唯一码
  traceCodeRule: Nullable<number> = null
  // 必订品：0-否；1-是
  reqOrd: Nullable<number> = null
  // 订货倍数
  ordMultiple: Nullable<number> = null
  // 库存成本价
  invPrc: Nullable<number> = null
  // 商品状态代码
  busGateCode: Nullable<string> = null
  // 商品状态名称
  busGateName: Nullable<string> = null
  // 批号信息
  batches: RequireGoodsBatch[] = []
  // 辅料信息
  accs: RequireGoodsAcc[] = []
}
