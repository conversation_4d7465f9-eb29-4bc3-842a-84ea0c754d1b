/*
 * @Author: weish<PERSON>
 * @Date: 2024-04-24 15:19:19
 * @LastEditTime: 2024-06-27 10:43:48
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: \soa\src\model\requireApply\AppRequireApplyLineDTO.ts
 * 记得注释
 */
import AppRequireApplyBillGoodsQueryDTO from './AppRequireApplyBillGoodsQueryDTO'
import Sort from './Sort'
import AppRequireApplyAccLineDTO from './draft/AppRequireApplyAccLineDTO'
import AppRequireApplySubBaseLineDTO from './draft/AppRequireApplySubBaseLineDTO'
import rand from './rand'

export default class AppRequireApplyLineDTO {
  // 数据标识
  billId: string = ''
  // 行号
  lineNo: number = 0
  // 商品
  goods: AppRequireApplyBillGoodsQueryDTO = new AppRequireApplyBillGoodsQueryDTO()
  // 面板分类大类Uuid
  categoryUuid: Nullable<string> = null
  // 面板分类大类代码
  categoryCode: Nullable<string> = null
  // 面板分类大类名称
  categoryName: Nullable<string> = null
  // 建议数量
  suggestQty: Nullable<number> = null
  // 已要货数量
  requiredQty: Nullable<number> = null
  // 数量
  qty: number = 0
  // 金额
  total: number = 0
  // 规格数量
  qpcQty: string = ''
  // 叫货原因代码
  reasonCode: Nullable<string> = null
  // 叫货原因名称
  reasonName: Nullable<string> = null
  // 备注
  note: Nullable<string> = null
  // 类别
  sort: Nullable<Sort> = null
  // 品牌
  brand: Nullable<rand> = null
  // 是否散货
  isDisp: Nullable<boolean> = null
  // 审批数量
  approvalQty: Nullable<number> = null
  // 审批金额
  approvalTotal: Nullable<number> = null
  // 审批规格数量
  approvalQpcQty: Nullable<string> = null
  // 确认数量
  confirmedQty: Nullable<number> = null
  // 确认金额
  confirmedTotal: Nullable<number> = null
  // 确认规格数量
  confirmedQpcQty: Nullable<string> = null
  // 来源类型，0-自主叫货，1-建议叫货
  srcType: Nullable<number> = null
  // 上架备注
  shelveNote: Nullable<string> = null
  // 是否限量,0-否，1-是
  isLimitQty: Nullable<number> = null
  // 活动标识
  activityId: Nullable<string> = null
  // 商品类型,0-普通商品，4-爆品
  goodsType: Nullable<number> = null
  // 方案编码
  schemeNo: Nullable<string> = null
  // 是否含辅料,0-否，1-是
  hasAccessory: Nullable<number> = null
  // 辅料金额
  accTotal: Nullable<number> = null
  // 辅料审批金额
  approvalAccTotal: Nullable<number> = null
  // 溯源码管理：0-否；1-是
  useTraceCode: Nullable<number> = null
  // 溯源码规则：0-标准；1-按肉品；2-酒类源码；3-唯一码
  traceCodeRule: Nullable<number> = null
  // 必订品：0-否；1-是
  reqOrd: Nullable<number> = null
  // 订货倍数
  ordMultiple: Nullable<number> = null
  // 要货下限
  lowOrd: Nullable<number> = null
  // 叫货下限控制
  lowOrdCtrl: Nullable<number> = null
  // 要货上限
  highOrd: Nullable<number> = null
  // 叫货上限控制
  highOrdCtrl: Nullable<number> = null
  // 来源行号
  srcLineNo: Nullable<number> = null
  // 辅料数量
  accQty: Nullable<number> = null
  // 辅料规格数量
  accQpcQty: Nullable<number> = null
  // 辅料审批数量
  approvalAccQty: Nullable<number> = null
  // 辅料审批规格数量
  approvalAccQpcQty: Nullable<number> = null
  // 商品合计数量
  goodsAggQty: Nullable<number> = null
  // 商品合计审批数量
  approvalGoodsAggQty: Nullable<number> = null
  // 辅料明细列表
  accLines: AppRequireApplyAccLineDTO[] = []
  // 子明细
  subLines: AppRequireApplySubBaseLineDTO[] = []
}
