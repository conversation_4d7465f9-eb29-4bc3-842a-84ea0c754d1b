<template>
  <view class="sale-list-contain">
    <view class="list-title" :class="{ 'title-sticky': isSticky }">
      <view class="name">
        {{ describe }}品名/代码/条码/
        <text v-if="showPrice">{{ title }}</text>
        /规格
      </view>
      <view class="qty">{{ describe }}数量</view>
      <view class="amount" v-if="showPrice">{{ total }}金额(元)</view>
    </view>
    <view class="divide"></view>
    <view class="list" v-for="(line, index) in lines" :key="index" :class="{ 'list-none-border': index == lines.length - 1 }">
      <view class="sku-sale">
        <view class="sku-left">
          <image lazy-load class="sku-img" :src="skuImg(line)" @click.stop="handlePreviewImg(line)" />
          <view class="info__scale">
            <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
          </view>
        </view>
        <view>
          <view class="sku-name">
            <text class="name">{{ line.goods.name | empty }}</text>
          </view>
          <view class="sku-code">
            <view class="goods-tag sku-tag" v-if="line.isDisp">散称</view>
            <view class="info-tag sku-tag">{{ line.goods.code | empty }}</view>
            <view class="info-tag sku-tag">{{ line.goods.inputCode | empty }}</view>
            <view class="info-tag sku-tag" v-if="showPrice">￥{{ line.goods.price | fmt | amountView }}/{{ line.goods.munit | empty }}</view>
          </view>
        </view>
      </view>
      <view class="sku-info">
        <text class="qpc-detail">规格：{{ line.goods.qpcStr | empty }}</text>
        <text class="qty price">
          <text class="qty-number">{{ line.qpcQty || (line.qty / line.goods.qpc) | fmt('0.0000') }}</text>
        </text>
        <text class="amount price" v-if="showPrice">{{ line.total | amountView }}</text>
      </view>
    </view>
  </view>
</template>
<script lang="ts" src="./SaleList.ts"></script>
<style lang="scss" scoped>
.sale-list-contain {
  width: 750rpx;
  padding: 0 $base-padding;
  background: #ffffff;
  box-sizing: border-box;
  border-radius: 12rpx;
  position: relative;
  .list-title {
    position: relative;
    width: 100%;
    height: 76rpx;
    display: flex;
    box-sizing: border-box;
    align-items: center;
    justify-content: space-between;
    font-size: 26rpx;
    color: $font-color-darklight;
    background: #ffffff;
  }
  .divide {
    left: 0;
    width: 750rpx;
    position: absolute;
    top: 76rpx;
    border-bottom: $list-border-bottom;
  }

  .name {
    flex: 2.5;
  }
  .qty {
    flex: 1;
    text-align: right;
  }
  .amount {
    flex: 1;
    text-align: right;
  }

  .title-sticky {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    left: 0;
  }

  .list {
    padding: $base-padding 0;
    border-bottom: $list-border-bottom;
    .sku-sale {
      display: flex;
    }
    .sku-left {
      position: relative;
      width: 120rpx;
      height: 120rpx;
      flex: 0 0 auto;
      margin-right: 16rpx;

      .sku-img {
        width: 120rpx;
        height: 120rpx;
      }
      .info__scale {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 24rpx;
        height: 24rpx;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 8rpx 0px 8rpx 0rpx;
        text-align: center;
        @include flex();
        &-img {
          width: 16rpx;
          height: 16rpx;
        }
      }
    }
    .sku-name {
      /*height: 44rpx;*/
      line-height: 44rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: $color-text-primary;
      overflow: hidden;
      text-overflow: ellipsis;
      /*white-space: nowrap;*/
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      .code {
        font-size: 26rpx;
        margin-right: 10rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(40, 44, 52, 1);
      }
      .qty {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #585a5e;
      }
    }
    .sku-code {
      font-size: 30rpx;
      line-height: 40rpx;
    }
    .sku-tag {
      margin-top: 8rpx;
    }
    .sku-info {
      line-height: 40rpx;
      display: flex;
      align-items: center;
      margin-top: 10rpx;
      justify-content: space-between;
      color: #585a5e;
      font-size: 26rpx;
      .code-name {
        flex: 2;
        max-width: 319rpx;
        @include ellipsis();
        .reasonName {
          color: #585a5e;
        }
      }
      .qty {
        box-sizing: border-box;
        padding-right: 10rpx;
        flex: 1;
        text-align: right;
      }
      .amount {
        flex: 1;
        text-align: right;
      }
      .num,
      .total {
        color: #ff8800;
      }
      .price {
        font-size: 26rpx;
        color: rgba(88, 90, 94, 1);
      }

      .amount {
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: bold;
        color: #ff8800;
      }
      .qty-number {
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #585a5e;
      }
      .qpc-detail {
        flex: 2;
        margin-left: 8rpx;
        font-size: 24rpx;
        color: rgba(148, 150, 154, 1);
        line-height: 40rpx;
      }
    }
  }
  .list-none-border {
    border: none;
  }
  .view-more {
    width: 100%;
    height: 88rpx;
    font-size: 32rpx;
    font-weight: 400;
    color: rgba(88, 90, 94, 1);
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    .arrow {
      width: 14rpx;
      height: 14rpx;
      display: inline-block;
      border-top: 1px solid #94969a;
      border-right: 1px solid #94969a;
      transform: rotate(45deg);
      margin-left: 20rpx;
    }
  }
}
</style>
