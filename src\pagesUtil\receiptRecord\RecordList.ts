/*
 * @Author: weisheng
 * @Date: 2024-09-26 16:19:34
 * @LastEditTime: 2025-03-14 16:45:33
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: \soa\src\pagesUtil\receiptRecord\RecordList.ts
 * 记得注释
 */
import { Component, Vue } from 'vue-property-decorator'
import ReceiptApiV2 from '@/network/receipt/ReceiptApiV2'
import AppReceiptDTO from '@/model/receipt/default/AppReceiptDTO'
import AdjustFeedback from './cmp/AdjustFeedback.vue'
import AppReceiptRecordApi from '@/network/AppReceiptRecordApi/AppReceiptRecordApi'
import AppReceiptRecordAttachQueryDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordAttachQueryDTO'
import ReceiptRecordSimple from '@/model/receipt/default/ReceiptRecordSimple'
import { State } from 'vuex-class'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'

@Component({
  components: { AdjustFeedback }
})
export default class RecordList extends Vue {
  @State('optionList') optionList: ModuleOption[]

  $refs: any
  detail: AppReceiptDTO = new AppReceiptDTO() // 单据详情
  images: string[] = [] // 外部传入图片
  currentInfo: ReceiptRecordSimple = new ReceiptRecordSimple() // 当前记录

  // 控制收货记录是否支持上传附件，0-否，1-是
  get uploadRecordAttach() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []

    if (moduleConfig.length > 0) {
      return moduleConfig.findIndex((item) => item.options && item.options.uploadRecordAttach === '1') > -1
    }
    return false
  }

  async onLoad(option) {
    if (option && option.id) {
      try {
        this.$showLoading()
        this.detail = await this.getDetail(option.id, 'records')
        this.$hideLoading()
      } catch (error) {
        this.$hideLoading()
        this.$showToast(error.msg)
      }
    }
  }

  /**
   * 编辑箱码
   * @param body
   * @returns
   */
  getDetail(billId: string, fetchParts?: string) {
    return new Promise<AppReceiptDTO>((resolve, reject) => {
      ReceiptApiV2.get(billId, fetchParts)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  handleNavDtl(billId: string) {
    this.$Router.push({
      name: 'receiptRecordDetail',
      params: {
        id: billId,
        receiptBillId: this.detail.billId
      }
    })
  }

  // 查看附件
  viewFile(item: ReceiptRecordSimple) {
    this.currentInfo = item
    AppReceiptRecordApi.listAttach({ id: item.billId })
      .then((resp) => {
        this.$hideLoading()
        this.images = []
        const data: AppReceiptRecordAttachQueryDTO[] = resp.data || []
        data.forEach((item: AppReceiptRecordAttachQueryDTO) => {
          this.images.push(item.fileUrl)
        })
        this.$refs.feedback.open()
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast(error.msg)
      })
  }

  // 查看附件关闭
  doCloseFeedback() {
    this.$refs.feedback.close()
  }
}
