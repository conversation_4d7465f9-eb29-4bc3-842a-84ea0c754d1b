/*
 * @Author: 徐庆凯
 * @Date: 2020-11-14 11:31:29
 * @LastEditTime: 2025-05-09 14:28:37
 * @LastEditors: weisheng
 * @Description: 应用树
 * @FilePath: /soa/src/utils/AppTree.ts
 * @记得注释
 */

import { Menu, MenuChild } from '@/model/base/Menu'
import config from '@/config'
import { Permission } from '@/model/user/Permission'
import PermissionMgr from '@/mgr/PermissionMgr'
import store from '@/store'

/**
 * 获取图标路径
 * @param iconName 图标名称，如ic_dinghuo.png
 * @param module 模块名称，如soa、jyzs、lyzs
 * @returns 图标路径
 */
function getIconSrc(iconName: string, module: 'soa' | 'lyzs' | 'jyzs' = 'soa') {
  let iconSrc = ''

  // #ifdef APP-PLUS
  try {
    // APP环境下尝试使用本地图片
    iconSrc = require(`@/static/app/icon/${module}/${iconName}`)
  } catch (error) {
    // 如果本地加载失败，使用线上URL
    iconSrc = config.sourceUrl + `hd_app/${module}/${iconName}?x-oss-process=image/quality,q_60`
  }
  // #endif

  // #ifndef APP-PLUS
  // 非APP环境使用线上URL
  iconSrc = config.sourceUrl + `hd_app/${module}/${iconName}?x-oss-process=image/quality,q_60`
  // #endif

  return iconSrc
}

export default class AppTree {
  static isAndroid() {
    let isAndroid: boolean = false
    // #ifdef APP-PLUS
    isAndroid = uni.getSystemInfoSync().platform === 'android'
    // #endif
    return isAndroid
  }

  static appList: Array<Menu> = [
    {
      name: '订货管理',
      showInShop: true,
      children: [
        {
          name: '订货', // 标准订货
          showInHome: true,
          icon: getIconSrc('ic_dinghuo.png'),
          activeIcon: getIconSrc('ic_dinghuo.png'),
          url: '/pagesSE/requireGoods/RequireGoodsList',
          permission: Permission.requireApplyView,
          route: 'requireGoodsList'
        },
        {
          name: '预报货', // 标准
          showInHome: true,
          icon: getIconSrc('ic_yubaohuo.png'),
          activeIcon: getIconSrc('ic_yubaohuo.png'),
          url: '/pagesSC/preRequireGoods/PreRequireGoodsList',
          permission: Permission.preOrderView,
          route: 'preRequireGoodsList'
        },
        {
          name: '订货', // 独立订货
          showInHome: true,
          icon: getIconSrc('ic_dinghuo.png'),
          activeIcon: getIconSrc('ic_dinghuo.png'),
          url: '/pagesSE/requireGoodsCdh/RequireGoodsList',
          permission: Permission.requireOrderView,
          route: 'requireGoodsListCdh'
        },
        {
          name: '订货活动',
          showInHome: true,
          icon: getIconSrc('ic_dinghuohuodong.png'),
          activeIcon: getIconSrc('ic_dinghuohuodong.png'),
          url: '/pagesSB/orderExplosiveActivity/OrderActivity',
          permission: Permission.orderExplosiveView,
          route: 'OrderActivity'
        },
        {
          name: '预订货活动',
          showInHome: true,
          icon: getIconSrc('ic_yudinghuohuodong.png'),
          activeIcon: getIconSrc('ic_yudinghuohuodong.png'),
          url: '/pagesSE/preOrderActivity/PreOrderActivityList',
          permission: Permission.preOrderActivityView,
          route: 'preOrderActivityList'
        },
        {
          name: '预订货订单',
          showInHome: true,
          icon: getIconSrc('ic_yudinghuodingdan.png'),
          activeIcon: getIconSrc('ic_yudinghuodingdan.png'),
          url: '/pagesSE/preGoodsOrder/PreGoodsOrderList',
          permission: Permission.preOrderOrderView,
          route: 'preGoodsOrderList'
        },
        {
          name: '价签申请',
          showInHome: true,
          icon: getIconSrc('ic_jiaqianshenqing.png'),
          activeIcon: getIconSrc('ic_jiaqianshenqing.png'),
          url: '/pagesSB/tallyApply/TallyApplyList',
          permission: Permission.TallyApplyView,
          route: 'TallyApplyList'
        }
      ]
    },
    {
      name: '收退货管理',
      showInShop: true,
      children: [
        {
          name: '收货',
          showInHome: true,
          icon: getIconSrc('ic_shouhuo.png'),
          activeIcon: getIconSrc('ic_shouhuo.png'),
          url: '/pagesSF/receipt/ReceiptList',
          permission: Permission.receiptView,
          route: 'receiptList'
        },
        {
          name: '直送收货', //直送收货
          showInHome: !store.state.bigDataTenant,
          icon: getIconSrc('ic_zhisongshouhuo.png'),
          activeIcon: getIconSrc('ic_zhisongshouhuo.png'),
          url: '/pagesSA/supplierDirect/SupplierDirectList',
          permission: Permission.directReceiptView,
          route: 'supplierDirectList'
        },
        {
          name: '门店自采', //门店自采
          showInHome: false,
          icon: getIconSrc('ic_zicai.png'),
          activeIcon: getIconSrc('ic_zicai.png'),
          url: '/pagesSA/selfPick/SelfPickList',
          permission: Permission.selfPurchaseView,
          route: 'selfPickList'
        },
        {
          name: '收货差异',
          showInHome: !store.state.bigDataTenant,
          icon: getIconSrc('ic_shouhuochayi.png'),
          activeIcon: getIconSrc('ic_shouhuochayi.png'),
          url: '/pagesShopManage/receiptDiff/ReceiptDiffList',
          permission: Permission.receiptDiffView,
          route: 'receiptDiffList'
        },

        {
          name: '退货申请',
          showInHome: true,
          icon: getIconSrc('ic_tuihuoshenqing.png'),
          activeIcon: getIconSrc('ic_tuihuoshenqing.png'),
          url: '/pagesShopManage/returnApply/ReturnApplyList',
          permission: Permission.returnApplyView,
          route: 'returnApplyList'
        },
        {
          name: '直送退货', //直送退货
          showInHome: !store.state.bigDataTenant,
          icon: getIconSrc('ic_zhisongtuihuo.png'),
          activeIcon: getIconSrc('ic_zhisongtuihuo.png'),
          url: '/pagesSA/supplierDirectBck/SupplierDirectBckList',
          permission: Permission.directBckView,
          route: 'supplierDirectBckList'
        },

        {
          name: '退仓',
          showInHome: !store.state.bigDataTenant,
          icon: getIconSrc('ic_tuicang.png'),
          activeIcon: getIconSrc('ic_tuicang.png'),
          url: '/pagesShopManage/stkOutBck/StkOutBckList',
          permission: Permission.stkOutBckView,
          route: 'stkOutBckList'
        },
        {
          name: '退供应商',
          showInHome: !store.state.bigDataTenant,
          icon: getIconSrc('ic_tuigongyingshang.png'),
          activeIcon: getIconSrc('ic_tuigongyingshang.png'),
          url: '/pagesShopManage/dirAlcBck/DirAlcBckList',
          permission: Permission.dirAlcBckView,
          route: 'dirAlcBckList'
        },
        {
          name: '退公司',
          showInHome: false,
          icon: getIconSrc('ic_tuigongsi.png'),
          activeIcon: getIconSrc('ic_tuigongsi.png'),
          url: '/pagesSB/croOrgSaleBck/CroOrgSaleBckList',
          permission: Permission.croOrgSaleBckView,
          route: 'CroOrgSaleBckList'
        },
        {
          name: '质量反馈',
          showInHome: !store.state.bigDataTenant,
          icon: getIconSrc('ic_zhiliangfankui.png'),
          activeIcon: getIconSrc('ic_zhiliangfankui.png'),
          url: '/pagesMkh/aftermarket/QtyFeedBack',
          permission: Permission.feedbackView,
          route: 'qtyFeedBack'
        }
      ]
    },
    {
      name: '库存管理',
      showInShop: true,
      children: [
        {
          name: '盘点',
          showInHome: true,
          icon: getIconSrc('ic_pandian.png'),
          activeIcon: getIconSrc('ic_pandian.png'),
          url: '/pagesSB/check/CheckPlanList',
          permission: Permission.checkView,
          route: 'CheckPlanList'
        },
        {
          name: '加工',
          showInHome: false,
          icon: getIconSrc('ic_jiagong.png'),
          activeIcon: getIconSrc('ic_jiagong.png'),
          url: '/pagesSC/processBill/ProcessBillList',
          permission: Permission.processBillView,
          route: 'processBillList'
        },
        {
          name: '调拨申请',
          showInHome: !store.state.bigDataTenant,
          icon: getIconSrc('ic_diaoboshenqing.png'),
          activeIcon: getIconSrc('ic_diaoboshenqing.png'),
          url: '/pagesShopManage/invXFApply/InvXFApplyList',
          permission: Permission.invxfApplyView,
          route: 'invXFApplyList'
        },
        {
          name: '调拨',
          showInHome: true,
          icon: getIconSrc('ic_diaobo.png'),
          activeIcon: getIconSrc('ic_diaobo.png'),
          url: '/pagesSA/invXF/InvXFList',
          permission: Permission.invxfView,
          route: 'invXFList'
        },
        {
          name: '报损',
          showInHome: true,
          icon: getIconSrc('ic_baosun.png'),
          activeIcon: getIconSrc('ic_baosun.png'),
          url: '/pagesShopManage/lossApply/LossApplyList',
          permission: Permission.lossApplyView,
          route: 'lossApplyList'
        },
        {
          name: '报溢',
          showInHome: true,
          icon: getIconSrc('ic_baoyi.png'),
          activeIcon: getIconSrc('ic_baoyi.png'),
          url: '/pagesShopManage/overApply/OverApplyList',
          permission: Permission.overApplyView,
          route: 'overApplyList'
        },
        {
          name: '商品查询',
          showInHome: true,
          icon: getIconSrc('ic_shangpinchaxun.png'),
          activeIcon: getIconSrc('ic_shangpinchaxun.png'),
          url: '/pagesOther/goodsSearch/GoodsSearch',
          permission: Permission.goodsSearchView,
          route: 'GoodsSearch'
        },
        {
          name: '周边门店库存',
          showInHome: true,
          icon: getIconSrc('ic_zhoubianmendiankucun.png'),
          activeIcon: getIconSrc('ic_zhoubianmendiankucun.png'),
          url: '/pagesSD/InventoryNearbyStores/InventoryNearbyStoresList',
          permission: Permission.circumStoreInvView,
          route: 'inventoryNearbyStoresList'
        },
        {
          name: '库存上下限调整',
          showInHome: false,
          icon: getIconSrc('ic_kucunshangxiaxiantiaozheng.png'),
          activeIcon: getIconSrc('ic_kucunshangxiaxiantiaozheng.png'),
          url: '/pagesSB/invLmtAdj/InvLmtAdjList',
          permission: Permission.invLmtAdjView,
          route: 'invLmtAdjList'
        },
        {
          name: '线上商品',
          showInHome: true,
          icon: getIconSrc('ic_xianshangshangpin.png'),
          activeIcon: getIconSrc('ic_xianshangshangpin.png'),
          url: '/pagesSA/platformShopSku/PlatformShopSku',
          permission: Permission.platfromSkuView,
          route: 'platformShopSku'
        },
        {
          name: '商品效期',
          showInHome: true,
          icon: getIconSrc('ic_shangpinxiaoqi.png'),
          activeIcon: getIconSrc('ic_shangpinxiaoqi.png'),
          url: '/pagesSC/goodsValidDate/gvdList/gvdList',
          permission: Permission.SkuValidDateView,
          route: 'gvdList'
        },
        {
          name: '自主盘点',
          showInHome: true,
          icon: getIconSrc('ic_zizhupandian.png'),
          activeIcon: getIconSrc('ic_zizhupandian.png'),
          url: '/pagesSD/cusInvCheck/CusInvCheckList',
          permission: Permission.cusInvCheckView,
          route: 'cusInvCheckList'
        },
        {
          name: '批次号调整',
          showInHome: true,
          icon: getIconSrc('ic_picihaotiaozheng.png'),
          activeIcon: getIconSrc('ic_picihaotiaozheng.png'),
          url: '/pagesSD/AppBatchAdjApply/AppBatchAdjApplyEdit',
          permission: Permission.batchAdjustApplyView,
          route: 'AppBatchAdjApplyEdit'
        },
        {
          name: '配货差异申请',
          showInHome: true,
          icon: getIconSrc('ic_peihuochayishenqing.png'),
          activeIcon: getIconSrc('ic_peihuochayishenqing.png'),
          url: '/pagesSD/ReceiptDiffApply/ReceiptDiffApplyList',
          permission: Permission.receiptDiffApplyView,
          route: 'ReceiptDiffApplyList'
        },
        {
          name: '店务商品查询',
          showInHome: false,
          icon: getIconSrc('ic_dianwushangpinchaxun.png'),
          activeIcon: getIconSrc('ic_dianwushangpinchaxun.png'),
          url: '/pagesSD/gdQuery/GdQueryPanel',
          route: 'gdQueryPanel',
          permission: Permission.sosSkuQueryView
        },
        {
          name: '商品淘汰',
          showInHome: false,
          icon: getIconSrc('ic_shangpintaotai.png'),
          activeIcon: getIconSrc('ic_shangpintaotai.png'),
          url: '/pagesUtil/gdeLimination/GdeLiminationList',
          permission: Permission.gdeLiminationView,
          route: 'gdeLiminationList'
        },
        {
          name: '门店盘点',
          showInHome: true,
          icon: getIconSrc('ic_mdpandian.png'),
          activeIcon: getIconSrc('ic_mdpandian.png'),
          url: 'pagesSF/hlxsCusInvCheck/CusInvCheckList',
          permission: Permission.taskCheckView,
          route: 'hlxsCusInvCheckList'
        },
        {
          name: '门店盘点任务',
          showInHome: true,
          icon: getIconSrc('ic_mdpandianrenwu.png'),
          activeIcon: getIconSrc('ic_mdpandianrenwu.png'),
          url: 'pagesSF/hlxsCusInvCheckTask/TaskList',
          permission: Permission.checkTaskView,
          route: 'hlxsCusInvCheckTaskTaskList'
        },
        {
          name: '特殊品管理',
          showInHome: true,
          icon: config.sourceUrl + 'icon/ic_teshupindengji.png?x-oss-process=image/quality,q_60',
          activeIcon: config.sourceUrl + 'icon/ic_teshupindengji.png?x-oss-process=image/quality,q_60',
          url: 'pagesSG/specGdRegist/SpecGdRegistList',
          permission: Permission.specGdRegistView,
          route: 'specGdRegistList'
        },
        {
          name: '特殊品库存查询',
          showInHome: true,
          icon: config.sourceUrl + 'icon/ic_teshupinkucunchaxun.png?x-oss-process=image/quality,q_60',
          activeIcon: config.sourceUrl + 'icon/ic_teshupinkucunchaxun.png?x-oss-process=image/quality,q_60',
          url: 'pagesSG/specGdRegistInv/SpecGdRegistInv',
          permission: Permission.specGdRegistInvView,
          route: 'specGdRegistInv'
        }
      ]
    },
    {
      name: '营销管理',
      showInShop: true,
      children: [
        {
          name: '营销玩法',
          showInHome: !store.state.bigDataTenant,
          icon: getIconSrc('ic_yingxiaowanfa.png'),
          activeIcon: getIconSrc('ic_yingxiaowanfa.png'),
          url: '/pagesMkh/marketPlay/MarketPlayList',
          permission: Permission.promRuleView,
          route: 'marketPlayList'
        },
        {
          name: '价格带',
          showInHome: true,
          icon: getIconSrc('ic_jiagedai.png'),
          activeIcon: getIconSrc('ic_jiagedai.png'),
          url: '/pagesSB/shopSkuTag/ShopSkuList',
          permission: Permission.shopSkuTagView,
          route: 'ShopSku'
        },
        {
          name: '试吃',
          showInHome: true,
          icon: getIconSrc('ic_shichi.png'),
          activeIcon: getIconSrc('ic_shichi.png'),
          url: '/pagesSD/AppForeTaste/AppForeTasteTest',
          permission: Permission.foreTasteView,
          route: 'appForeTasteTest'
        },
        {
          name: '调价申请',
          showInHome: true,
          icon: getIconSrc('ic_tiaojiashenqing.png'),
          activeIcon: getIconSrc('ic_tiaojiashenqing.png'),
          url: '/pagesSD/priceAdjApply/PriceAdjApplyList',
          permission: Permission.priceAdjApplyView,
          route: 'priceAdjApplyList'
        },
        {
          name: '标价签打印',
          showInHome: true,
          icon: getIconSrc('ic_biaoqiandayin.png'),
          activeIcon: getIconSrc('ic_biaoqiandayin.png'),
          url: AppTree.isAndroid() ? '/pagesSD/labelPrinting/LabelPrintingListAndroid' : '/pagesSD/labelPrinting/LabelPrintingList',
          permission: Permission.labelPrintingView,
          route: AppTree.isAndroid() ? 'labelPrintingListAndroid' : 'labelPrintingList'
        },
        {
          name: '单品打折',
          showInHome: true,
          icon: getIconSrc('ic_danpindazhe.png'),
          activeIcon: getIconSrc('ic_danpindazhe.png'),
          url: '/pagesSE/appCouponCode/AppCouponCodeList',
          permission: Permission.AppCouponCodeView,
          route: 'AppCouponCodeList'
        },
        {
          name: '促销申请',
          showInHome: true,
          icon: config.sourceUrl + 'icon_Perverted/<EMAIL>',
          activeIcon: config.sourceUrl + 'icon_Perverted/<EMAIL>',
          url: '/pagesSG/promApply/PromApplyList',
          permission: Permission.promApplyView,
          route: 'promApplyList'
        }
      ]
    },
    {
      name: '财务管理',
      showInShop: true,
      children: [
        {
          name: '交易流水',
          showInHome: false,
          icon: getIconSrc('ic_jiaoyiliushui.png'),
          activeIcon: getIconSrc('ic_jiaoyiliushui.png'),
          url: '/pagesSB/billManage/BillList',
          permission: Permission.billManageView,
          route: 'BillList'
        },
        {
          name: '账户流水',
          showInHome: false,
          icon: getIconSrc('ic_zhanghuliushui.png'),
          activeIcon: getIconSrc('ic_zhanghuliushui.png'),
          url: '/pagesSB/fundAccount/FundAccountList',
          permission: Permission.fundAccountView,
          route: 'FundAccountList'
        },
        {
          name: '利润核算',
          showInHome: false,
          icon: getIconSrc('ic_jiamengfeiyong.png'),
          activeIcon: getIconSrc('ic_jiamengfeiyong.png'),
          url: '/pagesSA/rechgBook/RechgBookList',
          permission: Permission.rechgBookView,
          route: 'rechgBookList'
        },
        {
          name: '门店对账',
          showInHome: false,
          icon: getIconSrc('ic_zhanghuliushui.png'),
          activeIcon: getIconSrc('ic_zhanghuliushui.png'),
          url: '/pagesOther/storeCheckBill/StoreCheckBillList',
          permission: Permission.storeConciliationView,
          route: 'storeCheckBillList'
        }
      ]
    },
    {
      name: '履约管理',
      showInShop: true,
      children: [
        {
          name: '订单履约',
          showInHome: true,
          icon: getIconSrc('ic_dingdanlvyue.png', 'lyzs'),
          activeIcon: getIconSrc('ic_dingdanlvyue.png', 'lyzs'),
          url: '/pagesLyzs/orderPerformance/OrderPerformanceList',
          route: 'orderPerformanceList',
          permission: [Permission.orderFulfillmentPickingView, Permission.orderFulfillmentChargebacksView]
        },
        {
          name: '商品管理',
          showInHome: true,
          icon: getIconSrc('ic_shangpinguanli.png', 'lyzs'),
          activeIcon: getIconSrc('ic_shangpinguanli.png', 'lyzs'),
          url: '/pagesLyzs/skuList/SkuList',
          route: 'skuList',
          permission: Permission.merchandiseManagementView
        },
        {
          name: '订单评价',
          showInHome: true,
          icon: getIconSrc('ic_dingdanpingjia.png', 'lyzs'),
          activeIcon: getIconSrc('ic_dingdanpingjia.png', 'lyzs'),
          url: '/pagesLyzs/evaluation/OrderEvaluation',
          route: 'orderEvaluation',
          permission: Permission.orderReviewsView
        },
        {
          name: '客服消息',
          showInHome: true,
          icon: getIconSrc('ic_kefuxiaoxi.png', 'lyzs'),
          activeIcon: getIconSrc('ic_kefuxiaoxi.png', 'lyzs'),
          url: '/pagesLyzs/customerService/CustomerService',
          permission: Permission.customerMessagesView,
          route: 'customerService'
        },
        {
          name: '拣货任务',
          showInHome: true,
          icon: getIconSrc('ic_jianhuorenwu.png', 'lyzs'),
          activeIcon: getIconSrc('ic_jianhuorenwu.png', 'lyzs'),
          url: '/pagesLyzs/pickingTask/pickingTask',
          permission: Permission.pickingTasksView,
          route: 'pickingTask'
        },
        {
          name: '摊位商品',
          showInHome: true,
          icon: getIconSrc('ic_tanweishangpin.png', 'lyzs'),
          activeIcon: getIconSrc('ic_tanweishangpin.png', 'lyzs'),
          url: '/pagesLyzs/pickingGoods/pickingGoods',
          permission: Permission.boothSkuView,
          route: 'pickingGoods'
        },
        {
          name: '平台商品',
          showInHome: true,
          icon: getIconSrc('ic_pingtaishangpin.png', 'lyzs'),
          activeIcon: getIconSrc('ic_pingtaishangpin.png', 'lyzs'),
          url: '/pagesLyzs/platformShop/PlatformShopList',
          permission: Permission.platformShopSkuView,
          route: 'platformShopList'
        },
        {
          name: '摊位账单',
          showInHome: true,
          icon: getIconSrc('ic_tanweizhangdan.png', 'lyzs'),
          activeIcon: getIconSrc('ic_tanweizhangdan.png', 'lyzs'),
          url: '/pagesLyzs/pickingBill/pickingBill',
          permission: Permission.pickBillsView,
          route: 'pickingBill'
        },
        {
          name: '商品货位',
          showInHome: true,
          icon: getIconSrc('ic_shangpinhuowei.png', 'lyzs'),
          activeIcon: getIconSrc('ic_shangpinhuowei.png', 'lyzs'),
          url: '/pagesLyzs/productLocation/ProductLocationList',
          permission: Permission.productLocationView,
          route: 'productLocationList'
        },
        // #ifndef APP-PLUS
        {
          name: '集采集配',
          showInHome: true,
          icon: getIconSrc('ic_jicaijipei.png', 'lyzs'),
          activeIcon: getIconSrc('ic_jicaijipei.png', 'lyzs'),
          url: '/pagesLyzs/purchase/PurchaseList',
          route: 'purchaseList',
          permission: '' as Permission
        },
        // #endif
        {
          name: '门店拣货',
          showInHome: true,
          icon: getIconSrc('ic_mendianjianhuo.png', 'lyzs'),
          activeIcon: getIconSrc('ic_mendianjianhuo.png', 'lyzs'),
          url: '/pagesLyzs/storePicking/StorePicking',
          permission: Permission.pickTaskView,
          route: 'storePicking'
        }
      ]
    },
    {
      name: '经营助手',
      showInData: true,
      children: [
        {
          name: '概览',
          showInHome: true,
          icon: getIconSrc('ic_gailan.png', 'jyzs'),
          activeIcon: getIconSrc('ic_gailan.png', 'jyzs'),
          url: '/pagePortalSub/azkWeb/AzkWeb',
          params: { path: '/pages/index/PeriodOverview' },
          permission: Permission.MODULE_INV,
          route: 'azkWebPortal'
        },
        {
          name: '门店',
          showInHome: true,
          icon: getIconSrc('ic_mendian.png', 'jyzs'),
          activeIcon: getIconSrc('ic_mendian.png', 'jyzs'),
          url: '/pagePortalSub/azkWeb/AzkWeb',
          params: { path: '/pages/index/StoreLogisticsDetail' },
          permission: Permission.MODULE_STORE,
          route: 'azkWebPortal'
        },
        {
          name: '每日',
          showInHome: true,
          icon: getIconSrc('ic_meiri.png', 'jyzs'),
          activeIcon: getIconSrc('ic_meiri.png', 'jyzs'),
          url: '/pagePortalSub/azkWeb/AzkWeb',
          params: { path: '/pages/index/DayLogisticsDetail' },
          permission: Permission.MODULE_DAILY,
          route: 'azkWebPortal'
        },
        {
          name: '商品',
          showInHome: true,
          icon: getIconSrc('ic_shangpin.png', 'jyzs'),
          activeIcon: getIconSrc('ic_shangpin.png', 'jyzs'),
          url: '/pagePortalSub/azkWeb/AzkWeb',
          params: { path: '/pages/index/GoodsLogisticsDetail' },
          permission: Permission.MODULE_GOODS,
          route: 'azkWebPortal'
        },
        {
          name: '分类',
          showInHome: true,
          icon: getIconSrc('ic_fenlei.png', 'jyzs'),
          activeIcon: getIconSrc('ic_fenlei.png', 'jyzs'),
          url: '/pagePortalSub/azkWeb/AzkWeb',
          params: { path: '/pages/index/CategoryLogisticsDetail' },
          permission: Permission.MODULE_CATEGORY,
          route: 'azkWebPortal'
        },
        {
          name: '库存',
          showInHome: true,
          icon: getIconSrc('ic_kucun.png', 'jyzs'),
          activeIcon: getIconSrc('ic_kucun.png', 'jyzs'),
          url: '/pagePortalSub/azkWeb/AzkWeb',
          params: { path: '/pages/index/InventoryLogisticsDetail' },
          permission: Permission.MODULE_INV,
          route: 'azkWebPortal'
        },
        {
          name: '支付',
          showInHome: true,
          icon: getIconSrc('ic_zhifu.png', 'jyzs'),
          activeIcon: getIconSrc('ic_zhifu.png', 'jyzs'),
          url: '/pagePortalSub/azkWeb/AzkWeb',
          params: { path: '/pages/index/PayIndexDetail' },
          permission: Permission.MODULE_PAY,
          route: 'azkWebPortal'
        },
        {
          name: '商品查询',
          showInHome: true,
          icon: getIconSrc('ic_shangpinchaxun.png', 'jyzs'),
          activeIcon: getIconSrc('ic_shangpinchaxun.png', 'jyzs'),
          url: '/pagePortalSub/azkWeb/AzkWeb',
          params: { path: '/pages/index/ItemSearchDetail' },
          permission: Permission.MODULE_GOOD_QUERY,
          route: 'azkWebPortal'
        },
        {
          name: '定制指标',
          showInHome: true,
          icon: getIconSrc('ic_dingzhizhibiao.png', 'jyzs'),
          activeIcon: getIconSrc('ic_dingzhizhibiao.png', 'jyzs'),
          url: '/pagePortalSub/azkWeb/AzkWeb',
          params: { path: '/pages/index/AchieveGoal' },
          permission: Permission.MODULE_CUSTOMIZE_INDEX,
          route: 'azkWebPortal'
        },
        {
          name: '会员',
          showInHome: true,
          icon: getIconSrc('ic_huiyuan.png', 'jyzs'),
          activeIcon: getIconSrc('ic_huiyuan.png', 'jyzs'),
          url: '/pagePortalSub/azkWeb/AzkWeb',
          params: { path: '/pages/index/MoreMemberDetail' },
          permission: Permission.MODULE_MEMBER,
          route: 'azkWebPortal'
        },
        {
          name: '关注',
          showInHome: true,
          icon: getIconSrc('ic_guanzhu.png', 'jyzs'),
          activeIcon: getIconSrc('ic_guanzhu.png', 'jyzs'),
          url: '/pagePortalSub/azkWeb/AzkWeb',
          params: { path: '/pages/index/MyFollow' },
          permission: Permission.MODULE_CONCERN,
          route: 'azkWebPortal'
        }
      ]
    },
    {
      name: '其他',
      showInShop: true,
      children: [
        {
          name: '订单查询',
          showInHome: true,
          icon: getIconSrc('ic_dingdanchaxun.png'),
          activeIcon: getIconSrc('ic_dingdanchaxun.png'),
          url: '/pagesSB/orderSearch/OrderList',
          permission: Permission.orderSearchView,
          route: 'OrderList'
        },
        {
          name: '线路调整',
          showInHome: false,
          icon: getIconSrc('ic_luxiantiaozheng.png'),
          activeIcon: getIconSrc('ic_luxiantiaozheng.png'),
          url: '/pagesSB/lineAdj/LineList',
          permission: Permission.lineAdjView,
          route: 'LineList'
        },
        {
          name: '知识库',
          showInHome: false,
          icon: getIconSrc('ic_zhishiku.png'),
          activeIcon: getIconSrc('ic_zhishiku.png'),
          url: '/pagesTaskSub/knowledgeBase/KnowledgeBaseList',
          permission: Permission.knowledgeBaseView,
          route: 'knowledgeBaseList'
        },
        {
          name: '提成绩效',
          showInHome: false,
          icon: getIconSrc('ic_tichengjixiao.png'),
          activeIcon: getIconSrc('ic_tichengjixiao.png'),
          url: '/pagesTaskSub/commissionPerformance/CommissionPerformance',
          permission: Permission.commissionPerformance,
          route: 'commissionPerformance'
        },
        {
          name: '文件登记',
          showInHome: false,
          icon: getIconSrc('ic_wenjiandengji.png'),
          activeIcon: getIconSrc('ic_wenjiandengji.png'),
          url: '/pagesSC/fileRegist/FileRegistList',
          permission: Permission.fileRegistView,
          route: 'fileRegistList'
        },
        {
          name: '周转筐管理',
          showInHome: false,
          icon: getIconSrc('ic_zhouzhuankuangguanli.png'),
          activeIcon: getIconSrc('ic_zhouzhuankuangguanli.png'),
          url: '/pagesSC/packReceipt/commonPage/SelectModule',
          permission: Permission.packManageView,
          route: 'packSelectModule'
        },
        {
          name: '门店领用',
          showInHome: false,
          icon: getIconSrc('ic_lingyong.png'),
          activeIcon: getIconSrc('ic_lingyong.png'),
          url: '/pagesSC/useApply/UseApplyList',
          permission: Permission.useApplyView,
          route: 'useApplyList'
        },
        {
          name: '异常商品',
          showInHome: false,
          icon: getIconSrc('ic_yichangshangpin.png'),
          activeIcon: getIconSrc('ic_yichangshangpin.png'),
          url: '/pagesOther/goodsExcption/GoodsExcptionMenu',
          permission: Permission.exceptionGoodsView,
          route: 'goodsExcptionMenu'
        },
        {
          name: '门店考评',
          showInHome: true,
          icon: getIconSrc('ic_mendiankaoping.png'),
          activeIcon: getIconSrc('ic_mendiankaoping.png'),
          url: '/pagesSD/shopScore/ShopScore',
          permission: Permission.shopScoreView,
          route: 'shopScore'
        },
        {
          name: '报表',
          showInHome: true,
          icon: getIconSrc('ic_baobiao.png'),
          activeIcon: getIconSrc('ic_baobiao.png'),
          url: '/pagesSA/report/Report',
          permission: Permission.reportView,
          route: 'report'
        },
        {
          name: '重点商品',
          showInHome: true,
          icon: getIconSrc('ic_shangpinchaxun.png'),
          activeIcon: getIconSrc('ic_shangpinchaxun.png'),
          url: '/pagesTaskSub/keyProducts/KeyProducts',
          permission: Permission.skuCheckView,
          route: 'keyProducts'
        },
        {
          name: '软折平台',
          showInHome: true,
          icon: config.sourceUrl + 'icon/ruanzhepingtai.png?x-oss-process=image/quality,q_60',
          activeIcon: config.sourceUrl + 'icon/ruanzhepingtai.png?x-oss-process=image/quality,q_60',
          url: '/pagesUtil/azkWeb/AzkWeb',
          permission: Permission.azkRZPlatformView,
          route: 'azlWeb'
        },
        {
          name: '经营分析',
          showInHome: true,
          icon: config.sourceUrl + 'icon/ic_azk_jyfx.png?x-oss-process=image/quality,q_60',
          activeIcon: config.sourceUrl + 'icon/ic_azk_jyfx.png?x-oss-process=image/quality,q_60',
          url: '/pagesUtil/azkWeb/AzkWeb',
          permission: Permission.azkSJKBView,
          route: 'azlWeb',
          params: {
            type: 'sjkb'
          }
        }
      ]
    }
  ]

  /**
   * 权限拉平
   * @param all 是否全部展示，如全部展示则不判断是否需要展示在首页
   */
  static toFlareout(all: boolean = false) {
    const menuList: MenuChild[] = []

    for (let i = 0; i < this.appList.length; i++) {
      for (let j = 0; j < this.appList[i].children.length; j++) {
        if (all) {
          if (PermissionMgr.hasPermission(this.appList[i].children[j].permission)) {
            this.appList[i].children[j].parent = this.appList[i].name
            menuList.push(this.appList[i].children[j])
          }
        } else if (this.appList[i].children[j].showInHome) {
          if (PermissionMgr.hasPermission(this.appList[i].children[j].permission)) {
            this.appList[i].children[j].parent = this.appList[i].name
            menuList.push(this.appList[i].children[j])
          }
        }
      }
    }

    return menuList
  }

  /**
   * 菜单搜索方法
   * @param keyword 关键词
   */
  static search(keyword: string) {
    const appList = this.toFlareout(true)

    return appList!.filter((item) => {
      return item.name.indexOf(keyword) > -1 && PermissionMgr.hasPermission(item.permission)
    })
  }

  /**
   * 获取权限数
   */
  static getTree() {
    const appList: Array<Menu> = []
    for (let i = 0; i < this.appList.length; i++) {
      const app: Menu = JSON.parse(JSON.stringify(this.appList[i]))
      app.children = []
      for (let j = 0; j < this.appList[i].children.length; j++) {
        if (PermissionMgr.hasPermission(this.appList[i].children[j].permission)) {
          app.children.push(this.appList[i].children[j])
        }
      }
      if (app.children.length > 0) {
        appList.push(app)
      }
    }
    return appList
  }
}
