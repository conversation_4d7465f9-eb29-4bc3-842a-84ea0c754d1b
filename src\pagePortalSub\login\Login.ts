/*
 * @Author: 徐庆凯
 * @Date: 2021-05-25 15:08:34
 * @LastEditTime: 2025-06-19 16:01:44
 * @LastEditors: shikailei
 * @Description: 登录
 * @FilePath: \soa\src\pagePortalSub\login\Login.ts
 * 记得注释
 */
import { Vue, Component, Mixins } from 'vue-property-decorator'
import { Action, Getter, Mutation, State } from 'vuex-class'
import User<PERSON><PERSON> from '@/network/user/UserApi'
import LoginReq from '@/model/user/LoginReq'
import config from '@/config'
import { rsa } from '@/utils/encryption'
import CommonUtil from '@/utils/CommonUtil'
import LoginByDingtalkReq from '@/model/user/LoginByDingtalkReq'
import { debounce } from 'ts-debounce'
import SafeConfigItem from '@/model/user/SafeConfigItem'
import LoginUser from '@/model/user/LoginUser'
import UserInfo from '@/model/user/UserInfo'
import UserInfoPortal from '@/model/portalModel/cache/UserInfo'
import Employee<PERSON>pi from '@/network/employee/EmployeeApi'
import SysConfigApi from '@/network/sysConfig/SysConfigApi'
import packageConfig from '../../../package.json'
import LicenseMixin from '@/common/LicenseMixin'
import FieldsConfigItem from '@/model/sysConfig/FieldsConfigItem'
import FieldList from '@/model/sysConfig/FieldList'
import { CredentialEnum, NextStepEnum } from '@/model/portalModel/login/LoginEnum'
import LoginFormVo from '@/model/portalModel/login/LoginFormVo'
import InputElementVo from '@/model/portalModel/login/InputElementVo'
import LoginResultVo from '@/model/portalModel/login/LoginResultVo'
import SmsCodeApi from '@/network/portal/SmsCodeApi'
import LoginFormApi from '@/network/portal/LoginFormApi'
import CaptchaApi from '@/network/portal/CaptchaApi'
import CredentialVo from '@/model/portalModel/login/CredentialVo'
import TokenMgr from '@/common/TokenMgr'
import SetLoginOrgMixin from '@/mixins/SetLoginOrgMixin'
import LogonUserApi from '@/network/portal/LogonUserApi'
import CUserLogonInfo from '@/model/portalModel/login/CUserLogonInfo'
import GetChildAppConfigMixin from '@/mixins/GetChildAppConfigMixin'
import TenantVo from '@/model/portalModel/tenant/TenantVo'
import LogonUserVo from '@/model/portalModel/user/LogonUserVo'
import CLastLoginVo from '@/model/portalModel/login/ClastLoginVo'
import CAppTenantVO from '@/model/portalModel/appFunction/CAppTenantVO'

import LoginFormApiAndroid from '@/network/portal/LoginFormApiAndroid'

import DateUtil from '@/utils/DateUtil'
const NetworkModule = uni.requireNativePlugin('NetMgr') as any

@Component({
  components: {}
})
export default class Login extends Mixins(LicenseMixin, SetLoginOrgMixin, GetChildAppConfigMixin) {
  @Mutation('userInfo') actionUserInfo // 登录后返回的用户信息
  @Getter('userInfo') getUserInfo // 登录后返回的用户信息
  @State('config') config: SafeConfigItem // 安全策略
  @State('active') active: boolean // 是否已激活
  @Action('config') actionConfig // 提交安全策略
  @Action('isLoginDirect') actionIsLoginDirect // 提交是否免密登录
  @Action('positionList') actionPositionList // 保存用户角色
  @Getter('hasPositionPermission') hasPositionPermission: boolean // 所属岗位是否有访问权限
  @Getter('employeeInfo') employeeInfo: CUserLogonInfo // 员工信息
  @Action('clear') actionClear // 退出登录后，清除登录信息
  @Action('bigDataTenant') actionbigDataTenant // 提交大数据对应租户
  @Action('fieldsConfig') actionFieldsConfig // 将配置权限存储在vuex中
  @Action('enableCheckLicense') actionCheckLicense // 提交允许开始校验license
  @State('currentTenant') currentTenant: any // 保存租户信息
  @State('lyzsTenant') lyzsTenant: any // 保存租户信息
  @State('jyzsTenant') jyzsTenant: any // 保存租户信息
  @State('tenantInfo') tenantInfo: TenantVo // 保存租户信息
  $refs: any
  username: string = '' // 用户名
  password: string = '' // 密码
  credentialEnum = CredentialEnum
  smsSendHint: string = '获取验证码' // 获取验证码提示
  smsSendRT: number = 0 // 可重发送验证吗剩余时间
  loginForm: LoginFormVo = new LoginFormVo()

  currentTab: 'mobile' | 'account' = 'account' // mobile、account
  startTime: any = 0 // 登录开始时间
  endTime: any = 0 // 登录结束时间
  // 是否可登录
  get disabled() {
    if (this.currentTab == 'mobile') {
      if (!this.loginForm.mobileElements || this.loginForm.mobileElements.length === 0) {
        return true
      }
      for (const item of this.loginForm.mobileElements) {
        if (item.inputText === null || item.inputText.trim() === '') {
          return true
        }
      }
    } else if (this.currentTab == 'account') {
      if (!this.loginForm.inputElements || this.loginForm.inputElements.length === 0) {
        return true
      }
      for (const item of this.loginForm.inputElements) {
        if (item.inputText === null || item.inputText.trim() === '') {
          return true
        }
      }
    }
    return false
  }

  // 能否发送验证码
  get enableSendSms() {
    const momber = this.inputElement(CredentialEnum.MOBILE).inputText?.trim()
    return this.smsSendRT == 0 && momber && /^\d{11}$/.test(momber)
  }
  /**
   * 版本号
   */
  get version() {
    return packageConfig.version
  }

  /**
   * 登录按钮是否可点击
   */
  get clickable() {
    return this.username !== '' && this.password !== ''
  }

  // 是否展示自动登录按钮
  get showAutoLogin() {
    if (this.config && this.config.enableDingTalkLogin) {
      return true
    } else {
      return false
    }
  }

  /**
   * 是否展示手机号改密码
   */
  get showMobile() {
    if (this.config && this.config.loginNameLimit === 2) {
      return true
    } else {
      return false
    }
  }
  onLoad(options) {
    this.loadForm()
  }

  onShow() {
    this.doGetConfig()
  }

  inputElement(type: string) {
    const list =
      type == CredentialEnum.MOBILE || type == CredentialEnum.SMSCODE ? this.loginForm.mobileElements || [] : this.loginForm.inputElements || []
    for (const item of list) {
      if (item.credentialType == type) {
        return item || new InputElementVo()
      }
    }
    return new InputElementVo()
  }
  // 已选中的Tab
  selectedTab(type: 'account' | 'mobile') {
    return this.currentTab == type
  }
  // 清空输入行
  next(type: string) {
    switch (type) {
      case CredentialEnum.MOBILE:
        this.snedSms()
        this.$refs.smsCode.requestFocus()
        break
      case CredentialEnum.SMSCODE:
        this.login()
        break
      case CredentialEnum.LOGIN_NAME:
        if (this.inputElement(CredentialEnum.PASSWORD).placeHolder) {
          this.$refs.pwd.requestFocus()
        } else if (this.inputElement(CredentialEnum.CAPTCHA).placeHolder) {
          this.$refs.captcha.requestFocus()
        } else {
          this.login()
        }
        break
      case CredentialEnum.PASSWORD:
        if (this.inputElement(CredentialEnum.CAPTCHA).placeHolder) {
          this.$refs.captcha.requestFocus()
        } else {
          this.login()
        }
        break
      case CredentialEnum.CAPTCHA:
        this.login()
        break
      default:
        break
    }
  }

  // 切换Tab
  onTab(tab: 'mobile' | 'account') {
    this.currentTab = tab
  }
  // 是否显示该Tab
  showTab(type: string) {
    return type == 'mobile' ? this.loginForm.mobileElements.length > 0 : this.loginForm.inputElements.length > 0
  }

  // 发送/重发 手机验证码
  snedSms() {
    if (!this.enableSendSms) {
      return
    }
    this.$showLoading()
    SmsCodeApi.sendForAuthentication(
      this.inputElement(CredentialEnum.MOBILE).inputText || '',
      this.inputElement(CredentialEnum.SMSCODE).accessKey || ''
    )
      .then((resp) => {
        this.$hideLoading()
        this.smsSendRT = 60
        this.smsSendHint = this.smsSendRT + 's后重发'
        const timer = setInterval(() => {
          this.smsSendRT--
          if (this.smsSendRT == 0) {
            this.smsSendHint = '重获验证码'
            clearInterval(timer)
          } else {
            this.smsSendHint = this.smsSendRT + 's后重发'
          }
        }, 1000)
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ title: e.msg, icon: 'error' })
      })
  }

  // 加载登录表单
  loadForm() {
    this.$showLoading({ delayTime: 200 })
    LoginFormApi.load()
      .then((resp) => {
        this.$hideLoading()
        if (resp.data) {
          this.loginForm = resp.data
          if (!this.loginForm.inputElements || this.loginForm.inputElements.length == 0) {
            this.loginForm.inputElements = []
            this.onTab('mobile')
          }
          if (!this.loginForm.mobileElements || this.loginForm.mobileElements.length == 0) {
            this.loginForm.mobileElements = []
            this.onTab('account')
          }
        }
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showModal({
          title: `${error.msg}`,
          confirmText: '重新加载',
          success: (action) => {
            if (action.confirm) {
              const timer = setTimeout((res) => {
                this.loadForm()
                clearTimeout(timer)
              }, 200)
            }
          }
        })
        console.log('', error)
      })
  }

  // 切换图形验证码
  refreshCaptcha() {
    CaptchaApi.generate()
      .then((resp) => {
        if (resp.data) {
          const captchaElement = this.inputElement(CredentialEnum.CAPTCHA)
          captchaElement.imageDataUrl = resp.data.imageDataUrl
          captchaElement.accessKey = resp.data.key
        }
      })
      .catch((e) => {
        this.$showToast({ title: e.msg, icon: 'error' })
      })
  }

  // 使用安卓原生的network请求登录。TODO 待调试
  doLoginAndroid() {
    this.startTime = new Date().getTime()
    const timestamp: number = new Date().getTime() // 时间戳
    const urlParmas = `terminalType=MOBILE&tenant=8211&timestamp=${timestamp}&token=${this.buildToken(timestamp)}`
    const url = `https://baas-test.hd123.com/unibff/v1/login-form/login?${urlParmas}`
    const headers = {}
    headers['language'] = 'zh-CN'
    headers['app_version'] = '2.5.0' // 版本号
    headers['Content-Type'] = 'application/json'
    headers['trace_id'] = DateUtil.format(new Date().getTime(), 'yyyyMMdd-HHmmss-SSS')
    headers['Authorization'] = ''
    headers['Accept-Language'] = 'zh-CN,zh;q=0.9' // 安卓app默认传的是en，返回的数据不对
    const params = this.buildLoginForm()
    console.log('请求地址', url, JSON.stringify(headers), JSON.stringify(params))
    NetworkModule.postJson(url, JSON.stringify(headers), JSON.stringify(params), (resp) => {
      this.$hideLoading()
      console.log('数据响应 ===>', resp)
      const data = JSON.parse(resp.data).data as LoginResultVo
      this.handleLogined(data, true) // 处理登录成功
      // this.doAnotherLogin(resp.data)
    })
  }
  // 采用android插件获取最后一次登录结果
  getAndroidLastAndLogin(): Promise<CLastLoginVo> {
    const url = 'https://baas-test.hd123.com/zl-portal/customerEnd/v1/api/org/getLastAndLogin'
    const headers = {}
    headers['language'] = 'zh-CN'
    headers['app_version'] = '2.5.0' // 版本号
    headers['Content-Type'] = 'application/json'
    headers['trace_id'] = DateUtil.format(new Date().getTime(), 'yyyyMMdd-HHmmss-SSS')
    headers['Authorization'] = this.$store.state.tokenInfo.accessToken
    headers['Accept-Language'] = 'zh-CN,zh;q=0.9' // 安卓app默认传的是en，返回的数据不对
    return new Promise((resolve, reject) => {
      NetworkModule.getAsync(url, JSON.stringify(headers), (ret) => {
        console.log('ret', JSON.parse(ret.data).data, JSON.parse(ret.data).data.userLogonInfo)
        if (ret && ret.data) {
          console.log('数据响应getLastAndLogin ===>', ret, JSON.parse(ret.headers)['access-token'])
          const respHeaders = JSON.parse(ret.headers)
          const tokenInfo = this.$store.state.tokenInfo || {}
          console.log('tokenInfo ===>', tokenInfo)
          if (respHeaders['access-token']) {
            tokenInfo.accessToken = respHeaders['access-token']
          }
          if (respHeaders['refresh-token']) {
            tokenInfo.refreshToken = respHeaders['refresh-token']
          }
          if (respHeaders['server-time']) {
            const currentTime = Math.floor(new Date().getTime()) // 当前时间戳,向下取整
            tokenInfo.serverDiffTime = respHeaders['server-time'] - currentTime // 时间差
          }
          this.$store.dispatch('tokenInfo', tokenInfo) // 更新token信息
          resolve(JSON.parse(ret.data).data)
        } else {
          reject(ret)
        }
      })
    })
  }
  getAndroidTenantByAppIds() {
    const url = 'https://baas-test.hd123.com/zl-portal/v2/customerEnd/api/appTenant/listByAppIds'
    const headers = {}
    const tokenInfo = this.$store.state.tokenInfo

    headers['language'] = 'zh-CN'
    headers['app_version'] = '2.5.0' // 版本号
    headers['Content-Type'] = 'application/json'
    headers['trace_id'] = DateUtil.format(new Date().getTime(), 'yyyyMMdd-HHmmss-SSS')
    headers['Authorization'] = tokenInfo.accessToken
    headers['Accept-Language'] = 'zh-CN,zh;q=0.9' // 安卓app默认传的是en，返回的数据不对
    const params = { appIds: ['sop', 'oas-osp', '经营助手'] }
    return new Promise((resolve, reject) => {
      NetworkModule.postJson(url, JSON.stringify(headers), JSON.stringify(params), (resp) => {
        console.log('数据响应 getAndroidTenantByAppIds===>', resp, JSON.parse(resp.data))
        // 创建appId到租户的映射
        const tenantMap = new Map<string, CAppTenantVO>()
        const respData = JSON.parse(resp.data).data
        respData.forEach((item) => tenantMap.set(item.appId, item))
        params.appIds.forEach((appId) => {
          const tenantInfo = tenantMap.get(appId)
          console.log('获取子应用的租户 ===>', tenantInfo)
          if (tenantInfo) {
            switch (appId) {
              case config.soaPortalAppid:
                this.mutationCurrentTenant(tenantInfo.appTenant)
                break
              case config.lyzsPortalAppid:
                this.setLyzsTenant(tenantInfo.appTenant)
                break
              case config.jyzsPortalAppid:
                this.actionJyzsTenant(tenantInfo.appTenant)
                break
              // 可扩展其他appId处理
            }
          }
        })
        resolve(true)
      })
    })
  }
  getAndroidpositions() {
    const url = 'https://baas-test.hd123.com/sop_bff/sop_bff/web/v2_5/mkhtest/employee/1022705/positions'
    const headers = {}
    const tokenInfo = this.$store.state.tokenInfo
    console.log('tokenInfo ===>', tokenInfo)
    headers['language'] = 'zh-CN'
    headers['app_version'] = '2.5.0' // 版本号
    headers['Content-Type'] = 'application/json'
    headers['trace_id'] = DateUtil.format(new Date().getTime(), 'yyyyMMdd-HHmmss-SSS')
    headers['Authorization'] = tokenInfo.accessToken
    headers['shop'] = '1003711'
    headers['shopid'] = '1003711'
    headers['shopname'] = '冷兵铺子'
    headers['appid'] = 'sopDingTalk'
    headers['Accept-Language'] = 'zh-CN,zh;q=0.9' // 安卓app默认传的是en，返回的数据不对
    const params = {}
    return new Promise((resolve, reject) => {
      NetworkModule.postJson(url, JSON.stringify(headers), JSON.stringify(params), (resp) => {
        console.log('数据响应 getAndroidpositions===>', resp, JSON.parse(resp.data))
        this.actionPositionList(JSON.parse(resp.data).data)
        resolve(true)
      })
    })
  }
  getAndroidlistFunctionPermissionsByAppIds() {
    const url = 'https://baas-test.hd123.com/zl-portal/v2/customerEnd/api/userPermission/listFunctionPermissionsByAppIds'
    const headers = {}
    const tokenInfo = this.$store.state.tokenInfo
    console.log('tokenInfo ===>', tokenInfo)
    headers['language'] = 'zh-CN'
    headers['app_version'] = '2.5.0' // 版本号
    headers['Content-Type'] = 'application/json'
    headers['trace_id'] = DateUtil.format(new Date().getTime(), 'yyyyMMdd-HHmmss-SSS')
    headers['Authorization'] = tokenInfo.accessToken

    headers['Accept-Language'] = 'zh-CN,zh;q=0.9' // 安卓app默认传的是en，返回的数据不对
    const params = { appIds: ['sop', 'oas-osp', '经营助手'] }
    return new Promise((resolve, reject) => {
      NetworkModule.postJson(url, JSON.stringify(headers), JSON.stringify(params), (resp) => {
        console.log('数据响应 getAndroidlistFunctionPermissionsByAppIds===>', JSON.parse(resp.data).data)
        if (resp.data) {
          const permissions: string[] = []
          const respData = JSON.parse(resp.data).data || {}
          params.appIds.forEach((appId) => {
            if (respData?.[appId]) {
              permissions.push(...respData?.[appId])
            }
          })
          console.log('获取当前用户的权限（含数据权限） ===>', permissions)
          this.actionPermission(permissions) // 存储合并后的权限列表到vuex
          resolve(true)
        }
      })
    })
  }

  // 登录
  login() {
    if (this.disabled) {
      return
    }
    console.log('login start ========>', new Date().getTime())
    this.startTime = new Date().getTime()
    this.$showLoading({ delayTime: 200 })
    const timestamp: number = new Date().getTime() // 时间戳
    ;(this.selectedTab('mobile')
      ? LoginFormApiAndroid.loginWithSmsCode(this.buildLoginForm(), timestamp, this.buildToken(timestamp))
      : LoginFormApiAndroid.login(this.buildLoginForm(), timestamp, this.buildToken(timestamp))
    )
      .then((resp) => {
        this.$hideLoading()
        this.handleLogined(resp.data)
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ title: e.msg, icon: 'error' })
      })
  }
  isAndroid() {
    let isAndroid: boolean = false
    // #ifdef APP-PLUS
    isAndroid = uni.getSystemInfoSync().platform === 'android'
    // #endif
    return isAndroid
  }
  // 登录后处理
  async handleLogined(data: Nullable<LoginResultVo>, testPerformance = false) {
    if (data && data.nextStep) {
      switch (data.nextStep) {
        case NextStepEnum.ENTER_HOME_PAGE:
          TokenMgr.inlayToken(data)
          // await this.getTenant() // 获取租户信息
          // await this.setDefaultOrg()
          if (this.isAndroid() && testPerformance) {
            const originUserInfo = new UserInfo()
            // const loginUserInfo: CUserLogonInfo = (await this.getAndroidLastAndLogin()) as any
            const loginUserInfo = await (await this.getAndroidLastAndLogin()).userLogonInfo
            const org = await (await this.getAndroidLastAndLogin()).lastLogin
            console.log('loginUserInfo ===>', loginUserInfo)
            originUserInfo.uuid = loginUserInfo.employeeId
            originUserInfo.user.code = loginUserInfo.employeeCode
            originUserInfo.user.name = loginUserInfo.employeeName
            originUserInfo.loginId = loginUserInfo.employeeCode
            originUserInfo.nickName = loginUserInfo.employeeName
            originUserInfo.mobile = loginUserInfo.mobile
            originUserInfo.linkMan = loginUserInfo.mobile
            originUserInfo.orgId = loginUserInfo.worKOrgId as string
            originUserInfo.token = this.$store.state.tokenInfo.accessToken
            this.actionUserInfo(originUserInfo)
            this.actionOrgInfo(org)
            await this.getAndroidTenantByAppIds() // 获取租户信息
            await this.getAndroidpositions() // 获取岗位信息
            await this.getAndroidlistFunctionPermissionsByAppIds() // 获取权限信息
            await this.actionCheckLicense(true)
            await this.doGetTenantConfig()
            // this.endTime = new Date().getTime()
            // {
            //   const diff = this.endTime - this.startTime // 安全声明在块作用域内
            //   console.log('登录耗时 ========>', diff)
            //   uni.showModal({
            //     title: ' android插件登录耗时',
            //     content: `${diff}ms`,
            //     confirmText: '确定',
            //     success: (action) => {
            //       if (action.confirm) {
            //         // 进入首页
            this.$Router.pushTab({ name: 'home' })
            //       }
            //     }
            //   })
            // }
          } else {
            await this.setDefaultOrgNew()
            // if (!this.currentTenant || !this.jyzsTenant || !this.lyzsTenant) {
            await this.getTenantByAppIds([config.soaPortalAppid || '', config.lyzsPortalAppid || '', config.jyzsPortalAppid || ''])
            // }
            // await this.getUser() // 获取用户信息
            this.handleOrginUserInfo(this.employeeInfo)
            await this.doPositionList()
            await this.getPermissionLists([config.soaPortalAppid || '', config.lyzsPortalAppid || '', config.jyzsPortalAppid || ''])
            // await this.doGetFields()
            // 开启轮询是否存在许可证
            await this.actionCheckLicense(true)
            await this.doGetTenantConfig()
            // console.log('login end ========>', new Date().getTime())
            this.endTime = new Date().getTime()
            {
              const diff = this.endTime - this.startTime // 安全声明在块作用域内
              console.log('登录耗时 ========>', diff)
              uni.showModal({
                title: ' webview登录耗时',
                content: `${diff}ms`,
                confirmText: '确定',
                success: (action) => {
                  if (action.confirm) {
                    // 进入首页
                    this.$Router.pushTab({ name: 'home' })
                  }
                }
              })
            }
          }

          break
        case NextStepEnum.RE_INPUT:
          this.loginForm = data.newLoginForm || this.loginForm
          this.$showToast({ title: data.promptText || '登录失败,请联系管理员!', icon: 'error' })
          break
        case NextStepEnum.CHANGE_PASSWORD:
          // 提示去修改密码
          this.$showModal({
            title: '密码到期啦',
            content: '您的账号密码已到期，请立即修改，避免影响您的使用',
            confirmText: '去修改',
            confirmColor: '#1C64FD',
            success: (action) => {
              if (action.confirm) {
                TokenMgr.inlayToken(data)
                uni.navigateTo({ url: '/pagePortalSub/minePasswordChange/PasswordChange?nextStep=' + NextStepEnum.CHANGE_PASSWORD }) // 去修改密码，修改成功后需要重新登录
              }
            }
          })

          break
        case NextStepEnum.SELECT_TENANT:
          // 理论上App端不会出现这个情况，因为App端进入之前需要进行租户绑定
          TokenMgr.inlayToken(data)
          // await this.getUser() // 获取用户信息
          // await this.getTenant() // 获取租户信息
          // await this.setDefaultOrg()
          break
        default:
          this.$showToast({ title: data.promptText || '登录失败,请联系管理员!', icon: 'error' })
      }
    } else {
      this.$showToast({ title: '登录失败,请联系管理员!', icon: 'error' })
    }
  }

  // 构造登录表单
  buildLoginForm() {
    const list: CredentialVo[] = []
    if (this.selectedTab('mobile')) {
      this.loginForm.mobileElements.forEach((item) => {
        const temp = new CredentialVo()
        temp.credentialType = item.credentialType
        temp.accessKey = item.accessKey
        temp.input = item.inputText?.trim() || null
        list.push(temp)
      })
    } else {
      this.loginForm.inputElements.forEach((item) => {
        const temp = new CredentialVo()
        temp.credentialType = item.credentialType
        temp.accessKey = item.accessKey
        if (item.credentialType == CredentialEnum.PASSWORD && item.encryptionAlgorithm == 'RSA') {
          temp.input = rsa(item.inputText?.trim() || '', item.secretKey || '') || null
        } else {
          temp.input = item.inputText?.trim() || null
        }
        list.push(temp)
      })
    }
    return list
  }

  // 构造重放攻击token
  buildToken(timestamp: number) {
    let token: string = `${'MOBILE'},${this.tenantInfo.id || null}` // 防重放攻击token
    if (this.currentTab == 'mobile') {
      for (const item of this.loginForm.mobileElements) {
        token = token + `,${item.inputText}`
      }
    } else if (this.currentTab == 'account') {
      for (const item of this.loginForm.inputElements) {
        if (item.credentialType != 'PASSWORD') {
          token = token + `,${item.inputText}`
        }
      }
    }
    token = token + `,${timestamp}`
    if (this.loginForm.tokenAlgorithm === 'RSA') {
      token = rsa(token, this.loginForm.tokenSecretKey || '') || ''
    }
    return token
  }

  // 获取登录的用户信息
  async getUser() {
    this.$showLoading({ delayTime: 200 })
    try {
      const resp = await LogonUserApi.getLogonUser()
      if (resp && resp.data) {
        const userInfo = resp.data
        const temp = new UserInfoPortal()
        temp.fullName = userInfo.fullName
        temp.iid = userInfo.iid
        if (this.selectedTab('mobile')) {
          temp.mobile = this.inputElement(CredentialEnum.MOBILE).inputText
        }
      }
    } catch (e) {
      const error = e as any
      this.$showToast({ title: error.msg, icon: 'error' })
    } finally {
      this.$hideLoading()
    }
  }
  handleOrginUserInfo(loginUserInfo: CUserLogonInfo) {
    const originUserInfo = new UserInfo()
    originUserInfo.uuid = loginUserInfo.employeeId
    originUserInfo.user.code = loginUserInfo.employeeCode
    originUserInfo.user.name = loginUserInfo.employeeName
    originUserInfo.loginId = loginUserInfo.employeeCode
    originUserInfo.nickName = loginUserInfo.employeeName
    originUserInfo.mobile = loginUserInfo.mobile
    originUserInfo.linkMan = loginUserInfo.mobile
    originUserInfo.orgId = loginUserInfo.worKOrgId as string
    originUserInfo.token = this.$store.state.tokenInfo.accessToken
    this.actionUserInfo(originUserInfo)
  }
  /**
   * 获取当前用户的权限（含数据权限）
   */
  // async getPermissionList(appId) {
  //   try {
  //     const resp = await CUserPermissionApiV2.listAppPermissions({
  //       appId: appId,
  //       funcIds: []
  //     })
  //     const permissionList: string[] = resp.data && resp.data.length ? resp.data : [] // 权限列表（单单权限信息）
  //     console.log('获取当前用户的权限（含数据权限） ===>', permissionList)
  //     this.actionPermission(permissionList) // 将权限列表（单单权限信息）存储在vuex中
  //   } catch (e) {
  //     // TODO handle the exception
  //     const error = e as any
  //     this.$showToast({ icon: 'error', title: error.msg })
  //   }
  // }
  /**
   * 获取当前用户的权限（含数据权限）
   */
  // getPermissionList() {
  //   this.$showLoading()
  //   UserApi.list('sopDingTalk')
  //     .then((resp) => {
  //       this.$hideLoading()
  //       const permissionList: string[] = [] // 权限列表（单单权限信息）
  //       if (resp.data.permissions && resp.data.permissions.length) {
  //         resp.data.permissions.forEach((item: any) => {
  //           if (item.appId === 'sopDingTalk') {
  //             permissionList.push(item.uuid)
  //           }
  //         })
  //       }
  //       this.actionPermission(permissionList) // 将权限列表（单单权限信息）存储在vuex中
  //       // uni.redirectTo({ url: '/pagesOther/storeSelect/StoreSelect?redirect=' + '/pages/home/<USER>' })
  //     })
  //     .catch((e) => {
  //       this.$hideLoading()
  //       this.$showToast({ icon: 'error', title: e.msg })
  //     })
  // }

  /**
   * 获取用户岗位信息
   */
  doPositionList() {
    this.$showLoading()
    return EmployeeApi.queryPosition()
      .then((res) => {
        this.$hideLoading()
        this.actionPositionList(res.data)
      })
      .catch((error) => {
        this.$hideLoading()
        if (error.msg) {
          this.$showToast({ icon: 'error', title: error.msg })
        } else {
          this.$showToast({ icon: 'error', title: error.message })
        }
      })
  }

  /**
   * 忘记密码
   */
  doPwdForget() {
    if (!this.showMobile) {
      this.$showToast({ icon: 'warning', title: '请联系总部管理员重置密码' })
    } else {
      uni.navigateTo({ url: '/pages/passwordChange/PasswordChange' })
    }
  }

  /**
   * 查看绑定信息
   */
  doBindinfoView() {
    this.$Router.push({
      name: 'appBindInfo'
    })
  }

  /**
   * 获取安全策略配置
   */
  doGetConfig() {
    UserApi.safeList()
      .then((resp) => {
        if (resp.data) {
          this.actionConfig(resp.data)
        }
      })
      .catch((error) => {
        console.log(error)
      })
  }

  /**
   * 获取配置
   */
  async doGetTenantConfig() {
    const { data: config } = await SysConfigApi.configList()
    if (config) {
      this.actionbigDataTenant(config.bigDataTenant)
    }
  }

  /**
   * 获取新配置
   */
  async doGetFields() {
    const { data: config } = await SysConfigApi.fieldsList()
    if (config && config.length) {
      const fieldsList: string[] = [] // 配置列表
      config.forEach((fieldItem: FieldsConfigItem) => {
        fieldItem.fieldList.forEach((item: FieldList) => {
          if (item.display) {
            fieldsList.push(item.fieldKey as string)
          }
        })
      })

      this.actionFieldsConfig(fieldsList) // 将权限列表（单单权限信息）存储在vuex中
    }
  }
}
