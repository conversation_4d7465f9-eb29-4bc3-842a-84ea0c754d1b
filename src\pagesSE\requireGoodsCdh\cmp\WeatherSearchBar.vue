<template>
  <view class="WeatherSearchBar" :class="{ 'WeatherSearchBar--active': active }">
    <view class="weather" v-if="!active" @click="doWeatherShow">
      <text class="date">明日：</text>
      <image v-if="haseTempture" class="weatherIcon" :src="weatherIcon"></image>
      <text class="temperature">{{ temperature }}</text>
      <image v-if="haseTempture" class="downIcon" :src="'/static/icon/ic_down_chevron_grey.png' | oss"></image>
    </view>
    <view class="search" :class="{ 'search--active': active }">
      <view class="search-content">
        <image class="search-icon" :class="[active ? 'search-img--active' : 'search-img']" :src="'/static/icon/ic_search.png' | oss"></image>
        <view class="search-input" v-if="!active" @click="jumpSearch">商品名称/代码</view>
        <input
          v-else
          class="search-inp"
          id="inp"
          type="search"
          placeholder="商品名称/代码"
          confirm-type="search"
          v-model="keyWord"
          @focus="doFoucs()"
          @confirm="doSearch"
        />
        <view class="clear-icon" v-if="active" @click="doClear">
          <icon v-if="keyWord.length" type="clear" size="18" />
        </view>
        <image class="search-icon scan-img" :src="'/static/icon/ic_saoma_green.png' | oss" @click="doScan"></image>
      </view>
      <view class="search-txt" v-if="active" @click="doCancel">取消</view>
    </view>
  </view>
</template>

<script lang="ts" src="./WeatherSearchBar.ts"></script>

<style lang="scss" scoped>
.WeatherSearchBar {
  width: 100%;
  height: 120rpx;
  display: flex;
  background-color: $color-primary;

  .weather {
    width: 320rpx;
    box-sizing: border-box;
    display: flex;
    padding: 0 22rpx 0 28rpx;
    align-items: center;
    .date {
      font-size: 26rpx;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
    }
    .weatherIcon {
      width: 52rpx;
      height: 52rpx;
    }
    .temperature {
      font-size: 28rpx;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
    }
    .downIcon {
      width: 32rpx;
      height: 32rpx;
    }
  }
  .search {
    width: 420rpx;
    display: flex;
    position: relative;
    align-items: center;
    box-sizing: border-box;
    padding: 24rpx 24rpx 24rpx 0;
    .search-content {
      flex: 1;
      display: flex;
      align-items: center;
      height: 72rpx;
      background: rgba(247, 248, 250, 1);
      border-radius: 36rpx;
      padding: 0 16rpx;
      box-sizing: border-box;
    }
    .search-inp {
      flex: 1;
    }
    .search-input {
      flex: 1;
      line-height: 72rpx;
      color: rgb(148, 150, 154);
    }
    .search-icon {
      width: 48rpx;
      height: 48rpx;
    }

    .clear-icon {
      display: flex;
      height: 72rpx;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;
    }

    .search-txt {
      margin-left: 24rpx;
      font-size: 32rpx;
      font-weight: 400;
      color: $color-primary;
    }
  }
  .search--active {
    width: 100%;
    padding: 0 24rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
  }
}
.WeatherSearchBar--active {
  box-sizing: border-box;
  width: 750rpx;
  display: flex;
  justify-content: center;
  background-color: #ffffff;
}
</style>
