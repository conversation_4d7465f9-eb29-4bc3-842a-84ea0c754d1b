/*
 * @Author: yuzhip<PERSON>
 * @Date: 2025-05-12 14:41:53
 * @LastEditTime: 2025-05-12 14:44:06
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: /soa/src/model/AppGrtPromApply/AppGrtPromApplyBillGoodsQueryDTO.ts
 * 记得注释
 */
import Category from 'model/default/Category'
import Sort from 'model/default/Sort'
import aseGoodsQueryDTO from 'model/AppGrtPromApply/aseGoodsQueryDTO'

export default class AppGrtPromApplyBillGoodsQueryDTO extends aseGoodsQueryDTO {
  // 一级面板分类
  firstCategory: Nullable<Category> = null
  // 二级面板分类
  secondCategory: Nullable<Category> = null
  // 类别
  sort: Nullable<Sort> = null
  // 是否新品
  isNewList: Nullable<boolean> = null
  // 推介类型
  recommandType: Nullable<string> = null
}
