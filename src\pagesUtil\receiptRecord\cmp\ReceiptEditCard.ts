/*
 * @Author: 刘湘
 * @Date: 2021-05-06 16:22:21
 * @LastEditTime: 2025-05-26 15:30:53
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: /soa/src/pagesUtil/receiptRecord/cmp/ReceiptEditCard.ts
 * 记得注释
 */
import { Vue, Component, Prop, Watch, Inject } from 'vue-property-decorator'
import ModuleOption from '@/model/default/ModuleOption'
import { Getter, State } from 'vuex-class'
import { ModuleId } from '@/model/common/OptionListModuleId'
import AppReceiptRecordLineDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordLineDTO'
import CommonUtil from '@/utils/CommonUtil'
import config from '@/config'
import DateUtil from '@/utils/DateUtil'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'
import UserInfo from '@/model/user/UserInfo'

// eslint-disable-next-line @typescript-eslint/ban-ts-ignore
// @ts-ignore
@Component({
  components: {},
  options: {
    virtualHost: true
  }
})
export default class ReceiptEditCard extends Vue {
  @Getter('qtyScale') qtyScale: number
  @State('userInfo') userInfo: UserInfo // 当前用户信息
  @Prop({ type: Object, default: () => new AppReceiptRecordLineDTO() }) sku: AppReceiptRecordLineDTO // 商品
  @Prop({ type: String, default: '' }) customStyle: string // 自定义样式
  @Prop({ type: Boolean, default: false }) trust: boolean // 是否信任模式
  @Prop({ type: Boolean, default: false }) showPrice: boolean // 是否显示价格
  @Prop({ type: Boolean, default: false }) showConfirmed: boolean // 是否展示已收

  @State('optionList') optionList: ModuleOption[]

  @Watch('sku', { immediate: true, deep: true })
  onskuChange() {
    // 散称使用双计量
    if (this.sku) {
      // 开启 单品多人收货 配置且在待收tab页签下，重新计算 收货数量
      if (this.enableSingleGoodsMulRcver && !this.showConfirmed) {
        // 待收货数量（本次应收数qty-收货数receiptQty）
        const realReceiptQty = Number(this.sku.qty - this.sku.receiptQty).scale(this.qtyScale)
        if (this.sku.isDisp) {
          this.wholeQty = (realReceiptQty / this.sku.goods.qpc).scale(this.qtyScale)
          const qtyArr = (this.sku.receiptQpcQty || '').split('+')
          if (qtyArr && qtyArr.length > 1) {
            this.wholeQty = this.wholeQty - Number(qtyArr[0])
          } else {
            this.wholeQty = this.wholeQty - Number(this.sku.receiptQpcQty)
          }
          if (this.wholeQty < 0) this.wholeQty = 0
          this.splitQty = realReceiptQty
        } else {
          this.wholeQty = Math.floor(realReceiptQty / this.sku.goods.qpc)
          this.splitQty = (realReceiptQty - Number(this.wholeQty) * this.sku.goods.qpc).scale(this.qtyScale)
        }
      } else {
        // 没开启 单品多人收货 配置，走之前的逻辑
        if (this.sku.isDisp) {
          const qtyArr = (this.sku.receiptQpcQty || '').split('+')
          if (qtyArr && qtyArr.length > 1) {
            this.wholeQty = (Number(this.sku.receiptQty) / this.sku.goods.qpc).scale(this.qtyScale)
          } else {
            this.wholeQty = Number(this.sku.receiptQpcQty)
          }
          this.splitQty = this.sku.receiptQty
        } else {
          const qtyArr = (this.sku.receiptQpcQty || '').split('+')
          if (qtyArr && qtyArr.length > 1) {
            this.wholeQty = Number(qtyArr[0])
            this.splitQty = Number(qtyArr[1])
          } else if (this.sku.receiptQpcQty) {
            this.wholeQty = Number(this.sku.receiptQpcQty)
            this.splitQty = 0
          } else {
            this.wholeQty = Math.floor(this.sku.qty / this.sku.goods.qpc)
            this.splitQty = (this.sku.qty - Number(this.wholeQty) * this.sku.goods.qpc).scale(this.qtyScale)
          }
        }
      }
      this.exhibitValue = ''

      this.mfgDate = this.sku.mfgDate
      this.expDate = this.sku.expDate
    }
  }

  wholeQty: number = 0 // 件数 对应商品的qpcQty
  splitQty: number = 0 // 重量 对应商品的qty
  exhibitValue: string = '' // 陈列位置输入框的值
  mfgDate: Nullable<string> = null // 生产日期
  expDate: Nullable<string> = null // 到效日期

  mounted() {
    this.mfgDate = this.sku.mfgDate
    this.expDate = this.sku.expDate
  }

  /**
   * 是否展示陈列位置按钮
   */
  get isShowExhibitBtn() {
    return PermissionMgr.hasPermission(Permission.receiptDisplayLocationBind)
  }

  //获取字段展示权限
  get showMaster() {
    const masterPermission = {
      showDisplayLocation: false // 陈列位置
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDISPLAYLOCATION)) {
      masterPermission.showDisplayLocation = true
    }
    return masterPermission
  }

  //直配收货是否录入生产日期和到效期
  get directFillPrddateAndValidate() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.directFillPrddateAndValidate == '1'
    }
    return false
  }

  /**
   * 是否允许一品多货位
   */
  get allowOneGoodsMultipleSlot() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.allowOneGoodsMultipleSlot) {
      return moduleConfig[0].options.allowOneGoodsMultipleSlot === 'true'
    }
    return false
  }

  /**
   * 是否展示可售库存
   */
  get showCurInvQty() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.showCurInvQty) {
      return moduleConfig[0].options.showCurInvQty === '1'
    }
    return false
  }

  /**
   * 数据来源：0，H6，不显示陈列位置相关；1，鼎力云，显示
   */
  get slotSource() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.slotSource) {
      return moduleConfig[0].options.slotSource === '1'
    }
    return false
  }

  /**
   * 陈列位置长度
   */
  get slotSegmentCount() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.slotSegmentCount) {
      return Number(moduleConfig[0].options.slotSegmentCount)
    }
    return 4
  }

  // 散称双计量是否需要录入件数（包装数）
  get doubleMeasureGoodsEnterQpcQty() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.doubleMeasureGoodsEnterQpcQty == '1'
    }
    return false
  }

  // 启用单品多人收货，0-否，1-是
  get enableSingleGoodsMulRcver() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []

    if (moduleConfig.length > 0) {
      return moduleConfig.findIndex((item) => item.options && item.options.enableSingleGoodsMulRcver === '1') > -1
    }
    return false
  }

  /**
   * 整件
   */
  get wholeMax() {
    if (this.sku) {
      // 开启 单品多人收货 配置且在待收tab页签下
      if (this.enableSingleGoodsMulRcver && !this.showConfirmed) {
        // 待收货数量（本次应收数qty-收货数receiptQty）
        const realReceiptQty = Number(this.sku.qty - this.sku.receiptQty).scale(this.qtyScale)
        // 散称的时候要把之前的qpcqty减去
        let wholeQty = (realReceiptQty / this.sku.goods.qpc).scale(this.qtyScale)
        const qtyArr = (this.sku.receiptQpcQty || '').split('+')
        if (qtyArr && qtyArr.length > 1) {
          wholeQty = wholeQty - Number(qtyArr[0])
        } else {
          wholeQty = wholeQty - Number(this.sku.receiptQpcQty)
        }
        if (wholeQty < 0) wholeQty = 0
        return this.sku.isDisp ? wholeQty : Math.floor(realReceiptQty / this.sku.goods.qpc)
      }
      return this.sku.isDisp ? Number(this.sku.qpcQty) : Math.floor((this.sku.qty - this.splitQty) / this.sku.goods.qpc)
    } else {
      return 9999999
    }
  }

  /**
   * 单品
   */
  get splitMax() {
    if (this.sku) {
      // 开启 单品多人收货 配置且在待收tab页签下
      if (this.enableSingleGoodsMulRcver && !this.showConfirmed) {
        // 待收货数量（本次应收数qty-收货数receiptQty）
        const realReceiptQty = Number(this.sku.qty - this.sku.receiptQty).scale(this.qtyScale)
        // 不是散称的时候，去计算最大值
        const wholeQty = Math.floor(realReceiptQty / this.sku.goods.qpc)
        const splitQty = (realReceiptQty - Number(wholeQty) * this.sku.goods.qpc).scale(this.qtyScale)
        return this.sku.isDisp ? realReceiptQty : splitQty
      }
      return this.sku.isDisp ? this.sku.qty : (this.sku.qty - this.wholeQty * this.sku.goods.qpc).scale(this.qtyScale)
    } else {
      return 9999999
    }
  }

  /**
   * 是否有多个陈列位置
   */
  get hasMutiple() {
    return (item) => {
      return item && item.split(',').length > 1
    }
  }

  // 商品图片
  get img() {
    const sku = this.sku
    return sku && sku.goodsImages && sku.goodsImages.length && CommonUtil.isImageUrl(sku.goodsImages[0])
      ? `${sku.goodsImages[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
      : `${config.sourceUrl}icon/pic_goods.png`
  }

  get imageList() {
    const sku = this.sku
    return sku && sku.goodsImages && sku.goodsImages.filter((item) => CommonUtil.isImageUrl(item)).length
      ? sku.goodsImages.filter((item) => CommonUtil.isImageUrl(item))
      : [`${config.sourceUrl}icon/pic_goods.png`]
  }

  // 判断是否过期
  get isIntime() {
    if (!this.sku || !this.sku.expDate) {
      return false
    }
    let date = this.sku.expDate.substring(0, 10) + ' 23:59:59'
    date = date.replace(/-/g, '/') //IOS不支持解析YYYY-MM格式
    const oDate1 = new Date(date)
    const oDate2 = new Date()

    if (oDate1.getTime() >= oDate2.getTime()) {
      return false
    } else {
      return true
    }
  }

  // 商品待收数量
  get pendingReceipts() {
    // 如果是待收tab且启用单品多人收货开启
    if (this.enableSingleGoodsMulRcver && !this.showConfirmed) {
      return (Number(this.sku.qty) - Number(this.sku.receiptQty)).scale(this.qtyScale)
    }
    return this.sku.qty
  }

  // 数量相关是否可编辑（收货人和当前登录人是否是同一个或者是“信任模式”）
  get isSkuQtyDisabeld() {
    if (this.trust) {
      return true
    }
    return this.sku && this.sku.consigneeId && this.sku.consigneeId !== this.userInfo.loginId
  }

  handleConfirm() {
    const sku: AppReceiptRecordLineDTO = CommonUtil.deepClone(this.sku)
    if (sku.isDisp) {
      sku.receiptQpcQty = this.doubleMeasureGoodsEnterQpcQty ? `${this.wholeQty}` : `${sku.qpcQty}`
      sku.receiptQty = this.splitQty
    } else {
      sku.receiptQpcQty = `${this.wholeQty}+${this.splitQty}`
      sku.receiptQty = Number(this.wholeQty) * this.sku.goods.qpc + Number(this.splitQty)
    }
    sku.mfgDate = this.mfgDate
    sku.expDate = this.expDate
    this.$emit('confirm', sku)
  }

  // 结束收货
  handleOver() {
    const sku: AppReceiptRecordLineDTO = CommonUtil.deepClone(this.sku)
    if (sku.isDisp) {
      sku.receiptQpcQty = this.doubleMeasureGoodsEnterQpcQty ? `${this.wholeQty}` : `${sku.qpcQty}`
      sku.receiptQty = this.splitQty
    } else {
      sku.receiptQpcQty = `${this.wholeQty}+${this.splitQty}`
      sku.receiptQty = Number(this.wholeQty) * this.sku.goods.qpc + Number(this.splitQty)
    }
    sku.mfgDate = this.mfgDate
    sku.expDate = this.expDate
    this.$emit('over', sku)
  }

  handleEdit() {
    this.$emit('edit', this.sku)
  }

  /**
   * 预览图片
   */
  handlePreviewImg() {
    uni.previewImage({
      current: String(0),
      urls: this.imageList
    })
  }
  handleViewDetail() {
    this.$emit('detail', this.sku)
  }

  handleWholeQtyChange(qty: number) {
    this.wholeQty = qty
    this.splitQty = Math.min(this.splitMax, (this.wholeQty * this.sku.goods.qpc).scale(this.qtyScale))
  }

  handleSplitQtyChange(qty: number) {
    if (this.sku.goods.qpcStr === '1*1' && this.wholeQty !== qty) {
      this.wholeQty = qty
    }
  }

  /**
   * 获取该商品信息
   */
  getGoodsInfo(e) {
    const value = e.detail.value
    if (!value && this.allowOneGoodsMultipleSlot) return
    if (!value && !this.allowOneGoodsMultipleSlot) {
      this.$showModal({
        title: '',
        content: '是否确定当前陈列位置置为空？',
        showCancel: true,
        confirmText: '确定',
        success: (action) => {
          if (action.confirm) {
            this.inputComfirm(value)
          }
        }
      })
      return
    }
    if (value) {
      this.inputComfirm(value)
    }
  }

  /**
   * 陈列位置确认
   */
  inputComfirm(value) {
    const regex = /^[A-Z0-9]+(-[A-Z0-9]+)*$/
    if (!regex.test(value) && value) {
      this.exhibitValue = ''
      this.$showToast({ title: '需要按照XX-XX-XX-XX，且只能为大写字母和数字的形式填写', icon: 'none' })
      return
    }
    if (regex.test(value) && value) {
      const list = value.split('-')
      if (list.length > this.slotSegmentCount) {
        this.exhibitValue = ''
        this.$showToast({ title: `陈列位置最大长度为${this.slotSegmentCount}级`, icon: 'none' })
        return
      } else {
        const idx = list.findIndex((item) => item.length > 4)
        if (idx > -1) {
          this.exhibitValue = ''
          this.$showToast({ title: `陈列位置每级最大长度为4`, icon: 'none' })
          return
        }
      }
    }
    this.sku.exhibitValue = value
    this.$emit('getRequestBody', this.sku)
  }

  // 时间选择
  // 开始日期选择点击事件
  doStartClick() {
    if (this.sku.confirmed) {
      return
    }
    const currentDate: string = this.mfgDate ? this.mfgDate : DateUtil.format(new Date(), 'yyyy-MM-dd')
    this.$showPicker({
      currentDate: currentDate,
      startDate: '2010-10-11',
      success: (res) => {
        if (res.date) {
          if (this.doTimeValidate(this.expDate, res.date)) {
            return this.$showToast({ icon: 'error', title: '到效日期不能早于生产日期～' })
          }
          this.mfgDate = res.date
          if (this.sku.validPeriod) {
            this.expDate = this.doDateChange(this.mfgDate, this.sku.validPeriod)
          }
        }
      }
    })
  }
  // 结束日期选择点击事件
  doEndClick() {
    if (this.sku.confirmed) {
      return
    }
    const currentDate: string = this.expDate ? this.expDate : DateUtil.format(new Date(), 'yyyy-MM-dd')
    this.$showPicker({
      currentDate: currentDate,
      startDate: '2010-10-11',
      success: (res) => {
        if (res.date) {
          if (this.doTimeValidate(res.date, this.mfgDate)) {
            return this.$showToast({ icon: 'error', title: '到效日期不能早于生产日期～' })
          }
          this.expDate = res.date
          if (this.sku.validPeriod) {
            this.mfgDate = this.doDateChange(this.expDate, 0 - this.sku.validPeriod)
          }
        }
      }
    })
  }

  //日期计算
  doDateChange(date, num) {
    date = date.replace(/-/g, '/') //IOS不支持解析YYYY-MM格式

    date = new Date(date).getTime() / 1000 //转换为时间戳
    date += 86400 * num //修改后的时间戳
    const newDate = new Date(parseInt(date) * 1000) //转换为时间
    return newDate.getFullYear() + '-' + (newDate.getMonth() + 1) + '-' + newDate.getDate()
  }

  // 检验时间，到效日期不满足时返回true
  doTimeValidate(date1, date2) {
    if (!date1 || !date2) {
      return false
    }
    date1 = date1.replace(/-/g, '/')
    date2 = date2.replace(/-/g, '/')
    const oDate1 = new Date(date1)
    const oDate2 = new Date(date2)
    if (oDate1.getTime() >= oDate2.getTime()) {
      return false
    } else {
      return true
    }
  }

  /**
   * 扫码陈列位置
   */
  doScanExhibit() {
    uni.scanCode({
      success: (res) => {
        this.inputComfirm(res.result || '')
      }
    })
  }

  /**
   * 绑定陈列位置
   */
  bindExhibit() {
    this.$emit('bindExhibit', this.sku)
  }

  /**
   * 陈列位置调整
   */
  resetExhibit() {
    this.$emit('resetExhibit', this.sku)
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit() {
    this.$emit('viewExhibit', this.sku)
  }
}
