import StkOutBckAttachLine from './StkOutBckAttachLine'
import StkOutBckReceiptRejectLine from 'model/stkOutBck/StkOutBckReceiptRejectLine'

export default class StkOutBckReasonLine {
  // 行号
  lineNo: number = 0
  // 申请数量
  qty: number = 0
  // 申请金额
  total: number = 0
  // 申请规格数
  qpcQty: string = ''
  // 批准数量
  approvalQty: number = 0
  // 批准金额
  approvalTotal: number = 0
  // 批准规格数量
  approvalQpcQty: number = 0
  // 实退数量
  rtnQty: Nullable<number> = null
  // 实退金额
  rtnTotal: Nullable<number> = null
  // 退货规格数量
  rtnQpcQty: Nullable<number> = null
  // 收货数量
  receiptQty: Nullable<number> = null
  // 收货金额
  receiptTotal: Nullable<number> = null
  // 收货规格数量
  receiptQpcQty: Nullable<string> = null
  // 退货原因代码
  reasonCode: string = ''
  // 退货原因名称
  reasonName: string = ''
  // 备注
  note: Nullable<string> = null
  // 拒收数量
  rejectQty: Nullable<number> = null
  // 拒收规格数量
  rejectQpcQty: Nullable<string> = null
  // 拒收金额
  rejectTotal: Nullable<number> = null
  // 明细附件
  attaches: StkOutBckAttachLine[] = []
  // 拒收明细
  rejects: StkOutBckReceiptRejectLine[] = []
}
