/*
 * @Author: 刘湘
 * @Date: 2021-05-06 16:22:21
 * @LastEditTime: 2025-05-26 16:03:07
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: /soa/src/pagesUtil/receiptRecord/cmp/BoxEditCard.ts
 * 记得注释
 */
import { Vue, Component, Prop, Watch, Inject } from 'vue-property-decorator'
import AppReceiptBoxDTO from '@/model/receipt/AppReceiptBoxDTO'
import CommonUtil from '@/utils/CommonUtil'
import ModuleOption from '@/model/default/ModuleOption'
import { Getter, State } from 'vuex-class'
import { ModuleId } from '@/model/common/OptionListModuleId'
import AppReceiptRecordBoxDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxDTO'
import config from '@/config'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'
import UserInfo from '@/model/user/UserInfo'

// eslint-disable-next-line @typescript-eslint/ban-ts-ignore
// @ts-ignore
@Component({
  components: {},
  options: {
    virtualHost: true
  }
})
export default class BoxEditCard extends Vue {
  @Getter('qtyScale') qtyScale: number
  @Prop({ type: Object, default: () => new AppReceiptRecordBoxDTO() }) box: AppReceiptRecordBoxDTO // 单据信息
  @Prop({ type: String, default: '' }) customStyle: string // 自定义样式
  @State('optionList') optionList: ModuleOption[]
  @State('userInfo') userInfo: UserInfo // 当前用户信息
  @Prop({ type: Boolean, default: false }) showConfirmed: boolean // 是否展示已收

  _uid: any

  @Watch('box', { immediate: true, deep: true })
  onBoxChange() {
    // 如果是待收tab且启用单品多人收货开启
    if (this.enableSingleGoodsMulRcver && !this.showConfirmed) {
      this.qty = (Number(this.box.qty) - Number(this.box.receiptQty)).scale(this.qtyScale)
    } else {
      this.qty = this.box.receiptQty
    }
    this.exhibitValue = ''
    if (this.box.type === 'whole') {
      // 散称使用双计量
      if (this.box.boxGoodss[0] && this.box.boxGoodss[0].goods.isDisp) {
        // 如果是待收tab且启用单品多人收货开启
        if (this.enableSingleGoodsMulRcver && !this.showConfirmed) {
          // 待收货数量（本次应收数qty-收货数receiptQty）
          const realReceiptQty = Number(this.box.boxGoodss[0].qty - this.box.boxGoodss[0].receiptQty).scale(this.qtyScale)
          this.wholeQty = (realReceiptQty / this.box.boxGoodss[0].goods.qpc).scale(this.qtyScale)
          const qtyArr = (this.box.boxGoodss[0].receiptQpcQty || '').split('+')
          if (qtyArr && qtyArr.length > 1) {
            this.wholeQty = this.wholeQty - Number(qtyArr[0])
          } else {
            this.wholeQty = this.wholeQty - Number(this.box.boxGoodss[0].receiptQpcQty)
          }
          if (this.wholeQty < 0) this.wholeQty = 0
          this.splitQty = realReceiptQty
        } else {
          this.wholeQty = Number(this.box.boxGoodss[0].receiptQpcQty)
          this.splitQty = Number(this.box.boxGoodss[0].receiptQty)
        }
      }
    }
  }

  qty: number = 0 // 实收数量

  wholeQty: number = 0 // 件数 对应商品的qpcQty
  splitQty: number = 0 // 重量 对应商品的qty

  showGift: boolean = false // 是否显示赠品
  exhibitValue: string = '' // 陈列位置输入框的值

  // 数量相关是否可编辑（收货人和当前登录人是否是同一个）
  get isSkuQtyDisabeld() {
    return this.box && this.box.consigneeId && this.box.consigneeId !== this.userInfo.loginId
  }

  // 散称的整件商品待收数量
  get dispWholePendinReceipts() {
    // 如果是待收tab且启用单品多人收货开启
    if (this.enableSingleGoodsMulRcver && !this.showConfirmed) {
      // 待收货数量（本次应收数qty-收货数receiptQty）
      const realReceiptQty = Number(this.box.boxGoodss[0].qty - this.box.boxGoodss[0].receiptQty).scale(this.qtyScale)
      // 散称的时候要把之前的qpcqty减去
      let wholeQty = (realReceiptQty / this.box.boxGoodss[0].goods.qpc).scale(this.qtyScale)
      const qtyArr = (this.box.boxGoodss[0].receiptQpcQty || '').split('+')
      if (qtyArr && qtyArr.length > 1) {
        wholeQty = wholeQty - Number(qtyArr[0])
      } else {
        wholeQty = wholeQty - Number(this.box.boxGoodss[0].receiptQpcQty)
      }
      if (wholeQty < 0) wholeQty = 0
      return wholeQty
    }
    return this.box.boxGoodss[0].qpcQty
  }

  // 非散称整件商品待收数量
  get wholePendinReceipts() {
    // 如果是待收tab且启用单品多人收货开启
    if (this.enableSingleGoodsMulRcver && !this.showConfirmed) {
      return (Number(this.box.qty) - Number(this.box.receiptQty)).scale(this.qtyScale)
    }
    return this.box.qty
  }

  // 整件商品单品维度待收数量
  get splitPendinReceipts() {
    // 如果是待收tab且启用单品多人收货开启
    if (this.enableSingleGoodsMulRcver && !this.showConfirmed) {
      return (Number(this.box.boxGoodss[0].qty) - Number(this.box.boxGoodss[0].receiptQty)).scale(this.qtyScale)
    }
    return this.box.boxGoodss[0].qty
  }

  // 启用单品多人收货，0-否，1-是
  get enableSingleGoodsMulRcver() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []

    if (moduleConfig.length > 0) {
      return moduleConfig.findIndex((item) => item.options && item.options.enableSingleGoodsMulRcver === '1') > -1
    }
    return false
  }

  /**
   * 是否展示陈列位置按钮
   */
  get isShowExhibitBtn() {
    return PermissionMgr.hasPermission(Permission.receiptDisplayLocationBind)
  }

  // 是否启用差异原因功能
  get diffReasonsReportedByStore() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.diffReasonsReportedByStore == '1'
    }
    return false
  }

  // 整件箱商品允许按最小规格收货：0-否；1-是
  get wholeBoxGoodsReceiptByMinQpc() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.wholeBoxGoodsReceiptByMinQpc == '1'
    }
    return false
  }

  //获取字段展示权限
  get showMaster() {
    const masterPermission = {
      showDisplayLocation: false // 陈列位置
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDISPLAYLOCATION)) {
      masterPermission.showDisplayLocation = true
    }
    return masterPermission
  }

  /**
   * 是否展示可售库存
   */
  get showCurInvQty() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.showCurInvQty) {
      return moduleConfig[0].options.showCurInvQty === '1'
    }
    return false
  }

  // 散称双计量是否需要录入件数（包装数）
  get doubleMeasureGoodsEnterQpcQty() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.doubleMeasureGoodsEnterQpcQty == '1'
    }
    return false
  }

  /**
   * 陈列位置长度
   */
  get slotSegmentCount() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.slotSegmentCount) {
      return Number(moduleConfig[0].options.slotSegmentCount)
    }
    return 4
  }

  get splitDiffCount() {
    if (!this.box || !this.box.boxGoodss.length || !this.box.confirmed) {
      return 0
    }

    let diffCount: number = 0
    this.box.boxGoodss.forEach((item) => {
      Number(item.qty) !== Number(item.receiptQty) && diffCount++
    })

    return diffCount
  }

  get diff() {
    if (!this.box || !this.box.boxGoodss.length) {
      return ''
    }
    let diff = ''
    if (this.box.boxGoodss[0].goods.isDisp) {
      const diffCount = Number(this.box.boxGoodss[0].qty).minus(Number(this.box.boxGoodss[0].receiptQty))
      if (diffCount > 0) {
        diff = `少${diffCount}${this.box.boxGoodss[0].goods.minMunit}`
      } else if (diffCount < 0) {
        diff = `多${Math.abs(diffCount)}${this.box.boxGoodss[0].goods.minMunit}`
      }
    } else {
      const diffCount = Number(this.box.qty).minus(Number(this.box.receiptQty))
      if (diffCount > 0) {
        diff = `少${diffCount}箱`
      } else if (diffCount < 0) {
        diff = `多${Math.abs(diffCount)}箱`
      }
    }
    return diff
  }

  /**
   * 是否有多个陈列位置
   */
  get hasMutiple() {
    return this.box.boxGoodss[0] && this.box.boxGoodss[0].displayLocation && this.box.boxGoodss[0].displayLocation.split(',').length > 1
  }

  /**
   * 是否允许一品多货位
   */
  get allowOneGoodsMultipleSlot() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.allowOneGoodsMultipleSlot) {
      return moduleConfig[0].options.allowOneGoodsMultipleSlot === 'true'
    }
    return false
  }

  /**
   * 数据来源：0，H6，不显示陈列位置相关；1，鼎力云，显示
   */
  get slotSource() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.slotSource) {
      return moduleConfig[0].options.slotSource === '1'
    }
    return false
  }

  // 商品图片
  get img() {
    const sku = this.box.boxGoodss[0]
    return sku && sku.goods.images && sku.goods.images.length && CommonUtil.isImageUrl(sku.goods.images[0])
      ? `${sku.goods.images[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
      : `${config.sourceUrl}icon/pic_goods.png`
  }

  get imageList() {
    const sku = this.box.boxGoodss[0]
    return sku && sku.goods.images && sku.goods.images.filter((item) => CommonUtil.isImageUrl(item)).length
      ? sku.goods.images.filter((item) => CommonUtil.isImageUrl(item))
      : [`${config.sourceUrl}icon/pic_goods.png`]
  }

  hanleConfirm() {
    const box: AppReceiptRecordBoxDTO = CommonUtil.deepClone(this.box)
    if (box.boxGoodss[0].goods.isDisp) {
      box.boxGoodss[0].receiptQpcQty = this.doubleMeasureGoodsEnterQpcQty ? `${this.wholeQty}` : `${box.boxGoodss[0].qpcQty}`
      box.boxGoodss[0].receiptQty = this.splitQty
      box.receiptQty = this.doubleMeasureGoodsEnterQpcQty ? this.wholeQty : box.qty
    } else {
      box.receiptQty = this.qty
      box.boxGoodss[0].receiptQty = box.receiptQty.multiply(box.boxGoodss[0].goods.qpc).scale(this.qtyScale)
      box.boxGoodss[0].receiptQpcQty = `${box.receiptQty.scale(this.qtyScale)}`
    }
    this.$emit('confirm', box)
  }

  handleViewDetail() {
    this.$emit('detail', this.box)
  }

  handleClick() {
    this.$emit('click', this.box)
  }

  handleEdit() {
    this.$emit('edit', this.box)
  }

  handleEditReason() {
    this.$emit('edit-reason', this.box)
  }

  handleEditSplitReason() {
    this.$emit('edit-split-reason', this.box)
  }

  handlePreviewImg() {
    uni.previewImage({
      current: String(0),
      urls: this.imageList
    })
  }

  handleWholeQtyChange(qty: number) {
    this.wholeQty = qty
    this.splitQty = Math.min(Number(this.box.boxGoodss[0].qty), (this.wholeQty * this.box.boxGoodss[0].goods.qpc).scale(this.qtyScale))
  }

  handleSplitQtyChange(qty: number) {
    if (this.box.boxGoodss[0].goods.qpcStr === '1*1' && this.wholeQty !== qty) {
      this.wholeQty = qty
    }
  }

  /**
   * 获取该商品信息
   */
  getGoodsInfo(e) {
    const value = e.detail.value
    if (!value && this.allowOneGoodsMultipleSlot) return
    if (!value && !this.allowOneGoodsMultipleSlot) {
      this.$showModal({
        title: '',
        content: '是否确定当前陈列位置置为空？',
        showCancel: true,
        confirmText: '确定',
        success: (action) => {
          if (action.confirm) {
            this.inputComfirm(value)
          }
        }
      })
      return
    }
    if (value) {
      this.inputComfirm(value)
    }
  }

  /**
   * 陈列位置确认
   */
  inputComfirm(value) {
    const regex = /^[A-Z0-9]+(-[A-Z0-9]+)*$/
    if (!regex.test(value) && value) {
      this.exhibitValue = ''
      this.$showToast({ title: '需要按照XX-XX-XX-XX，且只能为大写字母和数字的形式填写', icon: 'none' })
      return
    }
    if (regex.test(value) && value) {
      const list = value.split('-')
      if (list.length > this.slotSegmentCount) {
        this.exhibitValue = ''
        this.$showToast({ title: `陈列位置最大长度为${this.slotSegmentCount}级`, icon: 'none' })
        return
      } else {
        const idx = list.findIndex((item) => item.length > 4)
        if (idx > -1) {
          this.exhibitValue = ''
          this.$showToast({ title: `陈列位置每级最大长度为4`, icon: 'none' })
          return
        }
      }
    }
    this.box.boxGoodss[0].exhibitValue = value || ''
    this.$emit('getRequestBody', this.box.boxGoodss[0])
  }

  /**
   * 扫码陈列位置
   */
  doScanExhibit(info) {
    uni.scanCode({
      success: (res) => {
        this.inputComfirm(res.result || '')
      }
    })
  }

  /**
   * 绑定陈列位置
   */
  bindExhibit() {
    this.$emit('bindExhibit', this.box.boxGoodss[0])
  }

  /**
   * 陈列位置调整
   */
  resetExhibit() {
    this.$emit('resetExhibit', this.box.boxGoodss[0])
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit() {
    this.$emit('viewExhibit', this.box.boxGoodss[0])
  }
}
