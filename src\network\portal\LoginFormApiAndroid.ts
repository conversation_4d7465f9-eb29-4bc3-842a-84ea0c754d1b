/*
 * @Author: AI Assistant
 * @Date: 2025-01-19
 * @LastEditTime: 2025-06-19 14:26:22
 * @LastEditors: shikailei
 * @Description: Android原生登录表单API封装
 * @FilePath: \soa\src\network\portal\LoginFormApiAndroid.ts
 * 记得注释
 */

import ResponseVo from '@/model/portalModel/commons/ResponseVo'
import LoginFormVo from '@/model/portalModel/login/LoginFormVo'
import CredentialVo from '@/model/portalModel/login/CredentialVo'
import LoginResultVo from '@/model/portalModel/login/LoginResultVo'
import TokenLoginRequestVo from '@/model/portalModel/login/TokenLoginRequestVo'
import apiClientAndroid from '../apiClientAndroid'
import config from '@/config'
import store from '@/store'

export default class LoginFormApiAndroid {
  /**
   * 构建完整的URL
   * 参照flyPortal.ts的URL构建逻辑
   */
  private static buildUrl(path: string): string {
    let fullUrl = config.portalBaseUrl || ''

    // 后端分了两个网关，接口里不放网关，自行判断加网关
    if (path.indexOf('/customerEnd/v1') > -1 || path.indexOf('/v2/customerEnd') > -1) {
      // 选组织，查卡片等调下网关接口
      fullUrl += '/zl-portal' + path
    } else if (path.indexOf('/cprint-app') > -1) {
      fullUrl += '' + path
    } else {
      // 绑租户，登录等调下网关
      fullUrl += '/unibff' + path
    }

    return fullUrl
  }

  /**
   * 构建查询参数字符串
   */
  private static buildQueryString(params: Record<string, any>): string {
    const queryParams = new URLSearchParams()

    Object.keys(params).forEach((key) => {
      if (params[key] !== undefined && params[key] !== null) {
        queryParams.append(key, String(params[key]))
      }
    })

    const queryString = queryParams.toString()
    return queryString ? `?${queryString}` : ''
  }

  /**
   * 加载登录表单。
   * 加载登录表单。
   *
   */
  static load(language?: string): Promise<ResponseVo<LoginFormVo>> {
    const params = {
      terminalType: 'MOBILE',
      tenant: store.state.tenantInfo.id
    }

    const url = this.buildUrl('/v1/login-form/load') + this.buildQueryString(params)

    const headers: Record<string, any> = {}
    if (language) {
      headers.language = language
    }

    return apiClientAndroid.get(url, { headers }).then((res) => {
      return res
    })
  }

  /**
   * 登录。
   * 登录。
   *
   */
  static login(body: CredentialVo[], timestamp?: number, token?: string, language?: string): Promise<ResponseVo<LoginResultVo>> {
    const params = {
      terminalType: 'MOBILE',
      tenant: store.state.tenantInfo.id,
      timestamp: timestamp,
      token: token
    }

    const url = this.buildUrl('/v1/login-form/login') + this.buildQueryString(params)

    const headers: Record<string, any> = {}
    if (language) {
      headers.language = language
    }

    return apiClientAndroid.post(url, body, { headers }).then((res) => {
      return res
    })
  }

  /**
   * 通过OAuth登录。
   * 通过OAuth登录。
   *
   */
  static loginWithOAuth(
    oauthCorpAccount: string,
    oauthIdentifyingCode: string,
    timestamp?: number,
    token?: string,
    language?: string
  ): Promise<ResponseVo<LoginResultVo>> {
    const requestBody = {
      terminalType: 'MOBILE',
      tenant: store.state.tenantInfo.id,
      oauthCorpAccount: oauthCorpAccount,
      oauthIdentifyingCode: oauthIdentifyingCode,
      timestamp: timestamp,
      token: token
    }

    const url = this.buildUrl('/v1/login-form/login-with-oauth')

    const headers: Record<string, any> = {}
    if (language) {
      headers.language = language
    }

    return apiClientAndroid.post(url, requestBody, { headers }).then((res) => {
      return res
    })
  }

  /**
   * 通过手机短信验证码登录。
   * 通过手机短信验证码登录。
   *
   */
  static loginWithSmsCode(body: CredentialVo[], timestamp?: number, token?: string, language?: string): Promise<ResponseVo<LoginResultVo>> {
    const params = {
      terminalType: 'MOBILE',
      tenant: store.state.tenantInfo.id,
      timestamp: timestamp,
      token: token
    }

    const url = this.buildUrl('/v1/login-form/login-with-sms-code') + this.buildQueryString(params)

    const headers: Record<string, any> = {}
    if (language) {
      headers.language = language
    }

    return apiClientAndroid.post(url, body, { headers }).then((res) => {
      return res
    })
  }

  /**
   * 通过令牌登录。
   * 通过令牌登录。
   *
   */
  static loginWithToken(body: TokenLoginRequestVo, language?: string): Promise<ResponseVo<LoginResultVo>> {
    const url = this.buildUrl('/v1/login-form/login-with-token')

    const headers: Record<string, any> = {}
    if (language) {
      headers.language = language
    }

    return apiClientAndroid.post(url, body, { headers }).then((res) => {
      return res
    })
  }
}

/**
 * 使用示例：
 *
 * 在Login.ts中，您可以这样替换原来的LoginFormApi调用：
 *
 * // 原来的调用方式：
 * // import LoginFormApi from '@/network/portal/LoginFormApi'
 * // const response = await LoginFormApi.login(this.buildLoginForm(), timestamp, this.buildToken(timestamp))
 *
 * // 新的Android原生调用方式：
 * import LoginFormApiAndroid from '@/network/portal/LoginFormApiAndroid'
 * const response = await LoginFormApiAndroid.login(this.buildLoginForm(), timestamp, this.buildToken(timestamp))
 *
 * // 传参和回调完全一致，只需要替换类名即可！
 *
 * // 其他方法的使用也是一样的：
 * // const formData = await LoginFormApiAndroid.load()
 * // const smsLoginResult = await LoginFormApiAndroid.loginWithSmsCode(credentials, timestamp, token)
 * // const oauthResult = await LoginFormApiAndroid.loginWithOAuth(corpAccount, code, timestamp, token)
 * // const tokenResult = await LoginFormApiAndroid.loginWithToken(tokenRequest)
 *
 * // 在Login.ts的login方法中，可以这样修改：
 * // 原来：
 * // (this.selectedTab('mobile')
 * //   ? LoginFormApi.loginWithSmsCode(this.buildLoginForm(), timestamp, this.buildToken(timestamp))
 * //   : LoginFormApi.login(this.buildLoginForm(), timestamp, this.buildToken(timestamp))
 * // )
 *
 * // 改为：
 * // (this.selectedTab('mobile')
 * //   ? LoginFormApiAndroid.loginWithSmsCode(this.buildLoginForm(), timestamp, this.buildToken(timestamp))
 * //   : LoginFormApiAndroid.login(this.buildLoginForm(), timestamp, this.buildToken(timestamp))
 * // )
 */
