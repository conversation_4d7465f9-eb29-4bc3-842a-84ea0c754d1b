<!--
 * @Author: 徐庆凯
 * @Date: 2020-11-04 13:34:38
 * @LastEditTime: 2025-02-28 18:49:06
 * @LastEditors: hanwei
 * @Description: 
 * @FilePath: \soa\src\pagesShopManage\processBill\cmp\OperatorCell.vue
 * @记得注释
-->
<template>
  <view class="operator-cell">
    <view class="cell-title">
      <text class="cell-title-txt">{{ title }}</text>
      <view class="cell-title-info">
        <text>
          类别：
          <text class="text-value">{{ count }}种</text>
        </text>
        <text style="margin-left: 24rpx" v-if="showPrice">
          {{ describe }}
          <text class="text-value">￥{{ total | fmt('0.00') | amountView }}</text>
        </text>
      </view>
    </view>
    <view class="operator-group" v-if="clickable">
      <image @click="doAdd" class="operator-icon" :src="'/static/icon/ic_add_fill_grey.png' | oss" mode="aspectFill"></image>
      <image @click="doSearch" class="operator-icon" :src="'/static/icon/ic_search_green.png' | oss" mode="aspectFill"></image>
    </view>
  </view>
</template>
<script lang="ts" src="./OperatorCell.ts"></script>

<style lang="scss" scoped>
.operator-cell {
  @include flex(row, space-between, center);
  position: relative;
  width: 100%;
  height: 120rpx;
  box-sizing: border-box;
  padding: 14rpx 24rpx;
  background: #f7f8fa;
  .cell-title {
    flex: 1 1 auto;
    height: 100%;
    @include flex(column, space-around, flex-start);

    .cell-title-txt {
      font-size: 32rpx;
      color: #282c34;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
    }
    .cell-title-info {
      @include flex(row, flex-start, center);
      font-size: 24rpx;
      color: #94969a;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      .text-value {
        color: #282c34;
      }
    }
  }
  .operator-group {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    .operator-icon {
      height: 72rpx;
      width: 72rpx;
      margin-left: 12rpx;
    }
  }
}
</style>
