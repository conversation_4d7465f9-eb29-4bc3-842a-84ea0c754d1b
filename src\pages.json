{
  "pages": [
    // #ifdef HD-UNI
    {
      "path": "pages/routeRelay/RouteRelay",
      "name": "routeRelay",
      "style": {
        "navigationBarTitleText": "门店助手",
        "enablePullDownRefresh": false,
        "mp-alipay": {
          "allowsBounceVertical": "NO"
        },
        // #ifndef APP-PLUS
        "disableScroll": true,
        // #endif
        "navigationBarTextStyle": "black",
        "navigationBarBackgroundColor": "#E9EEFB"
      }
    },
    // #endif
    // #ifdef APP-PLUS
    // #ifndef HD-UNI
    {
      "path": "pages/appBind/AppBind",
      "name": "appBind",
      "style": {
        "navigationBarTitleText": "",
        "navigationBarBackgroundColor": "#FFFFFF",
        "navigationBarTextStyle": "black",
        "enablePullDownRefresh": false,
        "mp-alipay": {
          "allowsBounceVertical": "NO"
        },
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/appBind/AppBindTenant",
      "name": "appBindTenant",
      "style": {
        "navigationBarTitleText": "",
        "navigationBarBackgroundColor": "#FFFFFF",
        "navigationBarTextStyle": "black",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom",
        "mp-alipay": {
          "allowsBounceVertical": "NO"
        }
      }
    },
    {
      "path": "pages/appBind/AppBindTerminal",
      "name": "appBindTerminal",
      "style": {
        "navigationBarTitleText": "",
        "navigationBarBackgroundColor": "#FFFFFF",
        "navigationBarTextStyle": "black",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom",
        "mp-alipay": {
          "allowsBounceVertical": "NO"
        }
      }
    },
    {
      "path": "pages/appBind/AppBindInfo",
      "name": "appBindInfo",
      "style": {
        "navigationBarTitleText": "绑定信息",
        "navigationBarBackgroundColor": "#FFFFFF",
        "navigationBarTextStyle": "black",
        "enablePullDownRefresh": false,
        "mp-alipay": {
          "allowsBounceVertical": "NO"
        }
      }
    },
    // #endif
    // #endif
    {
      "path": "pages/home/<USER>",
      "name": "home",
      "style": {
        "navigationBarTitleText": "首页",
        "enablePullDownRefresh": true,
        "mp-alipay": {
          "allowsBounceVertical": "YES"
        },
        "navigationBarTextStyle": "black",
        "navigationBarBackgroundColor": "#E9EEFB"
      }
    },
    {
      "path": "pages/application/Application",
      "name": "application",
      "style": {
        "navigationBarTitleText": "应用",
        "enablePullDownRefresh": false,
        "mp-alipay": {
          "allowsBounceVertical": "NO"
        },
        "navigationBarTextStyle": "black",
        "navigationBarBackgroundColor": "#E9EEFB"
      }
    },
    {
      "path": "pages/home/<USER>",
      "name": "appSearch",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false,
        "navigationBarBackgroundColor": "#FFFFFF",
        "navigationBarTextStyle": "black",
        "mp-alipay": {
          "allowsBounceVertical": "NO"
        }
      }
    },
    {
      "path": "pages/mine/Mine",
      "name": "mine",
      "style": {
        "navigationBarTitleText": "我的",
        "enablePullDownRefresh": false,
        "mp-alipay": {
          "allowsBounceVertical": "NO"
        },
        "navigationBarTextStyle": "black",
        "navigationBarBackgroundColor": "#E9EEFB"
      }
    }
    // #ifndef MP-WEIXIN
    ,
    {
      "path": "pagesOther/bigData/BigData",
      "name": "bigData",
      "style": {
        "navigationBarTitleText": "数据中心",
        "enablePullDownRefresh": false,
        "mp-alipay": {
          "allowsBounceVertical": "NO"
        },
        "navigationBarTextStyle": "black",
        "navigationBarBackgroundColor": "#E9EEFB"
      }
    },
    {
      "path": "pagesOther/taskCenter/TaskCenter",
      "name": "taskCenter",
      "style": {
        "navigationBarTitleText": "任务中心",
        "navigationBarBackgroundColor": "#E9EEFB",
        "navigationBarTextStyle": "black",
        "enablePullDownRefresh": true,
        "mp-alipay": {
          "allowsBounceVertical": "YES"
        },
        "onReachBottomDistance": 50
      }
    },
    {
      "path": "pages/application/ApplicationData",
      "name": "applicationData",
      "style": {
        "navigationBarTitleText": "数据中心",
        "enablePullDownRefresh": false,
        "mp-alipay": {
          "allowsBounceVertical": "NO"
        },
        "navigationBarTextStyle": "black",
        "navigationBarBackgroundColor": "#E9EEFB"
      }
    }
    // #endif
  ],
  "subPackages": [
    {
      "root": "pagePortalSub",
      "pages": [
        {
          "path": "login/Login",
          "name": "loginPortal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "minePasswordChange/PasswordChange",
          "name": "minePasswordChangePortal",
          "style": {
            "navigationBarTitleText": "修改密码"
          }
        },
        {
          "path": "storeSelect/StoreSelect",
          "name": "storeSelectPortal",
          "style": {
            "navigationBarTitleText": "请选择门店",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "storeSelect/StoreSearch",
          "name": "storeSearchPortal",
          "style": {
            "navigationBarTitleText": "请选择门店",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "setting/messageSetting",
          "name": "messageSettingPortal",
          "style": {
            "navigationBarTitleText": "消息设置"
          }
        },
        {
          "path": "setting/printSetting",
          "name": "printSettingPortal",
          "style": {
            "navigationBarTitleText": "打印设置"
          }
        },
        {
          "path": "azkWeb/AzkWeb",
          "name": "azkWebPortal",
          "style": {
            "navigationBarTitleText": "经营助手",
            "navigationBarBackgroundColor": "#3d35d1",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "setting/printModuleSetting",
          "name": "printModuleSettingPortal",
          "style": {
            "navigationBarTitleText": "打印模块设置"
          }
        },
        {
          "path": "setting/cloudprintSetting",
          "name": "cloudprintSettingPortal",
          "style": {
            "navigationBarTitleText": "云打印设置"
          }
        },
        {
          "path": "setting/cloudPrintModuleSetting",
          "name": "cloudPrintModuleSettingPortal",
          "style": {
            "navigationBarTitleText": "云打印功能模块设置"
          }
        },
        {
          "path": "newsCenter/NewsList",
          "name": "portalNewsList",
          "style": {
            "navigationBarTitleText": "消息中心",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "newsCenter/NewsDetail",
          "name": "portalNewsDetail",
          "style": {
            "navigationBarTitleText": "公告",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesOther",
      "pages": [
        {
          "path": "storeCheckBill/StoreCheckBillList",
          "name": "storeCheckBillList",
          "style": {
            "navigationBarTitleText": "门店对账",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "storeCheckBill/StoreCheckBillSearch",
          "name": "storeCheckBillSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "storeCheckBill/StoreCheckBillDetail",
          "name": "storeCheckBillDetail",
          "style": {
            "navigationBarTitleText": "对账详情",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "login/Login",
          "name": "login",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "allAppList/AllAppList",
          "name": "allAppList",
          "style": {
            "navigationBarTitleText": "全部应用",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "passwordChange/PasswordChange",
          "name": "passwordChange",
          "style": {
            "navigationBarTitleText": "修改密码",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "disableScroll": true
          }
        },
        {
          "path": "minePasswordChange/PasswordChange",
          "name": "minePasswordChange",
          "style": {
            "navigationBarTitleText": "修改密码",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "storeSelect/StoreSelect",
          "name": "storeSelect",
          "style": {
            "navigationBarTitleText": "请选择门店",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
        // #ifdef APP-PLUS
        ,
        {
          "path": "about/About",
          "name": "about",
          "style": {
            "navigationBarTitleText": "关于我们",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false
          }
        }
        // #endif
        ,
        // #ifdef MP-WEIXIN
        {
          "path": "bigData/BigData",
          "name": "bigData",
          "style": {
            "navigationBarTitleText": "数据中心",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "navigationBarTextStyle": "black",
            "navigationBarBackgroundColor": "#E9EEFB"
          }
        },
        {
          "path": "taskCenter/TaskCenter",
          "name": "taskCenter",
          "style": {
            "navigationBarTitleText": "任务中心",
            "navigationBarBackgroundColor": "#E9EEFB",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        // #endif
        {
          "path": "storeSelect/StoreSearch",
          "name": "storeSearch",
          "style": {
            "navigationBarTitleText": "请选择门店",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "goodsSearch/GoodsSearch",
          "name": "GoodsSearch",
          "style": {
            "navigationBarBackgroundColor": "#f0f2f5",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "商品查询",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "goodsSearch/ItemInfo",
          "name": "goodsSearchItemInfo",
          "style": {
            "navigationBarBackgroundColor": "#f0f2f5",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "单品详情",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "goodsExcption/GoodsExcptionMenu",
          "name": "goodsExcptionMenu",
          "style": {
            "navigationBarBackgroundColor": "#E7F3FD",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "异常商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "goodsExcption/GoodsExcptionList",
          "name": "goodsExcptionList",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "商品列表",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "goodsExcption/GoodsExceptionListSearch",
          "name": "goodsExceptionListSearch",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesMkh",
      "pages": [
        {
          "path": "newsCenter/NewsList",
          "name": "newsList",
          "style": {
            "navigationBarTitleText": "消息中心",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "newsCenter/NewsDetail",
          "name": "newsDetail",
          "style": {
            "navigationBarTitleText": "公告",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/price/Price",
          "name": "price",
          "style": {
            "navigationBarTitleText": "促销价",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/price/Promption",
          "name": "pricePromption",
          "style": {
            "navigationBarTitleText": "促销折扣",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/discount/Common",
          "name": "discountCommon",
          "style": {
            "navigationBarTitleText": "普通折扣",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/discount/StepDiscount",
          "name": "discountStep",
          "style": {
            "navigationBarTitleText": "阶梯折扣",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/discount/Clear",
          "name": "discountClear",
          "style": {
            "navigationBarTitleText": "清仓促销",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/discount/Concat",
          "name": "discountConcat",
          "style": {
            "navigationBarTitleText": "组合折扣",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/fullreduce/Common",
          "name": "fullreduceCommon",
          "style": {
            "navigationBarTitleText": "普通满减",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/fullreduce/Per",
          "name": "fullreducePer",
          "style": {
            "navigationBarTitleText": "每满减",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/fullreduce/Step",
          "name": "fullreduceStep",
          "style": {
            "navigationBarTitleText": "阶梯满减",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/exchange/FullAmount",
          "name": "exchangeFullAmount",
          "style": {
            "navigationBarTitleText": "满额换购",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/exchange/SingleGood",
          "name": "exchangeSingleGood",
          "style": {
            "navigationBarTitleText": "单品换购",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/gift/SingleGift",
          "name": "singleGift",
          "style": {
            "navigationBarTitleText": "单品买赠",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/gift/WholeGift",
          "name": "wholeGift",
          "style": {
            "navigationBarTitleText": "整单满赠",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/MarketPlayList",
          "name": "marketPlayList",
          "style": {
            "navigationBarTitleText": "营销玩法",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "marketPlay/MarketPlayDetail",
          "name": "marketPlayDetail",
          "style": {
            "navigationBarTitleText": "营销玩法",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/Collide",
          "name": "collide",
          "style": {
            "navigationBarTitleText": "活动冲突提醒",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/TemplateChoose",
          "name": "templateChoose",
          "style": {
            "navigationBarTitleText": "新增促销",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "marketPlay/MarketPlaySkuSelect",
          "name": "marketPlaySkuSelect",
          "style": {
            "navigationBarTitleText": "选择商品",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "marketPlay/MarketPlaySkuSearch",
          "name": "marketPlaySkuSearch",
          "style": {
            "navigationBarTitleText": "选择商品",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "aftermarket/QtyFeedBack",
          "name": "qtyFeedBack",
          "style": {
            "navigationBarTitleText": "质量反馈",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "aftermarket/HistoryFeedBack",
          "name": "historyFeedBack",
          "style": {
            "navigationBarTitleText": "历史反馈记录",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "aftermarket/SearchGoods",
          "name": "aftermarketSearchGoods",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "aftermarket/AddGoods",
          "name": "aftermarketAddGoods",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "aftermarket/cmp/PreImage",
          "name": "aftermarketPreImage",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "aftermarketV2/AftermarketList",
          "name": "aftermarketList",
          "style": {
            "navigationBarTitleText": "质量反馈",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "aftermarketV2/AftermarketSearch",
          "name": "aftermarketSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "aftermarketV2/AftermarketEdit",
          "name": "aftermarketEdit",
          "style": {
            "navigationBarTitleText": "新增质量反馈",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "aftermarketV2/AftermarketGoodSearch",
          "name": "aftermarketGoodSearch",
          "style": {
            "navigationBarTitleText": "新增质量反馈",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "aftermarketV2/AftermarketDetail",
          "name": "aftermarketDetailt",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesShopManage",
      "pages": [
        {
          "path": "stkOutBck/StkOutBckList",
          "name": "stkOutBckList",
          "style": {
            "navigationBarTitleText": "退仓库",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "stkOutBck/StkOutBckListSearch",
          "name": "stkOutBckListSearch",
          "style": {
            "navigationBarTitleText": "退仓库",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "stkOutBck/StkOutBckDetail",
          "name": "stkOutBckDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "stkOutBck/StkOutBckEdit",
          "name": "stkOutBckEdit",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "stkOutBck/StkOutBckLineTotal",
          "name": "stkOutBckLineTotal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "returnApply/ReturnApplyList",
          "name": "returnApplyList",
          "style": {
            "navigationBarTitleText": "退货申请",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "returnApply/ReturnApplyListSearch",
          "name": "returnApplyListSearch",
          "style": {
            "navigationBarTitleText": "退货申请",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "returnApply/ReturnApplyEdit",
          "name": "returnApplyEdit",
          "style": {
            "navigationBarTitleText": "新增退货申请",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "returnApply/ReturnApplyGoodSearch",
          "name": "returnApplyGoodSearch",
          "style": {
            "navigationBarTitleText": "搜索商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "returnApply/ReturnApplySkuSelect",
          "name": "returnApplySkuSelect",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "returnApply/ReturnApplyDetail",
          "name": "returnApplyDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "returnApply/ReturnApplyLineTotal",
          "name": "returnApplyLineTotal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receiptDiff/ReceiptDiffList",
          "name": "receiptDiffList",
          "style": {
            "navigationBarTitleText": "收货差异",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "receiptDiff/ReceiptDiffListSearch",
          "name": "receiptDiffListSearch",
          "style": {
            "navigationBarTitleText": "收货差异",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receiptDiff/ReceiptDiffDetail",
          "name": "receiptDiffDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "overApply/OverApplyList",
          "name": "overApplyList",
          "style": {
            "navigationBarTitleText": "报溢",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "overApply/OverApplyListSearch",
          "name": "overApplyListSearch",
          "style": {
            "navigationBarTitleText": "报溢",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "overApply/OverApplyEdit",
          "name": "overApplyEdit",
          "style": {
            "navigationBarTitleText": "新增报溢",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "overApply/OverApplyGoodSearch",
          "name": "overApplyGoodSearch",
          "style": {
            "navigationBarTitleText": "搜索商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "overApply/OverApplySkuSelect",
          "name": "overApplySkuSelect",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "overApply/OverApplyDetail",
          "name": "overApplyDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "overApply/OverApplyLineTotal",
          "name": "overApplyLineTotal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "lossApply/LossApplyList",
          "name": "lossApplyList",
          "style": {
            "navigationBarTitleText": "报损",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "lossApply/LossApplyListSearch",
          "name": "lossApplyListSearch",
          "style": {
            "navigationBarTitleText": "报损",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "lossApply/LossApplyEdit",
          "name": "lossApplyEdit",
          "style": {
            "navigationBarTitleText": "新增报损",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "lossApply/LossApplyGoodSearch",
          "name": "lossApplyGoodSearch",
          "style": {
            "navigationBarTitleText": "搜索商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "lossApply/LossApplySkuSelect",
          "name": "lossApplySkuSelect",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "lossApply/LossApplyDetail",
          "name": "lossApplyDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "lossApply/LossApplyLineTotal",
          "name": "lossApplyLineTotal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXFApply/InvXFApplyList",
          "name": "invXFApplyList",
          "style": {
            "navigationBarTitleText": "调拨申请",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "invXFApply/InvXFApplyListSearch",
          "name": "invXFApplyListSearch",
          "style": {
            "navigationBarTitleText": "调拨申请",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXFApply/InvXFApplyEdit",
          "name": "invXFApplyEdit",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXFApply/InvXFApplyDetail",
          "name": "invXFApplyDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXFApply/InvXFApplyLineTotal",
          "name": "invXFApplyLineTotal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXFApply/InvXFApplySkuSelect",
          "name": "invXFApplySkuSelect",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXFApply/InvXFApplySkuSearch",
          "name": "invXFApplySkuSearch",
          "style": {
            "navigationBarTitleText": "搜索商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXFApply/InvXFApplyPkaSearch",
          "name": "InvXFApplyPkaSearch",
          "style": {
            "navigationBarTitleText": "选择包材",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXFApply/cmp/ShopSelect",
          "name": "invXFApplyShopSelect",
          "style": {
            "navigationBarTitleText": "选择门店",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXFApply/cmp/ShopSearch",
          "name": "invXFApplyShopSearch",
          "style": {
            "navigationBarTitleText": "选择门店",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "dirAlcBck/DirAlcBckList",
          "name": "dirAlcBckList",
          "style": {
            "navigationBarTitleText": "退供应商",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "dirAlcBck/DirAlcBckListSearch",
          "name": "dirAlcBckListSearch",
          "style": {
            "navigationBarTitleText": "退供应商",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "dirAlcBck/DirAlcBckDetail",
          "name": "dirAlcBckDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "dirAlcBck/DirAlcBckEdit",
          "name": "dirAlcBckEdit",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "dirAlcBck/DirAlcBckLineTotal",
          "name": "dirAlcBckLineTotal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesSA",
      "pages": [
        {
          "path": "openCheck/CheckPlanList",
          "name": "openCheckPlanList",
          "style": {
            "navigationBarTitleText": "不停业盘点",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "openCheck/CheckBillList",
          "name": "openCheckBillList",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "openCheck/CheckList",
          "name": "openCheckList",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "openCheck/CheckDtl",
          "name": "openCheckDtl",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "openCheck/NormalCheck",
          "name": "openNormalCheck",
          "style": {
            "navigationBarTitleText": "盘点",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "openCheck/CheckSkuSelect",
          "name": "openCheckSkuSelect",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "disableScroll": true
          }
        },
        {
          "path": "openCheck/CheckSkuSearch",
          "name": "openCheckSkuSearch",
          "style": {
            "navigationBarTitleText": "搜索商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXF/InvXFList",
          "name": "invXFList",
          "style": {
            "navigationBarTitleText": "调拨",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "invXF/InvXFListSearch",
          "name": "invXFListSearch",
          "style": {
            "navigationBarTitleText": "调拨",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXF/InvXFDetail",
          "name": "invXFDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "invXF/InvXFLineTotal",
          "name": "invXFLineTotal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "selfPick/SelfPickList",
          "name": "selfPickList",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "门店自采",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "selfPick/SelfPickSearch",
          "name": "selfPickSearch",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "门店自采",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "selfPick/SelfPickDetail",
          "name": "selfPickDetail",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "门店自采",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "selfPick/SelfPickEdit",
          "name": "selfPickEdit",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "onReachBottomDistance": 92,
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "新增门店自采",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "SelfPickGoodSearch",
          "path": "selfPick/SelfPickGoodSearch",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "SelfPickSkuSelect",
          "path": "selfPick/SelfPickSkuSelect",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "选择商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "selfPick/SelfPickLineTotal",
          "name": "selfPickLineTotal",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "supplierDirect/SupplierDirectList",
          "name": "supplierDirectList",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "直送收货",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "supplierDirect/SupplierDirectSearch",
          "name": "supplierDirectSearch",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "直送收货",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "supplierDirect/SupplierDirectDetail",
          "name": "supplierDirectDetail",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "直送收货",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "supplierDirect/SupplierDirectEdit",
          "name": "supplierDirectEdit",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "新增直送收货",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "supplierDirect/SupplierDirectLineTotal",
          "name": "supplierDirectLineTotal",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "supplierDirect/SupplierDirectGoodSearch",
          "name": "supplierDirectGoodSearch",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "搜索商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "supplierDirectBck/SupplierDirectBckList",
          "name": "supplierDirectBckList",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "直送退货",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "supplierDirectBck/SupplierDirectBckSearch",
          "name": "supplierDirectBckSearch",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "直送退货",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "supplierDirectBck/SupplierDirectBckDetail",
          "name": "supplierDirectBckDetail",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "直送退货",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "supplierDirectBck/SupplierDirectBckEdit",
          "name": "supplierDirectBckEdit",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "新增直送退货",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "supplierDirectBck/SupplierDirectBckLineTotal",
          "name": "supplierDirectBckLineTotal",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "supplierDirectBck/SupplierDirectBckGoodSearch",
          "name": "supplierDirectBckGoodSearch",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "搜索商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "faq/FaqList",
          "name": "faqList",
          "style": {
            "navigationBarTitleText": "常见问题",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "report/Report",
          "name": "report",
          "style": {
            "navigationBarTitleText": "报表",
            "navigationBarTextStyle": "black",
            "navigationBarBackgroundColor": "#FFFFFF",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "faq/FaqDetail",
          "name": "faqDetail",
          "style": {
            "navigationBarTitleText": "问题详情",
            "navigationBarTextStyle": "black",
            "navigationBarBackgroundColor": "#FFFFFF",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "faq/FaqListSearch",
          "name": "faqListSearch",
          "style": {
            "navigationBarTitleText": "常见问题",
            "navigationBarTextStyle": "black",
            "navigationBarBackgroundColor": "#FFFFFF",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "billPayments/PayList",
          "name": "payList",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "账单",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "billPayments/PaySearch",
          "name": "paySearch",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "账单",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "PayDetail",
          "path": "billPayments/PayDetail",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "账单详情",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "billPayments/PayLineTotal",
          "name": "PayLineTotal",
          "style": {
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationBarTitleText": "账单详情",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "platformShopSku/PlatformShopSku",
          "name": "platformShopSku",
          "style": {
            "navigationBarTitleText": "线上商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "platformShopSku/PlatformShopSkuSearch",
          "name": "platformShopSkuSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "rechgBookList",
          "path": "rechgBook/RechgBookList",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "enablePullDownRefresh": true,
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "利润核算",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "name": "rechgBookSearch",
          "path": "rechgBook/RechgBookSearch",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "rechgBookEdit",
          "path": "rechgBook/RechgBookEdit",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "onReachBottomDistance": 92,
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "新增费用",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "rechgBookDetail",
          "path": "rechgBook/RechgBookDetail",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "利润核算详情",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "rechgBookChart",
          "path": "rechgBook/RechgBookChart",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "onReachBottomDistance": 92,
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "门店收支",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesSB",
      "pages": [
        {
          "path": "tallyApply/TallyApplyList",
          "name": "TallyApplyList",
          "style": {
            "navigationBarTitleText": "价签申请",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "tallyApply/TallyApplyListSearch",
          "name": "TallyApplyListSearch",
          "style": {
            "navigationBarTitleText": "价签申请",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "tallyApply/TallyApplyDetail",
          "name": "TallyApplyDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "tallyApply/TallyApplyDetailLineTotal",
          "name": "TallyApplyDetailLineTotal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "tallyApply/TallyApplySkuSelect",
          "name": "TallyApplySkuSelect",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "tallyApply/TallyApplySkuSearch",
          "name": "TallyApplySkuSearch",
          "style": {
            "navigationBarTitleText": "搜索商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "tallyApply/TallyApplyEdit",
          "name": "TallyApplyEdit",
          "style": {
            "navigationBarTitleText": "新增价签申请",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "tallyApply/TallyApplyTypeEdit",
          "name": "TallyApplyTypeEdit",
          "style": {
            "navigationBarTitleText": "新增价签申请",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "orderExplosiveActivity/OrderActivity",
          "name": "OrderActivity",
          "style": {
            "navigationBarTitleText": "订货活动",
            "enablePullDownRefresh": true,
            "navigationBarTextStyle": "black",
            "navigationBarBackgroundColor": "#FFFFFF",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "orderExplosiveActivity/OrderActivityDetail",
          "name": "OrderActivityDetail",
          "style": {
            "navigationBarTitleText": "活动详情",
            "enablePullDownRefresh": false,
            "navigationBarTextStyle": "black",
            "navigationBarBackgroundColor": "#FFFFFF",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "orderExplosiveActivity/OrderPage",
          "name": "OrderPage",
          "style": {
            "navigationBarTitleText": "活动详情",
            "enablePullDownRefresh": false,
            "navigationBarTextStyle": "black",
            "navigationBarBackgroundColor": "#FFFFFF",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "croOrgSaleBck/CroOrgSaleBckList",
          "name": "CroOrgSaleBckList",
          "style": {
            "navigationBarTitleText": "退公司",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "croOrgSaleBck/CroOrgSaleBckListSearch",
          "name": "CroOrgSaleBckListSearch",
          "style": {
            "navigationBarTitleText": "退公司",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "croOrgSaleBck/CroOrgSaleBckDetail",
          "name": "CroOrgSaleBckDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "croOrgSaleBck/CroOrgSaleBckEdit",
          "name": "CroOrgSaleBckEdit",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "croOrgSaleBck/CroOrgSaleBckLineTotal",
          "name": "CroOrgSaleBckLineTotal",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "shopSkuTag/ShopSkuList",
          "name": "ShopSku",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "价格带",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "shopSkuTag/ShopSkuSearch",
          "name": "ShopSkuSearch",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "shopSkuTag/ShopSkuEdit",
          "name": "ShopSkuEdit",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "shopSkuTag/ShopSkuDetail",
          "name": "ShopSkuDetail",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "价格带",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "orderSearch/OrderList",
          "name": "OrderList",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "订单查询",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "orderSearch/OrderGoodList",
          "name": "OrderGoodList",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "商品清单",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "orderSearch/OrderGoodSearch",
          "name": "OrderGoodSearch",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "商品清单",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "check/CheckPlanList",
          "name": "CheckPlanList",
          "style": {
            "navigationBarTitleText": "盘点",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "check/CheckBillList",
          "name": "CheckBillList",
          "style": {
            "navigationBarTitleText": "盈亏",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "check/CheckList",
          "name": "CheckList",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "check/CheckDtl",
          "name": "CheckDtl",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "check/NormalCheck",
          "name": "NormalCheck",
          "style": {
            "navigationBarTitleText": "初盘",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "check/RepeatCheck",
          "name": "RepeatCheck",
          "style": {
            "navigationBarTitleText": "复盘",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "check/CheckSkuSelect",
          "name": "CheckSkuSelect",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "disableScroll": true
          }
        },
        {
          "path": "check/CheckSkuSearch",
          "name": "CheckSkuSearch",
          "style": {
            "navigationBarTitleText": "搜索商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "check/BillConfirm",
          "name": "billConfirm",
          "style": {
            "navigationBarTitleText": "单据确认",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "billManage/BillList",
          "name": "BillList",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "交易流水",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "billManage/BillDetail",
          "name": "BillDetail",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "交易详情",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "lineAdj/LineList",
          "name": "LineList",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "线路调整",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50,
            "disableScroll": true
          }
        },
        {
          "path": "lineAdj/LineEdit",
          "name": "LineEdit",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "线路调整",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "disableScroll": true
          }
        },
        {
          "path": "lineAdj/LineShop",
          "name": "LineShop",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "添加门店",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "disableScroll": true
          }
        },
        {
          "path": "lineAdj/LineDetail",
          "name": "LineDetail",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "订单详情",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "onReachBottomDistance": 80
          }
        },
        {
          "path": "fundAccount/FundAccountList",
          "name": "FundAccountList",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "账户流水",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "fundAccount/FundAccountSearch",
          "name": "FundAccountSearch",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "fundAccount/FundAccountDetail",
          "name": "FundAccountDetail",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "明细",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "invLmtAdjList",
          "path": "invLmtAdj/InvLmtAdjList",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "enablePullDownRefresh": true,
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "库存上下限调整",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "name": "invLmtAdjSearch",
          "path": "invLmtAdj/InvLmtAdjSearch",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "库存上下限调整",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "invLmtAdjDetail",
          "path": "invLmtAdj/InvLmtAdjDetail",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "详情",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "invLmtAdjEdit",
          "path": "invLmtAdj/InvLmtAdjEdit",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "onReachBottomDistance": 92,
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "新增上下限调整",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "invLmtAdjGoodSearch",
          "path": "invLmtAdj/InvLmtAdjGoodSearch",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "invLmtAdjSkuSelect",
          "path": "invLmtAdj/InvLmtAdjSkuSelect",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "选择商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "invLmtAdjLineTotal",
          "path": "invLmtAdj/InvLmtAdjLineTotal",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesSC",
      "pages": [
        {
          "path": "fileRegist/FileRegistList",
          "name": "fileRegistList",
          "style": {
            "navigationBarTitleText": "文件返回登记",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "fileRegist/FileRegistListSearch",
          "name": "fileRegistListSearch",
          "style": {
            "navigationBarTitleText": "文件返回登记",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "fileRegist/FileRegistEdit",
          "name": "fileRegistEdit",
          "style": {
            "navigationBarTitleText": "新增文件返回登记",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "fileRegist/FileRegistDetail",
          "name": "fileRegistDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "packReceipt/commonPage/SelectModule",
          "name": "packSelectModule",
          "style": {
            "navigationBarTitleText": "周转筐管理",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "packReceipt/PackReceiptList",
          "name": "packReceiptList",
          "style": {
            "navigationBarTitleText": "周转筐收货",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "packReceipt/PackReceiptSearch",
          "name": "packReceiptSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "packReceipt/PackReceiptEdit",
          "name": "packReceiptEdit",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "packReceipt/PackReceiptDetail",
          "name": "packReceiptDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "packRecycle/PackRecycleList",
          "name": "packRecycleList",
          "style": {
            "navigationBarTitleText": "周转筐回收",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "packRecycle/PackRecycleSearch",
          "name": "packRecycleSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "packRecycle/PackRecycleEdit",
          "name": "packRecycleEdit",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "packRecycle/PackRecycleDetail",
          "name": "packRecycleDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "useApply/UseApplyList",
          "name": "useApplyList",
          "style": {
            "navigationBarTitleText": "门店领用",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "useApply/UseApplyListSearch",
          "name": "useApplyListSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "useApply/UseApplyEdit",
          "name": "useApplyEdit",
          "style": {
            "navigationBarTitleText": "新增门店领用",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "useApply/UseApplySkuSearch",
          "name": "useApplySkuSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "useApply/UseApplyDetail",
          "name": "useApplyDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "useApply/UseApplyLineTotal",
          "name": "useApplyLineTotal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "packInventory/PackInventoryList",
          "name": "packInventory",
          "style": {
            "navigationBarTitleText": "周转筐盘点",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "packInventory/PackInventoryEdit",
          "name": "packInventoryEdit",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "packInventory/PackInventoryDetail",
          // "name": "packInventoryDetail",
          "name": "packCheckDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "goodsValidDate/gvdList/gvdList",
          "name": "gvdList",
          "style": {
            "navigationBarTitleText": "商品效期",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "goodsValidDate/gvdSearch/gvdSearch",
          "name": "gvdSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "processBill/ProcessBillList",
          "name": "processBillList",
          "style": {
            "navigationBarTitleText": "加工",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "processBill/ProcessBillListSearch",
          "name": "processBillListSearch",
          "style": {
            "navigationBarTitleText": "加工",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "processBill/ProcessBillEdit",
          "name": "processBillEdit",
          "style": {
            "navigationBarTitleText": "新增加工",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "processBill/ProcessBillSkuSelect",
          "name": "processBillSkuSelect",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "processBill/ProcessBillGoodSearch",
          "name": "processBillGoodSearch",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "processBill/ProcessBillDetail",
          "name": "processBillDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "processBill/FinishedProductPrint",
          "name": "finishedProductPrint",
          "style": {
            "navigationBarTitleText": "成品打印",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        // #ifdef APP-PLUS
        {
          "path": "processBill/FinishedProductPrintAndroid",
          "name": "finishedProductPrintAndroid",
          "style": {
            "navigationBarTitleText": "成品打印",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        // #endif
        {
          "path": "processBill/ProcessBillFormula",
          "name": "processBillFormula",
          "style": {
            "navigationBarTitleText": "选择配方",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "preRequireGoods/PreRequireGoodsList",
          "name": "preRequireGoodsList",
          "style": {
            "navigationBarTitleText": "预报货",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "requireGoodsHome/orderHome/OrderHome",
          "name": "orderHome",
          "style": {
            "navigationBarTitleText": "订货",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "requireGoodsHome/orderHome/OrderHomeSkuSearch",
          "name": "OrderHomeSkuSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoodsHome/activityPage/DeliveryActivity",
          "name": "DeliveryActivity",
          "style": {
            "enablePullDownRefresh": true,
            "navigationBarTitleText": "",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "requireGoodsHome/orderCategory/OrderCategory",
          "name": "OrderCategory",
          "style": {
            "navigationBarTitleText": "分类",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "disableScroll": true
          }
        },
        {
          "path": "requireGoodsHome/orderCategory/RequireGoodsCart",
          "name": "deliveryShopCart",
          "style": {
            "navigationBarTitleText": "商品明细",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoodsHome/orderList/OrderList",
          "name": "deliveryOrderList",
          "style": {
            "navigationBarTitleText": "订货单",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoodsHome/goodsDetail/GoodsDetail",
          "name": "deliveryGoodsDetail",
          "style": {
            "navigationBarTitleText": "商品详情",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoodsHome/goodsDetail/GoodsCommentList",
          "name": "GoodsCommentList",
          "style": {
            "navigationBarTitleText": "商品评价",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesSD",
      "pages": [
        {
          "path": "labelPrinting/LabelPrintingList",
          "name": "labelPrintingList",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "标价签打印",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        // #ifdef APP-PLUS
        {
          "path": "labelPrinting/LabelPrintingListAndroid",
          "name": "labelPrintingListAndroid",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "标价签打印",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        // #endif
        {
          "path": "labelPrinting/LabelPrintingSearch",
          "name": "labelPrintingSearch",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "labelPrinting/LabelPrintingSkuImportModeSelect",
          "name": "labelPrintingSkuImportModeSelect",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "标价签打印",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "labelPrinting/LabelPrintingSkuImport",
          "name": "labelPrintingSkuImport",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "labelPrinting/LabelPrintingSkuSelect",
          "name": "labelPrintingSkuSelect",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "添加商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "AppForeTaste/AppForeTasteHistory",
          "name": "appForeTasteHistory",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "AppForeTaste/AppForeTasteTest",
          "name": "appForeTasteTest",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "InventoryNearbyStores/InventoryNearbyStoresList",
          "name": "inventoryNearbyStoresList",
          "style": {
            "navigationBarTitleText": "周边门店库存",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "InventoryNearbyStores/InventoryNearbyStoresSearch",
          "name": "inventoryNearbyStoresSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "cusInvCheck/CusInvCheckList",
          "name": "cusInvCheckList",
          "style": {
            "navigationBarTitleText": "自主盘点",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "cusInvCheck/CusInvCheckSearch",
          "name": "cusInvCheckSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "cusInvCheck/CusInvCheckEdit",
          "name": "cusInvCheckEdit",
          "style": {
            "navigationBarTitleText": "盘点",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "onReachBottomDistance": 70
          }
        },
        {
          "path": "cusInvCheck/CusInvCheckTemplateEdit",
          "name": "cusInvCheckTemplateEdit",
          "style": {
            "navigationBarTitleText": "盘点",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "onReachBottomDistance": 70
          }
        },
        {
          "path": "cusInvCheck/CusInvCheckGoodSearch",
          "name": "cusInvCheckGoodSearch",
          "style": {
            "navigationBarTitleText": "搜索添加",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "cusInvCheck/CusInvCheckDetail",
          "name": "cusInvCheckDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "onReachBottomDistance": 102
          }
        },
        {
          "path": "cusInvCheck/CusInvCheckDiffDetail",
          "name": "cusInvCheckDiffDetail",
          "style": {
            "navigationBarTitleText": "盈亏",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          },
          "onReachBottomDistance": 50
        },
        {
          "path": "AppBatchAdjApply/AppBatchAdjApplyList",
          "name": "AppBatchAdjApplyList",
          "style": {
            "navigationBarTitleText": "调整记录",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "AppBatchAdjApply/AppBatchAdjApplyListSearch",
          "name": "AppBatchAdjApplyListSearch",
          "style": {
            "navigationBarTitleText": "调整记录",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "AppBatchAdjApply/AppBatchAdjApplyDetail",
          "name": "AppBatchAdjApplyDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "AppBatchAdjApply/AppBatchAdjApplyEdit",
          "name": "AppBatchAdjApplyEdit",
          "style": {
            "navigationBarTitleText": "批次号调整",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "onReachBottomDistance": 70
          }
        },
        {
          "path": "AppBatchAdjApply/AppBatchAdjApplySkuSelect",
          "name": "AppBatchAdjApplySkuSelect",
          "style": {
            "navigationBarTitleText": "添加商品",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "onReachBottomDistance": 70
          }
        },
        {
          "path": "AppBatchAdjApply/AppBatchAdjApplySkuSearch",
          "name": "AppBatchAdjApplySkuSearch",
          "style": {
            "navigationBarTitleText": "搜索添加",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "ReceiptDiffApply/ReceiptDiffApplyList",
          "name": "ReceiptDiffApplyList",
          "style": {
            "navigationBarTitleText": "配货差异申请",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#FFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "ReceiptDiffApply/ReceiptDiffApplyListSearch",
          "name": "ReceiptDiffApplyListSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "ReceiptDiffApply/ReceiptDiffApplyEdit",
          "name": "ReceiptDiffApplyEdit",
          "style": {
            "navigationBarTitleText": "新增配货差异申请",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "ReceiptDiffApply/ReceiptDiffApplySkuSelect",
          "name": "ReceiptDiffApplySkuSelect",
          "style": {
            "navigationBarTitleText": "添加商品",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "onReachBottomDistance": 70
          }
        },
        {
          "path": "ReceiptDiffApply/ReceiptDiffApplySkuSearch",
          "name": "ReceiptDiffApplySkuSearch",
          "style": {
            "navigationBarTitleText": "搜索添加",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "ReceiptDiffApply/ReceiptDiffApplyDetail",
          "name": "ReceiptDiffApplyDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "ReceiptDiffApply/ReceiptDiffApplyTotal",
          "name": "ReceiptDiffApplyTotal",
          "style": {
            "navigationBarTitleText": "全部商品",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "priceAdjApply/PriceAdjApplyList",
          "name": "priceAdjApplyList",
          "style": {
            "navigationBarTitleText": "调价申请",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "priceAdjApply/PriceAdjApplyEdit",
          "name": "priceAdjApplyEdit",
          "style": {
            "navigationBarTitleText": "新增调价申请",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "priceAdjApply/PriceAdjApplyDetail",
          "name": "PriceAdjApplyDetail",
          "style": {
            "navigationBarTitleText": "调价申请详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "priceAdjApply/PriceAdjApplyListSearch",
          "name": "priceAdjApplyListSearch",
          "style": {
            "navigationBarTitleText": "调价申请",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "priceAdjApply/PriceAdjApplyLineTotal",
          "name": "priceAdjApplyLineTotal",
          "style": {
            "navigationBarTitleText": "全部商品",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "priceAdjApply/PriceAdjApplySkuSelect",
          "name": "priceAdjApplySkuSelect",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "priceAdjApply/PriceAdjApplySkuSearch",
          "name": "priceAdjApplySkuSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "billPaymentsV2/PayList",
          "name": "payListV2",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "账单",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 100
          }
        },
        {
          "path": "billPaymentsV2/PaySearch",
          "name": "paySearchV2",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "billPaymentsV2/PayDetail",
          "name": "payDetailV2",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "billPaymentsV2/PayDebtRtnHistory",
          "name": "payDebtRtnHistory",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "欠款记录",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "shopScore/ShopScore",
          "name": "shopScore",
          "style": {
            "navigationBarBackgroundColor": "#E7F3FD",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "门店考评",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "webViewPage/index",
          "name": "webViewPage",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "gdQuery/GdQueryPanel",
          "name": "gdQueryPanel",
          "style": {
            "navigationBarTitleText": "商品查询",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "gdQuery/GdQueryPanelOnlySearch",
          "name": "gdQueryPanelOnlySearch",
          "style": {
            "navigationBarTitleText": "商品查询",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "gdQuery/DeleteExhibitLocation",
          "name": "deleteExhibitLocation",
          "style": {
            "navigationBarTitleText": "删除陈列位置",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "gdQuery/GdQueryPanelSearch",
          "name": "gdQueryPanelSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "gdQuery/GdQuerySkuDetail",
          "name": "gdQuerySkuDetail",
          "style": {
            "navigationBarTitleText": "商品详情",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesSE",
      "pages": [
        {
          "path": "requireGoods/RequireGoodsList",
          "name": "requireGoodsList",
          "style": {
            "navigationBarTitleText": "订货",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "requireGoods/RequireAllocdirList",
          "name": "requireAllocdirList",
          "style": {
            "navigationBarTitleText": "选择导入目录",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoods/RequireListSearch",
          "name": "requireListSearch",
          "style": {
            "navigationBarTitleText": "订货",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoods/RequireGoodsDetail",
          "name": "requireGoodsDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoods/RequireGoodsLineTotal",
          "name": "requireGoodsLineTotal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoods/RequireGoodsEdit",
          "name": "requireGoodsEdit",
          "style": {
            "navigationBarTitleText": "正常订货",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "disableScroll": true
          }
        },
        {
          "path": "requireGoods/RequireGoodsEditSearch",
          "name": "requireGoodsEditSearch",
          "style": {
            "navigationBarTitleText": "正常订货",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "disableScroll": true
          }
        },
        {
          "path": "requireGoods/RequireGoodsAddedEdit",
          "name": "requireGoodsAddedEdit",
          "style": {
            "navigationBarTitleText": "加单订货",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoods/RequireGoodsCart",
          "name": "requireGoodsCart",
          "style": {
            "navigationBarTitleText": "商品明细",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoods/RequireGoodsConfirm",
          "name": "requireGoodsConfirm",
          "style": {
            "navigationBarTitleText": "提交成功",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoods/RequireComment",
          "name": "requireComment",
          "style": {
            "navigationBarTitleText": "评价",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        // 周黑鸭通过模板订货
        {
          "path": "requireGoods/RequireByTemplate",
          "name": "requireByTemplate",
          "style": {
            "navigationBarTitleText": "订货"
          }
        },
        {
          "path": "requireGoods/RequireReduceList",
          "name": "requireReduceList",
          "style": {
            "navigationBarTitleText": "订货"
          }
        },
        {
          "path": "requireGoods/ReduceByTemplate",
          "name": "reduceByTemplate",
          "style": {
            "navigationBarTitleText": "订货"
          }
        },
        {
          "path": "requireGoodsCdh/RequireGoodsList",
          "name": "requireGoodsListCdh",
          "style": {
            "navigationBarTitleText": "订货",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#1C64FD",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "path": "requireGoodsCdh/RequireListSearch",
          "name": "requireListSearchCdh",
          "style": {
            "navigationBarTitleText": "订货",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoodsCdh/RequireGoodsDetail",
          "name": "requireGoodsDetailCdh",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoodsCdh/RequireGoodsLineTotal",
          "name": "requireGoodsLineTotalCdh",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoodsCdh/RequireGoodsEdit",
          "name": "requireGoodsEditCdh",
          "style": {
            "navigationBarTitleText": "订货",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoodsCdh/RequireGoodsEditSearch",
          "name": "requireGoodsEditCdhSearch",
          "style": {
            "navigationBarTitleText": "订货",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoodsCdh/RequireGoodsCart",
          "name": "requireGoodsCartCdh",
          "style": {
            "navigationBarTitleText": "商品明细",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoodsCdh/RequireSuggestedOrder",
          "name": "requireSuggestedOrderCdh",
          "style": {
            "navigationBarTitleText": "叫货建议单",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "requireGoodsCdh/RequireAddList",
          "name": "requireAddListCdh",
          "style": {
            "navigationBarTitleText": "添加商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "preOrderActivity/PreOrderActivityList",
          "name": "preOrderActivityList",
          "style": {
            "navigationBarTitleText": "预订货活动",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "preOrderActivity/PreOrderActivityDetail",
          "name": "preOrderActivityDetail",
          "style": {
            "navigationBarTitleText": "活动详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "preGoodsOrder/PreGoodsOrderList",
          "name": "preGoodsOrderList",
          "style": {
            "navigationBarTitleText": "预订货订单",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "preGoodsOrder/PreGoodsOrderListSearch",
          "name": "preGoodsOrderListSearch",
          "style": {
            "navigationBarTitleText": "预订货订单",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "preGoodsOrder/PreGoodsOrderDetail",
          "name": "preGoodsOrderDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "appCouponCode/AppCouponCodeList",
          "name": "AppCouponCodeList",
          "style": {
            "navigationBarTitleText": "单品打折",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "appCouponCode/AppCouponCodeEdit",
          "name": "AppCouponCodeEdit",
          "style": {
            "navigationBarTitleText": "新增单品打折",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "appCouponCode/AppCouponCodeSearch",
          "name": "AppCouponCodeSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "appCouponCode/AppCouponCodeDtl",
          "name": "AppCouponCodeDtl",
          "style": {
            "navigationBarTitleText": "单品打折详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesSF",
      "pages": [
        {
          "path": "receipt/ReceiptList",
          "name": "receiptList",
          "style": {
            "navigationBarTitleText": "收货",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "receipt/ReceiptCombList",
          "name": "receiptCombList",
          "style": {
            "navigationBarTitleText": "多单收货",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "receipt/ReceiptCombEdit",
          "name": "receiptCombEdit",
          "style": {
            "navigationBarTitleText": "收货详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receipt/ReceiptListSearch",
          "name": "receiptListSearch",
          "style": {
            "navigationBarTitleText": "收货",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receipt/ReceiptEdit",
          "name": "receiptEdit",
          "style": {
            "navigationBarTitleText": "收货详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receipt/ReceiptBoxEdit",
          "name": "receiptBoxEdit",
          "style": {
            "navigationBarTitleText": "收货详情",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        // 拆零箱编辑页面
        {
          "path": "receipt/ReceiptBoxLineEdit",
          "name": "receiptBoxLineEdit",
          "style": {
            "navigationBarTitleText": "收货详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receipt/ReceiptBoxDiffCheck",
          "name": "receiptBoxDiffCheck",
          "style": {
            "navigationBarTitleText": "差异明细",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receipt/ReceiptBoxSearch",
          "name": "receiptBoxSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receipt/ReceiptDetail",
          "name": "receiptDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receipt/ReceiptLineTotal",
          "name": "receiptLineTotal",
          "style": {
            "navigationBarTitleText": "全部商品",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "hlxsCusInvCheck/CusInvCheckList",
          "name": "hlxsCusInvCheckList",
          "style": {
            "navigationBarTitleText": "门店盘点",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          },
          "onReachBottomDistance": 50
        },
        {
          "path": "hlxsCusInvCheck/CusInvCheckSearch",
          "name": "hlxsCusInvCheckSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          },
          "onReachBottomDistance": 50
        },
        {
          "path": "hlxsCusInvCheck/CusInvCheckGoodSearch",
          "name": "hlxsCusInvCheckGoodSearch",
          "style": {
            "navigationBarTitleText": "搜索添加",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          },
          "onReachBottomDistance": 50
        },
        {
          "path": "hlxsCusInvCheck/CusInvCheckEdit",
          "name": "hlxsCusInvCheckEdit",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "hlxsCusInvCheck/CusInvCheckDetail",
          "name": "hlxsCusInvCheckDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "hlxsCusInvCheckTask/TaskList",
          "name": "hlxsCusInvCheckTaskTaskList",
          "style": {
            "navigationBarTitleText": "门店盘点任务",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "hlxsCusInvCheckTask/TaskSearch",
          "name": "hlxsCusInvCheckTaskTaskSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "hlxsCusInvCheckTask/DifferenceDetail",
          "name": "hlxsCusInvCheckTaskDifferenceDetail",
          "style": {
            "navigationBarTitleText": "盈亏详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "hlxsCusInvCheckTask/TaskAddAndEdit",
          "name": "hlxsCusInvCheckTaskTaskAddAndEdit",
          "style": {
            "navigationBarTitleText": "新增门店盘点任务",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "hlxsCusInvCheckTask/SubbillList",
          "name": "hlxsCusInvCheckTaskSubbillList",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "hlxsCusInvCheckTask/SkuSelectBySearch",
          "name": "hlxsCusInvCheckTaskSkuSelectBySearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "hlxsCusInvCheckTask/SkuSelectByCategory",
          "name": "hlxsCusInvCheckTaskSkuSelectByCategory",
          "style": {
            "navigationBarTitleText": "添加分类",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "hlxsCusInvCheckTask/SkuSelectByList",
          "name": "hlxsCusInvCheckTaskSkuSelectByList",
          "style": {
            "navigationBarTitleText": "添加商品",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesTaskCenter",
      "pages": [
        {
          "path": "taskCenter/TaskSearch",
          "name": "taskSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "taskDetail/TaskDetail",
          "name": "taskDetail",
          "style": {
            "navigationBarTitleText": "任务详情",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50,
            "app-plus": {
              "pullToRefresh": {
                "support": false
              }
            }
          }
        },
        {
          "path": "taskDetail/SoptCheckEval",
          "name": "soptCheckEval",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "taskDetail/SoptBatchEdit",
          "name": "SoptBatchEdit",
          "style": {
            "navigationBarTitleText": "任务详情",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50,
            "app-plus": {
              "pullToRefresh": {
                "support": false
              }
            }
          }
        },
        {
          "path": "taskDetail/historicalEvaluation/HistoricalEvaluation",
          "name": "historicalEvaluation",
          "style": {
            "navigationBarTitleText": "历史评价",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "taskDetail/inspectionEval/InspectionEval",
          "name": "inspectionEval",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "taskDetail/DeliverChoose",
          "name": "deliverChoose",
          "style": {
            "navigationBarTitleText": "选择转交人",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "myTodoTask/MyTodo",
          "name": "MyTodo",
          "style": {
            "navigationBarTitleText": "我的待办",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "myTodoTask/MyDone",
          "name": "MyDone",
          "style": {
            "navigationBarTitleText": "查看已办",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesTaskSub",
      "pages": [
        {
          "path": "knowledgeBase/KnowledgeBaseList",
          "name": "knowledgeBaseList",
          "style": {
            "navigationBarTitleText": "知识库",
            "navigationBarBackgroundColor": "#E7F3FD",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "knowledgeBase/KnowledgeBaseSearch",
          "name": "knowledgeBaseSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "knowledgeBase/MaterialsList",
          "name": "materialsList",
          "style": {
            "navigationBarTitleText": "视频资料",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "knowledgeBase/MaterialDetail",
          "name": "materialDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "knowledgeBase/ExamPaperList",
          "name": "examPaperList",
          "style": {
            "navigationBarTitleText": "试卷中心",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "knowledgeBase/ExamPaperDetail",
          "name": "examPaperDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "knowledgeBase/ExamQuestionDetail",
          "name": "examQuestionDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "commissionPerformance/CommissionPerformance",
          "name": "commissionPerformance",
          "style": {
            "navigationBarTitleText": "提成绩效",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "keyProducts/KeyProducts",
          "name": "keyProducts",
          "style": {
            "navigationBarTitleText": "重点商品",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "keyProducts/KeyProductsList",
          "name": "keyProductsList",
          "style": {
            "navigationBarTitleText": "榜单",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "keyProducts/StoreSituation",
          "name": "storeSituation",
          "style": {
            "navigationBarTitleText": "门店情况",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "keyProducts/KeyProductsDetail",
          "name": "keyProductsDetail",
          "style": {
            "navigationBarTitleText": "商品详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesSG",
      "pages": [
        {
          "path": "specGdRegist/SpecGdRegistList",
          "name": "specGdRegistList",
          "style": {
            "navigationBarTitleText": "特殊品管理",
            "navigationBarBackgroundColor": "#1C64FD"
          }
        },
        {
          "path": "specGdRegist/SpecGdRegistListSearch",
          "name": "specGdRegistListSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "specGdRegist/SpecGdRegistDetail",
          "name": "specGdRegistDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "specGdRegist/SpecGdRegistEdit",
          "name": "specGdRegistEdit",
          "style": {
            "navigationBarTitleText": "新增特殊品登记",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "specGdRegist/SpecGdRegistSkuSelect",
          "name": "specGdRegistSkuSelect",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "specGdRegist/SpecGdRegistSkuSearch",
          "name": "specGdRegistSkuSearch",
          "style": {
            "navigationBarTitleText": "搜索商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "specGdRegist/SpecGdRegistLineTotal",
          "name": "specGdRegistLineTotal",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "specGdRegistInv/SpecGdRegistInv",
          "name": "specGdRegistInv",
          "style": {
            "navigationBarTitleText": "特殊品库存查询",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "promApplyList",
          "path": "promApply/PromApplyList",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "enablePullDownRefresh": true,
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "促销申请",
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            }
          }
        },
        {
          "name": "promApplyListSearch",
          "path": "promApply/PromApplyListSearch",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "促销申请",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "promApplyEdit",
          "path": "promApply/PromApplyEdit",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "onReachBottomDistance": 92,
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "新增促销申请",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "promApplyGoodSearch",
          "path": "promApply/PromApplyGoodSearch",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "搜索",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "promApplySkuSelect",
          "path": "promApply/PromApplySkuSelect",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "选择商品",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "promApplyDetail",
          "path": "promApply/PromApplyDetail",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "促销详情",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "name": "promApplyLineTotal",
          "path": "promApply/PromApplyLineTotal",
          "style": {
            "navigationBarBackgroundColor": "#ffffff",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesUtil",
      "pages": [
        {
          "path": "azkWeb/AzkWeb",
          "name": "azlWeb",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "scan/Scan",
          "name": "scan",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "扫码",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "YES",
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "receiptRecord/RecordList",
          "name": "recordList",
          "style": {
            "navigationBarTitleText": "历史收货记录",
            "enablePullDownRefresh": true,
            "mp-alipay": {
              "allowsBounceVertical": "YES"
            },
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "receiptRecord/ReceiptEdit",
          "name": "receiptRecordEdit",
          "style": {
            "navigationBarTitleText": "收货详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receiptRecord/ReceiptBoxEdit",
          "name": "receiptBoxRecordEdit",
          "style": {
            "navigationBarTitleText": "收货详情",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receiptRecord/AddExhibitLocation",
          "name": "addExhibitLocation",
          "style": {
            "navigationBarTitleText": "添加陈列位置",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        // 拆零箱编辑页面
        {
          "path": "receiptRecord/ReceiptBoxLineEdit",
          "name": "receiptRecordBoxLineEdit",
          "style": {
            "navigationBarTitleText": "收货详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receiptRecord/RecordBoxMore",
          "name": "receiptRecordBoxMore",
          "style": {
            "navigationBarTitleText": "周转箱详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receiptRecord/ReceiptBoxSearch",
          "name": "receiptRecordBoxSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receiptRecord/ReceiptSkuSearch",
          "name": "receiptRecordSkuSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "receiptRecord/ReceiptRecordDetail",
          "name": "receiptRecordDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "print/BlueToothList",
          "name": "blueToothList",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "选择设备",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "print/CommonPrint",
          "name": "commonPrint",
          "style": {
            "navigationBarTitleText": "打印预览",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "gdeLimination/GdeLiminationList",
          "name": "gdeLiminationList",
          "style": {
            "navigationBarTitleText": "商品淘汰",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "gdeLimination/GdeLiminationListSearch",
          "name": "gdeLiminationListSearch",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "gdeLimination/GdeLiminationEdit",
          "name": "gdeLiminationEdit",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "componentPlaceholder": {
              "operate-inv-qty-dialog": "view"
            }
          }
        },
        {
          "path": "gdeLimination/GdeLiminationDetail",
          "name": "gdeLiminationDetail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "gdeLimination/GdeLiminationLineTotal",
          "name": "gdeLiminationLineTotal",
          "style": {
            "navigationBarTitleText": "查看全部",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "printAndriod/CommonPrint",
          "name": "androidCommonPrint",
          "style": {
            "navigationBarTitleText": "打印预览",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        },
        {
          "path": "printAndriod/BlueToothList",
          "name": "androidBlueToothList",
          "style": {
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "navigationBarTitleText": "选择设备",
            "enablePullDownRefresh": false,
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            }
          }
        }
      ]
    },
    {
      "root": "pagesLyzs",
      "pages": [
        {
          "path": "pickingBill/pickingBill",
          "name": "pickingBill",
          "style": {
            "navigationBarTitleText": "摊位账单",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "pickingBill/balanceStatement",
          "name": "balanceStatement",
          "style": {
            "navigationBarTitleText": "余额流水",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "pickingBill/pickingBillDetail",
          "name": "pickingBillDetail",
          "style": {
            "navigationBarTitleText": "摊位账单详情",
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "pickingBill/withdrawalBill",
          "name": "withdrawalBill",
          "style": {
            "navigationBarTitleText": "提现",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "pickingBill/withdrawalSetting",
          "name": "withdrawalSetting",
          "style": {
            "navigationBarTitleText": "提现设置",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/OrderPerformanceList",
          "name": "orderPerformanceList",
          "style": {
            "navigationBarTitleText": "订单履约",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/OrderInformationDetail",
          "name": "orderInformationDetail",
          "style": {
            "navigationBarTitleText": "订单详情",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/ExchangeRequest",
          "name": "exchangeRequest",
          "style": {
            "navigationBarTitleText": "换货申请",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/ConfirmExchangeGoods",
          "name": "confirmExchangeGoods",
          "style": {
            "navigationBarTitleText": "确定换货商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/LnitiateRefund",
          "name": "lnitiateRefund",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/OrderPerformanceSearchList",
          "name": "orderPerformanceSearchList",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/RtnOrderDetail",
          "name": "rtnOrderDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "orderPerformance/RtnGoodsEdit",
          "name": "rtnGoodsEdit",
          "style": {
            "navigationBarTitleText": "确认收货",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/OrderPerformanceDetail",
          "name": "orderPerformanceDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/SelfOrderDetail",
          "name": "selfOrderDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/OrderPerformanceSku",
          "name": "orderPerformanceSku",
          "style": {
            "navigationBarTitleText": "商品模式",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "orderPerformance/MaterialChange",
          "name": "materialChange",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/MaterialSearch",
          "name": "materialSearch",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "orderPerformance/OrderGrossProfitAnalysis",
          "name": "orderGrossProfitAnalysis",
          "style": {
            "navigationBarTitleText": "订单毛利分析 ",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "orderPerformance/OrderGrossProfitList",
          "name": "orderGrossProfitList",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "platformShop/PlatformShopList",
          "name": "platformShopList",
          "style": {
            "navigationBarTitleText": "平台商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "productLocation/ProductLocationList",
          "name": "productLocationList",
          "style": {
            "navigationBarTitleText": "商品货位",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "productLocation/AddStorageLocation",
          "name": "addStorageLocation",
          "style": {
            "navigationBarTitleText": "添加货位",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "productLocation/NoLevelAddLocation",
          "name": "noLevelAddLocation",
          "style": {
            "navigationBarTitleText": "新增货位",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "productLocation/HasLevelAddLocation",
          "name": "hasLevelAddLocation",
          "style": {
            "navigationBarTitleText": "按层新增货位",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "productLocation/NoLevelSelectLocation",
          "name": "noLevelSelectLocation",
          "style": {
            "navigationBarTitleText": "选择货位",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "productLocation/SelectGoods",
          "name": "selectGoods",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "productLocation/DeleteCargoLocation",
          "name": "deleteCargoLocation",
          "style": {
            "navigationBarTitleText": "删除货位",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "productLocation/SelectCargoLocation",
          "name": "selectCargoLocation",
          "style": {
            "navigationBarTitleText": "选择货位",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "productLocation/ModifStorageLocation",
          "name": "modifStorageLocation",
          "style": {
            "navigationBarTitleText": "批量修改货位",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "productLocation/ManuallyEnterBarcode",
          "name": "manuallyEnterBarcode",
          "style": {
            "navigationBarTitleText": "添加商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO"
          }
        },
        {
          "path": "evaluation/OrderEvaluation",
          "name": "orderEvaluation",
          "style": {
            "navigationBarTitleText": "订单评价",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "purchase/PurchaseList",
          "name": "purchaseList",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "customerService/CustomerService",
          "name": "customerService",
          "style": {
            "navigationBarTitleText": "客服消息",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "customerService/ChatDetail",
          "name": "chatDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "mp-alipay": {
              "allowsBounceVertical": "NO"
            },
            "disableScroll": true
          }
        },
        {
          "path": "skuList/SkuList",
          "name": "skuList",
          "style": {
            "navigationBarTitleText": "商品管理",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "pickingTask/pickingTask",
          "name": "pickingTask",
          "style": {
            "navigationBarTitleText": "拣货任务",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "pickingTask/pickingTaskDetail",
          "name": "pickingTaskDetail",
          "style": {
            "navigationBarTitleText": "拣货详情",
            "navigationBarBackgroundColor": "#1C64FD",
            // "navigationStyle": "custom",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "pickingGoods/pickingGoods",
          "name": "pickingGoods",
          "style": {
            "navigationBarTitleText": "摊位商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "skuList/AddGoods",
          "name": "addGoods",
          "style": {
            "navigationBarTitleText": "添加商品",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "skuList/GoodsQueryList",
          "name": "goodsQueryList",
          "style": {
            "navigationBarTitleText": "商品查询",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "skuList/ModifyPrice",
          "name": "modifyPrice",
          "style": {
            "navigationBarTitleText": "修改价格",
            "navigationBarBackgroundColor": "#1C64FD",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "storePicking/StorePicking",
          "name": "storePicking",
          "style": {
            "navigationBarTitleText": "门店拣货",
            "navigationBarBackgroundColor": "#1C64FD",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "supplierInfo/SupplierInfo",
          "name": "supplierInfo",
          "style": {
            "navigationBarTitleText": "供应商信息",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        },
        {
          "path": "supplierInfo/Sign",
          "name": "sign",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "allowsBounceVertical": "NO",
            "disableScroll": true
          }
        }
      ]
    }
  ],
  "tabBar": {
    // #ifdef MP-WEIXIN
    "custom": true,
    // #endif
    "color": "#7a7e83",
    "selectedColor": "#1C64FD",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "text": "首页",
        "iconPath": "static/icon/ic_tabbar_home_normal.png",
        "selectedIconPath": "static/icon/ic_tabbar_home_selected.png"
      },
      // #ifndef MP-WEIXIN
      {
        "pagePath": "pagesOther/taskCenter/TaskCenter",
        "text": "任务",
        "iconPath": "static/icon/ic_renwu_normal.png",
        "selectedIconPath": "static/icon/ic_renwu_selected.png"
      },
      // #endif
      {
        "pagePath": "pages/application/Application",
        "text": "店务",
        "iconPath": "static/icon/ic_tabbar_dianwu_normal.png",
        "selectedIconPath": "static/icon/ic_tabbar_dianwu_selected.png"
      },
      // #ifndef MP-WEIXIN
      {
        "pagePath": "pagesOther/bigData/BigData",
        "text": "数据",
        "iconPath": "static/icon/ic_tabbar_data_normal.png",
        "selectedIconPath": "static/icon/ic_tabbar_data_selected.png"
      },
      // #endif
      {
        "pagePath": "pages/mine/Mine",
        "text": "我的",
        "iconPath": "static/icon/ic_tabbar_my_normal.png",
        "selectedIconPath": "static/icon/ic_tabbar_my_selected.png"
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "white",
    "navigationBarBackgroundColor": "#1C64FD",
    // #ifdef H5
    "navigationStyle": "custom",
    // #endif
    "backgroundColor": "#fbf9fe",
    "app-plus": {
      // 关闭回弹
      "bounce": "none",
      "popGesture": "close",
      "pullToRefresh": {
        "style": "circle",
        "color": "#1C64FD"
      }
    }
  }
}