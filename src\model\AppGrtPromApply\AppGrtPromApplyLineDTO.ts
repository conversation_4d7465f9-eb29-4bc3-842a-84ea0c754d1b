import AppGrtPromApplyAttachDTO from 'model/AppGrtPromApply/AppGrtPromApplyAttachDTO'
import AppGrtPromApplyPeriodDTO from 'model/AppGrtPromApply/AppGrtPromApplyPeriodDTO'
import aseGoodsDTO from 'model/AppGrtPromApply/aseGoodsDTO'

export default class AppGrtPromApplyLineDTO {
  // 数据标识
  billId: string = ''
  // 行号
  lineNo: number = 0
  // 商品
  goods: aseGoodsDTO = new aseGoodsDTO()
  // 备注
  note: Nullable<string> = null
  // 面板分类大类Uuid
  categoryUuid: Nullable<string> = null
  // 面板分类大类代码
  categoryCode: Nullable<string> = null
  // 面板分类大类名称
  categoryName: Nullable<string> = null
  // 类别Uuid
  sortUuid: Nullable<string> = null
  // 类别代码
  sortCode: Nullable<string> = null
  // 类别名称
  sortName: Nullable<string> = null
  // 价格
  price: number = 0
  // 单品价
  singlePrice: number = 0
  // 申请促销价
  promPrice: number = 0
  // 申请促销会员价
  mbrPrice: number = 0
  // 申请数
  qty: number = 0
  // 申请规格数
  qpcQty: string = ''
  // 申请金额
  total: number = 0
  // 审批数
  approvalQty: Nullable<number> = null
  // 审批规格数
  approvalQpcQty: Nullable<string> = null
  // 审批金额
  approvalTotal: Nullable<number> = null
  // 保质期
  validDate: Nullable<Date> = null
  // 折扣率
  prmRate: Nullable<number> = null
  // 含量
  spec: Nullable<string> = null
  // 启用周期管理:0-否;1-是
  multiPeriod: number = 0
  // 临保促销单商品折扣周期明细
  periods: AppGrtPromApplyPeriodDTO[] = []
  // 临保商品特价单商品明细附件
  attaches: AppGrtPromApplyAttachDTO[] = []
}
