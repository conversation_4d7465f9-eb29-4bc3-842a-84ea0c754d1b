import fly from '@/network/fly'
import IdName from '@/model/base/IdName'
import ProcessBill from '@/model/processBill/ProcessBill'
import ProcessBillCreation from '@/model/processBill/ProcessBillCreation'
import ProcessBillLine from '@/model/processBill/ProcessBillLine'
import ProcessBillLog from '@/model/processBill/ProcessBillLog'
import ProcessBillModification from '@/model/processBill/ProcessBillModification'
import ProcessBillPrepareResponse from '@/model/processBill/ProcessBillPrepareResponse'
import ProcessBillSummary from '@/model/processBill/ProcessBillSummary'
import QueryRequest from '@/model/base/QueryRequest'
import BaseResponse from '@/model/base/BaseResponse'
import ProcessBillGdSearchResult from '@/model/processBill/ProcessBillGdSearchResult'
import ProcessBillGoods from '@/model/processBill/ProcessBillGoods'
import Ucn from '@/model/base/Ucn'
import Pscp from '@/model/processBill/Pscp'

export default class ProcessBillApi {
  /**
   * 复制指定单据
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static copy(body: IdName): Promise<BaseResponse<ProcessBill>> {
    return fly.post(`sos/v1/{tenant}/processbill/copy`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 创建单据
   *
   * @param body 创建对象
   * @param tenant 租户标识
   */
  static create(body: ProcessBillCreation): Promise<BaseResponse<string>> {
    return fly.post(`sos/v1/{tenant}/processbill/create`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 创建并提交单据
   *
   * @param body 创建对象
   * @param tenant 租户标识
   */
  static createAndSubmit(body: ProcessBillCreation): Promise<BaseResponse<string>> {
    return fly.post(`sos/v1/{tenant}/processbill/createAndSubmit`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取指定单据
   *
   * @param tenant 租户标识
   * @param id 单据标识
   * @param fetchParts 需要返回的部分信息，多个以逗号分隔
   */
  static get(id: string, fetchParts?: string): Promise<BaseResponse<ProcessBill>> {
    return fly
      .get(
        `sos/v1/{tenant}/processbill/${id}`,
        {},
        {
          params: {
            fetchParts: fetchParts
          }
        }
      )
      .then((res) => {
        return res
      })
  }

  /**
   * 获取指定单据日志
   *
   * @param tenant 租户标识
   * @param id 单据标识
   */
  static getLog(id: string): Promise<BaseResponse<ProcessBillLog[]>> {
    return fly.get(`sos/v1/{tenant}/processbill/log/${id}`, {}, {}).then((res) => {
      return res
    })
  }

  /**
   * 编辑单据
   *
   * @param body 编辑对象
   * @param tenant 租户标识
   */
  static modify(body: ProcessBillModification): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/processbill/modify`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 准备创建单据
   *
   * @param tenant 租户标识
   */
  static prepare(): Promise<BaseResponse<ProcessBillPrepareResponse>> {
    return fly.post(`sos/v1/{tenant}/processbill/create/prepare`, {}, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询单据
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static query(body: QueryRequest): Promise<BaseResponse<Nullable<ProcessBill[]>>> {
    return fly.post(`sos/v1/{tenant}/processbill/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询单据明细
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryLine(body: QueryRequest): Promise<BaseResponse<ProcessBillLine[]>> {
    return fly.post(`sos/v1/{tenant}/processbill/lines/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 删除指定单据
   *
   * @param body 单据标识
   * @param tenant 租户标识
   */
  static remove(id: string): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/processbill/remove`, { id: id }, {}).then((res) => {
      return res
    })
  }

  /**
   * 编辑并提交单据
   *
   * @param body 编辑对象
   * @param tenant 租户标识
   */
  static modifyAndSubmit(body: ProcessBillModification): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/processbill/submit`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询单据总数
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static sum(body: QueryRequest): Promise<BaseResponse<ProcessBillSummary>> {
    return fly.post(`sos/v1/{tenant}/processbill/sum`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询商品
   *
   * @param body 查询定义
   */
  static queryGoods(body: QueryRequest): Promise<BaseResponse<Nullable<ProcessBillGoods[]>>> {
    return fly.post(`/sos/v1/{tenant}/processbill/goods/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 单据和商品模糊搜索
   *
   * @param body 查询定义
   */
  static queryGoodsFuzzy(body: QueryRequest): Promise<BaseResponse<Nullable<ProcessBillGdSearchResult[]>>> {
    return fly.post(`/sos/v1/{tenant}/processbill/search`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询配方
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryPscp(body: QueryRequest): Promise<BaseResponse<Nullable<Ucn[]>>> {
    return fly.post(`sos/v1/{tenant}/processbill/pscp/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取配方
   *
   * @param id 单据标识
   * @param fetchParts 需要返回的部分信息，多个以逗号分隔
   */
  static getPscp(id: string): Promise<BaseResponse<Pscp>> {
    return fly.get(`sos/v1/{tenant}/processbill/pscp/${id}`, {}, {}).then((res) => {
      return res
    })
  }
}
