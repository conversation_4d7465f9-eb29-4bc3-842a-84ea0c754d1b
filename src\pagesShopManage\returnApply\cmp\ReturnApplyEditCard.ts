/*
 * @Author: 刘湘
 * @Date: 2024-03-26 17:17:15
 * @LastEditTime: 2025-05-13 19:15:09
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: /soa/src/pagesShopManage/returnApply/cmp/ReturnApplyEditCard.ts
 * 记得注释
 */
import { Vue, Component, Prop, Watch, Model } from 'vue-property-decorator'
import ReturnApplyCreationLine from '@/model/returnApply/ReturnApplyCreationLine'
import CommonUtil from '@/utils/CommonUtil'
import ReturnApplyBaseReasonLine from '@/model/returnApply/ReturnApplyBaseReasonLine'
import ReturnApplyReasonBatch from '@/model/returnApply/ReturnApplyReasonBatch'
import { Getter } from 'vuex-class'
import HdUpImgVideo from '@/components/hd-upImgVideo/hd-upImgVideo.vue'
import ReturnApplyReasonAttach from '@/model/returnApply/ReturnApplyReasonAttach'
import config from '@/config'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'

// eslint-disable-next-line @typescript-eslint/ban-ts-ignore
// @ts-ignore
@Component({
  components: { HdUpImgVideo },
  options: {
    virtualHost: true
  }
})
export default class ReturnApplyEditCard extends Vue {
  @Getter('qtyScale') qtyScale: number

  @Model('input', {
    type: [Object],
    // 默认值：''
    default: () => new ReturnApplyCreationLine()
  })
  value: ReturnApplyCreationLine // 商品信息

  @Prop({ type: Boolean, default: true }) showAlcPrice: boolean // 是否展示配货价配货金额 - 默认展示
  @Prop({ type: Boolean, default: false }) stkoutBckValidLimit: boolean // 统配退货是否开启效期控制(0-不启用(Def)，1-启用)
  @Prop({ type: Boolean, default: false }) croOrgSaleBckValidLimit: boolean // 公司间退货是否开启效期控制(0-不启用(Def)，1-启用)
  @Prop({ type: Boolean, default: false }) isNormal: boolean // 是否正常退货 正常退货原因&备注是否可以点击
  @Prop({ type: Boolean, default: false }) notifyEditable: boolean // 通知退货单是否可以编辑（用户点击按钮改变其状态）
  @Prop({ type: Boolean, default: false }) enableReasonValidDate: boolean // 原因上是否可以录效期 '0-不启用（Def）,1-统一启用(零食有鸣场景),2-按商品属性控制'  选项配置为2时，支持按照商品属性控制是否可以录入效期（是否开启效期管理）
  @Prop({ type: String, default: '' }) invCatName: string // 库存分类名称

  innerValue: ReturnApplyCreationLine = new ReturnApplyCreationLine() // 内部维护商品
  reasonAttaches: ReturnApplyReasonAttach[] = []
  signInfoKey: string = ''

  @Watch('value', { deep: true, immediate: true })
  onChange(n, O) {
    if (n) {
      this.innerValue = CommonUtil.deepClone(n)
    }
  }

  isVideoUrl = CommonUtil.isVideoUrl
  isImageUrl = CommonUtil.isImageUrl

  //获取字段展示权限
  get showMaster() {
    const masterPermission = {
      showDisplayLocation: false // 陈列位置
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDISPLAYLOCATION)) {
      masterPermission.showDisplayLocation = true
    }
    return masterPermission
  }

  // 判断是否在可退效期范围
  get validDate() {
    if (!this.innerValue.validDate) {
      return false
    }
    const maxBckDay = (this.innerValue.maxBckDay || 0) * (24 * 3600 * 1000)
    const minBckDay = (this.innerValue.minBckDay || 0) * (24 * 3600 * 1000)
    const date = this.innerValue.validDate.replace(/-/g, '/') //IOS不支持解析YYYY-MM格式
    const date1 = date.substring(0, 10) + ' 00:00:00'
    const date2 = date.substring(0, 10) + ' 23:59:59'
    const oDate1 = new Date(date1)
    const oDate2 = new Date(date2)
    const currentDate = new Date()
    if (oDate1.getTime() - maxBckDay <= currentDate.getTime() && oDate2.getTime() - minBckDay >= currentDate.getTime()) {
      return false //通过检验
    } else {
      return true
    }
  }

  // 是否是气调商品（周黑鸭）
  get isHasTraceCodes() {
    return this.innerValue.materialTypeGrp === '气调'
  }

  get batchs() {
    return (reason: ReturnApplyBaseReasonLine) => {
      return reason.reasonBatchs.filter((item) => !item.traceCode)
    }
  }

  get traceCodeBatchs() {
    return (reason: ReturnApplyBaseReasonLine) => {
      return reason.reasonBatchs.filter((item) => item.traceCode)
    }
  }

  /**
   * 是否有多个陈列位置
   */
  get hasMutiple() {
    return (item) => {
      return item && item.split(',').length > 1
    }
  }

  // 商品图片
  get skuImg() {
    return this.innerValue &&
      this.innerValue.goodsImages &&
      this.innerValue.goodsImages.length &&
      CommonUtil.isImageUrl(this.innerValue.goodsImages[0])
      ? `${this.innerValue.goodsImages[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
      : `${config.sourceUrl}icon/pic_goods.png`
  }

  get imageList() {
    return this.innerValue && this.innerValue.goodsImages && this.innerValue.goodsImages.filter((item) => CommonUtil.isImageUrl(item)).length
      ? this.innerValue.goodsImages.filter((item) => CommonUtil.isImageUrl(item))
      : [`${config.sourceUrl}icon/pic_goods.png`]
  }

  /**
   * 选择原因
   */
  handleSelectReason(reason: ReturnApplyBaseReasonLine, index: number) {
    this.$emit('select-reason', reason, index)
  }

  /**
   * 关联原单
   */
  handleSelectBill(reason: ReturnApplyBaseReasonLine, index: number) {
    this.$emit('select-bill', reason, index)
  }

  /**
   * 选择日期
   * @param type
   */
  handleSelectDate(type: 'start' | 'end') {
    this.$emit('select-date', type)
  }

  /**
   * 添加原因
   */
  handleAddReason() {
    this.$emit('add-reason')
  }

  /**
   * 添加效期
   * @param index
   */
  handleAddBatch(index: number) {
    this.$emit('add-batch', index)
  }

  /**
   * 编辑效期
   * @param index 原因下标
   */
  handleEditBatchDate(index: number, batch: ReturnApplyReasonBatch) {
    this.$emit('edit-batch-date', index, batch)
  }

  /**
   * 移除效期行
   * @param index
   * @param batch
   */
  handleRemoveBatch(index: number, batch: ReturnApplyReasonBatch) {
    this.$emit('remove-batch', index, batch)
  }

  /**
   * 编辑效期数量
   * @param index 原因下标
   * @param batch 效期行
   */
  handleEditBatchQty(index: number, batch: ReturnApplyReasonBatch) {
    this.$emit('edit-batch-qty', index, batch)
  }

  /**
   * 删除原因
   * @param index 原因下表
   */
  handleDeleteReason(index: number) {
    this.$emit('delete-reason', index)
  }

  /**
   * 商品数据变化
   * @param index 原因下标
   * @param isQpcQty 是否是配货整件数量
   */
  hanleNumberChange(index: number, isQpcQty: boolean) {
    // 商品是否开启了双计量且是散称
    const isDispAndUseDoubleMeasure = this.innerValue.useDoubleMeasure === 1 && this.innerValue.isDisp
    // 开启了双计量且是散称,不发生联动
    if (isQpcQty && !isDispAndUseDoubleMeasure) {
      this.innerValue.reasons[index].qty = this.innerValue.reasons[index].qpcQty.multiply(this.innerValue.goods.qpc).scale(this.qtyScale)
    } else if (!isQpcQty && !isDispAndUseDoubleMeasure) {
      this.innerValue.reasons[index].qpcQty = this.innerValue.reasons[index].qty.divide(this.innerValue.goods.qpc).scale(this.qtyScale)
    }
    // 获取退货规格价，有原单据时使用规格价，没有原单据时使用商品规格价
    const qpcPrice: number = Number(this.innerValue.reasons[index].bckPrice || this.innerValue.goods.price)
    this.innerValue.reasons[index].total = qpcPrice
      .multiply(this.innerValue.reasons[index].qty)
      .divide(this.innerValue.goods.qpc)
      .scale(this.qtyScale)
    let lineQpcQty: number = 0,
      lineQty: number = 0,
      lineTotal: number = 0
    for (let index = 0; index < this.innerValue.reasons.length; index++) {
      lineQpcQty = lineQpcQty + this.innerValue.reasons[index].qpcQty
      lineQty = lineQty + this.innerValue.reasons[index].qty
      lineTotal = Number(lineTotal).add(this.innerValue.reasons[index].total)
    }
    this.innerValue.qpcQty = parseFloat(lineQpcQty.toString()).scale(this.qtyScale)
    this.innerValue.qty = lineQty.scale(this.qtyScale)
    this.innerValue.total = lineTotal.scale(2)
    this.$emit('input', this.innerValue)
    this.$emit('change', this.innerValue, CommonUtil.deepClone(this.value))
  }

  /**
   * 计算总数量
   * @param reason
   * @returns
   */
  handleCalcTotal(reason: ReturnApplyBaseReasonLine) {
    let total: number = 0
    reason.reasonBatchs.forEach((item) => {
      total = total.add(item.qty)
    })

    return total.scale(this.qtyScale)
  }

  /**
   * 上传图片
   */
  doImageUpload(type: string, data: string[], index: number) {
    this.$emit('doImageUpload', index, type, data, this.signInfoKey)
  }

  /**
   * 删除图片
   */
  doImageRemove(type: string, data: string[], index: number) {
    this.$emit('doImageRemove', index, type, data, this.signInfoKey)
  }

  /**
   * 图片上传错误回调事件
   * @param tip 提示
   */
  doImageError(tip: any) {
    const error: string[] = Array.from(new Set(tip.error))
    let errMsg: string = ''
    for (let index = 0; index < error.length; index++) {
      if (index < error.length - 1) {
        errMsg += `${error[index]};`
      } else {
        errMsg += `${error[index]}`
      }
    }
    this.$showModal({
      title: `成功${tip.success.length}张，失败${tip.error.length}张`,
      content: `失败原因:${errMsg}`,
      confirmText: '我知道了',
      cancelText: '取消',
      showCancel: false,
      success: (res) => {
        console.log(res)
      }
    })
  }

  /**
   * 上传加载loading
   *
   */
  doShowLoading() {
    this.$showLoading()
  }

  getSignInfoKey(key) {
    this.signInfoKey = key
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info) {
    this.$emit('viewExhibit', info)
  }

  /**
   * 预览图片
   */
  handlePreviewImg(sku: ReturnApplyCreationLine) {
    uni.previewImage({
      current: String(0),
      urls: this.imageList
    })
  }
}
