<template>
  <uni-popup type="center" ref="edit" :maskClick="false">
    <view class="edit-card-dialog" v-if="sku">
      <view class="header">
        <text>{{ sku.goods.name | empty }}</text>
      </view>
      <view class="main">
        <view class="main-exhibit" v-if="showMaster.showDisplayLocation">
          <view :class="[hasMutiple ? 'goods-one' : '']">
            陈列位置：
            <text class="goods-text">{{ sku.displayLocation | empty }}</text>
          </view>
          <image class="good-img" :src="'/static/icon/ic_right_grey.png' | oss" v-if="hasMutiple" @click="viewExhibit" />
        </view>
        <view class="main-half">代码：{{ sku.goods.code | empty }}</view>
        <view class="main-half">条码：{{ sku.goods.inputCode | empty }}</view>
        <view class="main-half">规格：{{ sku.goods.qpcStr }}/{{ sku.goods.munit }}</view>
        <view class="main-half">价格：￥{{ sku.goods.price }}/{{ sku.goods.munit }}</view>

        <view class="main-total">
          <view class="main-total-qty">
            应收：
            <text class="main-total-value">{{ sku.shipQty | empty }}{{ sku.goods.minMunit | empty }}</text>
          </view>
          <view class="main-total-qty">
            待收：
            <text class="main-total-value">{{ sku.qty | empty }}{{ sku.goods.minMunit | empty }}</text>
          </view>
        </view>
        <template v-if="!sku.isDisp">
          <view class="main-qty" v-if="doubleMeasureGoodsEnterQpcQty" style="margin-bottom: 16rpx">
            <text>整件数（即规格的整倍数）</text>
            <hd-number-box-test v-model="wholeQty" :scale="0" :min="0" :max="wholeMax"></hd-number-box-test>
          </view>
          <view class="main-qty">
            <text>单品</text>
            <hd-number-box-test v-model="splitQty" :min="0" :max="splitMax" :scale="qtyScale"></hd-number-box-test>
          </view>
        </template>
        <template v-else>
          <view class="main-qty" v-if="doubleMeasureGoodsEnterQpcQty" style="margin-bottom: 16rpx">
            <text>件数</text>
            <hd-number-box-test v-model="wholeQty" :max="wholeMax" @change="handleWholeQtyChange" :min="0" :scale="qtyScale"></hd-number-box-test>
          </view>
          <view class="main-qty">
            <text>重量{{ sku.goods.minMunit ? `(${sku.goods.minMunit})` : '' }}</text>
            <hd-number-box-test v-model="splitQty" @change="handleSplitQtyChange" :max="splitMax" :min="0" :scale="qtyScale"></hd-number-box-test>
          </view>
        </template>

        <view class="good-time" v-if="directFillPrddateAndValidate && (sku.goods.storeUseExp || sku.goods.storeUseMfg)">
          <view class="time-start" @click="doStartClick">
            <view class="time-label">生产日期</view>
            <view class="flex">
              <view class="time-value">{{ sku.mfgDate | date | empty }}</view>
              <image class="time-icon" :src="'/static/icon/ic_right_grey.png' | oss" />
            </view>
          </view>
          <view class="time-start" @click="doEndClick">
            <view class="time-label">到效日期</view>
            <view class="flex">
              <view class="time-value">{{ sku.expDate | date | empty }}</view>
              <image class="time-icon" :src="'/static/icon/ic_right_grey.png' | oss" />
            </view>
          </view>
        </view>
      </view>

      <view class="footer">
        <view class="footer-btn" @click="handleCancel">取消</view>
        <view class="footer-btn footer-btn--confirm" @click="handleConfirm">确定修改</view>
      </view>
    </view>
  </uni-popup>
</template>

<script lang="ts" src="./EditCardDialog.ts"></script>

<style lang="scss" scoped>
.edit-card-dialog {
  position: relative;
  box-sizing: border-box;
  width: 644rpx;
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;

  .tip {
    width: 100%;
    box-sizing: border-box;
    padding: 20rpx 32rpx;
    background: rgba(253, 155, 28, 0.21);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 26rpx;
    color: #fd9b1c;
  }

  .header {
    display: flex;
    align-items: center;
    width: 100%;
    font-family: PingFangSC, PingFang SC;
    font-weight: 550;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 16rpx;
    padding: 32rpx 32rpx 0 32rpx;
    box-sizing: border-box;

    &-gift {
      flex: 0 0 auto;
      display: inline-flex;
      align-items: center;
      padding: 0 10rpx;
      box-sizing: border-box;
      margin-right: 8rpx;
      width: 68rpx;
      height: 40rpx;
      background: linear-gradient(313deg, #ff9a00 0%, #ffd05b 100%);
      border-radius: 8rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #ffffff;
    }
  }

  .main {
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
    box-sizing: border-box;
    padding: 0 32rpx 40rpx 32rpx;
    &-exhibit {
      width: 100%;
      background: #f5f5f5;
      border-radius: 8rpx;
      padding: 8rpx 16rpx;
      box-sizing: border-box;
      flex: 0 0 auto;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;

      .good-img {
        width: 32rpx;
        height: 32rpx;
        display: inline-table;
      }

      .goods-text {
        color: #333333;
        font-size: 26rpx;
        font-weight: 500;
      }

      .goods-one {
        flex: 1;
        @include ellipsis();
      }
    }
    &-count {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 32rpx;
    }

    &-half {
      width: 50%;
      flex: 0 0 auto;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      &:not(:last-child) {
        margin-bottom: 16rpx;
      }
    }

    &-total {
      width: 100%;
      height: 80rpx;
      background: #f5f6f7;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      padding: 0 24rpx;
      box-sizing: border-box;
      margin-bottom: 20rpx;

      &-qty {
        margin-right: 32rpx;
      }
      &-value {
        color: #333333;
      }
    }

    &-qty {
      width: 100%;
      display: flex;
      justify-content: space-between;
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
    }

    .good-time {
      width: 100%;
      margin-top: 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      background: $color-white;

      .time-start {
        height: 64rpx;
        width: 342rpx;
        box-sizing: border-box;
        padding: 12rpx;
        @include flex(row, space-between);
        background: $color-bg-primary;
        border-radius: 8rpx;
        font-size: 24rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;

        &:not(:last-child) {
          margin-right: 16rpx;
        }
      }
      .time-icon {
        width: 28rpx;
        height: 28rpx;
      }
    }
  }

  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100rpx;
    background: #ffffff;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 2rpx;
      transform: scaleY(0.5);
      background: #e5e5e5;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      width: 2rpx;
      height: 100rpx;
      background: #e5e5e5;
      transform: translateX(-50%) scaleX(0.5);
    }

    &-btn {
      flex: 0 0 auto;
      width: 50%;
      height: 100rpx;
      font-family: PingFangSC, PingFang SC;
      font-size: 36rpx;
      font-weight: 400;
      font-size: 36rpx;
      color: #666666;
      display: flex;
      justify-content: center;
      align-items: center;

      &--confirm {
        color: #1c64fd;
      }
    }
  }
}
</style>
