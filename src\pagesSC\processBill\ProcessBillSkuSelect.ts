import { Component, Vue } from 'vue-property-decorator'
import CollapseBar from '@/pages/cmp/CollapseBar.vue'
import SideBar from '@/pages/cmp/SideBar.vue'
import HdNumberBoxTest from '@/components/hd-number-box-test/hd-number-box-test.vue'
import HdButton from '@/components/hd-button/hd-button.vue'
import DataApi from '@/network/data/DataApi'
import GoodsCategory from '@/model/data/GoodsCategory'
import QueryRequest from 'model/base/QueryRequest'
import ProcessBillApi from '@/network/processBill/ProcessBillApi'
import CommonUtil from '@/utils/CommonUtil'
import ProcessBillGoods from '@/model/processBill/ProcessBillGoods'
import AbstractProcessBillLine from '@/model/processBill/AbstractProcessBillLine'
import Category from '@/model/data/Category'
import { Filter } from '@/pagesSC/processBill/cmd/Filter'
import { Action, Mutation } from 'vuex-class'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import { mixins } from 'vue-class-component'
import config from '@/config'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'

@Component({
  components: { CollapseBar, SideBar, HdNumberBoxTest, HdButton },
  filters: Filter
})
export default class ProcessBillSkuSelect extends mixins(BroadCast) {
  @Mutation('goodsList') mutationGoodsList // 提交到vuex
  @Action('isGoods') actionIsGoods // 是否为商品存到vuex中
  goodsList: ProcessBillGoods[] = [] // 商品列表
  goodsUncategorizedList: Nullable<ProcessBillGoods[]> = null // 未分类的商品列表
  selectedGoodsList: ProcessBillGoods[] = [] // 已选商品列表
  goodsCategoryList: Nullable<GoodsCategory[]> = null // 商品分类列表
  selectedCategory: GoodsCategory = new GoodsCategory() // 已选的商品分类
  selectedSubCategory: GoodsCategory = new GoodsCategory() // 已选的二级分类
  categoryGoodsCount: number[] = [] // 购物车中每种大类所选商品个数，用于左侧导航栏显示
  selectedIndex: number = 0 // 选中的一级分类的下标
  // 上拉加载相关
  isLoading: boolean = false // 是否正在加载
  finished: boolean = false // 数据是否加载完成
  page: number = 0 // 数据页码
  pageSize: number = 20 // 每页大小

  title: string = '' // 标题

  // 按钮是否可点击
  get canConfirm() {
    return this.selectedGoodsList && this.selectedGoodsList.length
  }

  // 商品图片
  get skuImg() {
    return (sku: ProcessBillGoods) => {
      return sku && sku.images && sku.images.length && CommonUtil.isImageUrl(sku.images[0])
        ? `${sku.images[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
        : `${config.sourceUrl}icon/pic_goods.png`
    }
  }

  get imageList() {
    return (sku: ProcessBillGoods) => {
      return sku && sku.images && sku.images.filter((item) => CommonUtil.isImageUrl(item)).length
        ? sku.images.filter((item) => CommonUtil.isImageUrl(item))
        : [`${config.sourceUrl}icon/pic_goods.png`]
    }
  }

  // 展示金额
  get showPrice() {
    if (this.title === '选择成品') {
      return true
    } else {
      return !PermissionMgr.hasPermission(Permission.globalPriceView)
    }
  }

  onLoad(option) {
    if (option) {
      this.$nextTick(() => {
        if (option.title) {
          try {
            this.title = decodeURIComponent(option.title)
          } catch (error) {
            this.title = option.title
          }

          uni.setNavigationBarTitle({
            title: this.title
          })
        }
      })
    }
  }

  mounted() {
    // 从storage中获得已选中商品
    if (uni.getStorageSync('processGoodsList')) {
      const before: AbstractProcessBillLine[] = uni.getStorageSync('processGoodsList')
      this.selectedGoodsList = this.doExchangeModel(before)
      this.goodsUncategorizedList = CommonUtil.copy(
        this.selectedGoodsList!.filter((selected) => !selected.firstCategory || !selected.firstCategory.uuid)
      )
      uni.removeStorage({ key: 'processGoodsList' })
    }
    this.doQuerySort().then((resp) => {
      this.getCountCategoryGoods()
      const queryRequest = new QueryRequest()
      if (this.selectedCategory.children.length > 0) {
        this.selectedSubCategory = this.selectedCategory.children[0]
      }
      if (this.selectedCategory.code) {
        queryRequest.conditions = [
          {
            operation: 'categoryCode:=',
            parameters: [this.selectedCategory.code]
          }
        ]
      }
      if (this.selectedSubCategory.code) {
        queryRequest.conditions = [
          {
            operation: 'categoryCode:=',
            parameters: [this.selectedSubCategory.code]
          }
        ]
      }
      this.doQueryGood(queryRequest)
      // 如果已选中的商品里存在未分类的商品，则一级分类添加未分类分组
      const found = this.selectedGoodsList!.find((selected) => !selected.firstCategory || !selected.firstCategory.uuid)
      if (found) {
        const category = new GoodsCategory()
        category.uuid = CommonUtil.uuid()
        category.code = null
        category.name = '未分类'
        this.goodsCategoryList && this.goodsCategoryList.push(category)
      }
    })
  }

  /**
   * 步进器change事件
   * @param qpcQty 数量
   * @param index 对应商品下标
   */
  doNumberChange(qpcQty: number, index: number) {
    this.goodsList[index].qty = qpcQty.multiply(this.goodsList[index].qpc).scale(4)
    this.goodsList[index].total = this.goodsList[index].price.multiply(qpcQty)
    const goods: ProcessBillGoods = this.goodsList[index]
    if (goods) {
      const index = this.selectedGoodsList.findIndex((item) => {
        return item.uuid === goods.uuid && item.inputCode === goods.inputCode && item.qpcStr === goods.qpcStr
      })
      if (index === -1 && qpcQty > 0) {
        this.selectedGoodsList.unshift(goods)
      } else {
        if (qpcQty <= 0) {
          this.selectedGoodsList.splice(index, 1)
        } else {
          this.selectedGoodsList[index].qty = goods.qty
          this.selectedGoodsList[index].qpcQty = goods.qpcQty
          this.selectedGoodsList[index].total = goods.total
        }
      }
    }
    this.getCountCategoryGoods()
  }

  /**
   * 模型转换
   * @param before 待转换数组
   */
  doExchangeModel(before: AbstractProcessBillLine[] = []) {
    const after: ProcessBillGoods[] = []
    for (let index = 0; index < before.length; index++) {
      let temp: ProcessBillGoods = new ProcessBillGoods()
      temp = { ...before[index].goods } as ProcessBillGoods
      temp.total = before[index].total
      temp.qty = before[index].qty
      temp.qpcQty = before[index].qpcQty
      temp.images = before[index].goodsImages
      if (before[index].categoryCode) {
        temp.firstCategory = new Category()
        temp.firstCategory.code = before[index].categoryCode || ''
        temp.firstCategory.name = before[index].categoryName || ''
        temp.firstCategory.uuid = before[index].categoryUuid || ''
      } else {
        temp.firstCategory = null
      }

      after.push(temp)
    }
    return after
  }

  /**
   * 模型还原
   * @param before 待还原模型
   */
  doReturnModel(before: ProcessBillGoods[] = []) {
    const after: AbstractProcessBillLine[] = []
    for (let index = 0; index < before.length; index++) {
      const temp: AbstractProcessBillLine = new AbstractProcessBillLine()
      temp.goods.code = before[index].code
      temp.goods.inputCode = before[index].inputCode
      temp.goods.minMunit = before[index].minMunit
      temp.goods.munit = before[index].munit
      temp.goods.name = before[index].name
      temp.goods.price = before[index].price
      temp.goods.qpc = before[index].qpc
      temp.goods.qpcStr = before[index].qpcStr
      temp.goods.singlePrice = before[index].singlePrice
      temp.goods.uuid = before[index].uuid
      temp.total = before[index].total
      temp.qty = before[index].qty
      temp.qpcQty = before[index].qpcQty
      temp.goodsImages = before[index].images
      if (before[index].firstCategory) {
        temp.categoryCode = before[index].firstCategory!.code || ''
        temp.categoryName = before[index].firstCategory!.name || ''
        temp.categoryUuid = before[index].firstCategory!.uuid || ''
      }
      after.push(temp)
    }
    return after
  }

  // 查询商品分类
  doQuerySort() {
    this.$showLoading({ delayTime: 200 })
    return new Promise((resolve, reject) => {
      DataApi.sortList()
        .then((resp) => {
          this.goodsCategoryList = resp.data
          if (this.goodsCategoryList && this.goodsCategoryList.length > 0) {
            this.selectedCategory = this.goodsCategoryList[0]
            resolve(this.selectedCategory)
          } else {
            this.$hideLoading()
            reject()
          }
        })
        .catch((e) => {
          this.$hideLoading()
          this.$showToast({ title: e.msg, icon: 'error' })
          reject()
        })
    })
  }

  /**
   * 查询商品列表
   * @param queryRequest QueryRequest 查询条件
   * @param type 查询类型：1.query:按照商品分类码查询 2.search:根据关键词查询
   */
  doQueryGood(queryRequest: QueryRequest, showLoading: boolean = true) {
    if (showLoading) {
      this.$showLoading({ delayTime: 200 })
    }
    if (this.selectedCategory.name === '未分类') {
      const data = this.goodsUncategorizedList as ProcessBillGoods[]
      this.goodsList = data
      if (showLoading) {
        this.$hideLoading()
      }
      this.finished = true
      return
    }
    queryRequest.page = this.page
    queryRequest.pageSize = this.pageSize
    queryRequest.fetchParts = ['category', 'reference', 'image']
    ProcessBillApi.queryGoods(queryRequest)
      .then((resp) => {
        if (showLoading) {
          this.$hideLoading()
        }
        this.page++
        this.isLoading = false
        if (!resp.more) {
          this.finished = true
        }
        if (resp.data && resp.data.length) {
          const data = resp.data
          for (let i = 0; i < data.length; i++) {
            data[i].qty = 0
            data[i].qpcQty = 0
            data[i].total = 0
            for (let j = 0; j < this.selectedGoodsList.length; j++) {
              if (
                data[i].inputCode === this.selectedGoodsList[j].inputCode &&
                data[i].uuid === this.selectedGoodsList[j].uuid &&
                data[i].qpcStr === this.selectedGoodsList[j].qpcStr
              ) {
                data[i].qty = this.selectedGoodsList[j].qty
                data[i].qpcQty = this.selectedGoodsList[j].qpcQty
                data[i].total = this.selectedGoodsList[j].total
              }
            }
          }
          this.goodsList.push(...data)
        }
      })
      .catch((e) => {
        if (showLoading) {
          this.$hideLoading()
        }
        this.$showToast({ title: e.msg, icon: 'error' })
      })
  }

  /**
   * 统计购物车中，每个大类有多少个商品(用于左侧导航栏)
   */
  getCountCategoryGoods() {
    if (this.goodsCategoryList && this.goodsCategoryList.length > 0) {
      // 根据有无未分类，处理arr的长度
      let arr: number[] = []
      const found = this.goodsCategoryList.find((category) => !category.code)
      if (found) {
        // 分类列表存在未分类
        arr = new Array(this.goodsCategoryList.length).fill(0)
      } else {
        // 分类列表不存在未分类
        arr = new Array(this.goodsCategoryList.length + 1).fill(0)
      }
      const lines = this.selectedGoodsList || []
      for (let i = 0; i < this.goodsCategoryList.length; i++) {
        for (let j = 0; j < lines.length; j++) {
          // 获得当前分类中存在多少个已选中的商品
          if (lines[j].firstCategory && lines[j].firstCategory!.uuid === this.goodsCategoryList[i].uuid) {
            arr[i] = arr[i] + 1
          }
        }
      }
      // 获得未分类的已选中的商品数
      if (this.selectedGoodsList && this.selectedGoodsList.length) {
        this.selectedGoodsList.forEach((selected) => {
          if (!selected.firstCategory || !selected.firstCategory.uuid) {
            arr[arr.length - 1] = arr[arr.length - 1] + 1
          }
        })
      }
      this.categoryGoodsCount = arr
    }
  }

  // 上拉加载更多
  doLoadMore() {
    if (this.finished || this.isLoading) {
      return
    }
    this.isLoading = true
    const queryRequest = new QueryRequest()
    if (this.selectedCategory.code) {
      queryRequest.conditions = [
        {
          operation: 'categoryCode:=',
          parameters: [this.selectedCategory.code]
        }
      ]
    }
    if (this.selectedSubCategory.code) {
      queryRequest.conditions = [
        {
          operation: 'categoryCode:=',
          parameters: [this.selectedSubCategory.code]
        }
      ]
    }
    this.doQueryGood(queryRequest, false)
  }

  /**
   * 搜索商品
   */
  doSearch() {
    let title = '搜索原料'
    if (this.title) {
      if (this.title === '选择成品') {
        title = '搜索成品'
      }
    }
    uni.setStorage({
      key: 'processGoodsList',
      data: this.doReturnModel(this.selectedGoodsList),
      success: function () {
        uni.navigateTo({ url: `/pagesSC/processBill/ProcessBillGoodSearch?from=select&title=${title}` })
      }
    })
  }

  /**
   * 扫描二维码
   */
  doScan() {
    const title = this.title ? this.title : '选择原料'
    this.mutationGoodsList(this.doReturnModel(this.selectedGoodsList))
    uni.scanCode({
      success: (res) => {
        const scanWord = res.result || ''
        this.$Router.push({
          name: 'processBillGoodSearch',
          params: {
            from: 'select',
            value: scanWord,
            title: title
          }
        })
      },
      fail: () => {
        this.mutationGoodsList([])
      }
    })
  }

  //PDA扫码回调事件
  doScanAfter(scanWord) {
    const title = this.title ? this.title : '选择原料'
    this.mutationGoodsList(this.doReturnModel(this.selectedGoodsList))
    this.$Router.push({
      name: 'processBillGoodSearch',
      params: {
        from: 'select',
        value: scanWord,
        title: title
      }
    })
  }

  // 一级分类点击事件
  doSiderChange(index) {
    this.selectedIndex = index
    this.selectedSubCategory = new GoodsCategory()
    this.selectedCategory = new GoodsCategory()
    this.page = 0 // 页码
    this.finished = false // 是否加载完成
    this.isLoading = false // 是否在加载
    this.goodsList = []
    this.$nextTick(() => {
      if (this.goodsCategoryList && this.goodsCategoryList.length > index) {
        this.selectedCategory = this.goodsCategoryList[index]
        if (this.selectedCategory.children.length > 0) {
          this.selectedSubCategory = this.selectedCategory.children[0]
        }
        const queryRequest = new QueryRequest()
        if (this.selectedCategory.code) {
          queryRequest.conditions = [
            {
              operation: 'categoryCode:=',
              parameters: [this.selectedCategory.code]
            }
          ]
        }
        if (this.selectedSubCategory.code) {
          queryRequest.conditions = [
            {
              operation: 'categoryCode:=',
              parameters: [this.selectedSubCategory.code]
            }
          ]
        }
        this.doQueryGood(queryRequest)
      }
    })
  }
  // 二级分类点击事件
  doSecondChange(goodsCategory: GoodsCategory) {
    this.selectedSubCategory = goodsCategory
    this.page = 0 // 页码
    this.finished = false // 是否加载完成
    this.isLoading = false // 是否在加载
    this.goodsList = []
    const queryRequest = new QueryRequest()
    if (this.selectedSubCategory.code) {
      queryRequest.conditions = [
        {
          operation: 'categoryCode:=',
          parameters: [this.selectedSubCategory.code]
        }
      ]
    }
    this.doQueryGood(queryRequest)
  }

  /**
   * 确认选择
   */
  doConfirm() {
    uni.setStorage({
      key: 'processGoodsList',
      data: this.doReturnModel(this.selectedGoodsList),
      success: () => {
        this.actionIsGoods(true)
        uni.navigateBack({})
      }
    })
  }

  /**
   * 预览图片
   */
  handlePreviewImg(sku: ProcessBillGoods) {
    uni.previewImage({
      current: String(0),
      urls: this.imageList(sku)
    })
  }
}
