import { Component, Vue } from 'vue-property-decorator'
import { State } from 'vuex-class'
import AppGd<PERSON>uery<PERSON><PERSON> from '@/network/gdQuery/AppGdQueryApi'
import QueryRequest from '@/model/base/QueryRequest'
import GoodsQueryInfo from '@/model/gdQuery/GoodsQueryInfo'
import SysConfigItem from '@/model/sysConfig/SysConfigItem'
import SkuCard from './cmp/SkuCard.vue'
import UseQueues from '@/components/hd-popover/UseQueues'
import { mixins } from 'vue-class-component'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import GoodsSlotBinder from '@/model/data/GoodsSlotBinder'
import SelectExhibit from '@/components/select-exhibit/SelectExhibit.vue'
import ModuleOption from '@/model/default/ModuleOption'
import ResetExhibit from '@/components/select-exhibit/ResetExhibit.vue'
import OperateInvQtyDialog from './cmp/OperateInvQtyDialog.vue'
import config from '@/config'
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js'

@Component({
  components: { SkuCard, ViewExhibit, ResetExhibit, SelectExhibit, OperateInvQtyDialog }
})
export default class GdQueryPanelOnlySearch extends mixins(UseQueues, BroadCast, MescrollMixin) {
  @State('sysConfig') sysConfig: SysConfigItem // 获取全局配置
  @State('optionList') optionList: ModuleOption[] //店务配置列表
  pageSize: number = 6 // 每页大小
  pageNum: number = 0 // 页码
  finished: boolean = false // 是否加载完成
  isLoading: boolean = false // 是否在加载
  skuList: GoodsQueryInfo[] = [] // 商品列表
  viewExhibitInfo: GoodsQueryInfo = new GoodsQueryInfo() // 商品信息
  exhibitIndex: number = -1 // 商品下标
  total: number = 0 // 商品总数量
  $refs: any
  keyWord: string = '' // 搜索关键词

  downOption = {
    use: false,
    auto: false
  }

  upOption = {
    auto: false,
    page: {
      num: 0,
      size: 20
    },
    textNoMore: '-- 我是有底线的 --',
    noMoreSize: 5,
    empty: {
      use: false,
      icon: config.sourceUrl + 'img/img_empty_content.png',
      tip: '暂无数据',
      fixed: true,
      top: '100rpx'
    }
  }

  onLoad() {
    this.$nextTick(async () => {
      uni.$off('refreshExhibit')
      uni.$on('refreshExhibit', () => {
        this.$refs.exhibit.refresh()
        this.$refs.resetExhibit.refresh()
      })
    })
  }

  onUnload() {
    uni.$off('refreshExhibit')
  }

  // 搜索框清空事件
  doSearchClear() {
    this.keyWord = ''
    this.pageNum = 0
    this.skuList = []
  }

  doSearch() {
    this.doQueryGoods(true)
  }

  mescrollInit(mescroll) {
    this.mescroll = mescroll
  }

  async doLoad() {
    await this.doQueryGoods(false)
  }

  async doRefresh() {
    this.pageNum = 0
    if (this.mescroll) {
      this.mescroll.resetUpScroll()
    }
  }

  handleClickOutside() {
    if (this.queueObj && this.queueObj.closeOutside) {
      this.queueObj.closeOutside()
    }
  }

  // 扫码按钮点击事件
  doScan() {
    uni.scanCode({
      success: (res) => {
        this.keyWord = res.result || ''
        if (this.keyWord) {
          this.doQueryGoods(true)
        }
      },
      fail: (res) => {}
    })
  }

  /**
   * 查询商品列表
   * @param body
   * @returns
   */
  doQueryGoods(showLoading: boolean = true) {
    this.isLoading = true
    if (showLoading) {
      this.pageNum = 0
      this.$showLoading({ delayTime: 200 })
      this.skuList = []
    }
    const params = new QueryRequest()
    if (this.keyWord) {
      params.conditions = [{ operation: 'keyword:%=%', parameters: [this.keyWord] }]
    }
    params.page = this.pageNum
    params.pageSize = this.upOption.page.size
    params.fetchParts = ['image', 'sort', 'tag']
    AppGdQueryApi.queryGoods(params)
      .then((resp) => {
        this.$hideLoading()
        if (this.pageNum === 0) {
          this.skuList = resp.data || []
        } else {
          this.skuList.push(...(resp.data || []))
        }
        this.pageNum++
        this.isLoading = false
        this.finished = !resp.more
        this.mescroll.endBySize(resp.data.length, resp.total)
        this.mescroll.endSuccess()
      })
      .catch((e) => {
        this.$hideLoading()
        this.mescroll.endErr()
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  handleShowDesc(desc: string) {
    this.$showModal({
      title: '查看说明',
      content: desc,
      showCancel: false,
      confirmText: '关闭',
      success: (action) => {}
    })
  }

  //PDA扫码回调事件
  doScanAfter(scanWord) {
    this.keyWord = scanWord || ''
    if (this.keyWord) {
      this.doQueryGoods(true)
    }
  }

  /**
   * 跳转商品详情
   * @param sku 商品
   */
  handleRedirctDetail(sku: GoodsQueryInfo) {
    this.$Router.push({
      name: 'gdQuerySkuDetail',
      params: {
        uuid: sku.uuid,
        inputCode: sku.inputCode,
        goodsType: sku.goodsType
      }
    })
  }

  /**
   * 陈列位置调整
   */
  resetExhibit(info: GoodsQueryInfo, index: number) {
    this.exhibitIndex = index
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.inputCode
    this.$refs.resetExhibit.open(slotGoods)
  }

  /**
   * 陈列位置绑定成功
   */
  async confirmExhibit(value) {
    await this.doQueryGoods(true)
  }

  /**
   * 添加陈列位置
   */
  addExhibit() {
    this.$Router.push({
      name: 'addExhibitLocation'
    })
  }

  /**
   * 删除陈列位置
   */
  deleteExhibit() {
    this.$Router.push({
      name: 'deleteExhibitLocation'
    })
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info: GoodsQueryInfo) {
    this.viewExhibitInfo = { ...info }
    this.$refs.viewExhibit.open()
  }

  /**
   * 打开库存调整弹窗
   */
  operateInvQty(sku: GoodsQueryInfo) {
    this.$refs.operateInvQty.open(sku)
  }
}
