export default class Goods {
  // 数据标识
  uuid: string = ''
  // 代码
  code: string = ''
  // 输入码
  inputCode: string = ''
  // 名称
  name: string = ''
  // 规格
  qpc: number = 0
  // 包装规格
  qpcStr: string = ''
  // 规格价
  price: number = 0
  // 单价
  singlePrice: number = 0
  // 包装单位
  munit: string = ''
  // 最小规格单位
  minMunit: Nullable<string> = null
  // 商品介绍
  description: Nullable<string> = null
  // 门店管理生产日期
  storeUseMfg: number = 0
  // 门店管理到效期
  storeUseExp: number = 0
  // 含量
  spec: Nullable<string> = null
  // 溯源码管理
  useTraceCode: Nullable<number> = null
  // 溯源码规则,0-按标准，1-肉品，2-酒类，3-一物一码
  traceCodeRule: Nullable<number> = null
}
