<template>
  <view class="lable-print">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <hd-date-picker></hd-date-picker>
    <hd-drag-button
      v-if="isNotImportMode && oldDataList.length > 0"
      :enablePointerEvents="false"
      custom-style="height:calc(100vh - 224rpx);position:fixed;top:202rpx;left:0;z-index:3;"
      :x="622"
      :y="500"
      :img="'/static/icon/move_scan_2x.png' | oss"
      @click="doScan"
    ></hd-drag-button>

    <view class="lable-print-header">
      <view class="lable-print-header-text">
        标签模板
        <text>*</text>
      </view>
      <view class="lable-print-header-content" @click="doReasonShow(true)">
        <view :class="['lable-print-header-content-name', isSelectTemplate ? 'active-color' : 'normal-color']">
          {{ isSelectTemplate ? headerTemplateInfo.name : '请选择标签模板' }}
        </view>
        <view class="lable-print-header-content-icon">
          <image class="image_40" :src="'/static/icon/ic_chevronright_line_grey.png' | oss"></image>
        </view>
      </view>
    </view>

    <view class="lable-print-blank"></view>

    <view class="lable-print-sub-header">
      <view class="lable-print-sub-header-tips">商品明细</view>
      <view class="lable-print-sub-header-operate" v-if="oldDataList.length > 0">
        <image v-if="isNotImportMode" @click="doSelectGoods" class="img" :src="'/static/icon/ic_add_line_grey_2x.png' | oss"></image>
        <image v-if="!isNotImportMode" @click="doImportGoods" class="img" :src="'/static/icon/ic_add_line_grey_2x.png' | oss"></image>
        <!-- <image @click="doSearchGoods" class="img" :src="'/static/icon/ic_search_line_grey_2x.png' | oss"></image> -->
        <image @click="doFilterGoods" class="img" :src="filterIcon"></image>
      </view>
    </view>

    <view class="lable-print-main">
      <view class="lable-print-main-list" v-if="dataList && dataList.length > 0">
        <hd-swipe-action
          v-for="(good, index) in dataList"
          :key="good.gid"
          @onDelete="doDelete(good)"
          :index="index"
          :swipeAble="true"
          :moveIndex="moveIndex"
          @updateIndex="onUpdateIndex"
        >
          <list-card
            :validateNotNull="validateNotNull"
            :good="good"
            :key="good.gid"
            :otherSorts="otherSorts"
            @print="handlePrint"
            @change="cardValueChange"
            @selectTemplate="selectTemplate"
            @quantityChange="(item) => updateGoodsQuantity(item)"
          ></list-card>
        </hd-swipe-action>
      </view>

      <!-- <mescroll-body
        v-if="!isNotImportMode"
        ref="mescrollRef"
        @init="mescrollInit"
        @up="doLoad"
        @down="doRefresh"
        :down="downOption"
        :up="upOption"
        :safearea="false"
      >
        <hd-swipe-action
          v-for="(good, index) in dataList"
          :key="index"
          @onDelete="doDelete(good)"
          :index="index"
          :swipeAble="true"
          :moveIndex="moveIndex"
          @updateIndex="onUpdateIndex"
        >
          <list-card
            :validateNotNull="validateNotNull"
            :good="good"
            :key="index"
            :otherSorts="otherSorts"
            @print="handlePrint"
            @change="cardValueChange"
            @selectTemplate="selectTemplate"
            @quantityChange="(item) => updateGoodsQuantity(item, index)"
          ></list-card>
        </hd-swipe-action>
      </mescroll-body> -->

      <view class="lable-print-main-none" v-if="oldDataList.length === 0">
        <view class="lable-print-main-none-img">
          <image class="lable-print-main-none-img" :src="'/static/icon/img_empty_goods.png' | oss"></image>
        </view>
        <view class="lable-print-main-none-txt">请先选择标签模板</view>
        <view class="lable-print-main-none-btn lable-print-main-none-special" @click="doScan">
          <image class="lable-print-main-none-btn-img" :src="'/static/icon/<EMAIL>' | oss"></image>
          <view class="lable-print-main-none-btn-text">扫码添加</view>
        </view>
        <!-- <view class="lable-print-main-none-btn lable-print-main-none-normal normal-color" @click="doSearchGoods">
          <image class="lable-print-main-none-btn-img" :src="'/static/icon/ic_search_line_grey_2x.png' | oss"></image>
          <view class="lable-print-main-none-btn-text">搜索添加</view>
        </view> -->
        <view class="lable-print-main-none-btn lable-print-main-none-normal normal-color" @click="doSelectGoods">
          <image class="lable-print-main-none-btn-img" :src="'/static/icon/ic_add_line_grey_2x.png' | oss"></image>
          <view class="lable-print-main-none-btn-text">分类添加</view>
        </view>
        <view class="lable-print-main-none-btn lable-print-main-none-normal normal-color" @click="doImportGoods">
          <image class="lable-print-main-none-btn-img" :src="'/static/icon/ic_add_line_grey_2x.png' | oss"></image>
          <view class="lable-print-main-none-btn-text">导入添加</view>
        </view>
        <view
          class="lable-print-main-none-btn lable-print-main-none-normal normal-color"
          v-if="isShowPrintListAddBtn"
          @click="doImportGoodsFromPrintList"
        >
          <image class="lable-print-main-none-btn-img" :src="'/static/icon/ic_import_good.png' | oss"></image>
          <view class="lable-print-main-none-btn-text">从打印列表导入</view>
        </view>
      </view>

      <view class="lable-print-main-none" v-if="dataList.length === 0 && oldDataList.length !== 0">
        <view class="lable-print-main-none-img">
          <image class="lable-print-main-none-img" :src="'/static/icon/img_empty_goods.png' | oss"></image>
        </view>
        <view class="lable-print-main-none-txt">暂无筛选数据</view>
      </view>
    </view>

    <uni-popup ref="template" type="bottom">
      <hd-reason title="选择标签模板" @confirm="doReasonConfirm" :reasons="templatelist" :reason="templateInfo" @close="doReasonClose"></hd-reason>
    </uni-popup>

    <uni-drawer mode="right" :visible="showFilter" @close="showFilter = false" @open="showFilter = true">
      <filter-dialog ref="filter" :otherSorts="otherSorts" @confirm="doConfirmFilter"></filter-dialog>
    </uni-drawer>
  </view>
</template>

<script lang="ts" src="./LabelPrintingList.ts"></script>

<style lang="scss" scoped>
.lable-print {
  position: relative;
  width: 750rpx;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: constant(safe-area-inset-bottom) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
  padding-top: 200rpx;
  &-header {
    width: 750rpx;
    height: 88rpx;
    background: #ffffff;
    padding: 22rpx 24rpx;
    box-sizing: border-box;
    @include flex(row, flex-start, center);
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 30rpx;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    &-text {
      width: 136rpx;
      color: #666666;
      margin-right: 84rpx;
      text {
        color: #fd3431;
      }
    }
    &-content {
      width: calc(100% - 220rpx);
      @include flex(row, flex-start, center);
      &-name {
        flex: 1;
        color: #333333;
        @include ellipsis();
      }
      &-icon {
        height: 32rpx;
        width: 32rpx;
      }
    }
  }
  &-blank {
    width: 750rpx;
    height: 20rpx;
    background: #f5f6f7;
    position: fixed;
    top: 88rpx;
    left: 0;
    z-index: 99;
  }
  &-sub-header {
    height: 92rpx;
    width: 100%;
    padding: 0 24rpx;
    box-sizing: border-box;
    @include flex(row, space-between, center);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
    font-style: normal;
    border-bottom: 1rpx solid #e3e4e8;
    position: fixed;
    top: 108rpx;
    left: 0;
    z-index: 99;
    background: #ffffff;
    &-operate {
      height: 64rpx;
    }
  }
  &-main {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    width: 750rpx;
    &-list {
      position: relative;
      overflow-y: auto;
      height: calc(100vh - 200rpx) !important;
      height: calc(100vh - 200rpx - constant(safe-area-inset-bottom)) !important;
      height: calc(100vh - 200rpx - env(safe-area-inset-bottom)) !important;
    }
    ::v-deep .mescroll-body {
      min-height: calc(100vh - 200rpx) !important;
      min-height: calc(100vh - 200rpx - constant(safe-area-inset-bottom)) !important;
      min-height: calc(100vh - 200rpx - env(safe-area-inset-bottom)) !important;
    }
    &-none {
      display: flex;
      align-items: center;
      flex-direction: column;
      padding-top: 80rpx;
      flex: 1 1 auto;
      &-img {
        width: 300rpx;
        height: 300rpx;
      }
      &-txt {
        font-family: PingFangSC, PingFang SC;
        font-style: normal;
        font-size: 32rpx;
        font-weight: 400;
        color: #999999;
        line-height: 44rpx;
        margin-bottom: 12rpx;
      }
      &-btn {
        width: 480rpx;
        height: 88rpx;
        box-shadow: 0rpx 8rpx 16rpx 0rpx rgba(32, 107, 253, 0.2);
        border-radius: 46rpx;
        @include flex(row, center, center);
        margin-top: 32rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 32rpx;
        font-style: normal;
        line-height: 40rpx;
        &-img {
          width: 40rpx;
          height: 40rpx;
          margin-right: 8rpx;
        }
      }
      &-special {
        color: #ffffff;
        background: linear-gradient(131deg, #2e84fe 0%, #1c64fd 100%);
      }
      &-normal {
        border: 1rpx solid #a7a7a7;
      }
    }
  }

  .img {
    width: 64rpx;
    height: 64rpx;
    margin-left: 24rpx;
  }

  .active-color {
    color: #666666;
  }

  .normal-color {
    color: #999999;
  }
}
</style>
