/*
 * @Author: weisheng
 * @Date: 2024-03-11 11:39:45
 * @LastEditTime: 2025-04-22 16:21:35
 * @LastEditors: hanwei
 * @Description:
 * @FilePath: \soa\src\pagesSD\gdQuery\GdQueryPanel.ts
 * 记得注释
 */
import { Component, Vue } from 'vue-property-decorator'
import { State } from 'vuex-class'
import WeatherSearchBar from './cmp/WeatherSearchBar.vue'
import AppGdQueryApi from '@/network/gdQuery/AppGdQueryApi'
import TabCategory from '@/model/gdQuery/TabCategory'
import QueryRequest from '@/model/base/QueryRequest'
import GoodsQueryInfo from '@/model/gdQuery/GoodsQueryInfo'
import SysConfigItem from '@/model/sysConfig/SysConfigItem'
import SideBar from '@/pages/cmp/SideBar.vue'
import SkuCard from './cmp/SkuCard.vue'
import UseQueues from '@/components/hd-popover/UseQueues'
import { mixins } from 'vue-class-component'
import QuerySort from './cmp/QuerySort.vue'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import GoodsSlotBinder from '@/model/data/GoodsSlotBinder'
import SelectExhibit from '@/components/select-exhibit/SelectExhibit.vue'
import ModuleOption from '@/model/default/ModuleOption'
import ResetExhibit from '@/components/select-exhibit/ResetExhibit.vue'
import OperateInvQtyDialog from './cmp/OperateInvQtyDialog.vue'
import { ModuleId } from '@/model/common/OptionListModuleId'
class GdQuerySort {
  show: Nullable<boolean> = true
  prop: string = ''
  label: string = ''
  order: 'asc' | 'desc' | null = null
  isMore: boolean = false
}

@Component({
  components: { WeatherSearchBar, SideBar, SkuCard, QuerySort, ViewExhibit, ResetExhibit, SelectExhibit, OperateInvQtyDialog }
})
export default class GdQueryPanel extends mixins(UseQueues, BroadCast) {
  @State('sysConfig') sysConfig: SysConfigItem // 获取全局配置
  @State('optionList') optionList: ModuleOption[] //店务配置列表
  categoryList: TabCategory[] = [] // 分类列表
  selectedCategory: TabCategory = new TabCategory() // 选中的分类
  selectedSecondCategory: TabCategory = new TabCategory() // 选中的二级分类
  pageSize: number = 6 // 每页大小
  pageNum: number = 0 // 页码
  finished: boolean = false // 是否加载完成
  isLoading: boolean = false // 是否在加载
  skuList: GoodsQueryInfo[] = [] // 商品列表
  inited: boolean = false // 是否初始化过
  sorts: GdQuerySort[] = []
  isRefresh: boolean = false // 是否重新刷新该页
  otherSorts: any = {
    existsSlot: null, // 是否存在货架位
    invMinQty: null, // 库存最小值
    invMaxQty: null // 库存最大值
  }
  viewExhibitInfo: GoodsQueryInfo = new GoodsQueryInfo() // 商品信息
  exhibitIndex: number = -1 // 商品下标
  scrollTop: number = 0 // scroll-view的scrollTop值
  oldScrollTop: number = 0 // 上一次的scrollTop值
  total: number = 0 // 商品总数量
  $refs: any

  // 被选中的一级类目
  get selectedIndex() {
    return this.categoryList.findIndex((item) => item.code === this.selectedCategory.code)
  }

  // 被选中的二级类目
  get selectedSecondIndex() {
    if (this.selectedCategory.children) {
      return this.selectedCategory.children.findIndex((item) => item.code === this.selectedSecondCategory.code)
    } else {
      return -1
    }
  }

  //商品面板类目展示左侧面板
  get showSinglePanel() {
    if (this.sysConfig && 'skuCategoryPanel' in this.sysConfig) {
      return this.sysConfig.skuCategoryPanel === 'single'
    }

    return false
  }

  // 店务商品查询是否展示周销量
  get showSkuWeekSale() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSKUWEEKSALE)) {
      return true
    }
    return false
  }

  // 店务商品查询是否展示月销量
  get showSkuMonthSale() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSKUMONTHSALE)) {
      return true
    }
    return false
  }

  // 店务商品查询是否展示可用库存
  get showSellableInvQty() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSELLABLEINVQTY)) {
      return true
    }
    return false
  }

  onLoad() {
    this.$nextTick(async () => {
      this.$showLoading()
      await this.doQueryCategory()
      const queryRequest: QueryRequest = new QueryRequest()
      queryRequest.page = this.pageNum
      queryRequest.pageSize = this.pageSize
      await this.doQueryGoods(queryRequest)
      this.inited = true
      this.$hideLoading()
      this.sorts = [
        {
          prop: 'weekSaleQty',
          label: '周销量',
          order: null,
          show: this.showSkuWeekSale,
          isMore: false
        },
        {
          prop: 'monthSaleQty',
          label: '月销量',
          order: null,
          show: this.showSkuMonthSale,
          isMore: false
        },
        {
          prop: 'sellableInvQty',
          label: '可售库存',
          order: null,
          show: this.showSellableInvQty,
          isMore: false
        },
        {
          prop: 'more',
          label: '更多',
          order: null,
          show: true,
          isMore: true
        }
      ] // 排序
      uni.$off('refreshExhibit')
      uni.$on('refreshExhibit', () => {
        this.$refs.exhibit.refresh()
        this.$refs.resetExhibit.refresh()
      })
    })
  }

  onUnload() {
    uni.$off('refreshExhibit')
  }

  handleClickOutside() {
    if (this.queueObj && this.queueObj.closeOutside) {
      this.queueObj.closeOutside()
    }
  }

  handleShowDesc(desc: string) {
    this.$showModal({
      title: '查看说明',
      content: desc,
      showCancel: false,
      confirmText: '关闭',
      success: (action) => {}
    })
  }

  // 更多筛选
  async handleMore(value) {
    this.otherSorts = JSON.parse(JSON.stringify(value))
    this.pageNum = 0 // 页码
    this.finished = false // 是否加载完成
    this.isLoading = false // 是否在加载
    const queryRequest = new QueryRequest()
    this.$showLoading()
    console.log(this.selectedCategory.code, 66666)
    await this.doQueryGoods(queryRequest)
    this.$hideLoading()
  }

  /**
   * 查询商品分类
   * @returns
   */
  doQueryCategory() {
    return new Promise<void>((resolve, reject) => {
      AppGdQueryApi.listCategory()
        .then((resp) => {
          this.categoryList = resp.data || []
          if (this.categoryList.length > 0) {
            this.selectedCategory = this.categoryList[0]
            if (this.selectedCategory.children.length > 0) {
              this.selectedSecondCategory = this.selectedCategory.children[0]
            }
          }
          resolve()
        })
        .catch((error) => {
          this.inited = true
          this.$hideLoading()
          this.$showToast({ icon: 'error', title: error.msg })
          reject()
        })
    })
  }

  /**
   * 查询商品列表
   * @param body
   * @returns
   */
  doQueryGoods(body: QueryRequest) {
    return new Promise<void>((resolve, reject) => {
      if (this.selectedCategory.code) {
        body.conditions = [
          {
            operation: 'categoryCode:=',
            parameters: [this.selectedCategory.code]
          }
        ]
      }
      if (this.selectedSecondCategory.code) {
        body.conditions = [
          {
            operation: 'categoryCode:=',
            parameters: [this.selectedSecondCategory.code]
          }
        ]
      }
      if (this.otherSorts.existsSlot !== null) {
        body.conditions.push({
          operation: 'existsSlot:=',
          parameters: [this.otherSorts.existsSlot]
        })
      }
      if (this.otherSorts.invMinQty !== null) {
        body.conditions.push({
          operation: 'invQty:>=',
          parameters: [this.otherSorts.invMinQty]
        })
      }
      if (this.otherSorts.invMaxQty !== null) {
        body.conditions.push({
          operation: 'invQty:<=',
          parameters: [this.otherSorts.invMaxQty]
        })
      }
      this.sorts.forEach((sort) => {
        if (sort.order) {
          body.sorts.push({
            // 排序字段
            field: sort.prop,
            // 排序方式，默认倒叙
            asc: sort.order === 'asc'
          })
        }
      })
      body.fetchParts = ['image', 'sort', 'tag']
      AppGdQueryApi.queryGoods(body)
        .then((resp) => {
          if (this.pageNum === 0) {
            this.skuList = resp.data || []
          } else {
            this.skuList.push(...(resp.data || []))
          }
          this.total = resp.total || 0
          this.pageNum++
          this.isLoading = false
          this.finished = !resp.more
          resolve()
        })
        .catch((error) => {
          this.inited = true
          this.$hideLoading()
          this.$showToast({ icon: 'error', title: error.msg })
          reject()
        })
    })
  }

  handleLoadmore() {
    if (this.finished || this.isLoading) {
      return
    }

    this.isLoading = true
    const queryRequest = new QueryRequest()
    queryRequest.page = this.pageNum
    queryRequest.pageSize = this.pageSize
    this.doQueryGoods(queryRequest)
  }

  listScroll(e) {
    this.oldScrollTop = e.detail.scrollTop
  }

  // 滚动到页面顶部
  scrollToTop() {
    this.scrollTop = this.oldScrollTop
    this.$nextTick(() => {
      this.scrollTop = 0
    })
  }

  /**
   * 跳转搜索页面
   */
  handleRedirctSearch() {
    this.$Router.push({
      name: 'gdQueryPanelSearch'
    })
  }

  /**
   * 扫码跳转
   * @param keyWord 扫码内容
   */
  handleScanSearch(keyWord: string) {
    this.$Router.push({
      name: 'gdQueryPanelSearch',
      params: {
        value: keyWord
      }
    })
  }

  //PDA扫码回调事件
  doScanAfter(scanWord) {
    this.$Router.push({
      name: 'gdQueryPanelSearch',
      params: {
        value: scanWord
      }
    })
  }

  /**
   * 跳转商品详情
   * @param sku 商品
   */
  handleRedirctDetail(sku: GoodsQueryInfo) {
    this.$Router.push({
      name: 'gdQuerySkuDetail',
      params: {
        uuid: sku.uuid,
        inputCode: sku.inputCode,
        goodsType: sku.goodsType
      }
    })
  }

  /**
   * 排序
   * @param sorts
   */
  async handleSort(sorts: GdQuerySort[]) {
    this.sorts = sorts
    this.pageNum = 0 // 页码
    this.finished = false // 是否加载完成
    this.isLoading = false // 是否在加载
    const queryRequest = new QueryRequest()
    this.$showLoading()
    await this.doQueryGoods(queryRequest)
    this.$hideLoading()
  }

  // 一级分类change事件
  async handleFirstChange(index: number) {
    // if (this.selectedIndex == index) {
    //   return
    // }
    this.pageNum = 0 // 页码
    this.finished = false // 是否加载完成
    this.isLoading = false // 是否在加载
    this.selectedCategory = new TabCategory()
    this.selectedSecondCategory = new TabCategory()
    if (this.categoryList && this.categoryList.length > index) {
      this.selectedCategory = this.categoryList[index]
      if (this.selectedCategory.children.length > 0) {
        this.selectedSecondCategory = this.selectedCategory.children[0]
      }
      const queryRequest = new QueryRequest()
      this.$showLoading()
      await this.doQueryGoods(queryRequest)
      this.$hideLoading()
      this.scrollToTop() // 回到顶部
    }
  }
  // 二级分类点击事件
  async handleSecondChange(goodsCategory: TabCategory, index: number) {
    // if (this.selectedSecondIndex == index) {
    //   return
    // }
    this.selectedSecondCategory = goodsCategory
    this.pageNum = 0 // 页码
    this.finished = false // 是否加载完成
    this.isLoading = false // 是否在加载
    const queryRequest = new QueryRequest()
    this.$showLoading()
    await this.doQueryGoods(queryRequest)
    this.$hideLoading()
    this.scrollToTop() // 回到顶部
  }

  /**
   * 陈列位置调整
   */
  resetExhibit(info: GoodsQueryInfo, index: number) {
    this.exhibitIndex = index
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.inputCode
    this.$refs.resetExhibit.open(slotGoods)
  }

  // 重新查询这个商品
  handleUpdateSku(sku: GoodsQueryInfo) {
    const queryRequest = new QueryRequest()
    queryRequest.page = 0
    queryRequest.pageSize = 1
    queryRequest.conditions = [
      {
        operation: 'uuid:=',
        parameters: [sku.uuid]
      }
    ]
    queryRequest.fetchParts = ['image']

    AppGdQueryApi.queryGoods(queryRequest)
      .then((resp) => {
        if (resp.data && resp.data.length > 0) {
          const updatedSku = resp.data[0]
          const index = this.skuList.findIndex((item) => item.uuid === sku.uuid)
          if (index !== -1) {
            this.$set(this.skuList, index, updatedSku)
          }
        }
      })
      .catch((error) => {
        this.$showToast({ icon: 'error', title: error.msg })
      })
  }

  /**
   * 陈列位置绑定成功
   */
  async confirmExhibit(value) {
    const queryRequest: QueryRequest = new QueryRequest()
    this.pageNum = 0
    this.finished = false // 是否加载完成
    this.isLoading = false // 是否在加载
    this.$showLoading()
    await this.doQueryGoods(queryRequest)
    this.handleLoadmore()
    this.$hideLoading()
  }

  /**
   * 添加陈列位置
   */
  addExhibit() {
    this.$Router.push({
      name: 'addExhibitLocation'
    })
  }

  /**
   * 删除陈列位置
   */
  deleteExhibit() {
    this.$Router.push({
      name: 'deleteExhibitLocation'
    })
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info: GoodsQueryInfo) {
    this.viewExhibitInfo = { ...info }
    this.$refs.viewExhibit.open()
  }

  /**
   * 打开库存调整弹窗
   */
  operateInvQty(sku: GoodsQueryInfo) {
    this.$refs.operateInvQty.open(sku)
  }
}
