/*
 * @Author: 刘湘
 * @Date: 2022-02-09 16:47:56
 * @LastEditTime: 2022-10-21 14:41:16
 * @LastEditors: 刘湘
 * @Description:
 * @FilePath: \soa\src\model\invXFApply\InvXFApplyCreation.ts
 * 记得注释
 */
import InvXFApplyCreationLine from 'model/invXFApply/InvXFApplyCreationLine'
import { InvXFApplyType } from 'model/invXFApply/InvXFApplyType'
import InvXFApplyPackLine from './InvXFApplyPackLine'

export default class InvXFApplyCreation {
  // 单据标识
  billId: string = ''
  // 发起方数据标识
  starterId: string = ''
  // 发起方名称
  starterName: string = ''
  // 调拨类型，取值范围：increase-调入，decrease-调出
  type: Nullable<InvXFApplyType> = null
  // 相对方门店标识,调拨类型为调入时，它为调出门店；调拨类型为调出时，它为调入门店
  counterShopId: string = ''
  // 相对方门店代码
  counterShopNo: string = ''
  // 相对方门店名称
  counterShopName: string = ''
  // 备注
  note: Nullable<string> = null
  // 商品明细
  lines: InvXFApplyCreationLine[] = []
  // 配送模式
  deliveryType: Nullable<number> = null
  // 包材明细
  packLines: InvXFApplyPackLine[] = []
}
