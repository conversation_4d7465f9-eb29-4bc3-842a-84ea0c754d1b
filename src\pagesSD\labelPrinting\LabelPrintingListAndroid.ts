import { Vue, Component, Watch } from 'vue-property-decorator'
import { Action, State } from 'vuex-class'
import { mixins } from 'vue-class-component'
import swipeMix from '@/components/hd-swipe-action/swipeMix'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import DateUtil from '@/utils/DateUtil'
import ListCard from './cmp/ListCard.vue'
import FilterDialog from './cmp/FilterDialog.vue'
import CodeName from '@/model/base/CodeName'
import AppGdQueryApi from '@/network/gdQuery/AppGdQueryApi'
import AppPrintLabelApi from '@/network/AppPrintLabelApi/AppPrintLabelApi'
import AppPrintLabelTemplateDTO from 'model/AppPrintLabel/AppPrintLabelTemplateDTO'
import AppPrintLabelGoodsExpandDTO from 'model/AppPrintLabel/AppPrintLabelGoodsExpandDTO'
import AppPrintLabelPreviewResultDTO from '@/model/AppPrintLabel/AppPrintLabelPreviewResultDTO'
import AppPrintLabelPreviewRequestDTO from '@/model/AppPrintLabel/AppPrintLabelPreviewRequestDTO'
import CommonUtil from '@/utils/CommonUtil'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import config from '@/config'
import QueryCondition from 'model/default/QueryCondition'
import AppPrintLabelGoodsDTO from 'model/AppPrintLabel/AppPrintLabelGoodsDTO'
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js'
import BluetoothPrinter, { BluetoothDeviceInfo } from '@/utils/bluetoothPrinter'
import QueryRequest from '@/model/base/QueryRequest'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
type PrintSku = {
  printFileUrl: string
  printCount: number
}
@Component({
  components: { ListCard, FilterDialog }
})
export default class LabelPrintingListAndroid extends mixins(swipeMix, BroadCast, MescrollMixin) {
  @Action('goodsList') actionGoodsList // 提交到vuex
  @State('optionList') optionList: ModuleOption[] //店务配置列表
  @State('currentDevice') currentDevice: Nullable<BluetoothDeviceInfo>

  $refs: any
  isGoodSelectTemplate: boolean = false // 是否是商品选择模板
  templateInfo: Nullable<CodeName> = new CodeName() // 当前选中模板
  headerTemplateInfo: Nullable<CodeName> = new CodeName() //表头的模板信息
  templatelist: AppPrintLabelTemplateDTO[] = [] // 模板列表
  currentGood: AppPrintLabelGoodsExpandDTO = new AppPrintLabelGoodsExpandDTO() // 当前操作的商品

  // 添加打印相关状态
  printIndex: number = 0 // 当前打印序号
  printing: boolean = false // 是否正在打印
  total: number = 0 // 总打印数量
  printTasks: PrintSku[] = [] // 打印任务列表

  bluetoothPrinter = new BluetoothPrinter() // 添加打印机实例

  otherSorts: any = {
    memberPriceFilter: 0 // 会员价格筛选
  }
  oldDataList: AppPrintLabelGoodsExpandDTO[] = [] // 非导入模式下，筛选前的商品列表，用于前端筛选
  currentMode: string = 'other' // 当前的商品添加模式（导入/非导入）
  condition: QueryCondition[] = [] // 筛选条件

  resultPage: number = 0 // 从打印列表导入商品的页码
  skuListFromPrintList: AppPrintLabelGoodsExpandDTO[] = [] // // 从打印列表导入商品的列表

  page: number = 0
  isLoading: boolean = false
  downOption = {
    use: false,
    auto: false
  }

  upOption = {
    auto: false,
    page: {
      num: 0,
      size: 20
    },
    textNoMore: '-- 我是有底线的 --',
    noMoreSize: 5,
    empty: {
      use: true,
      icon: config.sourceUrl + 'img/img_empty_content.png',
      tip: '暂无数据',
      fixed: true,
      top: '100rpx'
    }
  }
  showFilter: boolean = false // 是否显示筛选弹窗

  get dataList() {
    // 判断会员价的条件是会员价不为空且会员价!=零售价
    if (this.otherSorts.memberPriceFilter === 2) {
      return this.oldDataList.filter((item: AppPrintLabelGoodsExpandDTO) => !!item.mbrPrc && item.mbrPrc !== item.rtlPrc)
    } else if (this.otherSorts.memberPriceFilter === 1) {
      return this.oldDataList.filter((item: AppPrintLabelGoodsExpandDTO) => !(!!item.mbrPrc && item.mbrPrc !== item.rtlPrc))
    } else {
      return CommonUtil.deepClone(this.oldDataList)
    }
  }

  get filterIcon() {
    return Vue.filter('oss')(this.otherSorts.memberPriceFilter ? '/static/icon/ic_shaixuan_green.png' : '/static/icon/ic_shaixuan_grey.png')
  }

  // 是否是非导入模式
  get isNotImportMode() {
    return this.currentMode === 'other'
  }

  // 是否选择了模板
  get isSelectTemplate() {
    return this.headerTemplateInfo && this.headerTemplateInfo.name ? true : false
  }

  // 数据是否全部完成
  get isFinished() {
    return this.dataList.length > 0 && (this.dataList.every((item) => item.expDate && item.mfgDate) || !this.validateNotNull)
  }

  /**
   * 是否展示加入打印列表按钮
   */
  get isShowPrintListAddBtn() {
    return PermissionMgr.hasPermission(Permission.printListAdd)
  }

  // 效期是否必填 0-否（def）,1-是'
  get validateNotNull() {
    // 获取optionList中moduleId为ModuleId.sosPrintLabel的选项
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosPrintLabel
        })
      : []
    // 如果moduleConfig不为空，并且moduleConfig[0].options中存在validateNotNull属性，则返回validateNotNull属性的值
    if (moduleConfig.length > 0 && moduleConfig[0].options && 'validateNotNull' in moduleConfig[0].options) {
      return moduleConfig[0].options.validateNotNull == '1' ? true : false
    }

    return false
  }

  async onLoad() {
    const data: any = await this.getTemplateList()
    this.templatelist =
      (data &&
        data.map((e) => {
          return { uuid: e.name, code: e.name, name: e.name }
        })) ||
      []
    await this.handleInitBluetooth()
    // 非导入模式 - 获取当前编辑数据
    uni.$on('labelPrintingSkuChange', (skuList) => {
      this.currentMode = 'other'
      this.initData(skuList)
    })

    // 导入模式 - 获取当前编辑数据
    uni.$on('labelPrintingSkuNumChange', (obj) => {
      this.$showLoading()
      this.currentMode = 'import'
      this.condition = CommonUtil.deepClone(obj.conditionList)
      this.initData(obj.result)
    })
  }

  onShow() {
    const list: AppPrintLabelGoodsExpandDTO[] = uni.getStorageSync('labelPrintingList')
    if (list && list.length > 0) {
      this.oldDataList = []
      this.currentMode = 'other'
      const lineList: AppPrintLabelGoodsExpandDTO[] = []
      for (let index = 0; index < list.length; index++) {
        const line = list[index]
        lineList.push(line)
      }
      this.initData(lineList)
      uni.removeStorage({ key: 'labelPrintingList' })
    }
  }

  onUnload() {
    // 页面卸载时断开蓝牙连接
    this.bluetoothPrinter.disconnectBluetooth()
    uni.$off('labelPrintingSkuChange')
    uni.$off('labelPrintingSkuNumChange')
  }

  // 初始化数据
  initData(data: AppPrintLabelGoodsExpandDTO[]) {
    this.oldDataList = []
    this.oldDataList = data.reduce((acc: AppPrintLabelGoodsExpandDTO[], cur: AppPrintLabelGoodsExpandDTO) => {
      if (!cur.templateName) cur.templateName = this.headerTemplateInfo?.name!
      cur.qty = cur.qty || 1
      cur.checked = cur.checked || false
      cur.mfgDate = cur.mfgDate || null
      cur.expDate = cur.expDate || null
      cur.gid = CommonUtil.uuid()
      acc.push(cur)
      return acc
    }, [])
  }

  mescrollInit(mescroll) {
    this.mescroll = mescroll
  }

  async doLoad() {
    // await this.getResult()
  }

  async doRefresh() {
    this.page = 0
    if (this.mescroll) {
      this.mescroll.resetUpScroll()
    }
  }

  doDelete(data: any) {
    this.oldDataList = this.oldDataList.filter((item) => !(item.uuid === data.uuid && item.qpc === data.qpc))
  }

  // 扫码事件
  doScan() {
    if (!this.isSelectTemplate) {
      this.$showToast({ icon: 'warning', title: '请先选择标签模板～' })
      return
    }
    uni.scanCode({
      success: (res) => {
        const scanWord = res.result || ''
        this.doScanAfter(scanWord)
      },
      fail: () => {}
    })
  }

  // 添加新的搜索方法
  async searchByKeyword(keyword: string): Promise<AppPrintLabelGoodsExpandDTO[]> {
    const query = new QueryRequest()
    query.page = 0
    query.pageSize = 20
    query.conditions = [{ operation: 'keyword:%=%', parameters: [keyword] }]
    query.fetchParts = ['category']

    try {
      const resp = await AppPrintLabelApi.queryGoods(query)
      return (resp.data || []).map((sku) => {
        return {
          checked: false,
          qty: 1,
          ...sku,
          mfgDate: null,
          expDate: null,
          templateName: '',
          gid: CommonUtil.uuid()
        }
      })
    } catch (e) {
      this.$showToast({ icon: 'none', title: e.msg })
      return []
    }
  }

  // PDA扫码回调事件
  async doScanAfter(scanWord) {
    if (!this.isSelectTemplate) {
      this.$showToast({ icon: 'warning', title: '请先选择标签模板～' })
      return
    }

    this.$showLoading()
    try {
      const result = await this.searchByKeyword(scanWord)
      if (result.length === 1) {
        // 只有一个商品时直接添加
        const goods = result[0]
        goods.templateName = this.headerTemplateInfo!.name
        goods.qty = 1
        // 检查是否已存在
        const existIndex = this.oldDataList.findIndex((item) => item.uuid === goods.uuid && item.code === goods.code && item.qpc === goods.qpc)
        if (existIndex === -1) {
          this.oldDataList.unshift(goods)
        } else {
          // 弹窗询问是否增加打印数量
          this.$showModal({
            content: '已存在此商品，是否增加一份打印数量？',
            cancelText: '取消',
            confirmText: '确认',
            confirmColor: '#1C64FD',
            success: (res) => {
              if (res.confirm) {
                // 增加打印数量
                const currentQty = this.oldDataList[existIndex].qty || 1
                this.$set(this.oldDataList[existIndex], 'qty', currentQty + 1)
              }
            }
          })
        }
      } else if (result.length > 1) {
        // 多个商品时跳转到搜索页面
        this.pageJumpBefore('labelPrintingSearch', { from: 'edit', value: scanWord })
      } else {
        this.$showToast({ icon: 'none', title: '未找到相关商品' })
      }
    } catch (error) {
      this.$showToast({ icon: 'error', title: error.msg || '搜索失败' })
    } finally {
      this.$hideLoading()
    }
  }

  /**
   * 搜索添加商品
   */
  doSearchGoods() {
    if (!this.isSelectTemplate) {
      this.$showToast({ icon: 'warning', title: '请先选择标签模板～' })
      return
    }
    this.pageJumpBefore('labelPrintingSearch', { from: 'edit' })
  }

  /**
   * 筛选当前商品
   */
  doFilterGoods() {
    this.showFilter = !this.showFilter
    this.$refs.filter.init()
  }

  /**
   * 选择商品
   */
  doSelectGoods() {
    if (!this.isSelectTemplate) {
      this.$showToast({ icon: 'warning', title: '请先选择标签模板～' })
      return
    }
    this.pageJumpBefore('labelPrintingSkuSelect')
  }

  /**
   * 导入商品
   */
  doImportGoods() {
    if (!this.isSelectTemplate) {
      this.$showToast({ icon: 'warning', title: '请先选择标签模板～' })
      return
    }
    uni.setStorage({
      key: 'labelPrintingCondition',
      data: this.condition,
      success: () => {
        this.$Router.push({ name: 'labelPrintingSkuImportModeSelect' })
      },
      fail: (e) => {
        console.log(e)
      }
    })
  }

  /**
   * 从打印列表导入商品
   */
  async doImportGoodsFromPrintList() {
    if (!this.isSelectTemplate) {
      this.$showToast({ icon: 'warning', title: '请先选择标签模板～' })
      return
    }
    this.currentMode = 'other'
    this.skuListFromPrintList = []
    await this.getGoodsFromPrintList()
  }

  async getGoodsFromPrintList() {
    const params = new QueryRequest()
    params.page = this.resultPage
    params.pageSize = 20
    params.conditions.push({
      operation: 'importFromPrintList:=',
      parameters: ['1']
    })
    this.$showLoading({ delayTime: 200 })
    AppPrintLabelApi.queryGoods(params)
      .then((resp) => {
        this.$hideLoading()
        if (resp.data && resp.data.length === 0 && this.resultPage === 0) {
          this.$showToast({ icon: 'error', title: '先在[店务商品查询]中，向打印列表添加商品！' })
          return
        }
        this.resultPage++
        if (resp.data) {
          const listData: AppPrintLabelGoodsExpandDTO[] = resp.data.map((item: AppPrintLabelGoodsDTO) => {
            return {
              ...item,
              qty: 1,
              checked: false,
              mfgDate: null,
              expDate: null,
              templateName: '',
              gid: ''
            }
          })
          if (params.page === 0) {
            this.skuListFromPrintList = listData
          } else {
            this.skuListFromPrintList.push(...listData)
          }
          this.initData(this.skuListFromPrintList)
          if (resp.more) {
            this.getGoodsFromPrintList()
          } else {
            this.resultPage = 0
          }
        }
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg || '加载失败' })
      })
  }

  // 清除打印列表中的商品
  clearGoodsFromPrintList() {
    this.$showLoading()
    AppGdQueryApi.removePrintList()
      .then(() => {
        this.$hideLoading()
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg || '清除失败' })
      })
  }

  /**
   * 跳转页面之前的商品存储
   */
  pageJumpBefore(target: string, params: { [key: string]: string } = {}) {
    const selectedList: AppPrintLabelGoodsExpandDTO[] = []
    for (let index = 0; index < this.oldDataList.length; index++) {
      const obj: any = {
        ...this.oldDataList[index],
        qty: this.oldDataList[index].qty === undefined ? 1 : this.oldDataList[index].qty // 仅当qty未定义时才设置默认值1
      }
      selectedList.push(obj as AppPrintLabelGoodsExpandDTO)
    }
    this.actionGoodsList(selectedList)
    uni.setStorage({
      key: 'labelPrintingList',
      data: selectedList,
      success: () => {
        this.$Router.push({ name: target, params })
      },
      fail: (e) => {
        console.log(e)
      }
    })
  }

  cardValueChange(data) {
    const index = this.oldDataList.findIndex((item) => item.gid === data.gid)
    if (data.type === 'num') {
      this.$set(this.oldDataList[index], 'qty', data.num)
    } else {
      this.$showPicker({
        currentDate: DateUtil.format(new Date(), 'yyyy-MM-dd'),
        startDate: data.type === 'valid' ? DateUtil.format(new Date(), 'yyyy-MM-dd') : undefined,
        endDate: data.type === 'prd' ? DateUtil.format(new Date(), 'yyyy-MM-dd') : undefined,
        type: 'date',
        success: (res) => {
          if (res.date) {
            let mfgDate: any = null // 生产日期
            let expDate: any = null // 到效日期
            const validPeriod = (data.validPeriod || 0) * 24 * 60 * 60 * 1000 // 商品有效期
            if (data.type === 'prd') {
              mfgDate = res.date
              expDate = DateUtil.format(new Date(new Date(mfgDate).getTime() + validPeriod), 'yyyy-MM-dd')
              const isShowTips = new Date(expDate).getTime() < new Date(new Date().toLocaleDateString()).getTime()
              if (isShowTips) {
                this.$showToast({
                  icon: 'warning',
                  title: '到效日期不可小于当天~'
                })
                return
              }
            } else if (data.type === 'valid') {
              expDate = res.date
              mfgDate = DateUtil.format(new Date(new Date(expDate).getTime() - validPeriod), 'yyyy-MM-dd')
              const isShowTips = new Date(mfgDate).getTime() > new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1
              if (isShowTips) {
                this.$showToast({
                  icon: 'warning',
                  title: '生产日期不可大于当天~'
                })
                return
              }
            }
            this.$set(this.oldDataList[index], 'mfgDate', mfgDate)
            this.$set(this.oldDataList[index], 'expDate', expDate)
          }
        },
        fail: () => {}
      })
    }
  }

  // 模板列表查询
  getTemplateList() {
    this.$showLoading({ delayTime: 200 })
    return new Promise((reslove, reject) => {
      AppPrintLabelApi.listTemplate()
        .then((resp) => {
          reslove(resp.data || [])
        })
        .catch((e) => {
          this.$showToast({ icon: 'error', title: e.msg })
          reject([])
        })
        .finally(() => {
          this.$hideLoading()
        })
    })
  }

  selectTemplate(value: any) {
    this.currentGood = value.good
    this.doReasonShow(false, value.good.templateName)
  }

  doReasonShow(value: boolean, templateName?: string) {
    this.isGoodSelectTemplate = !value
    if (value) {
      this.templateInfo = this.headerTemplateInfo
    } else {
      this.templateInfo = {
        code: templateName!,
        name: templateName!
      }
    }
    this.$refs.template.open()
  }

  /**
   * 模板列表确认选择
   * @param type
   */
  async doReasonConfirm(info: CodeName) {
    console.log(this.isGoodSelectTemplate, 'isGoodSelectTemplate')
    if (this.isGoodSelectTemplate) {
      const index = this.oldDataList.findIndex((item) => item.gid === this.currentGood.gid)
      this.$set(this.oldDataList[index], 'templateName', info.name)
    } else {
      this.headerTemplateInfo = info
      this.oldDataList.forEach((item, index) => {
        if (!item.templateName) {
          this.$set(this.oldDataList[index], 'templateName', info.name)
        }
      })
      console.log(this.oldDataList, 'headerTemplateInfo')
    }
    this.doReasonClose()
  }

  /**
   * 模板列表关闭
   */
  doReasonClose() {
    this.$refs.template.close()
  }

  /**
   * 去打印预览
   */
  async handlePrint(goods: AppPrintLabelGoodsExpandDTO, num: number) {
    if (!this.isSelectTemplate) {
      this.$showToast({ icon: 'warning', title: '请先选择标签模板～' })
      return
    }
    this.$showLoading()
    try {
      this.bluetoothPrinter.disconnectBluetooth()
      const sku = await this.getPreviewInfo(goods)
      if (sku) {
        uni.setStorage({
          key: 'commonPrintSku',
          data: [
            {
              printFileUrl: sku.printFileUrl,
              printCount: num
            }
          ],
          success: () => {
            let name = 'commonPrint'
            // #ifdef APP-PLUS
            if (uni.getSystemInfoSync().platform === 'android') {
              name = 'androidCommonPrint'
            } else {
              name = 'commonPrint'
            }
            // #endif
            this.$Router.push({ name: name, params: { type: 'heibiao' } })
          },
          fail: (e) => {
            console.log(e)
          }
        })
      }
    } catch (error) {
      this.$showToast({ icon: 'error', title: error.msg })
    }
    this.$hideLoading()
  }

  getImageInfo(src: string) {
    return new Promise<UniApp.GetImageInfoSuccessData>((resolve, reject) => {
      uni.getImageInfo({
        src: src,
        success: (res) => {
          resolve(res)
        },
        fail: (res) => {
          reject(0)
        }
      })
    })
  }

  // 获取商品打印预览信息
  async getPreviewInfo(value: AppPrintLabelGoodsExpandDTO) {
    return await new Promise<AppPrintLabelPreviewResultDTO | null>((resolve, reject) => {
      const params = new AppPrintLabelPreviewRequestDTO()
      params.templateName = value.templateName
      const data = CommonUtil.deepClone(value)
      delete data.qty
      delete data.checked
      delete data.templateName
      params.goods = data
      AppPrintLabelApi.preview(params)
        .then((res) => {
          resolve(res.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  async handleInitBluetooth() {
    try {
      uni.showLoading({})
      const isBluetoothOpen = await this.bluetoothPrinter.checkBluetoothOpen()
      if (isBluetoothOpen) {
        uni.hideLoading()
      } else {
        throw new Error('蓝牙未开启')
      }
    } catch (error) {
      uni.hideLoading()
      uni.showToast({ title: error.message, icon: 'error' })
    }
  }

  handleSetting() {
    let name = 'blueToothList'
    // #ifdef APP-PLUS
    if (uni.getSystemInfoSync().platform === 'android') {
      name = 'androidBlueToothList'
    } else {
      name = 'blueToothList'
    }
    // #endif
    this.$Router.push({
      name: name
    })
  }

  /**
   * 清空商品列表
   */
  doClear() {
    this.$showModal({
      title: '提示',
      content: '是否确认清空所有商品？',
      success: (res) => {
        if (res.confirm) {
          this.currentMode === 'other'
          this.condition = []
          this.oldDataList = []
          this.actionGoodsList([])
        }
      }
    })
  }

  updateGoodsQuantity({ qty, gid }) {
    const index = this.oldDataList.findIndex((item) => item.gid === gid)
    this.$set(this.oldDataList[index], 'qty', qty)
  }

  /**
   * 全部打印
   */
  async doPrintAll() {
    if (!this.isSelectTemplate) {
      this.$showToast({ icon: 'warning', title: '请先选择标签模板～' })
      return
    }
    if (!this.dataList.length || !this.currentDevice) {
      this.$showModal({
        content: '未选择打印设备，请选择',
        cancelText: '取消',
        confirmText: '去选择',
        confirmColor: '#1C64FD',
        success: (res) => {
          if (res.confirm) {
            this.handleSetting()
          }
        }
      })
      return
    }

    this.$showModal({
      content: `是否确认打印已加入的${this.dataList.length}个商品`,
      cancelText: '取消',
      confirmText: '确认',
      confirmColor: '#1C64FD',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 重置打印状态
            this.printIndex = 0
            this.printing = true
            this.printTasks = []
            this.total = this.dataList.length // 直接使用商品数量

            // 连接打印机
            await this.bluetoothPrinter.confirmBluetooth(this.currentDevice!)
            this.$showLoading({
              title: `准备传输中~`
            })
            // 收集所有打印任务并打印
            for (const goods of this.dataList) {
              const sku = await this.getPreviewInfo(goods)
              if (sku) {
                this.printTasks.push({
                  printFileUrl: sku.printFileUrl,
                  printCount: goods.qty || 1
                })
                // 显示当前打印进度
                this.$showLoading({
                  title: `正在传输第 ${this.printIndex + 1} 个`
                })

                // 获取图片信息并打印
                const { path: imgUrl, height } = await this.getImageInfo(sku.printFileUrl)
                await this.bluetoothPrinter.printImageHeibiao(imgUrl, height.toString(), goods.qty || 1)
                this.printIndex++

                // 每个商品打印完成后等待一下,避免打印机缓冲区满
                await new Promise((resolve) => setTimeout(resolve, 1000))
              }
            }
            this.$hideLoading()
            this.$showToast({ title: '传输完成', icon: 'success' })
            this.clearGoodsFromPrintList()
          } catch (error) {
            this.$hideLoading()
            this.$showToast({ icon: 'error', title: error.msg || '打印失败' })
          } finally {
            this.printing = false
            this.printIndex = 0
          }
        }
      }
    })
  }

  // 确认筛选条件
  doConfirmFilter(value) {
    this.showFilter = false
    this.$set(this, 'otherSorts', value)
  }
}
