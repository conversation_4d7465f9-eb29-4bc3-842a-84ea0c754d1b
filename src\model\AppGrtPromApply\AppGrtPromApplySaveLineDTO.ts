/*
 * @Author: yuzhipi
 * @Date: 2025-05-12 14:41:53
 * @LastEditTime: 2025-05-12 14:49:07
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: /soa/src/model/AppGrtPromApply/AppGrtPromApplySaveLineDTO.ts
 * 记得注释
 */
import AppGrtPromApplyAttachDTO from 'model/AppGrtPromApply/AppGrtPromApplyAttachDTO'
import AppGrtPromApplyBaseLineDTO from 'model/AppGrtPromApply/AppGrtPromApplyBaseLineDTO'
import AppGrtPromApplyPeriodDTO from 'model/AppGrtPromApply/AppGrtPromApplyPeriodDTO'
import GrtPromApplyGoodsDTO from 'model/AppGrtPromApply/GrtPromApplyGoodsDTO'

export default class AppGrtPromApplySaveLineDTO extends AppGrtPromApplyBaseLineDTO {
  // 所属单据标识
  billId: string = ''
  // 商品
  goods: GrtPromApplyGoodsDTO = new GrtPromApplyGoodsDTO()
  // 临保促销单商品折扣周期明细
  periods: AppGrtPromApplyPeriodDTO[] = []
  // 临保商品特价单商品明细附件
  attaches: AppGrtPromApplyAttachDTO[] = []
}
