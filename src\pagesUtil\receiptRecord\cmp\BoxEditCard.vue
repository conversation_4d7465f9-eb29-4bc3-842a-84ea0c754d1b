<template>
  <view class="box-edit-card" :style="customStyle" @click.stop="box.type === 'split' ? handleClick() : ''">
    <view class="header">
      <template v-if="box.type === 'split'">
        <text class="header-tag">周转箱</text>
        <text class="header-boxno">{{ box.boxNo }}</text>
        <image class="header-arrow" :src="'/static/icon/ic_noticebar_right_grey.png' | oss" mode="aspectFill"></image>
      </template>
      <template v-if="box.type === 'whole'">
        <text class="header-disp" v-if="box.boxGoodss[0].goods.isDisp">散称</text>

        <text class="header-text">{{ box.boxGoodss[0].goods.name }}</text>
      </template>
    </view>
    <view class="main">
      <block v-if="box.type === 'split'">
        <!-- TODO 产品要求去掉差异的显示 害怕他们又改回来，先放在这 -->
        <!-- <view class="main-count">共包含{{ box.boxGoodss.length }}种商品{{ !!splitDiffCount ? `，${splitDiffCount}种商品有差异` : '' }}</view> -->
        <view class="main-count">共包含{{ box.boxGoodss.length }}种商品</view>

        <view v-if="box.confirmed" class="main-operation" style="margin-top: 16rpx">
          <view class="main-operation-left">
            <img :src="'/static/icon/ic_user.png' | oss" class="main-operation-left-icon" />
            {{ box.consigneeName | empty }}
          </view>
          <view class="main-operation-right">
            <view class="main-operation-right-btn" @click.stop="handleClick">修改</view>
          </view>
        </view>
      </block>
      <block v-if="box.type === 'whole'">
        <view class="main-info">
          <view class="main-info__img">
            <image lazy-load class="main-info__img-sku" :src="img" @click.stop="handlePreviewImg" />
            <view class="main-info__scale">
              <image :src="'/static/icon/img_enlarge2.png' | oss" class="main-info__scale-img"></image>
            </view>
          </view>
          <view class="main-info__value">
            <view class="main-half">箱码：{{ box.boxNo }}</view>
            <view class="main-half">代码：{{ box.boxGoodss[0].goods.code }}</view>
            <view class="main-half">价格：￥{{ box.boxGoodss[0].goods.price }}/{{ box.boxGoodss[0].goods.munit }}</view>
            <view class="main-half">规格：{{ box.boxGoodss[0].goods.qpcStr }}/{{ box.boxGoodss[0].goods.munit }}</view>
            <view class="main-half" v-if="showCurInvQty">可售库存：{{ box.boxGoodss[0].invQty }}{{ box.boxGoodss[0].goods.minMunit }}</view>
          </view>
        </view>
        <view class="main-exhibit" v-if="showMaster.showDisplayLocation">
          <view :class="[hasMutiple ? 'goods-one' : '']">
            陈列位置：
            <text class="goods-text">{{ box.boxGoodss[0].displayLocation | empty }}</text>
          </view>
          <image class="good-img" :src="'/static/icon/ic_right_grey.png' | oss" v-if="hasMutiple" @click="viewExhibit" />
        </view>

        <!-- 绑定陈列位置 -->
        <view class="bind-exhibit" v-if="!box.confirmed && slotSource && isShowExhibitBtn">
          <view class="bind-exhibit-select">
            <view class="bind-exhibit-block-left">
              <text class="left-text">绑定陈列位置</text>
            </view>
            <view>
              <text @click="resetExhibit" v-if="allowOneGoodsMultipleSlot">陈列位置调整</text>
              <!-- <text @click="bindExhibit" style="margin-left: 32rpx">手动选择陈列位置</text> -->
            </view>
          </view>
          <view class="bind-exhibit-block">
            <view class="bind-exhibit-block-right">
              <input class="right-input" v-model="exhibitValue" placeholder="输入商品陈列位置编号 例：A-1-1-1" @blur="getGoodsInfo" />
            </view>
            <view class="right-scan" @click="doScanExhibit">
              <image class="right-img" :src="'/static/icon/ic_scan_blue.png' | oss" />
              <text class="right-text">扫码识别</text>
            </view>
          </view>
        </view>

        <template v-if="box.confirmed">
          <!-- 散称显示商品应收重量 -->
          <template v-if="box.boxGoodss[0].goods.isDisp || wholeBoxGoodsReceiptByMinQpc">
            <view class="main-half">
              应收：
              <text class="main-half--important">{{ box.boxGoodss[0].shipQty }}{{ box.boxGoodss[0].goods.minMunit | empty }}</text>
            </view>
            <view class="main-half">
              待收：
              <text class="main-half--important">{{ box.boxGoodss[0].qty }}{{ box.boxGoodss[0].goods.minMunit | empty }}</text>
            </view>

            <view class="main-half">
              本次收：
              <text class="main-half--important">{{ box.boxGoodss[0].receiptQty }}{{ box.boxGoodss[0].goods.minMunit | empty }}</text>
              <!-- TODO 产品要求去掉差异的显示 害怕他们又改回来，先放在这 -->
              <!-- <text style="color: #fd3431" v-if="diff">({{ diff }})</text> -->
            </view>
          </template>
          <template v-else>
            <view class="main-half">
              应收：
              <text class="main-half--important">{{ box.shipQty }}箱</text>
            </view>
            <view class="main-half">
              待收：
              <text class="main-half--important">{{ box.qty }}箱</text>
            </view>

            <view class="main-half">
              本次收：
              <text class="main-half--important">{{ box.receiptQty }}箱</text>
              <!-- TODO 产品要求去掉差异的显示 害怕他们又改回来，先放在这 -->
              <!-- <text style="color: #fd3431" v-if="diff">({{ diff }})</text> -->
            </view>
          </template>
        </template>

        <template v-if="!box.confirmed">
          <template v-if="box.boxGoodss[0].goods.isDisp">
            <view class="main-operation">
              <text>
                应收：
                <text class="main-operation-qty">{{ box.boxGoodss[0].shipQty }}{{ box.boxGoodss[0].goods.minMunit | empty }}</text>
              </text>

              <text>
                待收：
                <text class="main-operation-qty">{{ splitPendinReceipts }}{{ box.boxGoodss[0].goods.minMunit | empty }}</text>
              </text>
            </view>
            <view class="main-disp" v-if="doubleMeasureGoodsEnterQpcQty">
              <view class="main-disp-txt">件数</view>
              <view class="main-disp-number">
                <view class="main-disp-operator" v-if="box.boxGoodss[0].goods.qpc">{{ box.boxGoodss[0].goods.qpc | empty }} ×</view>
                <hd-number-box-test
                  v-model="wholeQty"
                  :max="Number(dispWholePendinReceipts)"
                  :scale="qtyScale"
                  :min="0"
                  :disabled="isSkuQtyDisabeld"
                  @change="handleWholeQtyChange"
                ></hd-number-box-test>
              </view>
            </view>
            <view class="main-disp">
              <view class="main-disp-txt">重量{{ box.boxGoodss[0].goods.minMunit ? `(${box.boxGoodss[0].goods.minMunit})` : '' }}</view>
              <view class="main-disp-number">
                <hd-number-box-test
                  v-model="splitQty"
                  @change="handleSplitQtyChange"
                  :scale="qtyScale"
                  :max="Number(splitPendinReceipts)"
                  :min="0"
                  :disabled="isSkuQtyDisabeld"
                ></hd-number-box-test>
              </view>
            </view>
          </template>
          <view class="main-operation" v-else>
            <text>
              应收数：
              <text class="main-operation-qty">{{ box.shipQty }}箱</text>
            </text>
            <text>
              待收：
              <text class="main-operation-qty">{{ wholePendinReceipts }}箱</text>
            </text>
            <view class="main-operation-right">
              <hd-number-box-test v-model="qty" :scale="0" :max="wholePendinReceipts" :disabled="isSkuQtyDisabeld"></hd-number-box-test>
            </view>
          </view>
          <view class="main-operation main-operation--end">
            <view class="main-operation-detail" @click="handleViewDetail">收货明细</view>
            <view class="main-operation-right-btn" @click="hanleConfirm">确认收货</view>
          </view>
        </template>
        <view v-else class="main-operation">
          <view class="main-operation-left">
            <img :src="'/static/icon/ic_user.png' | oss" class="main-operation-left-icon" />
            {{ box.consigneeName | empty }}
          </view>
          <view class="main-operation-right">
            <view class="main-operation-detail" @click="handleViewDetail">收货明细</view>
            <view class="main-operation-right-btn" @click="handleEdit">修改</view>
          </view>
        </view>
      </block>
    </view>
  </view>
</template>

<script lang="ts" src="./BoxEditCard.ts"></script>

<style lang="scss" scoped>
.box-edit-card {
  position: relative;
  width: 100%;
  background: #ffffff;
  box-sizing: border-box;
  padding: 24rpx;
  border-radius: 16rpx;

  .header {
    display: flex;
    align-items: center;
    width: 100%;
    line-height: 40rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 16rpx;

    &-gift {
      flex: 0 0 auto;
      display: inline-flex;
      align-items: center;
      padding: 0 10rpx;
      box-sizing: border-box;
      margin-right: 8rpx;
      width: 68rpx;
      height: 40rpx;
      background: linear-gradient(313deg, #ff9a00 0%, #ffd05b 100%);
      border-radius: 8rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #ffffff;
    }

    &-disp {
      flex: 0 0 auto;
      display: inline-flex;
      align-items: center;
      padding: 0 10rpx;
      box-sizing: border-box;
      margin-right: 8rpx;
      width: 68rpx;
      height: 40rpx;
      background: #e9f0ff;
      border-radius: 8rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #1c64fd;
    }

    &-text {
      font-weight: 550;
    }

    &-boxno {
      font-weight: 550;
      @include ellipsis();
    }

    &-arrow,
    &-img {
      position: relative;
      flex: 0 0 auto;
      width: 32rpx;
      height: 32rpx;
      margin-left: 8rpx;
    }
    &-img {
      &-tip {
        position: absolute;
        z-index: 11;
        right: -24rpx;
        top: -60rpx;
        display: inline-flex;
        align-items: center;
        padding: 0 12rpx;
        height: 44rpx;
        background: #000000;
        border-radius: 8rpx;
        font-size: 20rpx;
        color: #ffffff;
        white-space: nowrap;
        // 顶点朝下的底边宽20rpx 高12rpx的等边三角形
        &::after {
          content: '';
          position: absolute;
          z-index: 10;
          right: 24rpx;
          bottom: -12rpx;
          width: 0;
          height: 0;
          border-left: 12rpx solid transparent;
          border-right: 12rpx solid transparent;
          border-top: 12rpx solid #000000;
        }
      }
    }

    &-tag {
      flex: 0 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 88rpx;
      height: 36rpx;
      background: rgba(8, 176, 124, 0.08);
      border-radius: 4rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #08b07c;
      margin-right: 12rpx;
    }
  }

  .main {
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
    &-count {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 32rpx;
    }

    &-info {
      width: 100%;
      display: flex;
      margin-bottom: 16rpx;
      &__img {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        margin-right: 24rpx;
        flex: 0 0 auto;
        &-sku {
          width: 120rpx;
          height: 120rpx;
        }
      }

      &__scale {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 24rpx;
        height: 24rpx;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 8rpx 0px 8rpx 0rpx;
        text-align: center;
        @include flex();
        &-img {
          width: 16rpx;
          height: 16rpx;
        }
      }

      &__value {
        position: relative;
        flex: 1 1 auto;
      }
    }

    &-half {
      width: 50%;
      flex: 0 0 auto;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      display: inline-flex;

      // 如果不是最后两个元素，则有向下16rpx的margin
      &:not(:nth-last-child(-n + 2)) {
        margin-bottom: 16rpx;
      }

      .good-img {
        width: 32rpx;
        height: 32rpx;
        display: inline-table;
      }
      .goods-one {
        flex: 1;
        @include ellipsis();
      }
    }

    &-exhibit {
      width: 100%;
      background: #f5f5f5;
      border-radius: 8rpx;
      padding: 8rpx 16rpx;
      box-sizing: border-box;
      flex: 0 0 auto;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .good-img {
        width: 32rpx;
        height: 32rpx;
        display: inline-table;
      }

      .goods-text {
        color: #333333;
        font-size: 26rpx;
        font-weight: 500;
      }

      .goods-one {
        flex: 1;
        @include ellipsis();
      }
    }

    &-half--important {
      font-family: PingFangSC, PingFang SC;
      font-size: 24rpx;
      color: #333333;
      font-weight: 550;
    }

    &-operation {
      width: 100%;
      flex: 0 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 24rpx;
      color: #999999;

      &-qty {
        color: #333333;
        font-weight: 550;
      }

      &:not(:last-child) {
        margin-bottom: 16rpx;
      }

      &--end {
        justify-content: flex-end;
      }

      &-detail {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 48rpx;
        background: #ffffff;
        border: 1rpx solid #cccccc;
        border-radius: 32rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
        padding: 0 12rpx;
        margin-left: 12rpx;
        min-width: 112rpx;
      }

      &-left {
        flex: 1 1 auto;
        @include ellipsis();
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        display: flex;
        align-items: center;
        &-icon {
          width: 40rpx;
          height: 40rpx;
        }
      }

      &-right {
        flex: 0 0 auto;
        display: flex;
        justify-content: flex-end;

        &-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 48rpx;
          background: #e9f0ff;
          border: 1rpx solid #e9f0ff;
          border-radius: 32rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #1c64fd;
          padding: 0 12rpx;
          margin-left: 12rpx;
          min-width: 112rpx;
        }

        &-diffbtn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 218rpx;
          height: 48rpx;
          border-radius: 28rpx;
          border: 1rpx solid #1c64fd;
          font-size: 24rpx;
          color: #1c64fd;
          box-sizing: border-box;
        }
      }

      &-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &-label {
          font-size: 26rpx;
          color: #666666;
        }
      }
    }

    .main-disp {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 48rpx;
      box-sizing: border-box;
      margin-bottom: 16rpx;
      .main-disp-number {
        display: flex;
        justify-content: flex-end;
        .main-disp-operator {
          height: 48rpx;
          margin-right: 12rpx;
          line-height: 48rpx;
          font-size: 24rpx;
          font-family: HelveticaNeue;
          color: rgba(88, 90, 94, 1);
        }
      }
      .main-disp-txt {
        height: 48rpx;
        max-width: 386rpx;
        line-height: 48rpx;
        font-size: 24rpx;
        color: #585a5e;
        @include ellipsis();
      }
      .main-disp-arrow {
        height: 32rpx;
        width: 32rpx;
      }
    }
  }

  &:not(:last-child) {
    margin-bottom: 16rpx;
  }

  .bind-exhibit {
    width: 100%;
    margin-bottom: 16rpx;
    display: flex;
    flex-direction: column;

    .bind-exhibit-block {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-left {
        display: flex;
        align-items: center;

        .left-text {
          height: 32rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #585a5e;
          line-height: 32rpx;
          text-align: left;
          font-style: normal;
        }

        .left-img {
          width: 32rpx;
          height: 32rpx;
        }
      }

      &-right {
        flex: 1;
        height: 72rpx;
        background: #f5f5f5;
        border-radius: 4rpx;
        display: flex;
        align-items: center;
        padding: 0 16rpx;
        box-sizing: border-box;

        .right-input {
          flex: 1;
          height: 40rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
        }
      }

      .right-scan {
        width: 188rpx;
        height: 72rpx;
        background: #e9f0ff;
        border-radius: 8rpx;
        margin-left: 16rpx;
        @include flex(row);

        .right-img {
          width: 36rpx;
          height: 36rpx;
          margin-right: 16rpx;
        }

        .right-text {
          width: 104rpx;
          height: 32rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #1c64fd;
          line-height: 32rpx;
          text-align: left;
          font-style: normal;
        }
      }
    }

    .bind-exhibit-select {
      width: 100%;
      height: 32rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #1c64fd;
      line-height: 32rpx;
      text-align: right;
      font-style: normal;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
    }
  }
}
</style>
