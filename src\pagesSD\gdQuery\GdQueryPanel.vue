<!--
 * @Author: weisheng
 * @Date: 2024-10-16 18:07:37
 * @LastEditTime: 2025-04-29 17:41:49
 * @LastEditors: hanwei
 * @Description: 
 * @FilePath: \soa\src\pagesSD\gdQuery\GdQueryPanel.vue
 * 记得注释
-->
<template>
  <view class="gd-query-panel safe-area-inset-bottom" @click="handleClickOutside">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <view class="header">
      <weather-search-bar @search="handleRedirctSearch" @jumpSearch="handleScanSearch" :placeholder="'搜索商品或货架位'"></weather-search-bar>
    </view>
    <view class="main">
      <scroll-view scroll-y :enhanced="true" :show-scrollbar="false" class="main-side" v-if="categoryList.length">
        <side-bar
          :index="selectedIndex"
          :secondIndex="selectedSecondIndex"
          :showSinglePanel="true"
          :goodsCategoryList="categoryList"
          @change="handleFirstChange"
          @secondChange="handleSecondChange"
        ></side-bar>
      </scroll-view>
      <view class="main-content">
        <view class="main-content-sort" v-if="sorts.filter((item) => item.show).length">
          <query-sort :sorts="sorts" :otherSorts="otherSorts" @change="handleSort" @changeMore="handleMore"></query-sort>
        </view>
        <view class="main-content-num" v-if="total > 0">
          共
          <text>{{ total }}</text>
          种
        </view>
        <scroll-view class="main-content-list" @scrolltolower="handleLoadmore" :scrollTop="scrollTop" @scroll="listScroll" scroll-y>
          <sku-card
            v-for="sku in skuList"
            :key="sku.uuid"
            :sku="sku"
            :id="`item-${sku.uuid}`"
            @detail="handleRedirctDetail"
            @showDesc="handleShowDesc"
            @viewExhibit="viewExhibit"
            @operateInvQty="operateInvQty"
            @resetExhibit="resetExhibit"
          ></sku-card>
          <hd-empty v-if="inited && !skuList.length" :img="'/static/icon/img_empty_bill.png' | oss" title="暂无分类商品"></hd-empty>
          <view class="loading" v-if="!finished && skuList.length && inited">
            <view class="load-more"></view>
            <text>拼命加载中~~~</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 查看多个陈列位置 -->
    <uni-popup ref="viewExhibit" type="bottom">
      <view-exhibit :name="viewExhibitInfo.friendlyStr" :displayLocation="viewExhibitInfo.displayLocation" @doClose="closeViewExhibit"></view-exhibit>
    </uni-popup>

    <!-- 选择陈列位置 -->
    <select-exhibit ref="exhibit" @deleteExhibit="deleteExhibit" @addExhibit="addExhibit" @success="confirmExhibit"></select-exhibit>

    <!-- 陈列位置调整 -->
    <reset-exhibit
      ref="resetExhibit"
      :showOperate="true"
      :isGoods="true"
      @deleteExhibit="deleteExhibit"
      @addExhibit="addExhibit"
      @success="confirmExhibit"
    ></reset-exhibit>

    <!-- 调整库存 -->
    <operate-inv-qty-dialog ref="operateInvQty" @success="handleUpdateSku"></operate-inv-qty-dialog>
  </view>
</template>
<script lang="ts" src="./GdQueryPanel.ts"></script>
<style lang="scss" scoped>
.gd-query-panel {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  .header {
    flex: 0 0 auto;
  }
  .main {
    // flex: 1 1 auto;
    height: calc(100vh - 120rpx);
    height: calc(100vh - 120rpx - constant(safe-area-inset-bottom));
    height: calc(100vh - 120rpx - env(safe-area-inset-bottom));
    display: flex;
    // overflow: hidden;
    &-side {
      height: 100%;
      width: 168rpx;
      overflow: auto;
      // flex: 0 0 auto;
    }
    &-content {
      // flex: 1 1 auto;
      height: 100%;
      display: flex;
      flex-direction: column;
      width: calc(100% - 168rpx);

      &-sort {
        flex: 0 0 auto;
      }

      &-list {
        // flex: 1 1 auto;
        height: calc(100% - 128rpx);
        .loading {
          height: 72rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24rpx;
          color: rgb(148, 150, 154);
        }
      }

      &-num {
        width: 100%;
        height: 56rpx;
        padding: 12rpx 20rpx;
        box-sizing: border-box;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #636978;
        text-align: left;

        text {
          color: #343840;
          font-weight: medium;
          margin: 0 10rpx;
        }
      }
    }
  }
}
</style>
