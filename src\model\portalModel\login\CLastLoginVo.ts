/**
 * 用户登录响应数据模型
 */
export default class UserResponse {
  /**
   * 最后登录信息
   */
  lastLogin: LastLogin

  /**
   * 用户登录信息
   */
  userLogonInfo: UserLogonInfo

  /**
   * 可登录组织总数
   */
  loginableOrgTotal: number

  /**
   * 可登录组织列表（当前为 null）
   */
  loginableOrgs: null
}

/**
 * 最后登录信息模型
 */
export interface LastLogin {
  /**
   * 组织 ID
   */
  id: string

  /**
   * 组织编码
   */
  code: string

  /**
   * 组织名称
   */
  name: string

  /**
   * 组织类型
   */
  type: string

  /**
   * 状态（示例值：enabled）
   */
  state: string

  /**
   * 上级组织 ID
   */
  parentOrgId: string

  /**
   * 最后登录时间（ISO 8601 格式）
   */
  loginTime: string
}

/**
 * 用户登录信息模型
 */
export interface UserLogonInfo {
  /**
   * 租户 ID
   */
  tenant: string

  /**
   * 用户 ID（数值类型）
   */
  userId: number

  /**
   * 用户名
   */
  userName: string

  /**
   * 工作组织 ID
   */
  worKOrgId: string

  /**
   * 工作组织编码
   */
  worKOrgCode: string

  /**
   * 工作组织名称
   */
  worKOrgName: string

  /**
   * 工作组织类型
   */
  worKOrgType: string

  /**
   * 总部组织名称
   */
  hqOrgName: string

  /**
   * 员工 ID
   */
  employeeId: string

  /**
   * 员工编码
   */
  employeeCode: string

  /**
   * 员工姓名
   */
  employeeName: string

  /**
   * 手机号
   */
  mobile: string

  /**
   * 员工在职状态（示例值：onDuty）
   */
  employeeStationState: string

  /**
   * 登录来源
   */
  source: string

  /**
   * 外部用户 ID（可空）
   */
  outerUserId: null

  /**
   * 外部用户编码（可空）
   */
  outerUserCode: null
}
