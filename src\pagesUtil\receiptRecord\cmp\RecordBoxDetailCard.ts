/*
 * @Author: 刘湘
 * @Date: 2021-05-06 16:22:21
 * @LastEditTime: 2025-03-18 14:03:23
 * @LastEditors: hanwei
 * @Description:
 * @FilePath: \soa\src\pagesUtil\receiptRecord\cmp\BoxEditCard.ts
 * 记得注释
 */
import { Vue, Component, Prop, Watch, Inject } from 'vue-property-decorator'
import ModuleOption from '@/model/default/ModuleOption'
import { Getter, State } from 'vuex-class'
import { ModuleId } from '@/model/common/OptionListModuleId'
import AppReceiptRecordBoxDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxDTO'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'

// eslint-disable-next-line @typescript-eslint/ban-ts-ignore
// @ts-ignore
@Component({
  components: {},
  options: {
    virtualHost: true
  }
})
export default class BoxEditCard extends Vue {
  @Getter('qtyScale') qtyScale: number
  @Prop({ type: Object, default: () => new AppReceiptRecordBoxDTO() }) box: AppReceiptRecordBoxDTO // 单据信息
  @Prop({ type: String, default: '' }) customStyle: string // 自定义样式
  @State('optionList') optionList: ModuleOption[]

  _uid: any

  // 散称双计量是否需要录入件数（包装数）
  get doubleMeasureGoodsEnterQpcQty() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.doubleMeasureGoodsEnterQpcQty == '1'
    }
    return false
  }

  //获取字段展示权限
  get showMaster() {
    const masterPermission = {
      showDisplayLocation: false // 陈列位置
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDISPLAYLOCATION)) {
      masterPermission.showDisplayLocation = true
    }
    return masterPermission
  }

  /**
   * 是否有多个陈列位置
   */
  get hasMutiple() {
    return this.box.boxGoodss[0] && this.box.boxGoodss[0].displayLocation && this.box.boxGoodss[0].displayLocation.split(',').length > 1
  }

  handleClick() {
    this.$emit('click', this.box)
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit() {
    this.$emit('viewExhibit', this.box.boxGoodss[0])
  }
}
