/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-01-08 19:10:17
 * @LastEditTime: 2025-04-17 17:30:11
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: /soa/src/pagesSD/labelPrinting/cmp/ListCard.ts
 * 记得注释
 */
import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
import CodeName from '@/model/base/CodeName'
import AppPrintLabelGoodsExpandDTO from 'model/AppPrintLabel/AppPrintLabelGoodsExpandDTO'

@Component({
  components: {}
})
export default class ListCard extends Vue {
  @Prop({ type: Object, default: () => new CodeName() })
  templateInfo: CodeName // 模板信息
  @Prop({ type: Object, default: () => new AppPrintLabelGoodsExpandDTO() })
  good: AppPrintLabelGoodsExpandDTO // 商品信息
  @Prop({ type: Object, default: () => {} })
  otherSorts: any // 筛选信息
  @Prop({ type: Boolean, default: false })
  validateNotNull: boolean // 效期是否必填

  templateName: string = ''

  num: number = 0

  printNum: number = 0 // 打印数量

  @Watch('good', { immediate: true, deep: true })
  onChange(n, o) {
    this.num = Number(n.qty || 0)
    this.printNum = Number(n.qty || 0) // 设置打印数量默认值为商品数量
    this.templateName = n.templateName || '--'
  }

  // 是否有生产日期
  get isHasPrdDate() {
    return !!this.good.mfgDate || !this.validateNotNull
  }

  // 是否有到效日期
  get isHasValidDate() {
    return !!this.good.expDate || !this.validateNotNull
  }

  selectTemplate() {
    this.$emit('selectTemplate', {
      good: this.good
    })
  }

  // 组件数据变化
  change(type: string) {
    const data = {
      uuid: this.good.uuid,
      num: this.num,
      mfgDate: this.good.mfgDate,
      expDate: this.good.expDate,
      type: type,
      gid: this.good.gid,
      validPeriod: this.good.validPeriod
    }
    this.$emit('change', data)
  }

  /**
   * 去打印
   */
  handlePrint() {
    this.$emit('print', this.good, this.printNum)
  }

  /**
   * 打印数量
   */
  doNumberChange(value: number) {
    this.$emit('quantityChange', {
      uuid: this.good.uuid,
      qty: value,
      qpc: this.good.qpc,
      gid: this.good.gid
    })
  }
}
