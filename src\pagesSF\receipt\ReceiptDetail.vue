<template>
  <view class="receipt-detail" :class="{ 'short-box': data.state === 'finished' && showComment }">
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <sku-record-dialog ref="recordDialog"></sku-record-dialog>

    <view class="top-header">
      <view class="state-line">
        <image :src="stateImg" class="header-image"></image>
        <view class="state-text">{{ state | empty }}</view>
        <view v-if="data.channel === 'EC'" class="ec-tag">
          <image :src="'/static/icon_Flat/ic_dianshang.png' | oss" class="ec-image"></image>
          <text>电商</text>
        </view>
      </view>
      <view class="num-text">{{ (data.displayNum || data.num) | empty }}</view>
    </view>
    <!-- 头部汇总数据 -->

    <view class="top-summary-expand" v-if="showPrice">
      <image :src="'/static/img/pay_detail_2x.png' | oss" class="back-img" mode="aspectFill"></image>

      <view class="order-amount">
        <view class="label">总金额（元）</view>
        <view class="data">
          {{ data.receiptTotal | fmt('#,##0.00') | empty }}
        </view>
      </view>

      <view class="content">
        <view class="order-num">
          <view class="label">订货类别（种）</view>
          <view class="data">
            {{ data.recCnt | empty }}
          </view>
        </view>
        <view class="order-num">
          <view class="label">商品（件）</view>
          <view class="data">
            {{ data.receiptQty | empty }}
          </view>
        </view>
      </view>
    </view>
    <view class="top-summary" v-else>
      <view class="order-amount">
        <view class="label">商品类别(种)</view>
        <view class="data">
          {{ data.recCnt | empty }}
        </view>
      </view>
      <view class="order-amount">
        <view class="label">商品(件)</view>
        <view class="data">
          {{ data.receiptQty | empty }}
        </view>
      </view>
      <image :src="'/static/icon/<EMAIL>' | oss" class="back-img" mode="aspectFill"></image>
    </view>
    <!--收货列表-->
    <sale-list
      v-if="data.receiptWay !== 1"
      :lines="receiptLines.slice(0, 4)"
      :showMore="showMore"
      @total="doViewMore"
      :showPrice="showPrice"
      @doShowWrhNote="doShowWrhNote"
      @doRetract="doRetract"
      @doOpenValid="doOpenValid"
      @detail="handleViewDetail"
      @viewExhibit="viewExhibit"
      :enableMultipleReceipt="enableMultipleReceipt"
      :showWeightSkuReceivedQty="doubleMeasureGoodsEnterQpcQty"
    ></sale-list>
    <sale-list-box
      v-else
      :lines="boxGoods.slice(0, 4)"
      :showMore="boxShowMore"
      @total="doViewMore"
      :showPrice="showPrice"
      @detail="handleViewDetail"
      @viewExhibit="viewBoxExhibit"
      :enableMultipleReceipt="enableMultipleReceipt"
      :showWeightSkuReceivedQty="doubleMeasureGoodsEnterQpcQty"
    ></sale-list-box>

    <view class="bottom-info">
      <hd-cell title="送货方" :value="data.deliveryName || '--'" :isLink="false" :hasLine="false"></hd-cell>
      <block v-if="data.diffs && data.diffs.length > 0">
        <hd-cell
          v-for="(diff, index) in data.diffs"
          :key="index"
          title="收货差异"
          :value="diff.num"
          :isLink="allowNavReceiptDiff && diffPermision"
          :hasLine="false"
          @onClick="diffPermision && doReceiptDiff(diff)"
        ></hd-cell>
      </block>
      <hd-cell title="收货人" :value="data.consigneeName || '--'" :isLink="false" :hasLine="false"></hd-cell>
      <hd-cell title="收货完成时间" v-if="data.state === 'finished'" :value="data.receiptTime" :isLink="false" :hasLine="false"></hd-cell>

      <hd-cell
        v-if="data.reqOrdDisplayNum || data.reqOrdNum"
        title="订货单号"
        :value="data.reqOrdDisplayNum || data.reqOrdNum"
        :isLink="true"
        :hasLine="false"
        @onClick="handleNavRequireApply"
      ></hd-cell>

      <hd-cell v-if="data.requireTmplName" title="订货模板" :value="data.requireTmplName" :isLink="false" :hasLine="false"></hd-cell>
    </view>

    <view class="footer-btn safe-area-inset-bottom" v-if="data.state === 'finished' && showComment">
      <hd-button type="primary" @click="doComment">去评价</hd-button>
    </view>

    <!--拣货备注详情弹框 -->
    <uni-popup ref="wrhNote" type="bottom">
      <view class="receipt-diff">
        <view class="receipt-diff-header">
          查看备注
          <image @click="doWrhNoteClose" :src="'/static/icon/ic_closegrey.png' | oss" class="receipt-diff-close" mode="scaleToFill" />
        </view>
        <view class="receipt-diff-body">
          <scroll-view scroll-y style="height: 720rpx">
            <view class="content-list-text">
              <view class="data">
                <text v-if="wrhNote">{{ wrhNote }}</text>
                <text v-if="wrhNote && note">；</text>
                <text v-if="note">{{ note }}</text>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </uni-popup>

    <!-- 查看多个陈列位置 -->
    <uni-popup ref="viewExhibit" type="bottom">
      <view-exhibit
        v-if="data.receiptWay !== 1"
        :name="viewExhibitInfo.goods.name"
        :displayLocation="viewExhibitInfo.displayLocation"
        @doClose="closeViewExhibit"
      ></view-exhibit>
      <view-exhibit
        v-else
        :name="viewBoxExhibitInfo.goods.name"
        :displayLocation="viewBoxExhibitInfo.goods.displayLocation"
        @doClose="closeViewExhibit"
      ></view-exhibit>
    </uni-popup>
  </view>
</template>
<script lang="ts" src="./ReceiptDetail.ts"></script>
<style lang="scss" scoped>
.receipt-detail {
  overflow-x: hidden;
  background: $list-bg-color-lx;
  box-sizing: border-box;
  padding-bottom: 24rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  &.short-box {
    padding-bottom: 120rpx !important;
    padding-bottom: calc(120rpx + constant(safe-area-inset-bottom)) !important;
    padding-bottom: calc(120rpx + env(safe-area-inset-bottom)) !important;
  }

  .top-header {
    width: 750rpx;
    box-sizing: border-box;
    padding: 20rpx $base-padding;

    .state-line {
      @include flex(row, flex-start, center);
      line-height: 48rpx;
      .header-image {
        margin-right: 12rpx;
        width: 48rpx;
        height: 48rpx;
      }

      .state-text {
        font-size: 36rpx;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: $color-text-secondary;
      }
      .ec-image {
        width: 28rpx;
        height: 28rpx;

        vertical-align: middle;
      }
      .ec-tag {
        width: 96rpx;
        height: 40rpx;
        line-height: 40rpx;

        background: #1857fc;
        border-radius: 20rpx;
        margin-left: 12rpx;

        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        text-align: center;
      }
    }
    .num-text {
      margin-left: 50rpx;
      margin-top: 8rpx;
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $color-text-thirdly;
      line-height: 40rpx;
    }
  }

  .top-summary-expand {
    margin: 0rpx 24rpx 20rpx 24rpx;
    width: 702rpx;
    box-sizing: border-box;
    background: #ffffff;
    position: relative;
    height: 292rpx;
    box-sizing: border-box;
    border-radius: 16rpx 16rpx 0 0;
    overflow: hidden;
    .back-img {
      width: 702rpx;
      height: 292rpx;
    }
    .order-amount {
      position: absolute;
      top: 28rpx;
      padding: 0rpx 36rpx;
      .label {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 40rpx;
      }
      .data {
        font-size: 48rpx;
        font-family: DINAlternate-Bold, DINAlternate;
        font-weight: bold;
        color: #ffffff;
        line-height: 56rpx;
      }
    }
    .content {
      width: 702rpx;
      box-sizing: border-box;
      position: absolute;
      bottom: 0;
      @include flex(row, flex-start, center);
      padding: 24rpx 36rpx;
      .order-num {
        flex: 1 1 auto;
        text-align: left;
        .label {
          font-size: 24rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: $color-text-lx-placeholder;
          line-height: 40rpx;
        }
        .data {
          font-size: 48rpx;
          font-family: DINAlternate-Bold, DINAlternate;
          font-weight: bold;
          color: $color-text-primary;
          line-height: 56rpx;
        }
      }
    }
  }

  .top-summary {
    margin: 0rpx 24rpx 20rpx 24rpx;
    width: 702rpx;
    padding: 32rpx 36rpx;
    box-sizing: border-box;
    background: #ffffff;
    position: relative;
    width: 702rpx;
    height: 164rpx;
    background: linear-gradient(127deg, #4f87ff 0%, #2268fd 100%);
    border-radius: 16rpx;
    @include flex(row, flex-start, center);

    .back-img {
      position: absolute;
      width: 130rpx;
      height: 117rpx;
      bottom: 0;
      right: 0rpx;
    }
    .order-amount {
      width: 50%;
      flex: 1 1 auto;
      .label {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.8);
        line-height: 40rpx;
      }
      .data {
        margin-top: 8rpx;
        font-size: 56rpx;
        font-family: DINAlternate-Bold, DINAlternate;
        font-weight: bold;
        color: #ffffff;
        line-height: 56rpx;
      }
    }
  }

  .bottom-info {
    background-color: #ffffff;
    border-radius: 12rpx;
    overflow: hidden;
    margin: 16rpx 24rpx 0 24rpx;
    box-sizing: border-box;
    ::v-deep .cell-title {
      max-width: 180rpx;
    }
    ::v-deep .cell-value {
      text-align: right;
      max-width: 530rpx !important;
    }

    ::v-deep .cell-title-txt {
      color: $font-color-darklight;
      font-size: 28rpx;
    }
    ::v-deep .cell-value-txt {
      color: $color-text-primary;
      font-size: 28rpx;
    }
    ::v-deep .cell--clickable {
      .cell-value-txt {
        color: $color-primary !important;
      }
    }
  }

  .receipt-diff {
    display: flex;
    flex-direction: column;
    height: 900rpx;
    width: 750rpx;
    background: #ffffff;
    border-radius: 20px 20px 0px 0px;
    .receipt-diff-header {
      flex: 0 0 auto;
      position: relative;
      height: 112rpx;
      width: 750rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: $color-text-secondary;
      .receipt-diff-close {
        position: absolute;
        width: 48rpx;
        height: 48rpx;
        top: 32rpx;
        right: 24rpx;
      }
    }
    .receipt-diff-body {
      box-sizing: border-box;

      width: 750rpx;
      background: #ffffff;

      .content-list-text {
        text-align: left;

        width: 702rpx;
        margin: 0 24rpx;
        box-sizing: border-box;

        .data {
          word-break: break-all;
          width: 702rpx;
          padding: 22rpx 8rpx;
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: $color-text-secondary;
          line-height: 44rpx;
          box-sizing: border-box;
        }
      }
    }
  }

  .footer-btn {
    width: 100%;
    position: fixed;
    background-color: #fff;
    bottom: 0;
    left: 0;
    z-index: 2;
  }
}
</style>
