/*
 * @Author: weisheng
 * @Date: 2024-09-26 16:19:34
 * @LastEditTime: 2025-05-12 10:39:18
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /soa/src/pagesUtil/receiptRecord/ReceiptBoxLineEdit.ts
 * 记得注释
 */
import { Component } from 'vue-property-decorator'
import swipeMix from '@/components/hd-swipe-action/swipeMix'
import { mixins } from 'vue-class-component'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import BoxEditSplitCard from './cmp/BoxEditSplitCard.vue'
import AppReceiptBoxGoodsDTO from '@/model/receipt/AppReceiptBoxGoodsDTO'
import UseQueues from '@/components/hd-popover/UseQueues'
import AppReceiptRecordBoxCheckDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxCheckDTO'
import AppReceiptRecordApi from '@/network/AppReceiptRecordApi/AppReceiptRecordApi'
import QueryRequest from '@/model/base/QueryRequest'
import AppReceiptRecordBoxDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxDTO'
import AppReceiptRecordBoxCheckGoodsDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxCheckGoodsDTO'
import SelectExhibit from '@/components/select-exhibit/SelectExhibit.vue'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import ResetExhibit from '@/components/select-exhibit/ResetExhibit.vue'
import Slot from '@/model/data/Slot'
import GoodsSlotBinder from '@/model/data/GoodsSlotBinder'
import AppReceiptRecordBoxGoodsDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxGoodsDTO'
import AppReceiptMultipleRcvRecordQueryer from '@/model/receipt/AppReceiptMultipleRcvRecordQueryer'
import AppReceiptMultipleRcvGdRecordDTO from '@/model/receipt/AppReceiptMultipleRcvGdRecordDTO'
import ReceiptApi from '@/network/receipt/ReceiptApi'
import SkuRecordDialog from '@/pages/cmp/SkuRecordDialog.vue'
import AppReceiptRecordDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordDTO'
import ModuleOption from '@/model/default/ModuleOption'
import { State } from 'vuex-class'
import { ModuleId } from '@/model/common/OptionListModuleId'
import CommonUtil from '@/utils/CommonUtil'

@Component({
  components: { BoxEditSplitCard, SelectExhibit, ViewExhibit, ResetExhibit, SkuRecordDialog }
})
export default class ReceiptBoxLineEdit extends mixins(swipeMix, BroadCast, UseQueues) {
  @State('optionList') optionList: ModuleOption[]

  $refs: any
  keyWord: string = '' // 搜索关键字
  boxNo: string = '' // 箱码
  uuid: string = '' // 箱码uuid
  billId: string = '' // 单据id 多次收货的id
  receiptBillId: string = '' // 收货单单据id
  box: AppReceiptRecordBoxDTO = new AppReceiptRecordBoxDTO() // 箱数据
  from: 'search' | 'edit' = 'edit' // 来源页面

  hasSelelcExhibit: Slot = new Slot() // 已选中的陈列位置
  viewExhibitInfo: AppReceiptBoxGoodsDTO = new AppReceiptBoxGoodsDTO() // 陈列位置弹窗数据
  exhibitIndex: number = 0 // 陈列位置下标

  // 散称双计量是否需要录入件数（包装数）
  get doubleMeasureGoodsEnterQpcQty() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.doubleMeasureGoodsEnterQpcQty == '1'
    }
    return false
  }

  /**
   * 商品种数
   */
  get count() {
    if (this.box && this.box.boxGoodss) {
      return this.box.boxGoodss.length
    } else {
      return 0
    }
  }

  /**
   * 差异金额
   */
  get diffTotal() {
    let diffTotal: number = 0
    if (this.box && this.box.boxGoodss) {
      this.box.boxGoodss.forEach((sku) => {
        diffTotal = diffTotal.add(Number(sku.total)).minus(Number(sku.receiptTotal))
      })
    }

    return diffTotal
  }

  /**
   * 展示的商品列表
   */
  get showList() {
    if (this.box && this.box.boxGoodss) {
      return this.box.boxGoodss.filter((sku) => {
        return (
          !this.keyWord ||
          sku.goods.name.indexOf(this.keyWord) > -1 ||
          sku.goods.inputCode.indexOf(this.keyWord) > -1 ||
          sku.goods.code.indexOf(this.keyWord) > -1 ||
          (sku.goods.inputCodes && sku.goods.inputCodes.indexOf(this.keyWord) > -1)
        )
      })
    } else {
      return []
    }
  }

  async onLoad(option) {
    if (option) {
      this.boxNo = option.boxNo || ''
      this.uuid = option.uuid || ''
      this.billId = option.id || ''
      this.receiptBillId = option.receiptBillId || ''

      this.from = option.from || 'edit'
      try {
        this.$showLoading()
        this.box = await this.getBox(this.uuid)
        this.$hideLoading()
      } catch (error) {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: error.msg })
      }
    }
    uni.$off('refreshExhibit')
    uni.$on('refreshExhibit', () => {
      this.$refs.exhibit.refresh()
      this.$refs.resetExhibit.refresh()
    })
  }

  onUnload() {
    uni.$off('refreshExhibit')
  }

  /**
   * 关闭所有气泡
   */
  doCloseOutside() {
    if (this.queueObj && this.queueObj.closeOutside) {
      this.queueObj.closeOutside()
    }
  }

  /**
   * 商品变化
   * @param sku 商品
   */
  handleSkuChange(sku: AppReceiptBoxGoodsDTO) {
    const index = this.box.boxGoodss.findIndex((item) => {
      return item.uuid === sku.uuid
    })
    if (index > -1) {
      this.$set(this.box.boxGoodss, index, sku)
    }
    this.box = this.handleSortBox(CommonUtil.deepClone(this.box))
  }

  /**
   * 排序
   * 已收的商品放在后面
   * 未收的商品放在前面
   * @param box 箱数据
   * @returns
   */
  handleSortBox(box: AppReceiptRecordBoxDTO) {
    // 根据confirmed字段的值排序，为true的放在后面
    box.boxGoodss.sort((a, b) => {
      return a.confirmed === b.confirmed ? 0 : a.confirmed ? 1 : -1
    })
    return CommonUtil.deepClone(box)
  }

  // 搜索框清空事件
  doClear() {
    this.keyWord = ''
  }

  // 搜索框搜索事件
  doSearch(keyWord: string = '') {
    this.doScanAfter(keyWord)
  }

  handleEditReason(sku: AppReceiptBoxGoodsDTO) {
    this.$refs.reason.open(sku)
  }

  /**
   * 标记已收
   * @param sku
   */
  handleMarkConfirmed(sku: AppReceiptBoxGoodsDTO) {
    this.handleSkuChange(sku)
  }

  /**
   * 晚点再收
   * 保存页面信息，且周转箱保留在待收页面
   */
  async doBack() {
    try {
      this.$showLoading()
      const boxGoodss: AppReceiptRecordBoxCheckGoodsDTO[] = this.box.boxGoodss.map((item) => {
        return {
          ...item,
          receiptQpcQty: this.doubleMeasureGoodsEnterQpcQty || !item.goods.isDisp ? item.receiptQpcQty : item.qpcQty,
          // 商品数据标识
          gdUuid: item.goods.uuid,
          // 商品输入码
          gdInputCode: item.goods.inputCode,
          confirmed: item.confirmed
        }
      })
      const body: AppReceiptRecordBoxCheckDTO = {
        receiptQty: this.box.receiptQty,
        boxNo: this.boxNo,
        confirmed: this.box.confirmed, // 保持未确认状态，保留在待收页面
        billId: this.billId,
        boxGoodss: boxGoodss
      }
      body.receiptQty = this.box.qty
      await this.modifyBox(body)
      this.$hideLoading()
      this.$Router.back(1)
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '保存失败',
        icon: 'error'
      })
    }
  }

  doConfirm() {
    this.$showModal({
      title: '确定已完成该商品收货？',
      success: async (action) => {
        if (action.confirm) {
          try {
            this.$showLoading()
            const boxGoodss: AppReceiptRecordBoxCheckGoodsDTO[] = this.box.boxGoodss.map((item) => {
              return {
                ...item,
                receiptQpcQty: this.doubleMeasureGoodsEnterQpcQty || !item.goods.isDisp ? item.receiptQpcQty : item.qpcQty,
                // 商品数据标识
                gdUuid: item.goods.uuid,
                // 商品输入码
                gdInputCode: item.goods.inputCode,
                confirmed: item.confirmed
              }
            })
            const body: AppReceiptRecordBoxCheckDTO = {
              receiptQty: this.box.receiptQty,
              boxNo: this.boxNo,
              confirmed: true,
              billId: this.billId,
              boxGoodss: boxGoodss
            }
            body.receiptQty = this.box.qty
            const summary = await this.modifyBox(body)
            this.$hideLoading()
            this.$showToast({ title: '操作成功', duration: 800, icon: 'success' })
            if (this.from === 'edit') {
              uni.$emit('boxQuery')
            }
            if (this.from === 'search') {
              uni.$emit('searchQuery')
            }
            // 更新待收数量
            uni.$emit('update-un-confirm', { unConfirmPack: summary.unConfirmPack, confirmedPack: summary.confirmedPack })
            const timer = setTimeout(() => {
              this.$Router.back(1)
              clearTimeout(timer)
            }, 800)
          } catch (error) {
            this.$hideLoading()
            this.$showToast({
              title: error.msg || '操作失败',
              icon: 'error'
            })
          }
        }
      }
    })
  }

  /**
   * 编辑箱码
   * @param body
   * @returns
   */
  modifyBox(body: AppReceiptRecordBoxCheckDTO) {
    return new Promise<AppReceiptRecordDTO>((resolve, reject) => {
      AppReceiptRecordApi.boxCheckLine(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 获取指定的箱
   * @param body
   * @returns
   */
  getBox(uuid: string) {
    const body = new QueryRequest()
    body.page = 0
    body.pageSize = 1
    body.conditions = [{ operation: 'uuid:=', parameters: [uuid] }]
    return new Promise<AppReceiptRecordBoxDTO>((resolve, reject) => {
      AppReceiptRecordApi.queryBox(body)
        .then((resp) => {
          if (resp.data[0]) {
            resolve(resp.data[0])
          } else {
            reject({ msg: `未找到指定箱${uuid}` })
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  //PDA扫码回调事件
  async doScanAfter(scanWord) {
    this.keyWord = scanWord
    this.$nextTick(() => {
      uni.pageScrollTo({
        scrollTop: 0
      })
    })
  }

  /**
   * 绑定陈列位置
   */
  bindExhibit(info: AppReceiptBoxGoodsDTO, index: number) {
    this.exhibitIndex = index
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.open(this.hasSelelcExhibit, slotGoods, 'bindExhibit')
  }

  /**
   * 陈列位置调整
   */
  resetExhibit(info: AppReceiptBoxGoodsDTO, index: number) {
    this.hasSelelcExhibit = new Slot()
    this.exhibitIndex = index
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.$refs.resetExhibit.open(slotGoods)
  }

  /**
   * 获取该商品信息
   */
  getRequestBody(info: AppReceiptBoxGoodsDTO) {
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 目标货位
    slotGoods.targetSlotCode = info.exhibitValue
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.success(this.hasSelelcExhibit, slotGoods)
  }

  /**
   * 输入框的值组装
   */
  inputExhibit(code) {
    this.hasSelelcExhibit.code = code
    this.hasSelelcExhibit.name = code
    this.hasSelelcExhibit.uuid = ''
  }

  /**
   * 陈列位置绑定成功
   */
  async confirmExhibit() {
    try {
      this.$showLoading()
      const box = await this.getBox(this.uuid)
      this.box = this.handleSortBox(CommonUtil.deepClone(box))
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: error.msg })
    }
    this.hasSelelcExhibit = new Slot()
  }

  /**
   * 添加陈列位置
   */
  addExhibit() {
    this.$Router.push({
      name: 'addExhibitLocation'
    })
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info: AppReceiptBoxGoodsDTO) {
    this.viewExhibitInfo = { ...info }
    this.$refs.viewExhibit.open()
  }

  /**
   * 查看商品行收货记录
   * @param sku
   */
  async handleViewDetail(sku: AppReceiptRecordBoxGoodsDTO) {
    try {
      this.$showLoading()
      const recordList = await this.listMultipleRcvRecord({
        billId: this.receiptBillId,
        gdInputCode: sku.goods.inputCode
      })
      this.$hideLoading()

      if (recordList.length === 0) {
        this.$showToast({
          title: '暂无收货记录'
        })
      } else {
        this.$refs.recordDialog.open(recordList)
      }
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败'
      })
    }
  }

  /**
   * 查询商品行多次收货记录
   * @param body
   * @returns
   */
  listMultipleRcvRecord(body: AppReceiptMultipleRcvRecordQueryer) {
    return new Promise<AppReceiptMultipleRcvGdRecordDTO[]>((resolve, reject) => {
      ReceiptApi.listMultipleRcvRecord(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
}
