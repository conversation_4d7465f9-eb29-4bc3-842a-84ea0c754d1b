<template>
  <view class="gd-query-panel safe-area-inset-bottom" @click="handleClickOutside">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <view class="header">
      <hd-simple-search-bar
        :showCancel="false"
        v-model="keyWord"
        placeholder="搜索商品"
        @clear="doSearchClear"
        @confirm="doSearch"
      ></hd-simple-search-bar>
      <view class="scan" @click.stop="doScan">
        <image class="scan-img" :src="'/static/icon/ic_saoma_green.png' | oss"></image>
        <text class="scan-txt">扫一扫</text>
      </view>
    </view>
    <view class="main">
      <mescroll-body ref="mescrollRef" @init="mescrollInit" @up="doLoad" @down="doRefresh" :down="downOption" :up="upOption" :safearea="false">
        <block v-if="skuList.length > 0">
          <sku-card
            v-for="sku in skuList"
            :key="sku.uuid"
            :sku="sku"
            @detail="handleRedirctDetail"
            @showDesc="handleShowDesc"
            @viewExhibit="viewExhibit"
            @resetExhibit="resetExhibit"
            @operateInvQty="operateInvQty"
          ></sku-card>
        </block>
        <hd-empty
          v-if="skuList.length <= 0 && !isLoading"
          :img="'/static/img/img_empty_list.png' | oss"
          marginTop="200rpx"
          subTitle="扫码或输入，可快速查看商品信息~"
        ></hd-empty>
      </mescroll-body>
    </view>

    <!-- 查看多个陈列位置 -->
    <uni-popup ref="viewExhibit" type="bottom">
      <view-exhibit :name="viewExhibitInfo.friendlyStr" :displayLocation="viewExhibitInfo.displayLocation" @doClose="closeViewExhibit"></view-exhibit>
    </uni-popup>

    <!-- 选择陈列位置 -->
    <select-exhibit ref="exhibit" @deleteExhibit="deleteExhibit" @addExhibit="addExhibit" @success="confirmExhibit"></select-exhibit>

    <!-- 陈列位置调整 -->
    <reset-exhibit
      ref="resetExhibit"
      :showOperate="true"
      :isGoods="true"
      @deleteExhibit="deleteExhibit"
      @addExhibit="addExhibit"
      @success="confirmExhibit"
    ></reset-exhibit>

    <!-- 调整库存 -->
    <operate-inv-qty-dialog ref="operateInvQty" @success="confirmExhibit"></operate-inv-qty-dialog>
  </view>
</template>
<script lang="ts" src="./GdQueryPanelOnlySearch.ts"></script>
<style lang="scss" scoped>
.gd-query-panel {
  position: relative;
  box-sizing: border-box;
  width: 750rpx;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-top: 120rpx;
  .header {
    z-index: 1001;
    width: 750rpx;
    flex: 0 0 auto;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    background: $color-primary;
    ::v-deep .search-bar {
      flex: 1;
      background: $color-primary;
      margin-bottom: 0;
    }
    .scan {
      flex: 0 0 auto;
      display: flex;
      align-items: center;
      width: 172rpx;
      height: 72rpx;
      border-radius: 46rpx;
      border: 1rpx solid #1c64fd;
      box-sizing: border-box;
      padding: 0 20rpx;
      background: #ffffff;
      margin-right: 24rpx;
      &-txt {
        font-size: 28rpx;
        color: #1c64fd;
      }
      &-img {
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
      }
    }
  }
  .main {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    width: 750rpx;
    ::v-deep .mescroll-body {
      min-height: calc(100vh - 120rpx) !important;
      min-height: calc(100vh - 120rpx - constant(safe-area-inset-bottom)) !important;
      min-height: calc(100vh - 120rpx - env(safe-area-inset-bottom)) !important;
    }
  }

  .uni-popup {
    position: fixed;
    bottom: env(safe-area-inset-bottom);
    left: 0;
    width: 100%;
    z-index: 9999;
    max-width: 100%;
    max-height: 100%;
    overflow: auto;
  }
}
</style>
