# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [2.56.1](https://gitlab.hd123.com/vue/soa/compare/v2.56.0...v2.56.1) (2025-05-20)


### Features

* ✨ 领用模块UI优化 ([49c5f7d](https://gitlab.hd123.com/vue/soa/commit/49c5f7d111ec47bf2c6145674853eb4e6ffd2b2d))
* ✨ 讯华PDA适配 ([bfda8dc](https://gitlab.hd123.com/vue/soa/commit/bfda8dc186d4593892d170299dcaf4809f01be67))
* ✨ PDA适配讯华优化 ([10be54d](https://gitlab.hd123.com/vue/soa/commit/10be54de1cdf2e36252250923d8390a4aaecefdb))
* ✨ SOP-11693 多次按箱收货待收周转箱待会再收功能改为保存 ([0f55ed0](https://gitlab.hd123.com/vue/soa/commit/0f55ed0ccbb744379c3bcef5e609c6db95e93d64))
* ✨ SOP-11698 小程序.门店助手.店务商品查询 - 陈列调整功能变更 ([007d872](https://gitlab.hd123.com/vue/soa/commit/007d872124e0499e52b5b9676ebb3bbb0ad61166))
* ✨ SOP-11698 小程序.门店助手.店务商品查询 - 陈列调整功能变更 ([e1471c2](https://gitlab.hd123.com/vue/soa/commit/e1471c26beec11cd12e319a10ea09ee19e0db3b2))
* ✨ SOP-11717 门店盘点任务-按分类添加按配置调用接口 ([0e100b5](https://gitlab.hd123.com/vue/soa/commit/0e100b5fd49c0c5acbb89a0aa0bce9baa328f822))


### Bug Fixes

* 🐛 去除手误增加的内容 ([d1e2ec3](https://gitlab.hd123.com/vue/soa/commit/d1e2ec3d2e8647a421c373009e6429506dafacea))
* 🐛 SOP-11539 小程序.门店助手.退货申请支持双计量 - 样式优化 ([44f7b0d](https://gitlab.hd123.com/vue/soa/commit/44f7b0dc8aef35aad9bedd393aa9b8bd4ed8681d))
* 🐛 SOP-11711 特殊品库存查询模块初始化加载问题修复 ([0c00bb4](https://gitlab.hd123.com/vue/soa/commit/0c00bb4a1f6a24d6c00990300f50e909d3aa96aa))

## [2.56.0](https://gitlab.hd123.com/vue/soa/compare/v2.55.4...v2.56.0) (2025-05-09)


### Features

* ✨ 冲突合并 ([263a0f0](https://gitlab.hd123.com/vue/soa/commit/263a0f09ddae69c77f2b92dfa66b9dafc999163d))
* ✨ 订货搜索页新增提交接口编辑状态修改 ([bd088bb](https://gitlab.hd123.com/vue/soa/commit/bd088bb4c65962440638fc9622b35d381e1500d4))
* ✨ feat: ✨ SOP-11641 门店盘点扫码限制保存接口优化 ([2d384de](https://gitlab.hd123.com/vue/soa/commit/2d384ded900605aec7ee725f7a6bd491d6f6d090))
* ✨ feat: ✨ SOP-11680 包装码溯源码添加 ([de03cf8](https://gitlab.hd123.com/vue/soa/commit/de03cf82483b88e169d7fd4bd499651fb543ed9e))
* ✨ HB-104949 报损检查接口增加invCatId参数 ([47ad2b2](https://gitlab.hd123.com/vue/soa/commit/47ad2b2067336f6f3d01920bcd504b034c976d6c))
* ✨ PDA适配修改调整 ([9097b2b](https://gitlab.hd123.com/vue/soa/commit/9097b2b17a32879b2179b8dddb224a2817f99a37))
* ✨ SOP-10495 消息通知增加跳转促销申请模块 ([08e1685](https://gitlab.hd123.com/vue/soa/commit/08e168505835f329c0fcd4c13f27f2b6f41d35d2))
* ✨ SOP-10495 新增促销申请模块 ([5b5ad8b](https://gitlab.hd123.com/vue/soa/commit/5b5ad8b22e1a6dd5b42c7c277c49d4c565bd42f5))
* ✨ SOP-10495 新增促销申请模块 ([1ffaa88](https://gitlab.hd123.com/vue/soa/commit/1ffaa8844cfb819bceadf1385a0221137a117d33))
* ✨ SOP-11428 小程序.门店助手.多次收货结束收货时支持上传差异图片 ([e92a34b](https://gitlab.hd123.com/vue/soa/commit/e92a34be82cb6b3da2b48682d5a048e35dd9171a))
* ✨ SOP-11437 小程序.门店助手.标价签打印支持便捷筛选功能 ([23b151d](https://gitlab.hd123.com/vue/soa/commit/23b151d272c093faf75cdf89d2ea942574ea25ec))
* ✨ SOP-11437 小程序.门店助手.标价签打印支持便捷筛选功能 ([e8d7ad5](https://gitlab.hd123.com/vue/soa/commit/e8d7ad56648723d79b12cf40a7b17034089a4ddd))
* ✨ SOP-11437 小程序.门店助手.标价签打印支持便捷筛选功能 ([975d7d8](https://gitlab.hd123.com/vue/soa/commit/975d7d8919aed852a4ea7d65e070b4bff8e23567))
* ✨ SOP-11437 小程序.门店助手.标价签打印支持便捷筛选功能 ([f18edb5](https://gitlab.hd123.com/vue/soa/commit/f18edb54d6b97125aa62808f9abea97298d6c217))
* ✨ SOP-11437 小程序.门店助手.标价签打印支持便捷筛选功能 ([2e420e6](https://gitlab.hd123.com/vue/soa/commit/2e420e650078ce8af9ae63fd4600281713cfcba8))
* ✨ SOP-11437 小程序.门店助手.标价签打印支持便捷筛选功能 ([187cd14](https://gitlab.hd123.com/vue/soa/commit/187cd148446f7db853918c52b4e618cfd24d71f4))
* ✨ SOP-11437 小程序.门店助手.标价签打印支持便捷筛选功能 ([d542b30](https://gitlab.hd123.com/vue/soa/commit/d542b30cddff54b8929372da3dc0cb0b417f1ecf))
* ✨ SOP-11437 小程序.门店助手.标价签打印支持便捷筛选功能 ([794f6d0](https://gitlab.hd123.com/vue/soa/commit/794f6d0d2213cca7b55e0bf1adc42dfdb9ded321))
* ✨ SOP-11437 小程序.门店助手.标价签打印支持便捷筛选功能 ([48ee474](https://gitlab.hd123.com/vue/soa/commit/48ee474fd24de77ba6dd99277b18acac657afc69))
* ✨ SOP-11439 调价申请支持pda扫码后搜索单据 ([3a9b001](https://gitlab.hd123.com/vue/soa/commit/3a9b0013afabb8413b289825a7e60c6d0b6f80a8))
* ✨ SOP-11439 加工模块新增加工支持PDA扫描 ([c749d61](https://gitlab.hd123.com/vue/soa/commit/c749d61be5b99ee8f54372798ed8d5b0c217055b))
* ✨ SOP-11439 质量反馈模块的PDA扫码适配 ([3caae7e](https://gitlab.hd123.com/vue/soa/commit/3caae7ecd6c01a93ac82ee7e0019171dd24a5bf8))
* ✨ SOP-11439 质量反馈模块PDA适配调整 ([eb3d26a](https://gitlab.hd123.com/vue/soa/commit/eb3d26a27939b470b5d73ffe84d287eed97a7e9a))
* ✨ SOP-11439 周边门店库存支持pda扫码后查询商品 ([e0653ae](https://gitlab.hd123.com/vue/soa/commit/e0653ae5f6d579158b1429d88ef3886eed17fc5f))
* ✨ SOP-11489 按箱收货差异核对页面按商品维度合并 ([1d3e056](https://gitlab.hd123.com/vue/soa/commit/1d3e05600f307e3f07f5001c6fcc7ca4fd1a0a03))
* ✨ SOP-11539 小程序.门店助手.退货申请支持双计量 ([ce46c56](https://gitlab.hd123.com/vue/soa/commit/ce46c562f13b2621cd88555ed8f30589d3776a84))
* ✨ SOP-11541 小程序.门店助手.退公司和退仓展示拒退商品信息 ([dc4e7f5](https://gitlab.hd123.com/vue/soa/commit/dc4e7f52d4755d0654a47f46446afd526ab4aeee))
* ✨ SOP-11542 小程序.门店助手.店务商品查询支持将商品添加到价签申请和标签打印列表 ([b4cca6e](https://gitlab.hd123.com/vue/soa/commit/b4cca6e3d48e9041366578a899c25c725d7b039b))
* ✨ SOP-11543 小程序.门店助手.加工模块支持配置原料产品数量可以不联动 ([c3942ff](https://gitlab.hd123.com/vue/soa/commit/c3942ff6e184f91e6925cbae67c2c8146ae7d6e1))
* ✨ SOP-11544 合并退货需求新增一个特殊品登记/移出模块 ([c6e53c8](https://gitlab.hd123.com/vue/soa/commit/c6e53c898e449d25e81bbf1c1af2c67c583438cb))
* ✨ SOP-11544 合并退货需求新增一个特殊品登记/移出模块 ([8a4a492](https://gitlab.hd123.com/vue/soa/commit/8a4a4924bfabf6913f48b5c2d4142f64455e7ced))
* ✨ SOP-11544 合并退货需求新增一个特殊品管理模块 ([ce925fa](https://gitlab.hd123.com/vue/soa/commit/ce925fae81c8702266ff8fc455df68a783458ea8))
* ✨ SOP-11545 合并退货需求新增一个特殊品库存查询 ([ede371c](https://gitlab.hd123.com/vue/soa/commit/ede371cd9722803712f1e05da5339786792bcf88))
* ✨ SOP-11545 合并退货需求新增一个特殊品库存查询 ([d7364db](https://gitlab.hd123.com/vue/soa/commit/d7364db3aad8b61795ec8556889fd85b7807ab01))
* ✨ SOP-11548 店务商品查询增加全部商品页签并展示商品数 ([72227a0](https://gitlab.hd123.com/vue/soa/commit/72227a084a5a14b7a828c7287950780d845a6c8f))
* ✨ SOP-11550 计划盘点初盘支持填写盘点合计数量值为负数 ([d22eb69](https://gitlab.hd123.com/vue/soa/commit/d22eb69c1c1072434835f37bcc012f5cd7deb32b))
* ✨ SOP-11550 计划盘点初盘支持填写盘点合计数量值为负数 ([bb60a03](https://gitlab.hd123.com/vue/soa/commit/bb60a031d3fad6966ae6b5dda1e8165d48b94b5c))
* ✨ SOP-11551 标准订货新增几个参考指标 ([a04ea5a](https://gitlab.hd123.com/vue/soa/commit/a04ea5a35190c6f6cee496c62c679452930925e7))
* ✨ SOP-11553 小程序.门店助手.收货模块交互逻辑优化 ([26c25b9](https://gitlab.hd123.com/vue/soa/commit/26c25b9bd1414211ea8eed6587e893f073a6f6ae))
* ✨ SOP-11562 小程序.门店助手.店务商品查询支持配置页面结构 ([83b3472](https://gitlab.hd123.com/vue/soa/commit/83b3472935561888f552c42d799525c54aca20f0))
* ✨ SOP-11570 店务商品查询适配讯华PDA ([294d34c](https://gitlab.hd123.com/vue/soa/commit/294d34cd4a31aeabc62e489c0ce7156c827ff818))
* ✨ SOP-11570 门店助手APP适配讯华PDA优化 ([1c3768d](https://gitlab.hd123.com/vue/soa/commit/1c3768d564584a7d56fc228aea74a67aa4a04393))
* ✨ SOP-11570 门店助手APP适配讯华PDA优化 ([af9a76d](https://gitlab.hd123.com/vue/soa/commit/af9a76d606ed15672f90c9f0eff585b7f1c1b949))
* ✨ SOP-11570 门店助手APP适配讯华PDA优化测试 ([878fb6b](https://gitlab.hd123.com/vue/soa/commit/878fb6b1f545a31d67650251873681eed342498d))
* ✨ SOP-11570 门店助手APP适配讯华PDA优化测试 ([2f1b363](https://gitlab.hd123.com/vue/soa/commit/2f1b3632bfd688acb2f850eca0a3e3bc008882cb))
* ✨ SOP-11570 门店助手APP适配讯华PDA优化首页 ([d2a890a](https://gitlab.hd123.com/vue/soa/commit/d2a890a3e70ee303ff90faad8ac2ad0712699ae6))
* ✨ SOP-11570 app适配测试 ([4b2bd7e](https://gitlab.hd123.com/vue/soa/commit/4b2bd7e03c8f198e72b7a8e67a89d4e86d54eb5e))
* ✨ SOP-11570 APP适配讯华PDA优化测试 ([39bde06](https://gitlab.hd123.com/vue/soa/commit/39bde06cc5a832b154b7eadc45f01792463f96ab))
* ✨ SOP-11571 新建任务【任务描述】字数扩展 ([717bfcf](https://gitlab.hd123.com/vue/soa/commit/717bfcf7f23e58638e4347d161ae615c53d42bef))
* ✨ SOP-11571 新建任务【任务描述】字数扩展 ([1232df3](https://gitlab.hd123.com/vue/soa/commit/1232df37e607eeccfd3e2c0627d35017f3d0a914))
* ✨ SOP-11572 店务商品查询指定设备操作优化 ([5a7cd0f](https://gitlab.hd123.com/vue/soa/commit/5a7cd0f8a57bd71c636bac880115cbf703065164))
* ✨ SOP-11573 门店盘点扫码限制和盘点前检查提醒需求 ([52277e8](https://gitlab.hd123.com/vue/soa/commit/52277e81efa136e0084e68489b837e841c1a05ff))
* ✨ SOP-11573 盘点扫码保存入参修改 ([322ff30](https://gitlab.hd123.com/vue/soa/commit/322ff305dd67d0661e1765fe8f038e96a6ae7ced))
* ✨ SOP-11573 盘点扫码gdInputCode入参判断 ([e9ab709](https://gitlab.hd123.com/vue/soa/commit/e9ab7096c5cdaa629cc53c41d39561e4d7a0fd80))
* ✨ SOP-11573 批次号商品行删除不需要传traceCode ([e427d0a](https://gitlab.hd123.com/vue/soa/commit/e427d0a50ef4a3bc48d320648484b8df9e3c9888))
* ✨ SOP-11574 标签打印支持自定义价格暂时隐藏 ([ca0d226](https://gitlab.hd123.com/vue/soa/commit/ca0d22649e12759562a38a8c5174370358708019))
* ✨ SOP-11580 账户流水单据跳转时单号参数调整为bizNum ([f638d6e](https://gitlab.hd123.com/vue/soa/commit/f638d6eab1527ff9c4113db9f7654ea32aa914ba))
* ✨ SOP-11580 账户流水支持订单跳转需求 ([04271a8](https://gitlab.hd123.com/vue/soa/commit/04271a836705e270c298c3be72bcf10502f8538d))
* ✨ SOP-11596 诚信志远叫货提交时检查当日提交次数 ([30bbb8d](https://gitlab.hd123.com/vue/soa/commit/30bbb8dadc9064237cec9c9341899a2db5fe7d1d))
* ✨ SOP-11601 店务商品查询零购陈列位置无法删除问题解决 ([fbe67ad](https://gitlab.hd123.com/vue/soa/commit/fbe67ad457a2f90c1d0ef769903a68e41e5c1b2b))
* ✨ SOP-11613 订货模式展示错误 ([eceee3b](https://gitlab.hd123.com/vue/soa/commit/eceee3b9a9fc10f87281f44e59b3ba0587dbce26))
* ✨ SOP-11620 门店盘点任务-按分类添加优化 ([38534fb](https://gitlab.hd123.com/vue/soa/commit/38534fb7e51d1845fe8f31d5b8e054e47116fd77))
* ✨ SOP-11625 新增促销申请模块.筛选条件缺少“促销方式” ([35b3342](https://gitlab.hd123.com/vue/soa/commit/35b334260bd03f52d7a003089410e16686bc8192))
* ✨ SOP-11627 复盘不应支持填写盘点合计数量值为负数 ([0de5a9f](https://gitlab.hd123.com/vue/soa/commit/0de5a9f58ad7a0da31dc7667ec9ffcf65a6be2db))
* ✨ SOP-11640 特殊品管理模块详情页移除操作类型 ([de347d7](https://gitlab.hd123.com/vue/soa/commit/de347d7231118a859c2abb979d5b57160c52bc96))
* ✨ SOP-11641 复盘模块保存增加planBillId字段 ([6d14994](https://gitlab.hd123.com/vue/soa/commit/6d149943930dad2f5c9eb5d406a7df9decba3f89))
* ✨ SOP-11641 门店盘点扫码搜索增加planBillId ([adde748](https://gitlab.hd123.com/vue/soa/commit/adde748703a6a641461b59db570d7e307e0c266f))
* ✨ SOP-11641 门店盘点扫码限制 -- 问题汇总 ([97f9400](https://gitlab.hd123.com/vue/soa/commit/97f9400f45d2194509b862ccffae9b68ce3d25c6))
* ✨ SOP-11641 门店盘点扫码优化 ([a84f3e0](https://gitlab.hd123.com/vue/soa/commit/a84f3e0d393b8b1bc6db0538b91bc4e26b9e7434))
* ✨ SOP-11641 盘点模块复盘增加溯源码分析 ([7ed654d](https://gitlab.hd123.com/vue/soa/commit/7ed654d1cb354ade3b2150cd421534e948872bae))
* ✨ SOP-11641 盘点模块复盘增加溯源码分析 ([852b3dd](https://gitlab.hd123.com/vue/soa/commit/852b3dd5baf502f0d5c050eef6c603d81eaebf60))
* ✨ SOP-11642 特殊品登记考虑库存分类库存上限 ([585d3f5](https://gitlab.hd123.com/vue/soa/commit/585d3f58ef1e67423373b6a913b29c604dc68e91))
* ✨ SOP-11644 小程序.门店助手.店务商品查询支持将商品添加到价签申请和标签打印列表.不存在商品时给出提示 ([a3b9b86](https://gitlab.hd123.com/vue/soa/commit/a3b9b863ef998159a49eda88229a7a3511566061))
* ✨ SOP-11645 自主盘点盈亏汇总信息区增加实盘金额 ([86d9603](https://gitlab.hd123.com/vue/soa/commit/86d96035598a02e484a2d3198afa2db03162d4b0))
* ✨ SOP-11649 多次收货支持标记周转箱商品为已收 ([9aa63fd](https://gitlab.hd123.com/vue/soa/commit/9aa63fdd29b896f94d59915d667c924ba688fe94))
* ✨ SOP-11651 复盘添加键盘录入批次号类型 ([c30dc05](https://gitlab.hd123.com/vue/soa/commit/c30dc050c077c0c4d13843e66ec93b958d917f59))
* ✨ SOP-11655 交易流水显示实收金额 ([83f150b](https://gitlab.hd123.com/vue/soa/commit/83f150b49750a8abe2ef49191626743fb31ad7bf))
* ✨ SOP-11680 复盘溯源码传参修改 ([06e304a](https://gitlab.hd123.com/vue/soa/commit/06e304ac8ed3f43c7252d1e68755eb0812131553))
* ✨ SOP-11680 门店盘点扫码恢复提示修改 ([271f3c4](https://gitlab.hd123.com/vue/soa/commit/271f3c42a8b574be3625bf051c87f1a4eb7c7cfe))
* ✨ SOP-11680 门店盘点自动保存刷新列表 ([ac876ca](https://gitlab.hd123.com/vue/soa/commit/ac876ca4acadec8d5c0526a188828f94a4b87e0b))
* ✨ SOP-11680 选抽盘商品的时候，不管是什么商品都不要传planBillId ([38ca4fb](https://gitlab.hd123.com/vue/soa/commit/38ca4fb133c44f090ce05b23250626641963e98d))
* ✨ SOP-11686 ([3e6f2d1](https://gitlab.hd123.com/vue/soa/commit/3e6f2d15daf2eb7bbf2e5f67e83d2434499dd8bd))


### Bug Fixes

* 🐛 修复特殊品管理分类数量不更新的问题 ([6626db4](https://gitlab.hd123.com/vue/soa/commit/6626db4066571aff4ae9526386e9c9c21757d8ff))
* 🐛 SOP-11428 小程序.门店助手.多次收货结束收货时支持上传差异图片-多次收货不支持清空差异原因 ([7352012](https://gitlab.hd123.com/vue/soa/commit/73520123871b3c8fab323c0d6dfd4bfe891e61ee))
* 🐛 SOP-11539 小程序.门店助手.退货申请支持双计量 ([405a36a](https://gitlab.hd123.com/vue/soa/commit/405a36a2290416b978dfdb504c4ae918159bce06))
* 🐛 SOP-11542 价签维度-详情页 价签和商品数量展示错误问题修复 ([df33d93](https://gitlab.hd123.com/vue/soa/commit/df33d9386f1e89fb7194ffd28c73c567618b813a))
* 🐛 SOP-11544 修复特殊品管理在app端详情页面展示异常的问题 ([ff6271e](https://gitlab.hd123.com/vue/soa/commit/ff6271ec8f93d11271f8864f04fa70a9fe813cea))
* 🐛 SOP-11553 小程序.门店助手.收货模块交互逻辑优化 ([a2f5201](https://gitlab.hd123.com/vue/soa/commit/a2f520139bb59396f37aaa59abf0fc22eba6d5d1))
* 🐛 SOP-11553 小程序.门店助手.收货模块交互逻辑优化 ([9641bd2](https://gitlab.hd123.com/vue/soa/commit/9641bd2c0943e2db315fc8122de7ae625da79176))
* 🐛 SOP-11553 小程序.门店助手.收货模块交互逻辑优化 ([f95cf7f](https://gitlab.hd123.com/vue/soa/commit/f95cf7fae871dc51e44e3bd0fbe34fee368c6fa6))
* 🐛 SOP-11637 特殊品管理模块列表添加类型的筛选条件 ([751ae6e](https://gitlab.hd123.com/vue/soa/commit/751ae6e8a7d9380cec954ad93198c03d71d42539))
* 🐛 SOP-11637 修复合并退货需求新增一个特殊品管理模块.列表页问题 ([4d1eb22](https://gitlab.hd123.com/vue/soa/commit/4d1eb22056c7efe62c07c224e967469a95b51256))
* 🐛 SOP-11639 修复特殊品管理模块编辑页的问题 ([6489b43](https://gitlab.hd123.com/vue/soa/commit/6489b4395b5e0e3d21a48744361fa382a00d8f62))
* 🐛 SOP-11640 特殊品管理模块详情页问题修复 ([99ba552](https://gitlab.hd123.com/vue/soa/commit/99ba5529889dc82e65636ec12adc8fc763754507))
* 🐛 SOP-11640 特殊品管理模块详情页问题修复 ([44bff2a](https://gitlab.hd123.com/vue/soa/commit/44bff2a1c32742ad0b5a9ca4aa4a2bd3075432fb))
* 🐛 SOP-11642 解决特殊品库存查询页面iOS安全区实效的问题 ([18ba3fa](https://gitlab.hd123.com/vue/soa/commit/18ba3fafc3f97e11781f3c14cc96cf1c3838a830))
* 🐛 SOP-11642 特殊品库存查询模块问题修复8.1 ([4a44a60](https://gitlab.hd123.com/vue/soa/commit/4a44a60012629565fe8ff7e701c671e1b13caf51))
* 🐛 SOP-11647 小程序.门店助手.多次收货结束收货时支持上传差异图片.界面问题 ([feb8691](https://gitlab.hd123.com/vue/soa/commit/feb8691cc3d9be9e669cb68da136bd4b447e8555))

### [2.54.12](https://gitlab.hd123.com/vue/soa/compare/v2.54.11...v2.54.12) (2025-03-13)


### Bug Fixes

* 🐛 SOP-11471 直送收货需要支持自动保存 - 增加商品默认进货价 ([7a643e6](https://gitlab.hd123.com/vue/soa/commit/7a643e69d44fb26840bd0175b6f7c25c5b67758e))

### [2.55.4](https://gitlab.hd123.com/vue/soa/compare/v2.55.3...v2.55.4) (2025-05-06)


### Features

* ✨ SOP-11654 门店助手订货，数量维护之后的保存问题 ([463412a](https://gitlab.hd123.com/vue/soa/commit/463412ab02b3c1f86b0eac0ddb2667d2515a6310))

### [2.55.3](https://gitlab.hd123.com/vue/soa/compare/v2.55.2...v2.55.3) (2025-04-17)


### Features

* ✨ SOP-11607 直送收货进货价/进货金额在价格精确度优化 ([767382b](https://gitlab.hd123.com/vue/soa/commit/767382bb9da83a9159808255115826911bf334df))
* ✨ SOP-11607 直送收货进货价/进货金额在价格精确度优化 ([62e500b](https://gitlab.hd123.com/vue/soa/commit/62e500bc40b5952605a14a6b6a55e04efba90758))
* ✨ SOP-11607 直送收货进货价/进货金额在价格精确度优化 ([5691b00](https://gitlab.hd123.com/vue/soa/commit/5691b0003adfdad2d857bd8d0fa4f8f9420e4056))
* ✨ SOP-11607 直送收货进货价/进货金额在app端无法自动保存问题解决 ([f1cd8dd](https://gitlab.hd123.com/vue/soa/commit/f1cd8dda863d095de70f583fca1c4f7116e7158a))

### [2.55.2](https://gitlab.hd123.com/vue/soa/compare/v2.55.1...v2.55.2) (2025-04-15)


### Features

* ✨ SOP-11518 标准订货爆品相关问题修复 ([f5feb0c](https://gitlab.hd123.com/vue/soa/commit/f5feb0c6db60953524d1b7729e5df3ea00775d06))
* ✨ SOP-11595 标准订货搜索页面保存失败问题解决 ([9c56d17](https://gitlab.hd123.com/vue/soa/commit/9c56d17c60c1904921cbeebfe5694adedb0aec8e))
* ✨ SOP-11595 标准订货搜索页面保存失败问题解决 ([ee503ff](https://gitlab.hd123.com/vue/soa/commit/ee503ff606b6161eb12c398dc355307a1c87d358))
* ✨ SOP-11601 店务商品查询零购陈列位置无法删除问题解决 ([8338cd7](https://gitlab.hd123.com/vue/soa/commit/8338cd7972806752f74f9b9985912b0338139a95))

### [2.55.1](https://gitlab.hd123.com/vue/soa/compare/v2.55.0...v2.55.1) (2025-04-07)

## [2.55.0](https://gitlab.hd123.com/vue/soa/compare/v2.54.19...v2.55.0) (2025-04-07)


### Features

* ✨ 冲突合并 ([666df27](https://gitlab.hd123.com/vue/soa/commit/666df2770fe189f592b4d0f4fc53fac27ddd8310))
* ✨ develop冲突合并 ([6c42df0](https://gitlab.hd123.com/vue/soa/commit/6c42df0c55be8d3057e03825034c95e2f490b3b7))
* ✨ SOP-10165 盘点计划支持维护时间控制门店盘点开始或截止盘点 ([7dbb120](https://gitlab.hd123.com/vue/soa/commit/7dbb1206840d2f908946692afcc8469c430c5294))
* ✨ SOP-11339 按箱收货支持数量多收 ([129cbfa](https://gitlab.hd123.com/vue/soa/commit/129cbfadaf19df3708a471c0f54f95041076fe19))
* ✨ SOP-11426 店务商品查询增加更多查询筛选条件 ([d7c0989](https://gitlab.hd123.com/vue/soa/commit/d7c0989138869e320bed9a7a02fb263b1a77a2af))
* ✨ SOP-11426 店务商品查询增加更多查询筛选条件 ([8df7755](https://gitlab.hd123.com/vue/soa/commit/8df77558c9c460257df75624c9f1085ba297f5ea))
* ✨ SOP-11426 店务商品查询增加更多查询筛选条件 ([71521e5](https://gitlab.hd123.com/vue/soa/commit/71521e532692b51ae36342930d07dfdd4daca374))
* ✨ SOP-11427 店务商品查询新增商品品类字段展示以及多个模块新增多个字段、操作的控制 ([7da2701](https://gitlab.hd123.com/vue/soa/commit/7da2701239a6796e87c4b5b8bda445b89edd64ff))
* ✨ SOP-11427 店务商品查询新增商品品类字段展示以及多个模块新增多个字段、操作的控制 ([e6fe8c3](https://gitlab.hd123.com/vue/soa/commit/e6fe8c31eda20b0b3ec7f227a7367262fd83aeed))
* ✨ SOP-11427 多个模块新增多个字段、操作的控制 ([8891bb4](https://gitlab.hd123.com/vue/soa/commit/8891bb474baa5f2481fd75bfa9dbb4565c4b281e))
* ✨ SOP-11427 全局陈列位置字段加上字段权限配置 ([c4f105e](https://gitlab.hd123.com/vue/soa/commit/c4f105ed8d81a79064d9989d4ec02fa164670387))
* ✨ SOP-11429 店务商品查询陈列位置调整界面调整 ([114f86d](https://gitlab.hd123.com/vue/soa/commit/114f86d5552b17c19865c71a185147e6f3243886))
* ✨ SOP-11430 商品淘汰单支持作废 ([31d437e](https://gitlab.hd123.com/vue/soa/commit/31d437ec6c624be08c8da4c3728e42e5c3d640e4))
* ✨ SOP-11430 商品淘汰单支持作废 ([f1700c7](https://gitlab.hd123.com/vue/soa/commit/f1700c778e80c76b7da334b495dc22aadd66d662))
* ✨ SOP-11430 商品淘汰单支持作废 ([4d020d4](https://gitlab.hd123.com/vue/soa/commit/4d020d4f36ac07d35622158313a85f57017789f1))
* ✨ SOP-11430 商品淘汰单支持作废 - 详情页的“已淘汰”/“未淘汰”去掉 是否操作过出清 的限制 ([9f3cdcf](https://gitlab.hd123.com/vue/soa/commit/9f3cdcfce7be380d25268511e32f855f91287bb9))
* ✨ SOP-11431 商品淘汰单支持展示图片和调整按钮文字 ([99ac739](https://gitlab.hd123.com/vue/soa/commit/99ac739f7d69dfa3585b51f18b4a61cab4ef9d6d))
* ✨ SOP-11432 商品查询新增商品标签显示 ([a011584](https://gitlab.hd123.com/vue/soa/commit/a0115840e7a2422ef5a8e6b8b56f4461e64be2d6))
* ✨ SOP-11432 商品查询新增商品标签显示 ([cb5652f](https://gitlab.hd123.com/vue/soa/commit/cb5652f516afea5f515398cfff2e9e32c303c3b3))
* ✨ sop-11433 收货单搜索页面优化调整支持展示图片并添加商品详情页面 ([32be389](https://gitlab.hd123.com/vue/soa/commit/32be3894965ac39b134ee7625a09b994e3d3b1c2))
* ✨ SOP-11435 直送收货、退货修改单价显示 ([4dba463](https://gitlab.hd123.com/vue/soa/commit/4dba463ad12e0bee514537397cf9c66a61ac33b6))
* ✨ SOP-11435 直送收货、退货修改单价显示 ([67b62e9](https://gitlab.hd123.com/vue/soa/commit/67b62e9d6e476dfbdcc50268c980878d5ae423df))
* ✨ SOP-11435 直送收货、退货支持修改进/退货金额，增加进/退货单价信息等 ([494b077](https://gitlab.hd123.com/vue/soa/commit/494b07791d30e6aadd2743ce8dd9025ed2ffb9b2))
* ✨ SOP-11435 直送收货、退货支持修改进/退货金额，增加进/退货单价信息等 ([837534f](https://gitlab.hd123.com/vue/soa/commit/837534fee6a26859a571215805157fc58bcf7c53))
* ✨ SOP-11435 直送收货、退货支持修改进/退货金额，增加进/退货单价信息等 ([3982f7c](https://gitlab.hd123.com/vue/soa/commit/3982f7c2abf68d30b871c971b8e8b57bc739131a))
* ✨ SOP-11435 直送收货、退货支持修改进/退货金额，增加进/退货单价信息等 ([f4e2786](https://gitlab.hd123.com/vue/soa/commit/f4e278670ab799e2bde8ddfd4eddbeb68503fb60))
* ✨ SOP-11435 直送收货、退货支持修改进/退货金额，增加进/退货单价信息等 ([03e376d](https://gitlab.hd123.com/vue/soa/commit/03e376dfe091e08c7b8301018938d87974734422))
* ✨ SOP-11435 直送收货、退货支持修改进/退货金额，增加进/退货单价信息等 ([d719447](https://gitlab.hd123.com/vue/soa/commit/d7194475497f700a1bb60c26c85a33b095d18cd9))
* ✨ SOP-11435 直送收货、退货支持修改进/退货金额，增加进/退货单价信息等 ([5a99ecf](https://gitlab.hd123.com/vue/soa/commit/5a99ecff3950d65511ba2ceb7aea8f6c89cc41a4))
* ✨ SOP-11435 直送收货、退货支持修改进/退货金额，增加进/退货单价信息等 ([18ad75c](https://gitlab.hd123.com/vue/soa/commit/18ad75ce67d5ca1980b31822d0358716ffd4ccd8))
* ✨ SOP-11443 账户流水模块新增支付明细的筛选方式 ([76c399a](https://gitlab.hd123.com/vue/soa/commit/76c399a6cbffd430dbb615941d5a787417932a94))
* ✨ SOP-11451 收货列表+收货单搜索到货日期统一改为创建日期_并按创建时间排序 ([662e3a0](https://gitlab.hd123.com/vue/soa/commit/662e3a02906897840ad7eff13fb9ef8da86447bd))
* ✨ SOP-11452 盘点盈亏页面默认筛选项支持配置 ([f60cd6c](https://gitlab.hd123.com/vue/soa/commit/f60cd6c83ce35edbf43c3f973a6a3f51653e619d))
* ✨ SOP-11465 隆玛特_门店助手退货界面展示商品条码 ([f072759](https://gitlab.hd123.com/vue/soa/commit/f0727596553bbd9a62663c45ac3d566161f11ff0))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([8c88203](https://gitlab.hd123.com/vue/soa/commit/8c88203b5412c7c789d79a7ba0f2380c89ef8617))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([61bcd5d](https://gitlab.hd123.com/vue/soa/commit/61bcd5d6b40bef06183cffc8042ab635c3e901de))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([b1f97ec](https://gitlab.hd123.com/vue/soa/commit/b1f97ec8742e3c26f3d869bcd54e30504e4853be))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([ee12e0c](https://gitlab.hd123.com/vue/soa/commit/ee12e0c02ce409554985f7f3e38651299cdfa25a))
* ✨ SOP-11471 直送收货需要支持自动保存 - 区分商品是正常商品还是赠品 ([3242c8d](https://gitlab.hd123.com/vue/soa/commit/3242c8d1f50ac402d1281c4fe3ebff84c9b2bd05))
* ✨ SOP-11473 直配收货上传图片的配置控制作用范围调整 ([92ccde5](https://gitlab.hd123.com/vue/soa/commit/92ccde5dc1abd05ee8272fb83c90fbb38ccd1e39))
* ✨ SOP-11473 直配收货上传图片的配置控制作用范围调整 ([30b7d53](https://gitlab.hd123.com/vue/soa/commit/30b7d53eaec7156cae8eb57d62344c03273cf12d))
* ✨ SOP-11473 直配收货上传图片的配置控制作用范围调整 - 只有直配且开启了上传附件配置才显示 查看附件 ([bd5ceb0](https://gitlab.hd123.com/vue/soa/commit/bd5ceb01c69321d5c01491cfda088065d7c3e14d))
* ✨ SOP-11473 直配收货上传图片的配置控制作用范围调整 - 只有直配且开启了上传附件配置才显示 上传附件 ([16d956b](https://gitlab.hd123.com/vue/soa/commit/16d956b43216cefe1e80dc5b015f4a5d3a05fd4c))
* ✨ SOP-11479 店务助手订货商品列表中选择的商品数量改为0提交后仍有这个商品 ([1833957](https://gitlab.hd123.com/vue/soa/commit/1833957fa5c4814637f94c3eea4db5d153a600ac))
* ✨ SOP-11483 商品详情删除订货商品后返回列表数量未更新 ([824995a](https://gitlab.hd123.com/vue/soa/commit/824995a0ec83b4e3fcef2fb1b5d0a9255fd12302))
* ✨ SOP-11483 商品详情删除订货商品后返回列表数量未更新 ([e30c071](https://gitlab.hd123.com/vue/soa/commit/e30c07188ed645fee2a577115390c5c4b72ee1f0))
* ✨ SOP-11484 商品详情平均日销权限配置 ([897581e](https://gitlab.hd123.com/vue/soa/commit/897581e3d403c5118ae5a07e570b760925752035))
* ✨ SOP-11484 直送收货调整陈列位置按钮没有受权限控制 ([23012b0](https://gitlab.hd123.com/vue/soa/commit/23012b0affd94874068d5a3235e56dc53cda4470))
* ✨ SOP-11485 直送收货需要支持自动保存.新建单据返回列表需要刷新 ([60bb2bf](https://gitlab.hd123.com/vue/soa/commit/60bb2bff98cc77ac9553a40f5e65aeb40b58562e))
* ✨ SOP-11485 直送收货需要支持自动保存.新建单据返回列表需要刷新 ([a6e98cb](https://gitlab.hd123.com/vue/soa/commit/a6e98cb704ab3ddb76d14219a85aaad0022f4694))
* ✨ SOP-11487 调整货位提示信息 ([84dba21](https://gitlab.hd123.com/vue/soa/commit/84dba21b85b5b4126bd59a5fad1b5b546c09aa4c))
* ✨ SOP-11487 删除按钮调整 ([64d5e26](https://gitlab.hd123.com/vue/soa/commit/64d5e26837f562cc54db32b25193a505c5066246))
* ✨ SOP-11489 对接mPaaS扫码 ([1a3bb5a](https://gitlab.hd123.com/vue/soa/commit/1a3bb5a87ddc192f29a6c131779ff3fe4331cdf3))
* ✨ SOP-11498 零购店务商品查询优化 - 【店务商品查询是否展示可用库存】配置作用区域优化 ([ecc10ca](https://gitlab.hd123.com/vue/soa/commit/ecc10ca6c9523517869f75ff024577d812ee814f))
* ✨ SOP-11498 零购店务商品查询优化 - 【店务商品查询是否展示可用库存】配置作用区域优化 ([5a75f31](https://gitlab.hd123.com/vue/soa/commit/5a75f31a8e9f854b9a80e49e2e2dd3e6577c774a))
* ✨ SOP-11498 零购店务商品查询优化 - 【店务商品查询是否展示可用库存】配置作用区域优化 ([fb73a5a](https://gitlab.hd123.com/vue/soa/commit/fb73a5aec68bd6d223b1086a10e155db9c5f5141))
* ✨ SOP-11498 零购店务商品查询优化 - 【店务商品查询是否展示可用库存】配置作用区域优化 ([fff1fac](https://gitlab.hd123.com/vue/soa/commit/fff1fac3b6dced6c3773b1fcd46a8fa055d022f2))
* ✨ SOP-11499 店务商品模块进行库存调整与报损/报溢保持一致 ([dc2e50f](https://gitlab.hd123.com/vue/soa/commit/dc2e50fc3efff6156fb73e14340d58976d220c61))
* ✨ SOP-11499 零购店务商品查询优化 - 店务商品模块进行库存调整与报损/报溢保持一致 ([8f067ec](https://gitlab.hd123.com/vue/soa/commit/8f067ec7aa4a8e27a2b2af04bbe81f2295d7f14e))
* ✨ SOP-11499 零购店务商品查询优化 - 店务商品模块进行库存调整与报损/报溢保持一致 ([94660df](https://gitlab.hd123.com/vue/soa/commit/94660dfda2f8901f78cd6110e9f743139052c746))
* ✨ SOP-11499 零购店务商品查询优化 - 店务商品模块进行库存调整与报损/报溢保持一致 ([80ecd87](https://gitlab.hd123.com/vue/soa/commit/80ecd8762ceb3a41b63ffbe3f3902948cae79f66))
* ✨ SOP-11499 零购店务商品查询优化 - 店务商品模块进行库存调整与报损/报溢保持一致 ([a96fd2c](https://gitlab.hd123.com/vue/soa/commit/a96fd2cbf593d21aad81c5a748db0ed87098c41d))
* ✨ SOP-11504 诚信志远新订货首页正常订货时关联预订货订单 ([cc552f3](https://gitlab.hd123.com/vue/soa/commit/cc552f3c47b8a4f58fdbc0e83be11321bb36b91d))
* ✨ SOP-11506 未添加商品数量时修改进货金额报错 ([1a536d6](https://gitlab.hd123.com/vue/soa/commit/1a536d65710b5a27287e877240b0e35382c93711))
* ✨ SOP-11507 添加包材盘点的消息跳转逻辑 ([c649d8a](https://gitlab.hd123.com/vue/soa/commit/c649d8adcfa8017b7e34b58f54f14857d502a374))
* ✨ SOP-11509 直送收货修改配置为0，没实现可以修改进货价 ([e5a22aa](https://gitlab.hd123.com/vue/soa/commit/e5a22aa49afe31b27caca93c5e83f0934073b6a9))
* ✨ SOP-11511 直送退货配置为0，修改退货价，1*1规格单价未联动 ([12217fe](https://gitlab.hd123.com/vue/soa/commit/12217fe1f562ae90ab4a7e05d5a2e3cfefdf6eff))
* ✨ SOP-11514 预订货订单点击订货跳转页面优化 ([0cc5e29](https://gitlab.hd123.com/vue/soa/commit/0cc5e29943703b13d7169fbdd6bb3b991e888c0f))
* ✨ SOP-11515 存在预订单提交弹窗没有提示 ([78a5f95](https://gitlab.hd123.com/vue/soa/commit/78a5f953e864b96f0846a211c5f6c05cbf6567ab))
* ✨ SOP-11517 新增爱折扣经营分析h5入口 ([a759dca](https://gitlab.hd123.com/vue/soa/commit/a759dca8fbc03e3475fd76fc0ee829b3f67321f9))
* ✨ SOP-11518 标准订货爆品相关问题修复 ([0c78685](https://gitlab.hd123.com/vue/soa/commit/0c78685033b21afd9841799c542f744201935bbc))
* ✨ SOP-11520 爱折扣先款后货模式叫货提交后云资金没有立即返回支付结果的处理方案 ([44c9e4c](https://gitlab.hd123.com/vue/soa/commit/44c9e4cebd53e8f3b0a815fe7da22b898c35858a))
* ✨ SOP-11520 爱折扣先款后货模式叫货提交后云资金没有立即返回支付结果的处理方案 ([f2ceaa3](https://gitlab.hd123.com/vue/soa/commit/f2ceaa34c68e4b69188921247b207939580d2915))
* ✨ SOP-11520 爱折扣先款后货模式叫货提交后云资金没有立即返回支付结果的处理方案 ([a82804e](https://gitlab.hd123.com/vue/soa/commit/a82804edfdf6d1ee0602b7cbaf98bb2ad236a05b))
* ✨ SOP-11547 账户流水模块新增支付明细的筛选方式.列表支付明细显示优化 ([28995c4](https://gitlab.hd123.com/vue/soa/commit/28995c429bf5e7fb176fc65fa6f4b1ed453e0d66))
* ✨ SOP-11557 收货详情页面根据reqOrdType区分跳转至标准订货还是独立订货 ([d2c5865](https://gitlab.hd123.com/vue/soa/commit/d2c5865c5752ae0c4f98193c7094ce6433c46e55))
* ✨ SOP-11558 修复按箱收货非散称周转箱商品receiptQpcqty不对的问题 ([8a81bb9](https://gitlab.hd123.com/vue/soa/commit/8a81bb9ba81cdea69a1ee255e96489fe779ac58f))
* ✨ SOP-11575 账单详情接口调整 ([245aa1e](https://gitlab.hd123.com/vue/soa/commit/245aa1ece4fd0a583ef7b93f859e36f9475116f6))
* ✨ SOP-11581 多次收货编辑和商品搜索添加{ asc: true, field: 'lineNo' }条件 ([f72d203](https://gitlab.hd123.com/vue/soa/commit/f72d2035afce5df55f07afcebdcbc2322b7d8176))


### Bug Fixes

* 🐛 去除测试环境 ([7bb39ad](https://gitlab.hd123.com/vue/soa/commit/7bb39ad6acad80a941b91598e71003a153ae3850))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 - app上列表添加商品白屏以及样式错乱修复 ([c56d6cb](https://gitlab.hd123.com/vue/soa/commit/c56d6cb779a2b9b106eb7fdb622698c38e08f0bb))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 - app上列表添加商品白屏以及样式错乱修复 ([fe8a494](https://gitlab.hd123.com/vue/soa/commit/fe8a4946219368d450c2e41ddd5aebc5d53c3be2))
* 🐛 SOP-11353 直送收货需要支持自动保存 ([6805d01](https://gitlab.hd123.com/vue/soa/commit/6805d01db4faeae0b5c5a22905763fd68add81bc))
* 🐛 SOP-11353 直送收货需要支持自动保存 ([2ff4b05](https://gitlab.hd123.com/vue/soa/commit/2ff4b05d6e3939987568cec6065e2d21d01767f5))
* 🐛 SOP-11432 修复店务商品查询标签字段不存在时页面显示异常的问题 ([2d93545](https://gitlab.hd123.com/vue/soa/commit/2d9354501fac196a36e75d1b2c21808fca3748bf))
* 🐛 SOP-11434 标准订货和独立订货拆分商品搜索和面板、搜索页面支持扫码 ([fa1edc9](https://gitlab.hd123.com/vue/soa/commit/fa1edc9b138c7281ba7a895da05d77a66868a2bd))
* 🐛 SOP-11471 直送收货需要支持自动保存 - 增加商品默认进货价 ([3ed0dff](https://gitlab.hd123.com/vue/soa/commit/3ed0dff64ffeb01dd93f42009b316b87d67084fb))
* 🐛 SOP-11495 陈列位置字段增加字段权限控制.字段展示问题 ([e022850](https://gitlab.hd123.com/vue/soa/commit/e0228509911bb417ac6d3d4a7df21deb888eb7d3))
* 🐛 SOP-11497 直送收货、退货支持修改进/退货金额.修改商品数量，界面一直加载中 ([ad0a8ac](https://gitlab.hd123.com/vue/soa/commit/ad0a8ac939de3b0dc3a8458c8ec4ce5c2aa9002e))
* 🐛 SOP-11509 直送收货修改配置为0，没实现可以修改进货价 ([0d17d34](https://gitlab.hd123.com/vue/soa/commit/0d17d346d5dbfd9f65e8aecac136417fedf79ee7))
* 🐛 SOP-11515 爆品弹窗商品数量修改不生效的问题修复 ([cbcc9ae](https://gitlab.hd123.com/vue/soa/commit/cbcc9ae2565f0450a53dd94a650108813c733460))

### [2.54.19](https://gitlab.hd123.com/vue/soa/compare/v2.54.18...v2.54.19) (2025-03-27)


### Features

* ✨ SOP-11517 新增爱折扣经营分析h5入口 ([74a7341](https://gitlab.hd123.com/vue/soa/commit/74a7341b566ee8e2a6d1be187dd557c062a004a8))


### Bug Fixes

* 🐛 修复加工模块打印页面加载错误的问题 ([2f0279a](https://gitlab.hd123.com/vue/soa/commit/2f0279ad72ec916513808ffd708616664166e258))

### [2.54.18](https://gitlab.hd123.com/vue/soa/compare/v2.54.17...v2.54.18) (2025-03-21)


### Bug Fixes

* 🐛 SOP-11439 修复直送收货编辑数量无法输入1.0xx的问题 ([8745816](https://gitlab.hd123.com/vue/soa/commit/8745816048f91052fe6b5c111aa49d538ce7715b))

### [2.54.17](https://gitlab.hd123.com/vue/soa/compare/v2.54.16...v2.54.17) (2025-03-21)


### Features

* ✨ SOP-11339 按箱收货支持数量多收 ([b6df53b](https://gitlab.hd123.com/vue/soa/commit/b6df53ba1184801d3d92e3c73a27eb4603f5be23))


### Bug Fixes

* 🐛 HEPH-5902 修复租户绑定页面扫不正确的码乱码的问题 ([713109a](https://gitlab.hd123.com/vue/soa/commit/713109ae31dbeebd85f4b45fd77ceeb30591050b))
* 🐛 SOP-11439 修复直送收货编辑数量未保存问题 ([87ad31b](https://gitlab.hd123.com/vue/soa/commit/87ad31b81b2091fe7b61d57dfe8c405e461b65cb))

### [2.54.16](https://gitlab.hd123.com/vue/soa/compare/v2.54.15...v2.54.16) (2025-03-19)


### Features

* ✨ SOP-11479 店务助手订货商品列表中选择的商品数量改为0提交后仍有这个商品 ([8eca3ba](https://gitlab.hd123.com/vue/soa/commit/8eca3ba594f9f982d1c409073c72b4dcddd33915))
* ✨ SOP-11483 商品详情删除订货商品后返回列表数量未更新 ([77ed9d3](https://gitlab.hd123.com/vue/soa/commit/77ed9d3c11f962d863e3019d0f44d383aba6f87d))
* ✨ SOP-11483 商品详情删除订货商品后返回列表数量未更新 ([4efc213](https://gitlab.hd123.com/vue/soa/commit/4efc213b7a558c6a3bd6b40f37a0b4d717ad6e9c))


### Bug Fixes

* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 - app上列表添加商品白屏以及样式错乱修复 ([4891662](https://gitlab.hd123.com/vue/soa/commit/4891662bf19d0f0e3da5bd5992962be34cf5e655))

### [2.54.15](https://gitlab.hd123.com/vue/soa/compare/v2.54.14...v2.54.15) (2025-03-17)


### Bug Fixes

* 🐛 SOP-11481 修复首页销售数据图表有时无法加载的问题 ([a9afc83](https://gitlab.hd123.com/vue/soa/commit/a9afc83f2ee3258ea20061c405cfec1961ab8949))
* 🐛 SOP-11482 修复价格带单价无法录入小数的问题 ([441ac2f](https://gitlab.hd123.com/vue/soa/commit/441ac2f19ebadd71e6de21cdfe267251fdd73460))

### [2.54.14](https://gitlab.hd123.com/vue/soa/compare/v2.54.13...v2.54.14) (2025-03-14)


### Features

* ✨ SOP-11472 消息中心兼容历史消息 ([604ceff](https://gitlab.hd123.com/vue/soa/commit/604ceffe8393ed304f39620d6d64c172e4aa885a))
* ✨ SOP-11473 直配收货上传图片的配置控制作用范围调整 ([ca27cd8](https://gitlab.hd123.com/vue/soa/commit/ca27cd8885662fa782a5b2e482b9044d8679f099))
* ✨ SOP-11473 直配收货上传图片的配置控制作用范围调整 ([288c505](https://gitlab.hd123.com/vue/soa/commit/288c50581a67a4683c0b8538f9cf0a3c38e64d5f))
* ✨ SOP-11473 直配收货上传图片的配置控制作用范围调整 - 只有直配且开启了上传附件配置才显示 查看附件 ([b7d84e9](https://gitlab.hd123.com/vue/soa/commit/b7d84e90ccd93a977146a428b27b107af091dce6))
* ✨ SOP-11473 直配收货上传图片的配置控制作用范围调整 - 只有直配且开启了上传附件配置才显示 上传附件 ([058d3c8](https://gitlab.hd123.com/vue/soa/commit/058d3c8fe41fbea3aaab9c268781d88c4756d42d))
* ✨ SOP-11478 按箱收货拆零箱商品新增展示代码字段 ([83b4ff0](https://gitlab.hd123.com/vue/soa/commit/83b4ff0445a6573f2162846918fd7be5e04efc3f))


### Bug Fixes

* 🐛 SOP-11477 修复利润核算在iOS端筛选弹框选择日期时报错的问题 ([d205ec5](https://gitlab.hd123.com/vue/soa/commit/d205ec583027d4fc578e6eb153abcaa4c935ac57))

### [2.54.13](https://gitlab.hd123.com/vue/soa/compare/v2.54.11...v2.54.13) (2025-03-13)


### Features

* ✨ SOP-11471 直送收货需要支持自动保存 - 区分商品是正常商品还是赠品 ([2266a2c](https://gitlab.hd123.com/vue/soa/commit/2266a2cd85deba356262f5a96afa4aeef9cacf6c))


### Bug Fixes

* 🐛 SOP-11471 直送收货需要支持自动保存 - 增加商品默认进货价 ([0371786](https://gitlab.hd123.com/vue/soa/commit/03717864989566145df7012ac58319aca9df4e7f))

### [2.54.12](https://gitlab.hd123.com/vue/soa/compare/v2.54.11...v2.54.12) (2025-03-13)


### Bug Fixes

* 🐛 SOP-11471 直送收货需要支持自动保存 - 增加商品默认进货价 ([7a643e6](https://gitlab.hd123.com/vue/soa/commit/7a643e69d44fb26840bd0175b6f7c25c5b67758e))

### [2.54.11](https://gitlab.hd123.com/vue/soa/compare/v2.54.10...v2.54.11) (2025-03-12)

### [2.54.10](https://gitlab.hd123.com/vue/soa/compare/v2.54.9...v2.54.10) (2025-03-12)


### Features

* ✨ SOP-11465 隆玛特_门店助手退货界面展示商品条码 ([1784568](https://gitlab.hd123.com/vue/soa/commit/1784568b17cec03e655cf4b4957b679eeb723216))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([bfbf951](https://gitlab.hd123.com/vue/soa/commit/bfbf95116a59567a8f91cd899ad90ddf384de4b6))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([4be9e61](https://gitlab.hd123.com/vue/soa/commit/4be9e61bc60f6c604f13886eab9445d85dd29b98))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([77a1453](https://gitlab.hd123.com/vue/soa/commit/77a1453a2f34d05235af8ab4190902c2711a468c))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([2831e1b](https://gitlab.hd123.com/vue/soa/commit/2831e1b6c118e980113a5a42d87b07fc5b44bec4))


### Bug Fixes

* 🐛 SOP-11353 直送收货需要支持自动保存 ([eaeb7a3](https://gitlab.hd123.com/vue/soa/commit/eaeb7a3d52994984147547eaceb103f1fb113718))
* 🐛 SOP-11353 直送收货需要支持自动保存 ([d1fd20c](https://gitlab.hd123.com/vue/soa/commit/d1fd20c32a468a1f7b19a50737de3fe6def0f4ef))

### [2.54.11](https://gitlab.hd123.com/vue/soa/compare/v2.54.10...v2.54.11) (2025-03-12)

### [2.54.10](https://gitlab.hd123.com/vue/soa/compare/v2.54.9...v2.54.10) (2025-03-12)


### Features

* ✨ SOP-11465 隆玛特_门店助手退货界面展示商品条码 ([1784568](https://gitlab.hd123.com/vue/soa/commit/1784568b17cec03e655cf4b4957b679eeb723216))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([bfbf951](https://gitlab.hd123.com/vue/soa/commit/bfbf95116a59567a8f91cd899ad90ddf384de4b6))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([4be9e61](https://gitlab.hd123.com/vue/soa/commit/4be9e61bc60f6c604f13886eab9445d85dd29b98))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([77a1453](https://gitlab.hd123.com/vue/soa/commit/77a1453a2f34d05235af8ab4190902c2711a468c))
* ✨ SOP-11470 首页展示常用应用数据缓存需要在页面路径发生调整时更新到最新的路径 ([2831e1b](https://gitlab.hd123.com/vue/soa/commit/2831e1b6c118e980113a5a42d87b07fc5b44bec4))


### Bug Fixes

* 🐛 SOP-11353 直送收货需要支持自动保存 ([eaeb7a3](https://gitlab.hd123.com/vue/soa/commit/eaeb7a3d52994984147547eaceb103f1fb113718))
* 🐛 SOP-11353 直送收货需要支持自动保存 ([d1fd20c](https://gitlab.hd123.com/vue/soa/commit/d1fd20c32a468a1f7b19a50737de3fe6def0f4ef))

### [2.54.9](https://gitlab.hd123.com/vue/soa/compare/v2.54.8...v2.54.9) (2025-03-12)

### [2.54.8](https://gitlab.hd123.com/vue/soa/compare/v2.54.7...v2.54.8) (2025-03-11)

### [2.54.7](https://gitlab.hd123.com/vue/soa/compare/v2.54.5...v2.54.7) (2025-03-11)


### Features

* ✨ 店务商品库存调整需要收到报损、报溢的提交权限控制 ([ad913ed](https://gitlab.hd123.com/vue/soa/commit/ad913ed6e9bcdc9df19036b5c7b9db6a19f0359e))
* ✨ SOP-11325 移植零食有鸣自主盘点任务并改造 ([0730a09](https://gitlab.hd123.com/vue/soa/commit/0730a090659983c8c9e77a4ad2e4b85d904f02e9))
* ✨ SOP-11353 直送收货需要支持自动保存 ([93d7849](https://gitlab.hd123.com/vue/soa/commit/93d7849c8ecfaad9d8e229cd385f6c17b5bee149))
* ✨ SOP-11353 直送收货需要支持自动保存 ([6bdb024](https://gitlab.hd123.com/vue/soa/commit/6bdb0241a092ba1966e42afe37b28efae78ad25f))
* ✨ SOP-11363 标准订货支持自动保存 ([d039f7b](https://gitlab.hd123.com/vue/soa/commit/d039f7b69f3d10adcfec327402db22f10e1452d7))
* ✨ SOP-11363 标准订货支持自动保存 ([46e3c7f](https://gitlab.hd123.com/vue/soa/commit/46e3c7fac365c5f8dfa9d29139e3f4d5c271645f))
* ✨ SOP-11363 标准订货支持自动保存 ([527ea29](https://gitlab.hd123.com/vue/soa/commit/527ea2945783fcfce481f07b306003814bd27dee))
* ✨ SOP-11364 预报货隐藏撤回订单按钮 ([d5ed136](https://gitlab.hd123.com/vue/soa/commit/d5ed13638078301ddba9d2af48414904cdf67628))
* ✨ SOP-11365 调价申请交互优化 ([92b5780](https://gitlab.hd123.com/vue/soa/commit/92b5780a68fe30c41356ce3d6a81f65d2e892cbb))
* ✨ SOP-11365 调价申请交互优化 ([9fb70ca](https://gitlab.hd123.com/vue/soa/commit/9fb70cac470899107bbd81995783e96eabecf38d))
* ✨ SOP-11420 标准订货模版订货保存接口替换 ([44965e2](https://gitlab.hd123.com/vue/soa/commit/44965e2a05acf28306cd120cd2ad046056e15f29))
* ✨ SOP-11420 标准订货模版订货保存接口替换 ([3b3f95b](https://gitlab.hd123.com/vue/soa/commit/3b3f95b8a190f6b2b08031999bfee2a69db06b13))
* ✨ SOP-11420 标准订货模版订货保存接口替换 ([7f3f2e1](https://gitlab.hd123.com/vue/soa/commit/7f3f2e18a4fd815149c5f9b8b3f1866f54a2438d))
* ✨ SOP-11420 标准订货模版订货保存接口替换 ([7bcd97c](https://gitlab.hd123.com/vue/soa/commit/7bcd97c4612dc8ea08561f6ba5ca7cdbc3a69c6e))
* ✨ SOP-11420 标准订货模版订货保存接口替换 ([6032cd0](https://gitlab.hd123.com/vue/soa/commit/6032cd024b1cae1692479944f0c147bd96dd8eac))
* ✨ SOP-11420 标准订货支持自动保存 ([9d8db2c](https://gitlab.hd123.com/vue/soa/commit/9d8db2c8b96f233c893de566e3255cd23a2fe2e2))
* ✨ SOP-11420 标准订货支持自动保存.问题汇总 ([5573fea](https://gitlab.hd123.com/vue/soa/commit/5573fea8339328cd15cd738810128847556e2598))
* ✨ SOP-11420 模版叫货自动保存模式接口替换 ([f500fdf](https://gitlab.hd123.com/vue/soa/commit/f500fdfc35301f3ce70854216eba7235d7fe74b0))
* ✨ SOP-11420 模版叫货自动保存模式接口替换 ([ac32277](https://gitlab.hd123.com/vue/soa/commit/ac32277e66a9ad111cdf04ce9db235edd5181831))
* ✨ SOP-11423 店务商品库存调整需要收到报损、报溢的提交权限控制 ([8dfbba1](https://gitlab.hd123.com/vue/soa/commit/8dfbba172fbc43cc42e71ed90d534c8712ffe7bb))
* ✨ SOP-11458 直配收货支持上传图片 - 新增配置控制 ([db64806](https://gitlab.hd123.com/vue/soa/commit/db648065267fae4e47c4e9f30ab4bb4b92400ca1))
* ✨ SOP-11459 直配收货支持扫快递单号获取商品场景 - 收货明细区分快递单收货和非快递单收货 ([3f72cce](https://gitlab.hd123.com/vue/soa/commit/3f72cce988f8751159a9204349cf945b3455ed3f))
* ✨ SOP-11462 首页支持扫码跳转至店务商品查询 ([9765a1a](https://gitlab.hd123.com/vue/soa/commit/9765a1a6b7ec658d7571c4e77a336cdf264fe5ea))
* ✨ SOP-11463 门店助手店务模块商品信息展示图片需求问题 ([bba4131](https://gitlab.hd123.com/vue/soa/commit/bba4131cabf8dae7772584d3db53ce9db687394f))
* ✨ SOP-11463 门店助手店务模块商品信息展示图片需求问题 ([2fc28a6](https://gitlab.hd123.com/vue/soa/commit/2fc28a6408e1748c81d8e590cf32f8beeaf200a3))
* ✨ SOP-11465 隆玛特_门店助手退货界面展示商品条码 ([b93a1cf](https://gitlab.hd123.com/vue/soa/commit/b93a1cf0cad9635defadf71ea748255e03a292ad))


### Bug Fixes

* 🐛 安卓APP标签打印要跳转至安卓专属页面 ([c2b147f](https://gitlab.hd123.com/vue/soa/commit/c2b147fe3b22fd72c8e835f1ecdb65a7c81736c5))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([63dca06](https://gitlab.hd123.com/vue/soa/commit/63dca0692e1e667a9b174d6ac9d87bc305e6c706))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([d2c818b](https://gitlab.hd123.com/vue/soa/commit/d2c818bba1856df26e1a78b832eb73f1e665f03c))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([6774dc0](https://gitlab.hd123.com/vue/soa/commit/6774dc0350d27a6f43d5eb639c78907da4596d45))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([dd601db](https://gitlab.hd123.com/vue/soa/commit/dd601db16cadb1083371be36ab9302342af912da))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([2fbf640](https://gitlab.hd123.com/vue/soa/commit/2fbf640b095992ce40e7b0bc6020777d900108f5))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([6305949](https://gitlab.hd123.com/vue/soa/commit/63059492ce1bffe643aed2dd7b711a9288a331a2))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([b4d8b47](https://gitlab.hd123.com/vue/soa/commit/b4d8b477d8075a8453c249094c7a5129d93ad1c7))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([a965a96](https://gitlab.hd123.com/vue/soa/commit/a965a96a2189b7546528be96fc1daedcac7de446))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([32429df](https://gitlab.hd123.com/vue/soa/commit/32429df16b4b1a6f61872cc5719180a31cf2d17b))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([67c3624](https://gitlab.hd123.com/vue/soa/commit/67c3624c80d19aab192df71c6822daa69fcff100))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([b1a1499](https://gitlab.hd123.com/vue/soa/commit/b1a1499d53ad04c6579819579e7d475b688e8fac))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([65aa111](https://gitlab.hd123.com/vue/soa/commit/65aa1110a06a4c25191771633a7697156b950d0d))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 ([4c1caff](https://gitlab.hd123.com/vue/soa/commit/4c1caff7a16c0c41b0253a7e16592daec3f89a01))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 - 支持pda扫码 ([fb569d2](https://gitlab.hd123.com/vue/soa/commit/fb569d2cf0fefe1619ea7dc11bd4d0d039bba6da))
* 🐛 SOP-11325 移植零食有鸣自主盘点任务并改造 - 支持pda扫码 ([229a818](https://gitlab.hd123.com/vue/soa/commit/229a818eab312d8d2b6d025afe5851cb07c99404))
* 🐛 SOP-11418 修复收货编辑页面初始化接口调用失败导致无法扫码的问题 ([e6c74d1](https://gitlab.hd123.com/vue/soa/commit/e6c74d1396993b73093e4304a74eddff3bf4add0))
* 🐛 SOP-11418 修复收货商品搜索页面PDA扫码未更新搜索关键词的问题 ([cce2ef7](https://gitlab.hd123.com/vue/soa/commit/cce2ef79859e45506fea9e88bac56e3b2a27758d))
* 🐛 SOP-11420 标准订货复制单据到购物车提交单号修改 ([c5607ec](https://gitlab.hd123.com/vue/soa/commit/c5607eca140272252408f73fe56bab1fb605d504))
* 🐛 SOP-11420 标准订货模版订货保存接口替换 ([1f4e877](https://gitlab.hd123.com/vue/soa/commit/1f4e877cbe329b9e4464f9cfc9400bc313f640dd))
* 🐛 SOP-11420 标准订货模版订货保存接口替换 ([42dd626](https://gitlab.hd123.com/vue/soa/commit/42dd626ba0374d9db5057af67b2cbf950985110a))
* 🐛 SOP-11420 标准订货模版订货保存接口替换 ([35f25cd](https://gitlab.hd123.com/vue/soa/commit/35f25cdc0512783883dc24a49c8a20baa360411e))
* 🐛 SOP-11420 标准订货模版订货保存接口替换 ([5cba343](https://gitlab.hd123.com/vue/soa/commit/5cba343f689e6954307bb0889ea9ae2a9b9bbab1))
* 🐛 SOP-11420 标准订货支持自动保存 ([f6c75f0](https://gitlab.hd123.com/vue/soa/commit/f6c75f00fb5d8c5fb5b12001eb5a9e71ae419b82))

### [2.54.5](https://gitlab.hd123.com/vue/soa/compare/v2.54.4...v2.54.5) (2025-03-03)


### Bug Fixes

* 🐛 SOP-11415 加工模块成本价权限按钮配置 ([893c463](https://gitlab.hd123.com/vue/soa/commit/893c46321f335523e122f8fc32cfa5ebda38598b))

### [2.54.4](https://gitlab.hd123.com/vue/soa/compare/v2.54.3...v2.54.4) (2025-02-28)


### Features

* ✨ SOP-11354 价签申请支持适配Pda扫描头连续扫描及扫码后自动添加精准匹配的商品 ([f59ab41](https://gitlab.hd123.com/vue/soa/commit/f59ab410972942a3a5ded194d72cdb48d9490890))
* ✨ SOP-11354 价签申请支持适配Pda扫描头连续扫描及扫码后自动添加精准匹配的商品 ([2ec9580](https://gitlab.hd123.com/vue/soa/commit/2ec95801414234b4427fc8d6d479393c8489cec2))
* ✨ SOP-11354 价签申请支持适配Pda扫描头连续扫描及扫码后自动添加精准匹配的商品 ([e2c65e9](https://gitlab.hd123.com/vue/soa/commit/e2c65e91c98aa283d388dc63ab5f6d079039618b))
* ✨ SOP-11357 待支付的门店欠款单支持自动划扣需求 ([575e3e6](https://gitlab.hd123.com/vue/soa/commit/575e3e63f42e07ce188cccc448291d2203a23722))
* ✨ SOP-11357 待支付的门店欠款单支持自动划扣需求 ([2d75004](https://gitlab.hd123.com/vue/soa/commit/2d75004ccf46cf291beb46e5d6259ce7410efa7e))
* ✨ SOP-11361 标准订货商品增加售价字段 ([15a56a4](https://gitlab.hd123.com/vue/soa/commit/15a56a4b62f3d58fda2a8e22589a8c919e7b54eb))
* ✨ SOP-11364 标准订货支持提交订货单后自主取消 ([1627a6e](https://gitlab.hd123.com/vue/soa/commit/1627a6ed3f7a588e34011e7fcee2dbd9c9fbea94))
* ✨ SOP-11365 调价申请交互优化 ([c82fe08](https://gitlab.hd123.com/vue/soa/commit/c82fe085fa2882089312e76c840f314a481ea653))
* ✨ SOP-11366 自主盘点盈亏汇总信息区分开盘盈和盘亏 ([d0bc922](https://gitlab.hd123.com/vue/soa/commit/d0bc922ac918f4885bae38e5740856b227491210))
* ✨ SOP-11366 自主盘点盈亏汇总信息区分开盘盈和盘亏 ([28bbacc](https://gitlab.hd123.com/vue/soa/commit/28bbacc07b301ab4b7f9c574174eef4486f83239))
* ✨ SOP-11367 调价申请门店商品售价调整 ([7d588f6](https://gitlab.hd123.com/vue/soa/commit/7d588f6ccdff24dd85f08caec34da7c82cecb467))
* ✨ SOP-11367 调价申请门店商品售价调整 ([8072d62](https://gitlab.hd123.com/vue/soa/commit/8072d62390ac224971b3d761b4d5842f5cd63475))
* ✨ SOP-11367 调价申请门店商品售价调整 ([dec89bb](https://gitlab.hd123.com/vue/soa/commit/dec89bb3ace37ae5cea759b9529c20acd72e8825))
* ✨ SOP-11367 调价申请门店商品售价调整 ([088af72](https://gitlab.hd123.com/vue/soa/commit/088af72333de9ab80f94db4cf7d3018ff426be99))
* ✨ SOP-11367 调价申请门店商品售价调整 ([ed9d123](https://gitlab.hd123.com/vue/soa/commit/ed9d12348a287d7943b17e5d767261d24849bbb8))
* ✨ SOP-11367 调价申请门店商品售价调整 ([41b3f8f](https://gitlab.hd123.com/vue/soa/commit/41b3f8f64e419b492c70ec8b74f9b889aa58da1f))
* ✨ SOP-11367 调价申请门店商品售价调整 ([741e9d5](https://gitlab.hd123.com/vue/soa/commit/741e9d59f7afe2f64e0c61f0c56a3fa77054218d))
* ✨ SOP-11367 调价申请门店商品售价调整 ([d8c6f13](https://gitlab.hd123.com/vue/soa/commit/d8c6f13e6f48c1c916f9a77bd9cb5909c074984c))
* ✨ SOP-11378 直配收货支持上传图片 ([51fb7b4](https://gitlab.hd123.com/vue/soa/commit/51fb7b44fa6f51d8645452fdd460f98c8b30da9b))
* ✨ SOP-11383 首页新增待办提醒需求_自动完成待办 ([2f14e08](https://gitlab.hd123.com/vue/soa/commit/2f14e08b039f40dcb76d7fe370d952d9074221c9))
* ✨ SOP-11384 初盘支持pda扫描商品查询 ([ea1e3f9](https://gitlab.hd123.com/vue/soa/commit/ea1e3f944d594624651259ad867cf47048d3ce9a))
* ✨ SOP-11394 门店对账单模块权限增加 ([d763d15](https://gitlab.hd123.com/vue/soa/commit/d763d154197558e58c7c332bda7871d5d157f87b))
* ✨ SOP-11394 门店对账明细修改 ([f682995](https://gitlab.hd123.com/vue/soa/commit/f6829954dd1718a031ee87c50ba5429b51c28c12))
* ✨ SOP-11397 标签打印和加工支持批量打印 ([409f699](https://gitlab.hd123.com/vue/soa/commit/409f6993ccb7d6d457ec86539b05f498d432eae9))
* ✨ SOP-11397 加工单打印支持批量打印 ([f8813ee](https://gitlab.hd123.com/vue/soa/commit/f8813eea6a169dd9a4dfacef0335298b249b470e))
* ✨ SOP-11397 全部打印逻辑调整 ([b878a46](https://gitlab.hd123.com/vue/soa/commit/b878a46afbcba2a6ef5ca8c5754497eee79d6585))
* ✨ SOP-11405 新增爱折扣h5入口 ([2d45438](https://gitlab.hd123.com/vue/soa/commit/2d45438ae89a39fcac5332f1e379846baec69fdf))
* ✨ SOP-11413 收货模块需支持单独配置一键收货权限 ([f6705f8](https://gitlab.hd123.com/vue/soa/commit/f6705f8f4db34823ba9eea8774b148ad8cc7578f))
* ✨ SOP-11414 店务商品模块查询数量调整 ([38c0737](https://gitlab.hd123.com/vue/soa/commit/38c07378c978a8a3dcd7ab035261948ad28be276))
* ✨ SOP-11414 店务商品模块查询数量调整 ([d20611b](https://gitlab.hd123.com/vue/soa/commit/d20611b893d981a3c5c3e911bf32145ccf12f33a))
* ✨ SOP-11415 加工模块受"隐藏成本价"权限的全局控制 ([2073e9e](https://gitlab.hd123.com/vue/soa/commit/2073e9e025ed7410ed41d01a409c0ced75e4b1ff))
* ✨ SOP-11415 加工模块受"隐藏成本价"权限的全局控制 ([898d3f4](https://gitlab.hd123.com/vue/soa/commit/898d3f45399634fb46d6580f6f984d4e90a6a829))
* ✨ SOP-11415 加工模块受"隐藏成本价"权限的全局控制 ([b2cda7b](https://gitlab.hd123.com/vue/soa/commit/b2cda7b50205b2f2b84972d45cb7dbd0de259399))


### Bug Fixes

* 🐛 SOP-11355 直配收货支持扫快递单号获取商品场景 ([d874e4a](https://gitlab.hd123.com/vue/soa/commit/d874e4ad59d2844615579f241d4ec64289daf3e8))
* 🐛 SOP-11355 直配收货支持扫快递单号获取商品场景 ([52d2738](https://gitlab.hd123.com/vue/soa/commit/52d273864055399fbe6a04d07928b36f16ca6aed))
* 🐛 SOP-11392 利润核算新增费用时切换收入/支出需要清除已选费用类型 ([38676a1](https://gitlab.hd123.com/vue/soa/commit/38676a108c636ae900de6d1b8daa7876650e4824))
* 🐛 SOP-11410 新增门店对账单模块详情页面.界面问题 ([ead95da](https://gitlab.hd123.com/vue/soa/commit/ead95dacb857371ac147630449067e31333367e5))
* 🐛 SOP-11410 新增门店对账单模块详情页面.界面问题 ([24b8354](https://gitlab.hd123.com/vue/soa/commit/24b8354eeaf8a996738dd4805d18b35f0f2263e2))
* 🐛 SOP-11410 新增门店对账单模块详情页面.界面问题 ([4ddc02f](https://gitlab.hd123.com/vue/soa/commit/4ddc02f9ee4ee9fe60482221db0983c6062ad4c0))
* 🐛 SOP-11410 新增门店对账单模块详情页面问题修复 ([51a83ab](https://gitlab.hd123.com/vue/soa/commit/51a83abb70e5734bff2379caeb744336921529ea))
* 🐛 SOP-11410 新增门店对账单模块详情页面增加图片预览 ([9172c55](https://gitlab.hd123.com/vue/soa/commit/9172c55a46a0f1a2f5b38b9ce28a0ad00f03b784))
* 🐛 SOP-11411 修复门店对账单列表页面+搜索页面.界面问题 ([46b061b](https://gitlab.hd123.com/vue/soa/commit/46b061b2e02756ccb786d6befd21809a042bcbda))
* 🐛 SOP-11411 修复门店对账单列表页面+搜索页面.界面问题 ([ea97398](https://gitlab.hd123.com/vue/soa/commit/ea97398475a2cb467dc1876c6b49f3b3a34878e8))
* 🐛 SOP-11411 修复门店对账单列表页面+搜索页面.界面问题 ([c68f9e8](https://gitlab.hd123.com/vue/soa/commit/c68f9e867300f21e7f3adb7f4d90f87ed856f0bb))
* 🐛 SOP-11411 修复门店对账单列表页面筛选条件传递异常的问题 ([96fc2af](https://gitlab.hd123.com/vue/soa/commit/96fc2afe84015ac29def186dcc7eeb16df4aadbe))

### [2.54.3](https://gitlab.hd123.com/vue/soa/compare/v2.54.2...v2.54.3) (2025-02-25)


### Features

* ✨ SOP-11355 直配收货支持扫快递单号获取商品场景 ([d885c31](https://gitlab.hd123.com/vue/soa/commit/d885c314e1d0e551686d51170011c7062c80a80a))
* ✨ SOP-11396 消息模块【费用到账提醒】消息点击不跳转 ([19e4f55](https://gitlab.hd123.com/vue/soa/commit/19e4f5534302578a80372c8378487fa32246ce4b))


### Bug Fixes

* 🐛 SOP-11355 直配收货支持扫快递单号获取商品场景 ([3c218a0](https://gitlab.hd123.com/vue/soa/commit/3c218a08f2571e19396bda2041fa01e76772ab4a))
* 🐛 SOP-11355 直配收货支持扫快递单号获取商品场景 ([1abe91c](https://gitlab.hd123.com/vue/soa/commit/1abe91c41b71ffb9ea88e75a67083898df4733c2))

### [2.54.2](https://gitlab.hd123.com/vue/soa/compare/v2.54.1...v2.54.2) (2025-02-20)


### Features

* ✨ SOP-11383 首页新增待办提醒需求_自动完成待办 ([5ded961](https://gitlab.hd123.com/vue/soa/commit/5ded9613c0826bb153353ff842991bfb8b651101))


### Bug Fixes

* 🐛 HB-100846 修复按箱收货搜索页面不支持确认收货的问题 ([826def0](https://gitlab.hd123.com/vue/soa/commit/826def0ec4321ccec97547f58dd41787b7316786))

### [2.54.1](https://gitlab.hd123.com/vue/soa/compare/v2.54.0...v2.54.1) (2025-02-19)

## [2.54.0](https://gitlab.hd123.com/vue/soa/compare/v2.53.4...v2.54.0) (2025-02-18)


### Features

* ✨ SOP-11319 门店助手店务模块商品信息展示图片 ([1e45197](https://gitlab.hd123.com/vue/soa/commit/1e45197ed235b5e045ddcb0b339dd7bc4ca4f907))
* ✨ SOP-11319 门店助手店务模块商品信息展示图片 ([b4e0ce1](https://gitlab.hd123.com/vue/soa/commit/b4e0ce107dfe46e3669c56691933da304f092805))
* ✨ SOP-11326 微信小程序分包优化调整 ([4168918](https://gitlab.hd123.com/vue/soa/commit/41689189f1cd333fb2635bd56ba1862205d2e662))
* ✨ SOP-11327 周黑鸭-门店日常表格系统化管理需求 ([160a06b](https://gitlab.hd123.com/vue/soa/commit/160a06bb8967931ec6699ac9e60e54f2a4284792))
* ✨ SOP-11327 周黑鸭-门店日常表格系统化管理需求 ([fe80d20](https://gitlab.hd123.com/vue/soa/commit/fe80d2094712743ce434c48ab03e7d444afb3a18))
* ✨ SOP-11335 加工打印预览参数修改 ([7d698e6](https://gitlab.hd123.com/vue/soa/commit/7d698e674113bac4a5209041728336a1c2d896e1))
* ✨ SOP-11350 门店助手店务模块商品信息展示图片.页面问题 ([71e5160](https://gitlab.hd123.com/vue/soa/commit/71e5160bcffab936b6fd48f5cf2aada528bd9d5c))
* ✨ SOP-11370 兼容EK200B打印机 ([5d4b3a5](https://gitlab.hd123.com/vue/soa/commit/5d4b3a5cba3beaa5d964f20e9929df8cd979eab7))
* ✨ SOP-11370 兼容EK200B打印机 ([18b47a0](https://gitlab.hd123.com/vue/soa/commit/18b47a07aea604631536ca6cdf2eaac1443746a8))


### Bug Fixes

* 🐛 SOP-11319 店务模块商品信息展示图片 ([c594bf8](https://gitlab.hd123.com/vue/soa/commit/c594bf89ca332c08bcda5dd27cbd202d841db370))
* 🐛 SOP-11319 店务商品增加图片 ([0a7c4e3](https://gitlab.hd123.com/vue/soa/commit/0a7c4e394b17b6acfbeb287de4619daee21c5a4a))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([e24880b](https://gitlab.hd123.com/vue/soa/commit/e24880be9038790b1303802da6995be8549c44f6))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([d8f5510](https://gitlab.hd123.com/vue/soa/commit/d8f55103053c36ba86254cd38badb2273770d5f4))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([68ba616](https://gitlab.hd123.com/vue/soa/commit/68ba616ee5096000bef204cc86dfdaede84ffcb5))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([cb1f883](https://gitlab.hd123.com/vue/soa/commit/cb1f8831ec3825021d13bdd21ccd4b32f123beba))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([12ef670](https://gitlab.hd123.com/vue/soa/commit/12ef6705179cc60be81455a1c544110e8208e6d4))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([4629b5b](https://gitlab.hd123.com/vue/soa/commit/4629b5b185f8521384c643626859f4bf45bccb6b))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([522ac19](https://gitlab.hd123.com/vue/soa/commit/522ac1980086d75a5a92678a31021bace69a89c0))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([a71b351](https://gitlab.hd123.com/vue/soa/commit/a71b351a9abc0cdd35587f30b0831789255bd5cb))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([8518b5d](https://gitlab.hd123.com/vue/soa/commit/8518b5d3c8e0034f1944de944e7b84952d799fbe))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([07a735c](https://gitlab.hd123.com/vue/soa/commit/07a735cf168514724bd5f4f512565a6fda383f23))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([392c81f](https://gitlab.hd123.com/vue/soa/commit/392c81fe64294cb6bad21479337d00676419ea5f))

### [2.52.8](https://gitlab.hd123.com/vue/soa/compare/v2.52.7...v2.52.8) (2025-01-22)


### Bug Fixes

* 🐛 SOP-11318 修复周黑鸭自主盘点连续录入多个小数点变成999999999的问题 ([7ca6ac1](https://gitlab.hd123.com/vue/soa/commit/7ca6ac1402788a41ca7d4b58c9308299c72f8cdc))

### [2.52.7](https://gitlab.hd123.com/vue/soa/compare/v2.53.3...v2.52.7) (2025-01-20)


### Features

* ✨ SOP-11208 重点商品检查支持订货提醒 ([e76fc95](https://gitlab.hd123.com/vue/soa/commit/e76fc95e54127ffe8e53418969f9d97af312748a))
* ✨ SOP-11314 安卓打印优化支持经典蓝牙打印 ([ffda74e](https://gitlab.hd123.com/vue/soa/commit/ffda74e6e4c66dd60dce5cca7e0717cfb936c9c4))


### Bug Fixes

* 🐛 PB-4542 修复诚信致远订货首页切换分类错误的问题 ([fdcf5d6](https://gitlab.hd123.com/vue/soa/commit/fdcf5d6479c47c74293f36331b1c82e50c6fc3db))
* 🐛 PB-4597 修复诚信志远订货新首页点加入购物车后再点确定一直转圈 ([0fc3bac](https://gitlab.hd123.com/vue/soa/commit/0fc3baccbcac65b8e38def84e13b02db439f6329))
* 🐛 SOP-11310 修复诚信志远订货首页滚动不流畅的问题 ([5ccf5d5](https://gitlab.hd123.com/vue/soa/commit/5ccf5d5801879d625976a3866f5388b7a7561d46))
* 🐛 SOP-11318 周黑鸭自主盘点散称商品录入0.变成999999问题修复 ([341e4a1](https://gitlab.hd123.com/vue/soa/commit/341e4a186739302c801e4e9c96b41b19c413d54a))
* 🐛 SOP-11324 修复诚信志远订货首页在商品详情页面更新数量，未同步到商品分类面板和购物车的问题 ([7135b70](https://gitlab.hd123.com/vue/soa/commit/7135b700ac199cef74e0d9fbe7d7f9c4098fc897))

### [2.52.6](https://gitlab.hd123.com/vue/soa/compare/v2.53.2...v2.52.6) (2025-01-08)


### Features

* ✨ SOP-11209 新增重点商品模块展示重点商品数据 ([e635417](https://gitlab.hd123.com/vue/soa/commit/e6354171dbd114302c6a3cebbbfc121c962c916c))
* ✨ SOP-11209 新增重点商品模块展示重点商品数据 ([169ee9d](https://gitlab.hd123.com/vue/soa/commit/169ee9d77ee61420a3b54221b0a03aba4b063a80))
* ✨ SOP-11290 诚信志远加盟费用调整 ([bcb3a14](https://gitlab.hd123.com/vue/soa/commit/bcb3a146098638d708de44ff9bd3d5324c779084))
* ✨ SOP-11290 优化加盟费用提交和保存后的跳转逻辑 ([978e4aa](https://gitlab.hd123.com/vue/soa/commit/978e4aa1d140d5233304317af8eb8023c43666b6))


### Bug Fixes

* 🐛 SOP-11209 新增重点商品模块展示重点商品数据 ([15424bc](https://gitlab.hd123.com/vue/soa/commit/15424bccab86fc957fe8ffa292506490fa92c0fd))
* 🐛 SOP-11285 处理诚信志远订货首页问题 ([cee5516](https://gitlab.hd123.com/vue/soa/commit/cee5516950d19b651b35387282ad198e0062af08))

### [2.52.5](https://gitlab.hd123.com/vue/soa/compare/v2.53.1...v2.52.5) (2024-12-30)


### Features

* ✨ SOP-11256 调查类任务支持保存和撤回功能，任务项中添加时间和日期的项 ([8218bb6](https://gitlab.hd123.com/vue/soa/commit/8218bb6e0b1ee0fd0c0c1715cdf46f9c7aeb52ea))

### [2.53.5](https://gitlab.hd123.com/vue/soa/compare/v2.53.4...v2.53.5) (2025-02-18)


### Features

* ✨ SOP-11326 微信小程序分包优化调整 ([4168918](https://gitlab.hd123.com/vue/soa/commit/41689189f1cd333fb2635bd56ba1862205d2e662))
* ✨ SOP-11327 周黑鸭-门店日常表格系统化管理需求 ([160a06b](https://gitlab.hd123.com/vue/soa/commit/160a06bb8967931ec6699ac9e60e54f2a4284792))
* ✨ SOP-11327 周黑鸭-门店日常表格系统化管理需求 ([fe80d20](https://gitlab.hd123.com/vue/soa/commit/fe80d2094712743ce434c48ab03e7d444afb3a18))


### Bug Fixes

* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([e24880b](https://gitlab.hd123.com/vue/soa/commit/e24880be9038790b1303802da6995be8549c44f6))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([d8f5510](https://gitlab.hd123.com/vue/soa/commit/d8f55103053c36ba86254cd38badb2273770d5f4))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([68ba616](https://gitlab.hd123.com/vue/soa/commit/68ba616ee5096000bef204cc86dfdaede84ffcb5))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([cb1f883](https://gitlab.hd123.com/vue/soa/commit/cb1f8831ec3825021d13bdd21ccd4b32f123beba))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([12ef670](https://gitlab.hd123.com/vue/soa/commit/12ef6705179cc60be81455a1c544110e8208e6d4))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([4629b5b](https://gitlab.hd123.com/vue/soa/commit/4629b5b185f8521384c643626859f4bf45bccb6b))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([522ac19](https://gitlab.hd123.com/vue/soa/commit/522ac1980086d75a5a92678a31021bace69a89c0))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([a71b351](https://gitlab.hd123.com/vue/soa/commit/a71b351a9abc0cdd35587f30b0831789255bd5cb))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([8518b5d](https://gitlab.hd123.com/vue/soa/commit/8518b5d3c8e0034f1944de944e7b84952d799fbe))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([07a735c](https://gitlab.hd123.com/vue/soa/commit/07a735cf168514724bd5f4f512565a6fda383f23))
* 🐛 SOP-11343 周黑鸭-门店日常表格系统化管理需求.页面显示问题 ([392c81f](https://gitlab.hd123.com/vue/soa/commit/392c81fe64294cb6bad21479337d00676419ea5f))

### [2.52.8](https://gitlab.hd123.com/vue/soa/compare/v2.52.7...v2.52.8) (2025-01-22)


### Bug Fixes

* 🐛 SOP-11318 修复周黑鸭自主盘点连续录入多个小数点变成999999999的问题 ([7ca6ac1](https://gitlab.hd123.com/vue/soa/commit/7ca6ac1402788a41ca7d4b58c9308299c72f8cdc))

### [2.52.7](https://gitlab.hd123.com/vue/soa/compare/v2.53.3...v2.52.7) (2025-01-20)


### Features

* ✨ SOP-11208 重点商品检查支持订货提醒 ([e76fc95](https://gitlab.hd123.com/vue/soa/commit/e76fc95e54127ffe8e53418969f9d97af312748a))


### Bug Fixes

* 🐛 PB-4542 修复诚信致远订货首页切换分类错误的问题 ([fdcf5d6](https://gitlab.hd123.com/vue/soa/commit/fdcf5d6479c47c74293f36331b1c82e50c6fc3db))
* 🐛 PB-4597 修复诚信志远订货新首页点加入购物车后再点确定一直转圈 ([0fc3bac](https://gitlab.hd123.com/vue/soa/commit/0fc3baccbcac65b8e38def84e13b02db439f6329))
* 🐛 SOP-11310 修复诚信志远订货首页滚动不流畅的问题 ([5ccf5d5](https://gitlab.hd123.com/vue/soa/commit/5ccf5d5801879d625976a3866f5388b7a7561d46))
* 🐛 SOP-11318 周黑鸭自主盘点散称商品录入0.变成999999问题修复 ([341e4a1](https://gitlab.hd123.com/vue/soa/commit/341e4a186739302c801e4e9c96b41b19c413d54a))
* 🐛 SOP-11324 修复诚信志远订货首页在商品详情页面更新数量，未同步到商品分类面板和购物车的问题 ([7135b70](https://gitlab.hd123.com/vue/soa/commit/7135b700ac199cef74e0d9fbe7d7f9c4098fc897))

### [2.52.6](https://gitlab.hd123.com/vue/soa/compare/v2.53.2...v2.52.6) (2025-01-08)


### Features

* ✨ SOP-11209 新增重点商品模块展示重点商品数据 ([e635417](https://gitlab.hd123.com/vue/soa/commit/e6354171dbd114302c6a3cebbbfc121c962c916c))
* ✨ SOP-11209 新增重点商品模块展示重点商品数据 ([169ee9d](https://gitlab.hd123.com/vue/soa/commit/169ee9d77ee61420a3b54221b0a03aba4b063a80))
* ✨ SOP-11290 诚信志远加盟费用调整 ([bcb3a14](https://gitlab.hd123.com/vue/soa/commit/bcb3a146098638d708de44ff9bd3d5324c779084))
* ✨ SOP-11290 优化加盟费用提交和保存后的跳转逻辑 ([978e4aa](https://gitlab.hd123.com/vue/soa/commit/978e4aa1d140d5233304317af8eb8023c43666b6))


### Bug Fixes

* 🐛 SOP-11209 新增重点商品模块展示重点商品数据 ([15424bc](https://gitlab.hd123.com/vue/soa/commit/15424bccab86fc957fe8ffa292506490fa92c0fd))
* 🐛 SOP-11285 处理诚信志远订货首页问题 ([cee5516](https://gitlab.hd123.com/vue/soa/commit/cee5516950d19b651b35387282ad198e0062af08))

### [2.52.5](https://gitlab.hd123.com/vue/soa/compare/v2.53.1...v2.52.5) (2024-12-30)


### Features

* ✨ SOP-11256 调查类任务支持保存和撤回功能，任务项中添加时间和日期的项 ([8218bb6](https://gitlab.hd123.com/vue/soa/commit/8218bb6e0b1ee0fd0c0c1715cdf46f9c7aeb52ea))

### [2.53.4](https://gitlab.hd123.com/vue/soa/compare/v2.53.3...v2.53.4) (2025-01-22)


### Features

* ✨ SOP-11307 收货模块支持同步提交 ([5c5d1a6](https://gitlab.hd123.com/vue/soa/commit/5c5d1a64b67af6d1a331bd37342d5d55f145b55c))
* ✨ SOP-11307 收货模块支持同步提交 ([554bea3](https://gitlab.hd123.com/vue/soa/commit/554bea33da1355852e7a4eb5160ecffec1f016fb))
* ✨ SOP-11319 报损、报溢商品信息添加图片 ([482a700](https://gitlab.hd123.com/vue/soa/commit/482a700151629b18382fafa0bf7b022242e85d4a))
* ✨ SOP-11319 报损、报溢图片修改 ([d988ab8](https://gitlab.hd123.com/vue/soa/commit/d988ab8a5308f5cb89fdd29d0a949673683607ef))
* ✨ SOP-11319 报损报溢图片修改 ([05dbadb](https://gitlab.hd123.com/vue/soa/commit/05dbadbcca770dc79574a9b4248804e5e3bdd066))
* ✨ SOP-11319 商品查询添加图片查询 ([3b572a1](https://gitlab.hd123.com/vue/soa/commit/3b572a1a2b609f78cb585fb59752e893c934e1bf))
* ✨ SOP-11320 支持portal消息 ([415d5e6](https://gitlab.hd123.com/vue/soa/commit/415d5e63c01384fb64558ff6af427221f54def0b))
* ✨ SOP-11321 直配收货支持展示多少种未收 ([623daff](https://gitlab.hd123.com/vue/soa/commit/623daff934d64f31ed4b57816a8f94e82d53bffa))
* ✨ SOP-11323 多次收货支持扫商品码跳转单据并定位到商品 ([51c9a5c](https://gitlab.hd123.com/vue/soa/commit/51c9a5c006295dddca7b10f9681eb802aa218c1e))
* ✨ SOP-11323 多次收货支持扫商品码跳转单据并定位到商品 ([875c958](https://gitlab.hd123.com/vue/soa/commit/875c95857086ad9581c37dbfbdbe446785462ed9))
* ✨ SOP-11323 多次收货支持扫商品码跳转单据并定位到商品 ([b749ed9](https://gitlab.hd123.com/vue/soa/commit/b749ed9a68bcd71ac0392a28191a52ef768fc874))
* ✨ SOP-11323 多次收货支持扫商品码跳转单据并定位到商品 ([63e75d1](https://gitlab.hd123.com/vue/soa/commit/63e75d1e9436b46de5267b25e33b08ccaf2c9e21))
* ✨ SOP-11323 收货搜索单据待收状态需要考虑是否有未完成收货的商品 ([57d85f6](https://gitlab.hd123.com/vue/soa/commit/57d85f6062a2225fb984e9b6b9136331b98df5ca))
* ✨ SOP-11323 支持扫商品码跳转单据并定位到商品区分手输和扫码 ([586802e](https://gitlab.hd123.com/vue/soa/commit/586802e113975f7f12e0e8096b0cb9d6ea90d2fc))


### Bug Fixes

* 🐛 SOP-11323 商品加载报错时增加提示 ([c7d0f3d](https://gitlab.hd123.com/vue/soa/commit/c7d0f3d7f56785c83f999c62d2c88a8912913486))
* 🐛 SOP-11323 收货中的单子也要作为待收处理 ([1ab7dcb](https://gitlab.hd123.com/vue/soa/commit/1ab7dcb5d3d31627687d068d44899a62c0bed47b))
* 🐛 SOP-11323 修复收货商品搜索提示文字不正确的问题 ([c65a04d](https://gitlab.hd123.com/vue/soa/commit/c65a04dfd964a9f37b143714a2048fd560c7174e))

### [2.53.3](https://gitlab.hd123.com/vue/soa/compare/v2.53.2...v2.53.3) (2025-01-13)


### Features

* ✨ 多次收货的按箱收货详情增加可售库存 ([3f5b756](https://gitlab.hd123.com/vue/soa/commit/3f5b756afa1e4688810bf81e6493e25691ba37ab))
* ✨ 可售库存单位替换为最小规格单位 ([0949f04](https://gitlab.hd123.com/vue/soa/commit/0949f042764f3774f4b647a76142740fbc7d93b6))
* ✨ SOP_11305 陈列位置改回权限 ([e82bc4f](https://gitlab.hd123.com/vue/soa/commit/e82bc4fd98f01d169890b76501a1a4effbcedafa))
* ✨ SOP-11289 商品淘汰模块删除无用代码 ([e541a1c](https://gitlab.hd123.com/vue/soa/commit/e541a1c6841e094bdef26e54e86c48d68bc28e16))
* ✨ SOP-11289 消息中心增加商品淘汰模块查看权限 ([191e625](https://gitlab.hd123.com/vue/soa/commit/191e625b755b33e6c17c1d6b039f0de5ce316fc5))
* ✨ SOP-11289 小程序.门店助手.快送熊新增商品淘汰模块 ([216b381](https://gitlab.hd123.com/vue/soa/commit/216b381f89263e8c0b134daadd361fe902e787ff))
* ✨ SOP-11289 小程序.门店助手.快送熊新增商品淘汰模块部分提交 ([bc5de9b](https://gitlab.hd123.com/vue/soa/commit/bc5de9b2cea0c4d65e4bc571c3affaf2c8d8780c))
* ✨ SOP-11289 小程序.门店助手.快送熊新增商品淘汰模块部分提交 ([0b32228](https://gitlab.hd123.com/vue/soa/commit/0b3222811f7e7079f660909a03f92fffaab11572))
* ✨ SOP-11298 店务商品查询页面增加刷新按钮 ([92c5a64](https://gitlab.hd123.com/vue/soa/commit/92c5a646f4588a63c5f12bb2a2f47463ee826cf8))
* ✨ SOP-11298 店务商品查询页面增加刷新按钮 ([ddee2e9](https://gitlab.hd123.com/vue/soa/commit/ddee2e97b3ee342a85d255160d3efdc8a45b4ccd))
* ✨ SOP-11299 多次收货的直配收货新增结束收货按钮 ([85f41aa](https://gitlab.hd123.com/vue/soa/commit/85f41aab378ceb19346506491f3b01bc51ac4555))
* ✨ SOP-11299 收货模块优化 ([80ed2e3](https://gitlab.hd123.com/vue/soa/commit/80ed2e3ae86791a8420caed340f1fbb6151ad8ba))
* ✨ SOP-11303 各模块支持PDA扫码排查 ([c516a99](https://gitlab.hd123.com/vue/soa/commit/c516a99e3f76091c1bd2f11325c522aa5195c49c))
* ✨ SOP-11305 多次收货的直配收货新增结束收货按钮.页面问题 ([d650e51](https://gitlab.hd123.com/vue/soa/commit/d650e513160351a3b7550840529b48c7616b46f3))
* ✨ SOP-11305 去掉手动选择陈列位置 ([85ac73f](https://gitlab.hd123.com/vue/soa/commit/85ac73ffc00b4cc39ae149c50f2529f0f2ad129a))
* ✨ SOP-11305 修改确认是否结束收货传参 ([f689366](https://gitlab.hd123.com/vue/soa/commit/f689366ea420c34be549effc8b933ca1d202757d))
* ✨ SOP-11306 收货模块的手动选择陈列位置去掉 ([b5c6587](https://gitlab.hd123.com/vue/soa/commit/b5c6587eaaa0792198d474f28d0bd0c9b90a2ce7))
* ✨ SOP-11307 店务中台单据支持同步提交 ([deb9eae](https://gitlab.hd123.com/vue/soa/commit/deb9eaeee5ce35f7c80499975c790bcdf345ad64))
* ✨ SOP-11307 店务中台单据支持同步提交 ([7f95326](https://gitlab.hd123.com/vue/soa/commit/7f95326f3026811e52a179c12ca7d03062720a63))
* ✨ SOP-11307 店务中台单据支持同步提交 ([4cbc6a5](https://gitlab.hd123.com/vue/soa/commit/4cbc6a577963b53fb3d0beda0309fb3003b1d1a5))
* ✨ SOP-11307 店务中台单据支持同步提交 ([64eac6c](https://gitlab.hd123.com/vue/soa/commit/64eac6ca1f92c4c29186c35a8c80c8e024a705af))
* ✨ SOP-11307 取消收货 ([7368232](https://gitlab.hd123.com/vue/soa/commit/7368232a780e0d8a6401830b02877554f9b4cc11))
* ✨ SOP-11309 修改取消收货传参 ([be2ea1f](https://gitlab.hd123.com/vue/soa/commit/be2ea1f30ed8fc3255ad13496ae7a47db121a34d))


### Bug Fixes

* 🐛 SOP-11309 小程序.门店助手.快送熊新增商品淘汰模块.界面问题汇总 ([8c0b7db](https://gitlab.hd123.com/vue/soa/commit/8c0b7db841f2b714880594762d19282a2aad30c5))

### [2.53.2](https://gitlab.hd123.com/vue/soa/compare/v2.53.1...v2.53.2) (2025-01-03)


### Features

* ✨ SOP-11277 快送熊货架位支持货位排序 ([89cd0bf](https://gitlab.hd123.com/vue/soa/commit/89cd0bf5126deeb64baa68dd28e86f8f69a2ce27))
* ✨ SOP-11288 快送熊多次收货直配收货支持填写生产日期和到效期 ([ee18e40](https://gitlab.hd123.com/vue/soa/commit/ee18e40393aa19a55a2ba18a9e1acc5781c6be3d))
* ✨ SOP-11292 退仓和退供应商支持按配置控制是否可以退货数量大于批准数量 ([34c05a3](https://gitlab.hd123.com/vue/soa/commit/34c05a3313c11fca1ef0f99f54d04c491bccf8c3))


### Bug Fixes

* 🐛 陈列位置扫码模块进行规则校验 ([9b3ab7a](https://gitlab.hd123.com/vue/soa/commit/9b3ab7ae73fb9e8216ceac790b25a323419a8cc7))
* 🐛 配货差异数量没变修改 ([92812d1](https://gitlab.hd123.com/vue/soa/commit/92812d12cc576404d0c653a2f5862d06001c99eb))

### [2.53.1](https://gitlab.hd123.com/vue/soa/compare/v2.53.0...v2.53.1) (2024-12-27)


### Features

* ✨ 代码调试 - 调试安卓启动子应用缓慢问题 ([d4353bb](https://gitlab.hd123.com/vue/soa/commit/d4353bbfbb7f38eedb3866a29a2139118d6438f4))
* ✨ 重新加回批次调整按钮权限 ([27daef4](https://gitlab.hd123.com/vue/soa/commit/27daef41a456b9d3272a7ee68ae3ca808c43174a))
* ✨ SOP-11108 零购店务商品查询优化 ([bd9d17a](https://gitlab.hd123.com/vue/soa/commit/bd9d17a19ac0cf07eb86e4045ca8d415a5ecc4bd))
* ✨ SOP-11112 陈列位置优化调整 ([f03e052](https://gitlab.hd123.com/vue/soa/commit/f03e052ec99625a15cd558ba600279ad70498cec))
* ✨ SOP-11112 货架位添加修改 ([a5fd6e3](https://gitlab.hd123.com/vue/soa/commit/a5fd6e323f4b27f65931e2a0fa30a5dd6041ef23))
* ✨ SOP-11112 货位分支冲突合并 ([7abc562](https://gitlab.hd123.com/vue/soa/commit/7abc562e76f21e766d3024cf725c44cd5c43f090))
* ✨ SOP-11112 货位页面显示调整 ([4027c66](https://gitlab.hd123.com/vue/soa/commit/4027c665c39e129dc70a2d6c37d38bf236ff4a72))
* ✨ SOP-11112 快送熊支持在多个模块展示并可以调整货架位 ([1f1bd4e](https://gitlab.hd123.com/vue/soa/commit/1f1bd4edfb1dbdd2d2d535a257b58a087c6ae041))
* ✨ SOP-11112 快送熊支持在多个模块展示并可以调整货架位 ([0a306ae](https://gitlab.hd123.com/vue/soa/commit/0a306ae0b1256e153af4ddf189974165dfb624b2))
* ✨ SOP-11112 快送熊支持在多个模块展示并可以调整货架位 ([4aa6871](https://gitlab.hd123.com/vue/soa/commit/4aa6871c57b8449e5d52035125c066d98d25dba6))
* ✨ SOP-11112 快送熊支持在多个模块展示并可以调整货架位 ([4686ccd](https://gitlab.hd123.com/vue/soa/commit/4686ccd00b43414939ef13fa268c76c0af635c35))
* ✨ SOP-11112 快送熊支持在多个模块展示并可以调整货架位 ([98784fd](https://gitlab.hd123.com/vue/soa/commit/98784fde5cb7139dccf0cb63fc56984cc70624f4))
* ✨ SOP-11113 快送熊收货支持多次收货及其他优化需求 ([35c4289](https://gitlab.hd123.com/vue/soa/commit/35c428986c0b1316e611a53332d0c5e09612fd80))
* ✨ SOP-11193 详情页-提货信息-提交详情弹窗，显示层级问题 ([4d5d2b8](https://gitlab.hd123.com/vue/soa/commit/4d5d2b89e869098a2d48a9e15d7e25a1163470ad))
* ✨ SOP-11207 新增单品打折码申请单模块 - 编辑页支持PDA扫码 ([2256d2f](https://gitlab.hd123.com/vue/soa/commit/2256d2f723d18127c20543325fe5f55af3161d3d))
* ✨ SOP-11217 portal-app的改动-回归测试问题修复 ([7bca027](https://gitlab.hd123.com/vue/soa/commit/7bca027b750880c609044bb842330403982882a9))
* ✨ SOP-11219 快送熊报损报溢扫码支持单个商品直接增加到商品明细 ([11b0800](https://gitlab.hd123.com/vue/soa/commit/11b0800c3dbeed6d4473c151db49e15697caab49))
* ✨ SOP-11222 不停业盘点的盈亏查询只显示20条数据，没有显示全部数据 ([2dcad70](https://gitlab.hd123.com/vue/soa/commit/2dcad7067261d87de72e45200c4cbb0eda6b5dc7))
* ✨ SOP-11235 补全多次收货按箱收货的收货明细功能 ([7268a71](https://gitlab.hd123.com/vue/soa/commit/7268a71f2b1cafda1bf771c98ecb263364e71b83))
* ✨ SOP-11235 陈列位置按箱收货调整 ([781f98e](https://gitlab.hd123.com/vue/soa/commit/781f98eb80bfb4059c6203aed0329c398441d0b8))
* ✨ SOP-11235 陈列位置调整 ([0c585ee](https://gitlab.hd123.com/vue/soa/commit/0c585ee1980e88fe01508e84edea668c26dc1f51))
* ✨ SOP-11235 陈列位置调整修改 ([3eddae5](https://gitlab.hd123.com/vue/soa/commit/3eddae52621726eeb3f86fe1976043e429a5c4d0))
* ✨ SOP-11235 陈列位置调整修改 ([1ad83e4](https://gitlab.hd123.com/vue/soa/commit/1ad83e4c345e06972b1cdffa44e12a02c1c2bd4f))
* ✨ SOP-11235 陈列位置调整修改 ([b19ece9](https://gitlab.hd123.com/vue/soa/commit/b19ece9629aa296663c64b1bc6e5e05cc84c1ad2))
* ✨ SOP-11235 陈列位置下拉查询调整 ([fba3c4f](https://gitlab.hd123.com/vue/soa/commit/fba3c4fff33f0988857092d6109179d09883bff4))
* ✨ SOP-11235 陈列位置优化调整 ([d2997de](https://gitlab.hd123.com/vue/soa/commit/d2997def1b065710b65f0473a595e5461e8f75cc))
* ✨ SOP-11235 陈列位置优化调整 ([a614d23](https://gitlab.hd123.com/vue/soa/commit/a614d233f65bea93e25c3b588f7bf16ddad138f7))
* ✨ SOP-11235 调整陈列位置展示及输入样式 ([2d79a15](https://gitlab.hd123.com/vue/soa/commit/2d79a15d74afe601766fa8604492cd8f22556af0))
* ✨ SOP-11235 收货兼容服务端返回小数位全是0的问题 ([491f34f](https://gitlab.hd123.com/vue/soa/commit/491f34f0941a3f52e722a58128fffd8a8ae31b62))
* ✨ SOP-11235 修复差异核对页货位弹窗不生效的问题 ([38e6b78](https://gitlab.hd123.com/vue/soa/commit/38e6b7866982f37f5c9fcb99cda8b4dbf8f27af9))
* ✨ SOP-11235 修复陈列位置相关问题 ([d55399b](https://gitlab.hd123.com/vue/soa/commit/d55399b69a6dba143d0e41166f423894df804ac9))
* ✨ SOP-11235 直送收货修改货位后更改货位 ([bab298f](https://gitlab.hd123.com/vue/soa/commit/bab298f3f9c587075b3c83992b2ecf1447941035))
* ✨ SOP-11237 收货模块展示商品图片 ([4dc9ad1](https://gitlab.hd123.com/vue/soa/commit/4dc9ad1335af32e2391d825bf42029c538da5324))
* ✨ SOP-11240 报损报溢优化加购闪烁问题 ([d19d140](https://gitlab.hd123.com/vue/soa/commit/d19d140d1b1ed015e6ec69295185984f4f9ad715))
* ✨ SOP-11242 货架位相关调整 ([a93fd85](https://gitlab.hd123.com/vue/soa/commit/a93fd857c756cd73d909e496aa7c252e9524be88))
* ✨ SOP-11242 货架位展示优化 ([7d9b000](https://gitlab.hd123.com/vue/soa/commit/7d9b000971883863c1e03d688a56844e3d20e16b))
* ✨ SOP-11243 质量反馈去掉选择日期的校验 ([beed108](https://gitlab.hd123.com/vue/soa/commit/beed10867cf6b1cc0342b1afa53d5220b71c6f20))
* ✨ SOP-11251 多次收货散称商品1*1规格支持件数联动重量且重量联动件数 ([b4c1e1e](https://gitlab.hd123.com/vue/soa/commit/b4c1e1e97c3a7fb1c93a81882f74ad25c9308e62))
* ✨ SOP-11257 多次收货支持2000个商品 ([476c2a9](https://gitlab.hd123.com/vue/soa/commit/476c2a9f58dca721f83e520b551cf956a6c002d5))
* ✨ SOP-11257 多次收货支持2000个商品 ([070c4d1](https://gitlab.hd123.com/vue/soa/commit/070c4d18ac77b70f1dc1ed6b9890104aa51940c4))
* ✨ SOP-11273 商品查询支持按配置控制库存显示单品数还是规格数 ([753d36c](https://gitlab.hd123.com/vue/soa/commit/753d36c7a62a4215242cebea015e308854becfd2))


### Bug Fixes

* 🐛 解决多次收货按箱搜索是查看收货明细报错的问题 ([d3080e2](https://gitlab.hd123.com/vue/soa/commit/d3080e20951f7a3e3e7f94659e3e6d889a74e322))
* 🐛 修复订货提交产生重复单据问题 ([6212c5a](https://gitlab.hd123.com/vue/soa/commit/6212c5a137f3c22b74f15f54ad96fe65044d5e87))
* 🐛 修复APP标准订货预计到货时间显示异常 ([ac3b5d9](https://gitlab.hd123.com/vue/soa/commit/ac3b5d97f258fc228b0ed2785aa4fb6fd5822054))
* 🐛 SOP_11235 陈列位置UI调整 ([c63a6dd](https://gitlab.hd123.com/vue/soa/commit/c63a6dd25d5a2403c22ad8bf027bc49033a4341a))
* 🐛 SOP-11108 零购店务商品查询优化 ([5468238](https://gitlab.hd123.com/vue/soa/commit/5468238bdc6981523bfe96ea90c601819eaf654e))
* 🐛 SOP-11171 添加分类商品页面，列表存在商品的时候，二级分类点击下拉展开，没有展示下拉框 ([a69804e](https://gitlab.hd123.com/vue/soa/commit/a69804e089f4f1cc24fb1978a53cb52108522aa2))
* 🐛 SOP-11190 修复质量反馈文本输入框占位文字显示不全的问题 ([407a34d](https://gitlab.hd123.com/vue/soa/commit/407a34d2199e6d3b9a747d4cf14b2cf557253474))
* 🐛 SOP-11193 标准订货购物车添加备注功能 ([1f25ac7](https://gitlab.hd123.com/vue/soa/commit/1f25ac7e3ea767ad0672705c0d8e5f8ed84f95ea))
* 🐛 SOP-11193 修复标准订货问题6 ([70870e1](https://gitlab.hd123.com/vue/soa/commit/70870e1b4b45b74f0785c8e10d2e3e44cce6f5fc))
* 🐛 SOP-11193 修复标准订货问题7 ([8602a92](https://gitlab.hd123.com/vue/soa/commit/8602a92cca91370e2b17dca76ab9e1a0453c0193))
* 🐛 SOP-11193 修复订货底部操作栏层级过高的问题 ([f4c561b](https://gitlab.hd123.com/vue/soa/commit/f4c561b94f01a2de018b3401f2f4d7cb63908648))
* 🐛 SOP-11193 修复订货购物车页面展示不对的问题 ([dc8e9a0](https://gitlab.hd123.com/vue/soa/commit/dc8e9a05c274b47e50b0bfcf753b2a8fe61162eb))
* 🐛 SOP-11193 修复订货活动-修改活动报名数量问题 ([2ae0c18](https://gitlab.hd123.com/vue/soa/commit/2ae0c18a5bca424e4b970c999c9d110da1a60eec))
* 🐛 SOP-11193 修复价签申请问题 ([ab94ded](https://gitlab.hd123.com/vue/soa/commit/ab94ded7426147b8d75f24ca3527f85e79526756))
* 🐛 SOP-11193 iOS兼容问题修复 ([fbb1a0c](https://gitlab.hd123.com/vue/soa/commit/fbb1a0cc26a4017046017b87a37240b0a72c8ff5))
* 🐛 SOP-11195 库存管理 - 问题修复 ([18d027c](https://gitlab.hd123.com/vue/soa/commit/18d027c6a519fc43c868547eba4b8cf5ef0253d8))
* 🐛 SOP-11202 修复APP上传图片未限制图片大小的问题 ([410b5a3](https://gitlab.hd123.com/vue/soa/commit/410b5a3b26516df9b140e0e5982d1bcd3b08f59a))
* 🐛 SOP-11204 门店助手合并接入portal-app的改动 ([1a0e13d](https://gitlab.hd123.com/vue/soa/commit/1a0e13d0463c9edf78a162fbf5dc9f35e4d34a70))
* 🐛 SOP-11207 单品折扣新增页面报错 ([5dfcbe6](https://gitlab.hd123.com/vue/soa/commit/5dfcbe60f14e9f3efc26e6f8b067afe3272a6b36))
* 🐛 SOP-11220 修复直配收货搜索商品后编辑商品生产日期界面不更新的问题 ([021aec9](https://gitlab.hd123.com/vue/soa/commit/021aec930d4928d160d4e5fad29883b4c74eb887))
* 🐛 SOP-11230 新增单品打折码申请单模块.页面问题 ([a246299](https://gitlab.hd123.com/vue/soa/commit/a246299fbb6dcca024dde928063bbf88325d8474))
* 🐛 SOP-11235 陈列位置调整 ([5af70d8](https://gitlab.hd123.com/vue/soa/commit/5af70d89481c9affd8693a1abf294b9a70db3571))
* 🐛 SOP-11235 陈列位置调整 ([4eeb566](https://gitlab.hd123.com/vue/soa/commit/4eeb56603ffa721fafea8a568d804a7b0f38a47a))
* 🐛 SOP-11235 陈列位置UI调整 ([c60a2a9](https://gitlab.hd123.com/vue/soa/commit/c60a2a97aee1e52f70c23110c5897d5a8ab8d5a8))
* 🐛 SOP-11235 冲突合并 ([9e4b8aa](https://gitlab.hd123.com/vue/soa/commit/9e4b8aa9506c6597bf62d6221795cfcafe4276ae))
* 🐛 SOP-11235 多次收货统配差异提示添加红色标识 ([6b69bc7](https://gitlab.hd123.com/vue/soa/commit/6b69bc7c10b712930cc626dd2797ccab3f63bc54))
* 🐛 SOP-11235 解决问题第24、25点 ([373f2e1](https://gitlab.hd123.com/vue/soa/commit/373f2e15f310b68e035d19f40811eb519f31f3ea))
* 🐛 SOP-11235 收货历史记录展示历史记录单号 ([002c4d1](https://gitlab.hd123.com/vue/soa/commit/002c4d151a806b6230b51b18ed1a83846c7defa4))
* 🐛 SOP-11235 修复陈列位置展示 ([a683122](https://gitlab.hd123.com/vue/soa/commit/a683122f33635c738518a067ea927f9770ff66ee))
* 🐛 SOP-11235 修复多次收货历史记录显示标题和商品数量错误的问题 ([c95b60e](https://gitlab.hd123.com/vue/soa/commit/c95b60e1ef311caf0acc8631ff03595db26b4851))
* 🐛 SOP-11235 修复多次收货现实时间格式等问题 ([46ac58c](https://gitlab.hd123.com/vue/soa/commit/46ac58cca02cd306d655e50aa3d3a8fac3d03c7b))
* 🐛 SOP-11235 修复收货散称商品件数重量联动产生的问题11 ([ac10fdc](https://gitlab.hd123.com/vue/soa/commit/ac10fdc6f65a7d7e1c8ef4c1673362de68bd5627))
* 🐛 SOP-11235 修复提交成功页面显示数量的问题 ([2aaa6fe](https://gitlab.hd123.com/vue/soa/commit/2aaa6fe0135b39e2084cff9e645ab6670f5b1ed3))
* 🐛 SOP-11242 陈列位置超过长度提示 ([558e3e9](https://gitlab.hd123.com/vue/soa/commit/558e3e9601e56082e96a6a4972322c2bd0d3eabe))
* 🐛 SOP-11242 快送熊支持在多个模块展示并可以调整货架位.问题 ([739f32a](https://gitlab.hd123.com/vue/soa/commit/739f32a279e864e8881ec91a02896788e9feb977))
* 🐛 SOP-11242 快送熊支持在多个模块展示并可以调整货架位.问题处理 ([4b3766e](https://gitlab.hd123.com/vue/soa/commit/4b3766ef21bd6cf51bacf04ff08388cad8bff8b8))
* 🐛 SOP-11242 商品查询货架位单独起一行 ([a56d84a](https://gitlab.hd123.com/vue/soa/commit/a56d84a1e0d4e54749a91bc4d73fd8b2d030047c))
* 🐛 TM-25844 修复周黑鸭减量订货数量编辑时未同步修改购物车商品数量的问题 ([7b3f12c](https://gitlab.hd123.com/vue/soa/commit/7b3f12ca96cec4524b0f500570287deaddadad42))
* 🐛 TM-25844 修复周黑鸭模板订货数量编辑时未同步修改购物车商品数量的问题 ([3ce2bb6](https://gitlab.hd123.com/vue/soa/commit/3ce2bb649502879cfe5db2f69798afd615cac9c9))
* 🐛 TM-25856 不停业盘点的盈亏查询只显示20条数据，没有显示全部数据 ([3a3113b](https://gitlab.hd123.com/vue/soa/commit/3a3113bc0583783dff21e3a403d191835454731b))

### [2.52.8](https://gitlab.hd123.com/vue/soa/compare/v2.52.7...v2.52.8) (2025-01-22)


### Bug Fixes

* 🐛 SOP-11318 修复周黑鸭自主盘点连续录入多个小数点变成999999999的问题 ([7ca6ac1](https://gitlab.hd123.com/vue/soa/commit/7ca6ac1402788a41ca7d4b58c9308299c72f8cdc))

### [2.52.7](https://gitlab.hd123.com/vue/soa/compare/v2.52.6...v2.52.7) (2025-01-20)


### Bug Fixes

* 🐛 PB-4542 修复诚信致远订货首页切换分类错误的问题 ([fdcf5d6](https://gitlab.hd123.com/vue/soa/commit/fdcf5d6479c47c74293f36331b1c82e50c6fc3db))
* 🐛 PB-4597 修复诚信志远订货新首页点加入购物车后再点确定一直转圈 ([0fc3bac](https://gitlab.hd123.com/vue/soa/commit/0fc3baccbcac65b8e38def84e13b02db439f6329))
* 🐛 SOP-11310 修复诚信志远订货首页滚动不流畅的问题 ([5ccf5d5](https://gitlab.hd123.com/vue/soa/commit/5ccf5d5801879d625976a3866f5388b7a7561d46))
* 🐛 SOP-11318 周黑鸭自主盘点散称商品录入0.变成999999问题修复 ([341e4a1](https://gitlab.hd123.com/vue/soa/commit/341e4a186739302c801e4e9c96b41b19c413d54a))
* 🐛 SOP-11324 修复诚信志远订货首页在商品详情页面更新数量，未同步到商品分类面板和购物车的问题 ([7135b70](https://gitlab.hd123.com/vue/soa/commit/7135b700ac199cef74e0d9fbe7d7f9c4098fc897))

### [2.52.6](https://gitlab.hd123.com/vue/soa/compare/v2.52.5...v2.52.6) (2025-01-08)


### Features

* ✨ SOP-11290 诚信志远加盟费用调整 ([bcb3a14](https://gitlab.hd123.com/vue/soa/commit/bcb3a146098638d708de44ff9bd3d5324c779084))
* ✨ SOP-11290 优化加盟费用提交和保存后的跳转逻辑 ([978e4aa](https://gitlab.hd123.com/vue/soa/commit/978e4aa1d140d5233304317af8eb8023c43666b6))


### Bug Fixes

* 🐛 SOP-11285 处理诚信志远订货首页问题 ([cee5516](https://gitlab.hd123.com/vue/soa/commit/cee5516950d19b651b35387282ad198e0062af08))

### [2.52.5](https://gitlab.hd123.com/vue/soa/compare/v2.52.3...v2.52.5) (2024-12-30)


### Features

* ✨ 重新加回批次调整按钮权限 ([27daef4](https://gitlab.hd123.com/vue/soa/commit/27daef41a456b9d3272a7ee68ae3ca808c43174a))
* ✨ SOP-11210 报表新增传入参数orgId ([2ce2eee](https://gitlab.hd123.com/vue/soa/commit/2ce2eee8933b2c6602ad973e56999d8ac2861260))
* ✨ SOP-11222 不停业盘点的盈亏查询只显示20条数据，没有显示全部数据 ([2dcad70](https://gitlab.hd123.com/vue/soa/commit/2dcad7067261d87de72e45200c4cbb0eda6b5dc7))
* ✨ SOP-11243 质量反馈去掉选择日期的校验 ([beed108](https://gitlab.hd123.com/vue/soa/commit/beed10867cf6b1cc0342b1afa53d5220b71c6f20))
* ✨ SOP-11256 调查类任务支持保存和撤回功能，任务项中添加时间和日期的项 ([8218bb6](https://gitlab.hd123.com/vue/soa/commit/8218bb6e0b1ee0fd0c0c1715cdf46f9c7aeb52ea))


### Bug Fixes

* 🐛 修复订单活动的搜索问题 ([78085f6](https://gitlab.hd123.com/vue/soa/commit/78085f6037291006f43fea3b287e39c89e2aea7f))
* 🐛 修复订货提交产生重复单据问题 ([6212c5a](https://gitlab.hd123.com/vue/soa/commit/6212c5a137f3c22b74f15f54ad96fe65044d5e87))
* 🐛 TM-25844 修复周黑鸭减量订货数量编辑时未同步修改购物车商品数量的问题 ([7b3f12c](https://gitlab.hd123.com/vue/soa/commit/7b3f12ca96cec4524b0f500570287deaddadad42))
* 🐛 TM-25844 修复周黑鸭模板订货数量编辑时未同步修改购物车商品数量的问题 ([3ce2bb6](https://gitlab.hd123.com/vue/soa/commit/3ce2bb649502879cfe5db2f69798afd615cac9c9))
* 🐛 TM-25856 不停业盘点的盈亏查询只显示20条数据，没有显示全部数据 ([3a3113b](https://gitlab.hd123.com/vue/soa/commit/3a3113bc0583783dff21e3a403d191835454731b))

### [2.50.2](https://gitlab.hd123.com/vue/soa/compare/v2.51.1...v2.50.2) (2024-11-05)


### Bug Fixes

* 🐛 TM-25011 修复报损原因查询接口传入[]时报错的问题 ([1d49f27](https://gitlab.hd123.com/vue/soa/commit/1d49f2700b5057e875561ca760331bcaa310ed2f))

### [2.52.3](https://gitlab.hd123.com/vue/soa/compare/v2.52.2...v2.52.3) (2024-12-13)


### Features

* ✨ 增加showModal调试 ([535604e](https://gitlab.hd123.com/vue/soa/commit/535604e47f4f797b8b6c4764d87e5ad8190b43b3))
* ✨ SOP-11111 小程序.门店助手.店务商品查询和收货展示图片 - 店务商品查询更改 ([fb90754](https://gitlab.hd123.com/vue/soa/commit/fb9075492d31a1cac8fcde2ae7c54e9703d6829b))
* ✨ SOP-11112 快送熊支持在多个模块展示并可以调整货架位 ([de10aab](https://gitlab.hd123.com/vue/soa/commit/de10aab4ae9cf82df2757ff29d6fc9d2cfce4edb))
* ✨ SOP-11112 快送熊支持在多个模块展示并可以调整货架位 ([7009bb7](https://gitlab.hd123.com/vue/soa/commit/7009bb75bdfa7fa58956301e02241a4f454f1e43))
* ✨ SOP-11112 快送熊支持在多个模块展示并可以调整货架位 ([f41607d](https://gitlab.hd123.com/vue/soa/commit/f41607d4cad7dd88a6135a4406f32c7fb0e35305))
* ✨ SOP-11112 快送熊支持在多个模块展示并可以调整货架位 ([59de521](https://gitlab.hd123.com/vue/soa/commit/59de52138279da3a4a4bb267bf959eb19e411814))
* ✨ SOP-11112 商品货架位开发 ([085fe01](https://gitlab.hd123.com/vue/soa/commit/085fe0157fe2193c1cfe6f045f0656029bfbbe44))
* ✨ SOP-11112 添加货架位模块 ([2b2580b](https://gitlab.hd123.com/vue/soa/commit/2b2580bf31d27c27f0aadba65da7e95b3972ed65))
* ✨ SOP-11114 门店领用增加商品代码、填单人和当前库存展示 ([38989dc](https://gitlab.hd123.com/vue/soa/commit/38989dca949f084fef0c3dd62880e8854e771f36))
* ✨ SOP-11115 快送熊退货处理逻辑优化 ([7d11aab](https://gitlab.hd123.com/vue/soa/commit/7d11aab3c02d3807f64e219adcc027c33d63ee86))
* ✨ SOP-11115 快送熊退货处理逻辑优化 ([0a106e2](https://gitlab.hd123.com/vue/soa/commit/0a106e22693bbde59e888a9a15d62f62dcb36a67))
* ✨ SOP-11115 快送熊退货处理逻辑优化 ([6c37f41](https://gitlab.hd123.com/vue/soa/commit/6c37f41907237971a40bc96533ad2850406781d3))
* ✨ SOP-11115 快送熊退货处理逻辑优化 - 去除多余的代码 ([a7bb530](https://gitlab.hd123.com/vue/soa/commit/a7bb5302041454361c4947eac6cb06a0fc480071))
* ✨ SOP-11116 报损报溢列表 商品品相数单位由 件 改为 种 ([a7d199d](https://gitlab.hd123.com/vue/soa/commit/a7d199daa8b1cd5ad615e18b4e304734dbe1e2f4))
* ✨ SOP-11116 小程序.门店助手.快送熊报损报溢商品信息及定单展示优化 ([f6c0ac2](https://gitlab.hd123.com/vue/soa/commit/f6c0ac2e5384a72fa2973f47c64bd1ea3cf5b651))
* ✨ SOP-11116 小程序.门店助手.快送熊报损报溢商品信息及定单展示优化 ([ab56028](https://gitlab.hd123.com/vue/soa/commit/ab56028a513bae762f48873fd07226e834ac55a1))
* ✨ SOP-11210 报表新增传入参数orgId ([2ce2eee](https://gitlab.hd123.com/vue/soa/commit/2ce2eee8933b2c6602ad973e56999d8ac2861260))
* ✨ SOP-11212 按箱收货扩展“整件”场景&支持散货 ([16c728d](https://gitlab.hd123.com/vue/soa/commit/16c728dfeb713e433ed588102f77af6569e41b55))
* ✨ SOP-11215 小程序.门店助手.快送熊退货处理逻辑优化 - P2 ([934dbea](https://gitlab.hd123.com/vue/soa/commit/934dbea980782a87ec342ec598e5b3e1fb8368bb))
* ✨ SOP-11219 小程序.门店助手.快送熊报损报溢扫码支持单个商品直接增加到商品明细 ([8db479a](https://gitlab.hd123.com/vue/soa/commit/8db479af3f89c1bd5b9a60bd7489def4ecd2750f))


### Bug Fixes

* 🐛 修复订单活动的搜索问题 ([78085f6](https://gitlab.hd123.com/vue/soa/commit/78085f6037291006f43fea3b287e39c89e2aea7f))
* 🐛 fix-2.52.3 修复首页预订货活动无权限展示问题 ([9f34b29](https://gitlab.hd123.com/vue/soa/commit/9f34b29e1f02daaad8e0e90611965fc3aa99bc02))
* 🐛 SOP-11112 冲突合并 ([83bcc2b](https://gitlab.hd123.com/vue/soa/commit/83bcc2bb4320a84c7bad93d2c3088832e3996c4a))
* 🐛 SOP-11115 小程序.门店助手.快送熊退货处理逻辑优化 ([d584ee0](https://gitlab.hd123.com/vue/soa/commit/d584ee04c35bbb08076f2932ce5e1908ab2180e2))
* 🐛 SOP-11115 小程序.门店助手.快送熊退货处理逻辑优化 ([de5f437](https://gitlab.hd123.com/vue/soa/commit/de5f437bf1ff63d2ad949ea4d1b55fff5a3f602a))
* 🐛 SOP-11115 小程序.门店助手.快送熊退货处理逻辑优化 - 后端返回数据与预期不符更改 ([92d71d3](https://gitlab.hd123.com/vue/soa/commit/92d71d369053ad1a4772293c99c9b78748e13a20))
* 🐛 SOP-11116 报溢模块 新增页 成本价和当前库存丢失 ([1409676](https://gitlab.hd123.com/vue/soa/commit/1409676f5b55fe48e4642138997a337028c493c6))
* 🐛 SOP-11116 小程序.门店助手.快送熊报损报溢商品信息及定单展示优化 ([8f4ea7d](https://gitlab.hd123.com/vue/soa/commit/8f4ea7d0417954b1b5d7dcd341b50b8107fbab21))

### [2.50.2](https://gitlab.hd123.com/vue/soa/compare/v2.51.1...v2.50.2) (2024-11-05)


### Bug Fixes

* 🐛 TM-25011 修复报损原因查询接口传入[]时报错的问题 ([1d49f27](https://gitlab.hd123.com/vue/soa/commit/1d49f2700b5057e875561ca760331bcaa310ed2f))

## [2.53.0](https://gitlab.hd123.com/vue/soa/compare/v2.52.2...v2.53.0) (2024-12-20)


### Features

* ✨ SOP-11207 新增单品打折码申请单模块 ([eab3a70](https://gitlab.hd123.com/vue/soa/commit/eab3a70afc74ba0f2466555f106fb091c9bcff8b))
* ✨ SOP-11207 新增单品打折码申请单模块 ([4d18ca5](https://gitlab.hd123.com/vue/soa/commit/4d18ca59aaa1ed870e41a2cccd5b7cbbdb11b955))
* ✨ SOP-11207 新增单品打折码申请单模块 ([b57264e](https://gitlab.hd123.com/vue/soa/commit/b57264ef0ba4f53ed1dd8d4056bc0fb623b327fc))
* ✨ SOP-11207 新增单品打折码申请单模块 ([10c70fd](https://gitlab.hd123.com/vue/soa/commit/10c70fd3f7fb5de81caf8cadb27c1497f047b17a))
* ✨ SOP-11207 新增单品打折码申请单模块 - 编辑页支持PDA扫码 ([a3f18f5](https://gitlab.hd123.com/vue/soa/commit/a3f18f5aebadcd87678c60d38f42b05516d3e452))


### Bug Fixes

* 🐛 SOP-11207 单品折扣新增页面报错 ([230003d](https://gitlab.hd123.com/vue/soa/commit/230003dd8f416f5953cf0f236ea5da01cf3f3d37))
* 🐛 SOP-11230 新增单品打折码申请单模块.页面问题 ([fbd1c03](https://gitlab.hd123.com/vue/soa/commit/fbd1c03e19259e42709c56f5c347df5d083d5b99))

### [2.52.3](https://gitlab.hd123.com/vue/soa/compare/v2.52.2...v2.52.3) (2024-12-13)


### Bug Fixes

* 🐛 fix-2.52.3 修复首页预订货活动无权限展示问题 ([9f34b29](https://gitlab.hd123.com/vue/soa/commit/9f34b29e1f02daaad8e0e90611965fc3aa99bc02))

### [2.52.2](https://gitlab.hd123.com/vue/soa/compare/v2.52.1...v2.52.2) (2024-12-07)


### Features

* ✨ 修复接入portal权限调用接口的问题 ([1688a4b](https://gitlab.hd123.com/vue/soa/commit/1688a4b84bf97dd3dd7c17423e397cf5b0a0cacd))
* ✨ HEPH-5363 调整调用portal提供的接口查询权限 ([4fffbff](https://gitlab.hd123.com/vue/soa/commit/4fffbffd81e86ba585d9f061e30f630fd63e5275))
* ✨ HEPH-5363 支持接收Portal的关闭通知 ([a6973e3](https://gitlab.hd123.com/vue/soa/commit/a6973e3493409e50c1f15d4681a3c511a7e8e1a2))
* ✨ SOP-11023 调拨申请设备扫码优化 ([b1f595f](https://gitlab.hd123.com/vue/soa/commit/b1f595f9a82b8adb3d6fce8a9f4bdafc6abd10c8))
* ✨ SOP-11023 调整调拨申请设备扫码 ([e27f438](https://gitlab.hd123.com/vue/soa/commit/e27f438e543289fbce6e1085529c947bc97e0c19))
* ✨ SOP-11129 iOS部分机型无法输入小数点 ([3dae796](https://gitlab.hd123.com/vue/soa/commit/3dae796c30e7e3ea5a1a75cd1383e7f82e97e917))
* ✨ SOP-11129 ios部分机型无法输入小数点问题 ([6d777a3](https://gitlab.hd123.com/vue/soa/commit/6d777a3d77cc18da9f092f1e1f1e76333ce4742d))
* ✨ SOP-11140 部分手机输入框输入小数点问题 ([db57b54](https://gitlab.hd123.com/vue/soa/commit/db57b541f75111907409e1a981e44c41212060cc))
* ✨ SOP-11140 修复iOS在部分机型输入框选中问题 ([e1754c4](https://gitlab.hd123.com/vue/soa/commit/e1754c462236e1204b592699a0fe367ffc599eb7))
* ✨ SOP-11140 H5在iOS手机系统16的版本无法输入小数点 ([99e9ddd](https://gitlab.hd123.com/vue/soa/commit/99e9ddd57f3a8200e96ca86ab94b639830a2bb60))
* ✨ SOP-11194 添加关于收货差异原因填写的控制 ([bd6a903](https://gitlab.hd123.com/vue/soa/commit/bd6a903da552e64030b9d2161425880f9018f7eb))


### Bug Fixes

* 🐛 试吃模块 购物车增加商品数量点击无效问题 ([7b79109](https://gitlab.hd123.com/vue/soa/commit/7b7910945ce67b35468e23ec27d49546d418bf72))
* 🐛 修复钉钉图片上传组件传参问题 ([62075ee](https://gitlab.hd123.com/vue/soa/commit/62075eee9f4d5fe1220350469d72eb72b057506a))
* 🐛 SOP-11022 修复按箱收货拆零箱仍显示差异金额的问题 ([5de85c8](https://gitlab.hd123.com/vue/soa/commit/5de85c8438c2d5ce09e81ff54aadd0dfc6f61417))
* 🐛 SOP-11173 修复评价页面iOS端保存传参错误的问题 ([88b0046](https://gitlab.hd123.com/vue/soa/commit/88b00468388b121b092a95e58bdda93a47e54fb7))
* 🐛 SOP-11186 货郎客户待办功能问题技术支持 ([d0e9363](https://gitlab.hd123.com/vue/soa/commit/d0e9363df6d12a5807cf4e241dd3bfb2a52ee001))
* 🐛 SOP-11186 货郎客户待办功能问题技术支持 ([f6ad022](https://gitlab.hd123.com/vue/soa/commit/f6ad0228b46648d177a866df87e125217ed132f3))
* 🐛 SOP-11186 修复首页扫码按钮展示扫码文案的问题 ([64298f6](https://gitlab.hd123.com/vue/soa/commit/64298f611d195f7ce88e2ad3acaba2f4fe9e0286))
* 🐛 SOP-11197 修复价签申请模块编辑数量后底部价签件数未随之改变的问题 ([7f4de18](https://gitlab.hd123.com/vue/soa/commit/7f4de184cde90145ed41d81de583e8e43a134a3a))

### [2.51.5](https://gitlab.hd123.com/vue/soa/compare/v2.51.4...v2.51.5) (2024-11-15)


### Features

* ✨ SOP-11129 质量反馈部分手机型号无法输入小数点 ([cfdc033](https://gitlab.hd123.com/vue/soa/commit/cfdc0330d3565af31af0420d7b5a71e2676c4d86))

### [2.51.4](https://gitlab.hd123.com/vue/soa/compare/v2.51.3...v2.51.4) (2024-11-15)


### Bug Fixes

* 🐛 SOP-11127 按箱收货拆零箱确认收货时应将收货箱数赋值为发货箱数 ([29ff1bb](https://gitlab.hd123.com/vue/soa/commit/29ff1bb4bfe02e05ed3a5a5129720c7cac4cdb0e))

### [2.51.3](https://gitlab.hd123.com/vue/soa/compare/v2.51.2...v2.51.3) (2024-11-11)


### Features

* ✨ SOP-11125 收货编辑散称字段显示 ([e426dbb](https://gitlab.hd123.com/vue/soa/commit/e426dbbf510a84d16c96ec633eeba1bb531f8bb4))

### [2.51.2](https://gitlab.hd123.com/vue/soa/compare/v2.51.1...v2.51.2) (2024-11-11)


### Features

* ✨ SOP-11103 调整正常订货加载商品时不展示骨架屏改为显示加载loading ([5bca341](https://gitlab.hd123.com/vue/soa/commit/5bca341a24bf218bed423b2ee36b3965e4cd3fb3))

### [2.52.1](https://gitlab.hd123.com/vue/soa/compare/v2.52.0...v2.52.1) (2024-12-03)


### Features

* ✨ SOP-11142 提货中也能去订货 ([1880296](https://gitlab.hd123.com/vue/soa/commit/1880296ba150e8e26eef0b7b23f541c22febc8e1))
* ✨ SOP-11179 退仓详情显示金额优化 ([39d8b26](https://gitlab.hd123.com/vue/soa/commit/39d8b26415e9c64400c3125eca88d412eb2879cb))
* ✨ SOP-11179 退仓详情显示字段优化 ([e27f49b](https://gitlab.hd123.com/vue/soa/commit/e27f49b697d75ef7e4f2b911ed1077dd9218be07))
* ✨ SOP-11183 订货数量提示修改 ([ef1a620](https://gitlab.hd123.com/vue/soa/commit/ef1a6200511b9e4a55ebd1f3cc0a8d646ee78a88))
* ✨ SOP-11183 新增预订货活动首页弹窗.页面问题 ([dca2a88](https://gitlab.hd123.com/vue/soa/commit/dca2a8865d897f784444d83dc2bcadcc1fc800a7))
* ✨ SOP-11183 新增预订货活动首页弹窗.页面问题 ([c0f3036](https://gitlab.hd123.com/vue/soa/commit/c0f3036440f64964b4b0a42254318b92dc655fc3))
* ✨ SOP-11183 新增预订货活动首页弹窗.页面问题 ([2cccbb4](https://gitlab.hd123.com/vue/soa/commit/2cccbb4b0272c7bebf666513c77741424f21c2f6))

## [2.52.0](https://gitlab.hd123.com/vue/soa/compare/v2.51.1...v2.52.0) (2024-12-02)


### Features

* ✨ 支持检查更新 ([39659a3](https://gitlab.hd123.com/vue/soa/commit/39659a315e67b52bd0cbe8d0e14621e500cad018))
* ✨ HB-98208 添加输入框的在h5环境的编译条件 ([c2f642e](https://gitlab.hd123.com/vue/soa/commit/c2f642e52a62550bc7b46f202dd3250bce19de40))
* ✨ mine 页面支持查看版本号 ([07892b9](https://gitlab.hd123.com/vue/soa/commit/07892b9b2c5481483389165ce3ac0e4c84225928))
* ✨ SOP-10942 调整建议叫货数上下限控制 ([c3a4395](https://gitlab.hd123.com/vue/soa/commit/c3a4395397404a6ec5bfc799c6444513496deee8))
* ✨ SOP-10947 小程序.门店助手.标准订货操作优化 ([f0d3ef7](https://gitlab.hd123.com/vue/soa/commit/f0d3ef7682e1af7e0267fe20b39c9168344015b5))
* ✨ SOP-10947 小程序.门店助手.标准订货操作优化 ([654aa62](https://gitlab.hd123.com/vue/soa/commit/654aa626abfd45684c5e10103b2972aa223e632f))
* ✨ SOP-11022 按箱收货支持赠品收货，收货商品列表支持区分出赠品 ([d757a19](https://gitlab.hd123.com/vue/soa/commit/d757a19747b32aef9982356d56eecdac1ed3846d))
* ✨ SOP-11022 按箱收货支持赠品收货，收货商品列表支持区分出赠品 ([c08b0a4](https://gitlab.hd123.com/vue/soa/commit/c08b0a4ba0239b9decfdfef2f4e69bc0305c49eb))
* ✨ SOP-11022 按箱收货支持赠品收货，收货商品列表支持区分出赠品 ([d6ff493](https://gitlab.hd123.com/vue/soa/commit/d6ff49352e683bfed5ce64c46c7a42303499cc8f))
* ✨ SOP-11022 移除按箱收货中关于差异金额的显示 ([bb8ad42](https://gitlab.hd123.com/vue/soa/commit/bb8ad42dc1b901f625b5845dc54a6276ecb34c9f))
* ✨ SOP-11023 货郎客户APP优化 ([f1bdd13](https://gitlab.hd123.com/vue/soa/commit/f1bdd13759a2a954b2fa1d009dc09e42c8c81cd3))
* ✨ SOP-11103 调整正常订货加载商品时不展示骨架屏改为显示加载loading ([e71317f](https://gitlab.hd123.com/vue/soa/commit/e71317f5f618132b9cdf532ae3a9915a1d18d80d))
* ✨ SOP-11104 收货去掉行号显示 ([42e9596](https://gitlab.hd123.com/vue/soa/commit/42e9596b22f5054276f2ee230fd355837b0c04e9))
* ✨ SOP-11105 价签申请模块增加规格单位展示并支持精确查询 ([f9e159d](https://gitlab.hd123.com/vue/soa/commit/f9e159d28c3f26e70d50a6e33bf4e5bbd2d03c12))
* ✨ SOP-11125 收货编辑散称字段显示 ([770f16e](https://gitlab.hd123.com/vue/soa/commit/770f16eaa7a2bea88cf50ebacbc4846799178c19))
* ✨ SOP-11129 质量反馈部分手机型号无法输入小数点 ([095e76e](https://gitlab.hd123.com/vue/soa/commit/095e76e857f7ffb13aac44c2ba6f21f1296ab4f8))
* ✨ SOP-11129 质量反馈部分手机型号无法输入小数点 ([867e494](https://gitlab.hd123.com/vue/soa/commit/867e4941047352eac785e527d8be59a674648878))
* ✨ SOP-11135 商品选择分类页签按H6要货面板顺序生效+支持无商品明细页签不展示配合接口调整 ([fd0f488](https://gitlab.hd123.com/vue/soa/commit/fd0f48869a1a498f4b232ea2bf1ab18d06539bbb))
* ✨ SOP-11136 退供应商、退仓、退公司支持查看视频和图片 ([40b8ce9](https://gitlab.hd123.com/vue/soa/commit/40b8ce99ceb4db4f2c7e8e2dfc5d067ed0240071))
* ✨ SOP-11137 小程序.门店助手.加工支持按配置不允许添加配方范围外的商品 ([1bb7191](https://gitlab.hd123.com/vue/soa/commit/1bb71919ff1ef7d0de1e69b7eb621c497532fcb8))
* ✨ SOP-11142 预订货订单查看跳转 ([eac68fc](https://gitlab.hd123.com/vue/soa/commit/eac68fcb471728e4ad240a1a3fc1e141a3ee4959))
* ✨ SOP-11151 路由变化时添加时间戳参数 ([63f7ede](https://gitlab.hd123.com/vue/soa/commit/63f7ede2a9a60e2bda05398a6433f704bb7aa07e))
* ✨ SOP-11151 移动报表扫码微信小程序端优化 ([136c911](https://gitlab.hd123.com/vue/soa/commit/136c911c70ca36f908fb27792c8dadb2222db0eb))
* ✨ SOP-11152 门店助手上下限调整受到配货规格控制.增加控制选项 ([22ef6a0](https://gitlab.hd123.com/vue/soa/commit/22ef6a0e5d1d58b0e9c00a6e06b4a9faa9e63c31))
* ✨ SOP-11152 门店助手上下限调整受到配货规格控制.增加控制选项 ([ba88780](https://gitlab.hd123.com/vue/soa/commit/ba887807dcf5ff23c85fb2fe70ddcbe91b473d01))
* ✨ SOP-11170 预订货模块问题修改 ([19700f5](https://gitlab.hd123.com/vue/soa/commit/19700f58ee6c4f4a537a112dcc49a1927af2ac3f))
* ✨ SOP-11173 检查更新时上报租户、登录账号和门店id ([99df44f](https://gitlab.hd123.com/vue/soa/commit/99df44f6f5d84a66bec5dd941557082bd2af2275))
* ✨ SOP-11176 质量反馈搜索时不隐藏提交按钮 ([471cbb3](https://gitlab.hd123.com/vue/soa/commit/471cbb3076a70eebf4a9056e0b5d978619a201f2))
* ✨ SOP-11176 质量反馈增加安全区 ([a1d9030](https://gitlab.hd123.com/vue/soa/commit/a1d90301d26dfc047de137b757c5c67791ab33aa))
* ✨ SOP-11177 修复钉钉小程序订货时上拉下拉切换分类过于灵敏的问题 ([90562fd](https://gitlab.hd123.com/vue/soa/commit/90562fd6c50d9f8dd34ba38b9de1ae54d68e85e7))
* ✨ SOP-11178 门店建议叫货单按照自动配货规则选项进行控制.操作优化 ([28c069a](https://gitlab.hd123.com/vue/soa/commit/28c069a9da992e4db4e8f629d9f5432ac7fd8f33))
* ✨ SOP-11179 退仓显示金额优化 ([82c02e4](https://gitlab.hd123.com/vue/soa/commit/82c02e4f9df5077d2c54d55c142a52b2ff982bac))
* ✨ SOP-11180 新增预订货活动首页弹窗 ([31c16c8](https://gitlab.hd123.com/vue/soa/commit/31c16c84278784d46033104888162f12e3570809))


### Bug Fixes

* 🐛 订货活动页面，小程序和app，样式问题兼容处理 ([d987d78](https://gitlab.hd123.com/vue/soa/commit/d987d782cf32d052b3daedebccbbf04d3cc281cb))
* 🐛 建议数量与可订数量判断修改 ([96129c8](https://gitlab.hd123.com/vue/soa/commit/96129c88c2afc1121e9fa854ec421df8c787873a))
* 🐛 普通折扣选择商品分类popup流畅度优化 ([3761157](https://gitlab.hd123.com/vue/soa/commit/37611571182d4452db7a70f5e56150e2cd22304f))
* 🐛 启动页增加权限获取 ([3defaa2](https://gitlab.hd123.com/vue/soa/commit/3defaa235c4a9d59cf399c2e0db73dcfbd130f27))
* 🐛 收货差异详情页，审批数量/审批金额，过长的时候，小程序折行显示，APP没有换行 ([0e919a6](https://gitlab.hd123.com/vue/soa/commit/0e919a69ddffb9e56e98823b2040eea9d0630b55))
* 🐛 跳转盘点模块做特殊的逻辑处理，代码优化 ([dd7f6a6](https://gitlab.hd123.com/vue/soa/commit/dd7f6a6281f0f31348546b22d0f4cc6908bf87a3))
* 🐛 新增直送收货页面，选择商品后没有显示商品进货价 ([0336205](https://gitlab.hd123.com/vue/soa/commit/03362054a152e58f058799d693270a2ebb9774ab))
* 🐛 修复隆玛特按箱收货拆零箱数量计算未应用全局小数位数配置 ([2d08a2c](https://gitlab.hd123.com/vue/soa/commit/2d08a2c3c0608400490abc29ecd1dd5c8195e2e4))
* 🐛 修复我的页面显示关于样式异常的问题 ([ce7f532](https://gitlab.hd123.com/vue/soa/commit/ce7f53257b065d4ff78c26fc6b0d10a97a6012d3))
* 🐛 营销玩法 有商品分类选择框的，对弹出popup都做交互优化 ([6107538](https://gitlab.hd123.com/vue/soa/commit/61075386e25fae50bdcdadbc17e9a8333488d4cf))
* 🐛 质量反馈 模块，线下文案在app上颜色显示错误 ([3990ee3](https://gitlab.hd123.com/vue/soa/commit/3990ee364bd6a67b3d79a9c7e86b907bab0eb497))
* 🐛 app 直送退货模块，对方单号栏placeholder样式问题优化 ([e44936f](https://gitlab.hd123.com/vue/soa/commit/e44936f0478acccde8997a2a5d28c83ae175a459))
* 🐛 app端历史反馈记录，滑动页面样式问题调整 ([9ca016b](https://gitlab.hd123.com/vue/soa/commit/9ca016bf0aaaed726e7acda48025e44027469842))
* 🐛 app上对于组件传入得空字符串得识别问题 ([557a29b](https://gitlab.hd123.com/vue/soa/commit/557a29b7288e9afb56f87bee6d2827972475ec8a))
* 🐛 hd-number-box-test组件在iphone X上不支持digit类型问题调整 ([308c442](https://gitlab.hd123.com/vue/soa/commit/308c442c74167c8c6c868c857d2982dfc95ad224))
* 🐛 IPHONE X兼容问题调整2 ([a4a1d35](https://gitlab.hd123.com/vue/soa/commit/a4a1d350415157d2f748648993e46e8329af4106))
* 🐛 iphoneX的兼容问题调整 ([75aa1ab](https://gitlab.hd123.com/vue/soa/commit/75aa1ab5c5e4bee6a5617168ca408c070bee1dd9))
* 🐛 routeRelay 页面增加跳转页是盘点的判断，是盘点弹出选择框，不直接跳转页面 ([d962079](https://gitlab.hd123.com/vue/soa/commit/d962079e15dccca35396b9a7df1e6e857341bfe3))
* 🐛 routeRelay新增几个配置的获取 ([fe40c8a](https://gitlab.hd123.com/vue/soa/commit/fe40c8a918c31e26debb19901cd88480570f0969))
* 🐛 SOP-10912 修复诚信志远订货首页投放类目跳转时未切换到指定类目的问题 ([c74cd05](https://gitlab.hd123.com/vue/soa/commit/c74cd05996e645ddae148762418920b6b61563aa))
* 🐛 SOP-10912 修复诚信志远订货首页样式scroll-view长度异常的问题 ([eb92b4e](https://gitlab.hd123.com/vue/soa/commit/eb92b4ed0695b0ec7c61fed77a85b6cb38dcbedf))
* 🐛 SOP-10942 修改可订货范围 ([d5638ce](https://gitlab.hd123.com/vue/soa/commit/d5638ce308a011b9d81b103a05d8b40df0001da1))
* 🐛 SOP-10942 修改可订货范围提示 ([a2ef132](https://gitlab.hd123.com/vue/soa/commit/a2ef1323f1cbd0fdd02a851c1f3e992efa258f53))
* 🐛 SOP-11022 按箱收货支持显示赠品 ([26af3ca](https://gitlab.hd123.com/vue/soa/commit/26af3cab04686087c9597a5224d5b12079998779))
* 🐛 SOP-11022 修复正常收货的赠品保存后数量复显不正确的问题 ([0bef5bd](https://gitlab.hd123.com/vue/soa/commit/0bef5bd5c5a9d06dd9236d58ec568609de0485b9))
* 🐛 SOP-11127 按箱收货拆零箱确认收货时应将收货箱数赋值为发货箱数 ([4eb6c3c](https://gitlab.hd123.com/vue/soa/commit/4eb6c3cb0cc164e871c67603be1753e9a16c4e44))
* 🐛 SOP-11136 图片展示问题修复 ([9724685](https://gitlab.hd123.com/vue/soa/commit/9724685a28cbc18c0757f97494a31bcaac43591d))
* 🐛 SOP-11137 加工模块使用cds过滤器优化 ([148378f](https://gitlab.hd123.com/vue/soa/commit/148378f22c8c9ae724504cfa729a63d03ac57733))
* 🐛 SOP-11166 新增预订货活动模块问题修改 ([b0da63b](https://gitlab.hd123.com/vue/soa/commit/b0da63b04a2b30ac0f88b9d749c34351dc71d61a))
* 🐛 SOP-11170 新增预订货订单模块.界面修改 ([f846dfc](https://gitlab.hd123.com/vue/soa/commit/f846dfcea213c8eb74a0eedda1e2d45935674dff))
* 🐛 SOP-11170 预订货问题修复 ([3858314](https://gitlab.hd123.com/vue/soa/commit/38583142d4df839a84c483a38d14ace3cd81e4e7))
* 🐛 SOP-11174 解决诚信致远订货编辑商品行提示当前单据不存在的问题 ([cd6880c](https://gitlab.hd123.com/vue/soa/commit/cd6880c4b0600dd264123b682b1a9ed18a44c2b3))

### [2.51.5](https://gitlab.hd123.com/vue/soa/compare/v2.51.4...v2.51.5) (2024-11-15)


### Features

* ✨ SOP-11129 质量反馈部分手机型号无法输入小数点 ([cfdc033](https://gitlab.hd123.com/vue/soa/commit/cfdc0330d3565af31af0420d7b5a71e2676c4d86))

### [2.51.4](https://gitlab.hd123.com/vue/soa/compare/v2.51.3...v2.51.4) (2024-11-15)


### Bug Fixes

* 🐛 SOP-11127 按箱收货拆零箱确认收货时应将收货箱数赋值为发货箱数 ([29ff1bb](https://gitlab.hd123.com/vue/soa/commit/29ff1bb4bfe02e05ed3a5a5129720c7cac4cdb0e))

### [2.51.3](https://gitlab.hd123.com/vue/soa/compare/v2.51.2...v2.51.3) (2024-11-11)


### Features

* ✨ SOP-11125 收货编辑散称字段显示 ([e426dbb](https://gitlab.hd123.com/vue/soa/commit/e426dbbf510a84d16c96ec633eeba1bb531f8bb4))

### [2.51.2](https://gitlab.hd123.com/vue/soa/compare/v2.51.1...v2.51.2) (2024-11-11)


### Features

* ✨ SOP-11103 调整正常订货加载商品时不展示骨架屏改为显示加载loading ([5bca341](https://gitlab.hd123.com/vue/soa/commit/5bca341a24bf218bed423b2ee36b3965e4cd3fb3))

### [2.51.1](https://gitlab.hd123.com/vue/soa/compare/v2.51.0...v2.51.1) (2024-11-05)


### Features

* ✨ SOP-11091 收货评价保存时如果用户登录信息不存在mobile时给memberTel传- ([54c4eaf](https://gitlab.hd123.com/vue/soa/commit/54c4eaf3acb2ddcb0abff0a23ffaeff4280049a2))


### Bug Fixes

* 🐛 修复隆玛特按箱收货无法分页加载箱数据的问题 ([708f844](https://gitlab.hd123.com/vue/soa/commit/708f84495cd99036f3096759f4fdb9f7b87502db))
* 🐛 TM-25011 修复报损原因查询接口传入[]时报错的问题 ([ced2571](https://gitlab.hd123.com/vue/soa/commit/ced2571356b95bc8e51b1c7089fd5e78ec080ec2))

## [2.51.0](https://gitlab.hd123.com/vue/soa/compare/v2.50.1...v2.51.0) (2024-10-31)


### Features

* ✨ 11024陈列位置需求分支合并 ([3547064](https://gitlab.hd123.com/vue/soa/commit/3547064a15ebbab59f99b673277c0fe3348564d9))
* ✨ 修复合并兼容H5分支产生的pages.json编译错误问题 ([a72aa6a](https://gitlab.hd123.com/vue/soa/commit/a72aa6a60d45929362660740058e3889b5445651))
* ✨ SOP-10834 尝试适配钉钉h5的蓝牙 ([457dc60](https://gitlab.hd123.com/vue/soa/commit/457dc607b420d64695d0a3aff386decbd89d2c05))
* ✨ SOP-10834 路由切换为hash模式 ([c06da9e](https://gitlab.hd123.com/vue/soa/commit/c06da9e4281915f3aa7ed5869943906b7ed4fb5d))
* ✨ SOP-10834 门店助手h5搭建 ([df8281b](https://gitlab.hd123.com/vue/soa/commit/df8281b19e7fe4c0766555bc6e1b280c8f608c43))
* ✨ SOP-10834 适配钉钉h5应用的API ([03737a7](https://gitlab.hd123.com/vue/soa/commit/03737a74c9aab46c4fd8ce300d3fa34cfb778ca2))
* ✨ SOP-10834 提供ddH5Adapter支持钉钉h5调用扫码 ([1a78aa7](https://gitlab.hd123.com/vue/soa/commit/1a78aa7abab21d67d60a844f1af86a0b5ce39800))
* ✨ SOP-10834 添加运行的基础路径 ([2b96f0a](https://gitlab.hd123.com/vue/soa/commit/2b96f0a5a3fdf1694198be5888b1432bb1baee94))
* ✨ SOP-10834 重构消息列表页面 ([8e5d004](https://gitlab.hd123.com/vue/soa/commit/8e5d004f53b1dc70f203d99636d1e1961c799cf2))
* ✨ SOP-10968 收货差异详情差异明细展示差异数量绝对值 ([3238298](https://gitlab.hd123.com/vue/soa/commit/3238298972c718893f7f2aaf1fcd970c7bbd9ec4))
* ✨ SOP-10968 退货申请添加视频预览 ([aef0035](https://gitlab.hd123.com/vue/soa/commit/aef0035d8ac5bb32fe23ebc35393ad8745daa735))
* ✨ SOP-10968 退货申请添加视频组件 ([fb66250](https://gitlab.hd123.com/vue/soa/commit/fb662509fb993dbe58633d64bf8960120690ddfb))
* ✨ SOP-10970 退货申请支持上传图片 ([e3dffdd](https://gitlab.hd123.com/vue/soa/commit/e3dffdd62ffa992e8f22ad12e5f01dbcdf0992ae))
* ✨ SOP-10970 退货申请支持上传图片 ([858c008](https://gitlab.hd123.com/vue/soa/commit/858c008c8d572805eb81439149ffbea49fe4726f))
* ✨ SOP-11024 收货、直送收货和复盘增加显示商品陈列位置 ([ab071e7](https://gitlab.hd123.com/vue/soa/commit/ab071e715ba3617b0a656177b51c16059d56fe39))
* ✨ SOP-11024 收货、直送收货和复盘增加显示商品陈列位置 ([5829809](https://gitlab.hd123.com/vue/soa/commit/582980990f7088bc836c664eb1310964db8f9773))
* ✨ SOP-11024 收货模块增加陈列位置信息字段 ([4521d96](https://gitlab.hd123.com/vue/soa/commit/4521d961fc622eaec864d057ee13ae45c25e1a5a))
* ✨ SOP-11025 报损报溢原因及直送界面优化需求 ([55054ac](https://gitlab.hd123.com/vue/soa/commit/55054ac599b1c123c0c216b5a0ba1aea80a871a6))
* ✨ SOP-11028 小程序.门店助手.任务及巡检数据展示问题优化 ([3f4150f](https://gitlab.hd123.com/vue/soa/commit/3f4150f12c30ee722ed92f1c6ac7fe02d72c32ca))
* ✨ SOP-11048 按箱收货支持维护差异明细 ([e95973d](https://gitlab.hd123.com/vue/soa/commit/e95973d64e8da5bb339eecf53a66fb65c611a400))
* ✨ SOP-11048 收货差异支持展示差异明细 ([41f78e0](https://gitlab.hd123.com/vue/soa/commit/41f78e07cf532e488db6620b7935b7d86d71dfe3))
* ✨ SOP-11070 走店务支付时账单待支付的消息应该跳转到v2账单详情 ([35830b7](https://gitlab.hd123.com/vue/soa/commit/35830b7132d5f3d85b0b0aa514dfe2f76b262b29))
* ✨ SOP-11081 按箱收货维护差异明细时仅支持拍照且图片必填 ([4a0d79d](https://gitlab.hd123.com/vue/soa/commit/4a0d79d93bb2e9c17d5a15a5591325064abedf0b))
* ✨ SOP-11088 收货移除receipt/sum接口的调用 ([eb94f83](https://gitlab.hd123.com/vue/soa/commit/eb94f83287bd5d64e6328954df9a0312e0d39c20))


### Bug Fixes

* 🐛 SOP-10834 修复店务商品查询商品列表无法滚动的问题 ([defb54c](https://gitlab.hd123.com/vue/soa/commit/defb54c8963e186acb295f6bf23b576190b72fcc))
* 🐛 SOP-10834 修复调价申请h5端修改价格不调save接口保存的问题 ([23fc308](https://gitlab.hd123.com/vue/soa/commit/23fc3080c46d4d1eba696adf94a35e277f3aeb43))
* 🐛 SOP-10834 修复独立订货金额栏透明度问题 ([487fc13](https://gitlab.hd123.com/vue/soa/commit/487fc1358d706f13ac9452bb1271970624533b8c))
* 🐛 SOP-10834 修复价格带单品定价无法输入数字的问题 ([df77439](https://gitlab.hd123.com/vue/soa/commit/df77439ef879b15297a7d39b3fd3d2d40e6f7dda))
* 🐛 SOP-10834 修复门店考评在钉钉h5端滚动出现2个滚动条的问题 ([605f258](https://gitlab.hd123.com/vue/soa/commit/605f25822187be73878ce268f7a7795b1d657ec8))
* 🐛 SOP-10834 修复试吃商品不允许负库存且商品库存为负时仍可添加商品数量的问题 ([b32c00a](https://gitlab.hd123.com/vue/soa/commit/b32c00ab0bc1d2e5ea6e91eff47a17df9294ee3a))
* 🐛 SOP-10834 修复质量反馈成功跳转详情无反应的问题 ([0deefe9](https://gitlab.hd123.com/vue/soa/commit/0deefe9b734e8116bbfa86161e1a089b96772804))
* 🐛 SOP-10834 修复H5调拨申请编辑页面提交按钮被遮挡的问题 ([086a0cf](https://gitlab.hd123.com/vue/soa/commit/086a0cf7b1ddd0343473b86768532a630bf5b6f4))
* 🐛 SOP-10834 修复H5端文件登记详情页面按钮显示异常的问题 ([5a2a0fc](https://gitlab.hd123.com/vue/soa/commit/5a2a0fc396bd2b8fd24a64a8ca4468642b1e4d9b))
* 🐛 SOP-10834 修复H5在iOS端无法预览文件的问题 ([b8c9c68](https://gitlab.hd123.com/vue/soa/commit/b8c9c682ca05d9ec1b8b0c30641764e3c17bf1ff))
* 🐛 SOP-10970 退货申请支持上传图片问题修复 ([08829d9](https://gitlab.hd123.com/vue/soa/commit/08829d9c52d963a9383a49230f783cc1a41f1564))
* 🐛 SOP-11048 按箱收货搜索页面支持填写差异明细 ([8815299](https://gitlab.hd123.com/vue/soa/commit/88152994ff700d19549cd0ebaa583dfebb46f7b3))
* 🐛 SOP-11049 修复钉钉h5标题展示上一个页面的问题 ([e430b97](https://gitlab.hd123.com/vue/soa/commit/e430b9765f37f1d0f40579e489a934b49ebb1ac1))
* 🐛 SOP-11049 修复钉钉h5扫码异常的问题 ([1ef49d4](https://gitlab.hd123.com/vue/soa/commit/1ef49d4f03f1bf7c6bf952fa6c13f3c86be8e682))
* 🐛 SOP-11049 修复钉钉h5设置页面标题异常的问题 ([8d899c4](https://gitlab.hd123.com/vue/soa/commit/8d899c4cf8b47028b45dcbaa78e99293ace86f48))
* 🐛 SOP-11049 修复直送收货兼容h5的问题 ([e75922b](https://gitlab.hd123.com/vue/soa/commit/e75922be1c380cb7b4bc552007f31a2f3d959cff))
* 🐛 SOP-11049 修复直送退货搜索商品显示规格样式异常的问题 ([87386f9](https://gitlab.hd123.com/vue/soa/commit/87386f9bc0a819ca481b609b226b93534c801f41))
* 🐛 SOP-11049 修复质量反馈兼容钉钉h5的问题 ([1aaf245](https://gitlab.hd123.com/vue/soa/commit/1aaf245f17ce60c9b6a402024eacca7c2cb3c7d9))
* 🐛 SOP-11049 修复h5无法预览视频的问题 ([8194b82](https://gitlab.hd123.com/vue/soa/commit/8194b825dec4e4dbea004a586b2339c99b16c39d))
* 🐛 SOP-11066 修复按箱收货维护差异明细产生的问题 ([1bffe47](https://gitlab.hd123.com/vue/soa/commit/1bffe47ed725fa5556710bd59939e03ba0c5b427))
* 🐛 SOP-11066 修复收货差异详情页面无查看差异明细的提示的问题 ([3dca3c6](https://gitlab.hd123.com/vue/soa/commit/3dca3c6e4b05d109762c56e7dca9cf76212d03b7))
* 🐛 SOP-11066 修复收货上传组件显示提示错误的问题 ([de7aca2](https://gitlab.hd123.com/vue/soa/commit/de7aca25362900d86d419047da1267852f17877a))

### [2.50.1](https://gitlab.hd123.com/vue/soa/compare/v2.50.0...v2.50.1) (2024-10-24)


### Features

* ✨ SOP-10791 交投库存调整模块及调价申请模块需求开发 ([aa747e2](https://gitlab.hd123.com/vue/soa/commit/aa747e2e9fa974cf0fc5a2ab8e8e216b8bf3ad50))
* ✨ SOP-10972 调价申请修改uuid为inputinputCode ([60a8800](https://gitlab.hd123.com/vue/soa/commit/60a8800b33bcd7bf569bfb15931c10e6a6a8af0a))
* ✨ SOP-10972 调价申请支持按配置价格调价 ([f6b9802](https://gitlab.hd123.com/vue/soa/commit/f6b980299eecc25463cda3fda94bbce2eaeef218))
* ✨ SOP-10972 调价申请支持按配置价格调价 ([6728fa1](https://gitlab.hd123.com/vue/soa/commit/6728fa199e5af642facf4389655aeb6172cfefbe))
* ✨ SOP-10972 调价申请支持按配置价格调价 ([071c276](https://gitlab.hd123.com/vue/soa/commit/071c276a2525369bbe10744170a23f1e251b55b4))
* ✨ SOP-10972 调价申请支持按配置价格调价 ([9180645](https://gitlab.hd123.com/vue/soa/commit/9180645970fcbb7d823b11527dbd5356f8590b4c))
* ✨ SOP-10972 调价申请支持按配置价格调价 ([e0e70c6](https://gitlab.hd123.com/vue/soa/commit/e0e70c685a19de6f85b48f4956011ebddb9b9df9))
* ✨ SOP-10972 调价申请支持按配置价格调价 ([3878c89](https://gitlab.hd123.com/vue/soa/commit/3878c89993cf0886d5cc0d1ddb2d32ea2ca13305))
* ✨ SOP-10972 调价申请支持按配置价格调价 ([fc72144](https://gitlab.hd123.com/vue/soa/commit/fc7214436ea840ce4b5df06775306658cb28283d))
* ✨ SOP-10972 调价申请支持按配置价格调价 ([f9006f9](https://gitlab.hd123.com/vue/soa/commit/f9006f9f3743580860e3cfd6024e9d767ad0cd7a))
* ✨ SOP-10972 售价支持下拉数据为一个默认选中 ([e8d0bc1](https://gitlab.hd123.com/vue/soa/commit/e8d0bc1e77fdd749fdfa67e1cdca53a88488cfb3))
* ✨ SOP-10973 订单详情分配属性修改 ([7ec3e86](https://gitlab.hd123.com/vue/soa/commit/7ec3e86e5963eb2876bb9044e23ce863bb13d19a))
* ✨ SOP-10973 库存上下限调整支持设置上限-下限不能小于门店商品的默认配货规格 ([e9a58b4](https://gitlab.hd123.com/vue/soa/commit/e9a58b431896d1aca0af657d4a2193cea8350b98))
* ✨ SOP-10973 库存上下限调整支持设置上限-下限不能小于门店商品的默认配货规格 ([99fa9fa](https://gitlab.hd123.com/vue/soa/commit/99fa9fa83dfa00c4cef286f10fa123205a215b54))
* ✨ SOP-10973 库存上下限调整支持设置上限-下限不能小于门店商品的默认配货规格 ([347c566](https://gitlab.hd123.com/vue/soa/commit/347c566b20dbbd55d173335cb39d6946226d55bb))
* ✨ SOP-10973 库存上下限禁止点击保存和提交 ([937f403](https://gitlab.hd123.com/vue/soa/commit/937f403b3424e37d5cbacdb6bb56b5c7f0b66f49))
* ✨ SOP-10973 替换毛利率和配货价展示判断字段 ([cf2c011](https://gitlab.hd123.com/vue/soa/commit/cf2c0111ec28241b51c50c6ccfcd33bff8d904da))
* ✨ SOP-10974 增加门店上下限统管必配选配逻辑分配属性权限控制 ([af8c144](https://gitlab.hd123.com/vue/soa/commit/af8c14423af6ea501a1f22434d0d2bdadae436e7))
* ✨ SOP-11010 售价改价需求 ([dd876a8](https://gitlab.hd123.com/vue/soa/commit/dd876a8117d8a7962ef8c4a96b3a2d6c7fd15eab))
* ✨ SOP-11041 支持物流发车单按箱收货增加筛选条件 ([79327c2](https://gitlab.hd123.com/vue/soa/commit/79327c2a178c3a680326779394dda63229396961))
* ✨ SOP-11041 支持物流发车单按箱收货增加筛选条件 ([20f55dc](https://gitlab.hd123.com/vue/soa/commit/20f55dc92aa5d994ace9335337e7e14668da9549))
* ✨ SOP-11042 调价申请支持按配置价格调价 ([abc0ece](https://gitlab.hd123.com/vue/soa/commit/abc0ece939fb8c7c5bef4a69c689cf2a2bcf5e8a))
* ✨ SOP-11044 库存上下限调整支持设置上限-下限不能小于门店商品的默认配货规格_toast提示后不允许 ([983e24c](https://gitlab.hd123.com/vue/soa/commit/983e24cdd75f2a34493dfe55f725004fd2ff26ce))
* ✨ SOP-11045 增加门店上下限统管必配选配逻辑_选配/个性商品当库存上下限一致时提示 ([c5c0865](https://gitlab.hd123.com/vue/soa/commit/c5c08656c1b573fe33c0284fbf0a469c343509d5))


### Bug Fixes

* 🐛 SOP-10946 修复按箱收货拆零箱滚动高度异常的问题 ([58e3124](https://gitlab.hd123.com/vue/soa/commit/58e3124a69ba743afe2cc34f2f12fb2e97e1300a))
* 🐛 SOP-10946 修复按箱收货扫码搜索跳转页面地址不正确的问题 ([8f19c19](https://gitlab.hd123.com/vue/soa/commit/8f19c1955393510459e1b0359863c569480ba0b9))

## [2.50.0](https://gitlab.hd123.com/vue/soa/compare/v2.49.0...v2.50.0) (2024-10-12)


### Features

* ✨ SOP-10780 订货商品详情支持展示视频 ([7046696](https://gitlab.hd123.com/vue/soa/commit/7046696932fb65c2c36bc46e55b1e6ca91e737e2))
* ✨ SOP-10780 订货商品详情支持展示视频 ([8e3cebf](https://gitlab.hd123.com/vue/soa/commit/8e3cebf73579536182ca9eee978d5dc1a1091813))
* ✨ SOP-10880 支持上传图片/视频到京东云 ([1259c5a](https://gitlab.hd123.com/vue/soa/commit/1259c5a3388bea1b8712a3c7687c27c01978cd30))
* ✨ SOP-10931 没有可用信用额度时不展示 ([640aecb](https://gitlab.hd123.com/vue/soa/commit/640aecbfaa316d1dab5c944318177610497c137b))
* ✨ SOP-10931 账户流水部分显示内容调整 ([19a21b7](https://gitlab.hd123.com/vue/soa/commit/19a21b7c74022a344f4ed72a1e8c38f595e313ba))
* ✨ SOP-10931 账户流水改动优化首页获取配置接口调用逻辑 ([016316f](https://gitlab.hd123.com/vue/soa/commit/016316f80f0cd2a254229ab915c6da07d7efb8c0))
* ✨ SOP-10931 支持显示可用信用额度 ([dc7893f](https://gitlab.hd123.com/vue/soa/commit/dc7893f49feb4a311552e26c41fb10c382406deb))
* ✨ SOP-10931 balance、debtTotal、creditTotal分开控制 ([0f78024](https://gitlab.hd123.com/vue/soa/commit/0f78024b375195fa46e709eeb2b25378b011207b))
* ✨ SOP-10936 门店助手模块商品需要同时展示代码和条码 ([c3d3124](https://gitlab.hd123.com/vue/soa/commit/c3d31247feef4f32e39da29ad6ac93ea7157ede0))
* ✨ SOP-10936 门店助手模块商品需要同时展示代码和条码 ([b4f7ad0](https://gitlab.hd123.com/vue/soa/commit/b4f7ad074b3b8239743fbbc3c3a61ce1cc972f5a))
* ✨ SOP-10937 模板订货编辑后返回列表时更新列表数据 ([3932fc6](https://gitlab.hd123.com/vue/soa/commit/3932fc6ad87c6b95078121c0700dbd9129ec16c2))
* ✨ SOP-10937 周黑鸭订货支持订货倍数为小数 ([daf77cc](https://gitlab.hd123.com/vue/soa/commit/daf77ccab195b031dd1992964aaa60714bd17dcc))
* ✨ SOP-10944 收货详情收货完成时间显示调整 ([d14b68e](https://gitlab.hd123.com/vue/soa/commit/d14b68ec784ddbe40bf0e9f8f40d76e0f5ad71ef))
* ✨ SOP-10945 支持使用店务配置控制质量反馈是否可以上传视频 ([80cdebf](https://gitlab.hd123.com/vue/soa/commit/80cdebf8a051e46abc262fe5dde8291da0317b96))
* ✨ SOP-10946 按箱收货显示优化 ([fb81545](https://gitlab.hd123.com/vue/soa/commit/fb8154504d84772f2892c48d54648be1b7016c12))
* ✨ SOP-10946 收货差异详情查询支持dataSource参数 ([82d51fa](https://gitlab.hd123.com/vue/soa/commit/82d51fa43dadfec5e517e3e293f6b4e0ae1addd8))
* ✨ SOP-10946 支持物流发车单按箱收货 ([31154a0](https://gitlab.hd123.com/vue/soa/commit/31154a0a68392e81a5c3111b29dcd5ae2249bacf))
* ✨ SOP-10952 代码，条码超长省略 ([fbed115](https://gitlab.hd123.com/vue/soa/commit/fbed115d53fdd5eed438d54d513ef7a254ab5b3b))
* ✨ SOP-10952 高亮条件下的省略显示 ([2b1766d](https://gitlab.hd123.com/vue/soa/commit/2b1766d9a2c48e0880874af92fca2c9afae7c729))
* ✨ SOP-10952 门店助手模块商品需要同时展示代码和条码.超长显示省略 ([1b84d2d](https://gitlab.hd123.com/vue/soa/commit/1b84d2d76479d7a28611aaacb7d7ce1b78da2ffe))
* ✨ SOP-10952 修改代码、条码在分类查询中的显示的长度 ([9692855](https://gitlab.hd123.com/vue/soa/commit/96928556f96b3e77d6058e80253c98b7627c65ab))
* ✨ SOP-10992 小数位配置优化 ([26d4f88](https://gitlab.hd123.com/vue/soa/commit/26d4f88c79fc3c903246ecbdcc263aa447a54aff))
* ✨ SOP-10992 支持录入数量的小数位配置 ([8762911](https://gitlab.hd123.com/vue/soa/commit/8762911838f6228fc3fddfb839423ebed7ef1732))
* ✨ SOP-11001 周黑鸭模板订货时支持无订货倍数时任意输入小数 ([a095d20](https://gitlab.hd123.com/vue/soa/commit/a095d201d6dfc1a4d92057b5e7c0e79fe0d2570d))
* ✨ SOP-11009 小程序.门店助手.试吃赠送非气调品校验黑名单时机与黑名单提示修改 - P2 ([17ec281](https://gitlab.hd123.com/vue/soa/commit/17ec2814283ba4d3998801dacd42698bf98fde13))


### Bug Fixes

* 🐛 试吃黑名单可继续销售显示错误 ([8627db4](https://gitlab.hd123.com/vue/soa/commit/8627db4785fb5cbbdfe1e9de88d5bc4e1c970178))
* 🐛 试吃黑名单提示优化 ([01ab38f](https://gitlab.hd123.com/vue/soa/commit/01ab38fab5094e7f3d4d293a8515b0d6377bba55))
* 🐛 SOP-10780 订货活动页详情商品主图设置 ([2dd7c9f](https://gitlab.hd123.com/vue/soa/commit/2dd7c9f4b55e90f8c7a1f4dd42149059ac258ea3))
* 🐛 SOP-10780 订货商品详情支持展示视频 ([d92499b](https://gitlab.hd123.com/vue/soa/commit/d92499b55ef5a3a1694f50ed6706ededed512cfd))
* 🐛 SOP-10946 按箱收货编辑弹框支持显示箱码 ([b780c60](https://gitlab.hd123.com/vue/soa/commit/b780c608fcf3f64e03946f95e9ecc089df8bce13))
* 🐛 SOP-10946 按箱收货待收数量更新逻辑优化 ([ae63ba5](https://gitlab.hd123.com/vue/soa/commit/ae63ba58623968b072da76a1501b12f7bd2608f1))
* 🐛 SOP-10946 支持显示待收数量 ([01c89de](https://gitlab.hd123.com/vue/soa/commit/01c89de417e2b898dad144cc56d0a2d3ed7ff1a0))
* 🐛 SOP-11001 修复无倍数时无法加减录入数字 ([585442a](https://gitlab.hd123.com/vue/soa/commit/585442a4cd43121b48aaf6f259c06315dc9517c4))
* 🐛 SOP-11007 修复试吃模块金额计算未四舍五入保留2位小数的问题 ([5b9382b](https://gitlab.hd123.com/vue/soa/commit/5b9382b9c1fda5541e4d8a7b1f0f2cd8cdaeab44))
* 🐛 SOP-11007 修复试吃模块金额计算未四舍五入保留2位小数的问题 ([1488b19](https://gitlab.hd123.com/vue/soa/commit/1488b1989209b93d6518aab47fbbd27dc8e8086d))

## [2.49.0](https://gitlab.hd123.com/vue/soa/compare/v2.48.4...v2.49.0) (2024-09-02)


### Features

* ✨ SOP-10676 独立订货无库存商品沉底显示并且不可订货 ([e73f2b0](https://gitlab.hd123.com/vue/soa/commit/e73f2b0013139c5fc7e1d6ea2cbd9d0fb2e74a2d))
* ✨ SOP-10853 调整获取门店运营系统设置中门店助手字段控制的接口 ([d6c6eb2](https://gitlab.hd123.com/vue/soa/commit/d6c6eb2d4a178d7f8a95bbbef7705f6d17678395))
* ✨ SOP-10855 集成移动报表H5 ([9939af0](https://gitlab.hd123.com/vue/soa/commit/9939af07c0abaa610b657b7b7bce2f06f124d97d))
* ✨ SOP-10855 修复BIURL可能为空 ([80d837f](https://gitlab.hd123.com/vue/soa/commit/80d837f80856f5fe31dea9fcc887be02a5bf5094))
* ✨ SOP-10864 调拨申请调整调出单作废时不限制发起方为当前门店 ([b7f972e](https://gitlab.hd123.com/vue/soa/commit/b7f972e86dfabaaeb55ed2a975494b5b664eece7))
* ✨ SOP-10867 将node-sass迁移到sass解决node-sass的安装困难问题 ([f641a6f](https://gitlab.hd123.com/vue/soa/commit/f641a6f08da35454dafd9b5e932af0e838a7145b))
* ✨ SOP-10867 将node-sass迁移到sass修复sass问题 ([b9cd091](https://gitlab.hd123.com/vue/soa/commit/b9cd091d5f228dc52518e6dfbd4edb4066b7067b))
* ✨ SOP-10868 从列表进入详情后返回刷新的逻辑优化 ([0dbbe21](https://gitlab.hd123.com/vue/soa/commit/0dbbe21a9fcdd909309d556acc5c21284b738e56))
* ✨ SOP-10868 从列表进入详情后返回刷新的逻辑优化 ([2cdbdf0](https://gitlab.hd123.com/vue/soa/commit/2cdbdf0f1ddbc5542f32fbbdb41525292d31f741))
* ✨ SOP-10868 从列表进入详情后返回刷新的逻辑优化 ([a6adfe2](https://gitlab.hd123.com/vue/soa/commit/a6adfe2d01a1b34f993995c27b56e8bff4979ad3))
* ✨ SOP-10869 盘点模块商品录入弹框统一既支持覆盖也支持累加 ([d44049e](https://gitlab.hd123.com/vue/soa/commit/d44049edf2fcc0b56bcb9c12d46970cfcef8f35d))
* ✨ SOP-10869 盘点模块商品录入弹框统一既支持覆盖也支持累加 ([1a9290f](https://gitlab.hd123.com/vue/soa/commit/1a9290fe50fb93532fb9dc7aa57da3d69ee59688))
* ✨ SOP-10873 加盟费用-新增页-选择日期  ios无法兼容只传年和月 ([4629a8f](https://gitlab.hd123.com/vue/soa/commit/4629a8ff12a2331bfe8bc83caf458a63170e47fa))
* ✨ sop-10873 加盟费用的记账类目去掉（费用卡片组件） ([258f8ce](https://gitlab.hd123.com/vue/soa/commit/258f8cef19dca10a1b37816b979883fa7821c59f))
* ✨ SOP-10873 加盟费用的记账类目直接去掉 ([72cab7c](https://gitlab.hd123.com/vue/soa/commit/72cab7c6848570bb675a2bbcf3ff5edeec7b439b))
* ✨ SOP-10876 调查类任务.提交增加批量保存接口 ([763e6a8](https://gitlab.hd123.com/vue/soa/commit/763e6a8a9d395e4c49d148dc15d672df194d5165))
* ✨ SOP-10886 门店助手的自主盘点功能，没有开放提交权限，但仍可以进行结束盘点 ([f861096](https://gitlab.hd123.com/vue/soa/commit/f8610963fc8d51d98c91c1fbb0be19d0516aa082))
* ✨ SOP-10889 配货差异申请添加商品列表增加原单商品分类 ([52d517d](https://gitlab.hd123.com/vue/soa/commit/52d517d2bcbedffd55d7fbe88b77c43f54a1dbec))
* ✨ SOP-10890 批次调整提交关闭前端校验，采用后端返回 ([0784571](https://gitlab.hd123.com/vue/soa/commit/07845716a7ee6157650989a1b453e0c3e5f25625))
* ✨ SOP-10890 批次调整直观展示存在未生效的单据 ([f8558b7](https://gitlab.hd123.com/vue/soa/commit/f8558b79499a4892d407118ff2b5d343b0064599))
* ✨ SOP-10920 自主盘点单列表与详情界面改造 ([9b7223f](https://gitlab.hd123.com/vue/soa/commit/9b7223fceebe873ef504c59299b1d944e48607a4))


### Bug Fixes

* 🐛 SOP-10754 包材模块插槽在钉钉不显示的问题 ([bcb836b](https://gitlab.hd123.com/vue/soa/commit/bcb836be76d88c4af114c438356e5030845bfd8f))
* 🐛 SOP-10862 调整调拨详情的页面底部展示问题 ([3006407](https://gitlab.hd123.com/vue/soa/commit/3006407883da09eea4eaa2efb8f5aac66ef7c527))
* 🐛 SOP-10863 包材调拨模块详情没有包材时去掉tab栏位 ([04a389c](https://gitlab.hd123.com/vue/soa/commit/04a389cc5c427e2ef59c2fd7a690764b40f93ed8))
* 🐛 SOP-10869 对于非散称的商品只能输入整数。 ([399e92b](https://gitlab.hd123.com/vue/soa/commit/399e92bcd78e843b42dcc6096626e03dad4edd79))
* 🐛 SOP-10875 自主盘点录入数量既支持增量也支持覆盖.操作优化 ([b6b1b46](https://gitlab.hd123.com/vue/soa/commit/b6b1b46c8042e62ec072d98da54aa5ce20f93591))
* 🐛 SOP-10876 任务模块修改类目显示 ([c62ac88](https://gitlab.hd123.com/vue/soa/commit/c62ac88ff68b132e8fa834fff1fbdf32c745e2de))
* 🐛 SOP-10877 盘点模块商品录入弹框要求非散称商品录入盘点数量时只能输入整数 ([25e2e28](https://gitlab.hd123.com/vue/soa/commit/25e2e28a48bf282b7e93e3f7e7d3afccbca84e45))
* 🐛 SOP-10896 盘点模块不停业盘点中进行中和已结束未编辑的情况下，返回列表没有定位到上次浏览的位置 ([3f448f1](https://gitlab.hd123.com/vue/soa/commit/3f448f18de3e9b856e05564b08ecb4f9b6d89b8e))
* 🐛 SOP-10913 修复钉钉平台调价申请售价和会员价都为0的单据无法调价的问题 ([ad76247](https://gitlab.hd123.com/vue/soa/commit/ad7624736a9e245f7343e7239ae71c0d8189f618))

### [2.48.3](https://gitlab.hd123.com/vue/soa/compare/v2.48.0...v2.48.3) (2024-08-14)
### [2.48.4](https://gitlab.hd123.com/vue/soa/compare/v2.48.3...v2.48.4) (2024-08-15)


### Bug Fixes

* 🐛 SOP-10754 包材清空 ([3580454](https://gitlab.hd123.com/vue/soa/commit/358045436565f61897a616a69858e42f4d1145d3))

### [2.48.3](https://gitlab.hd123.com/vue/soa/compare/v2.48.1...v2.48.3) (2024-08-14)


### Bug Fixes

* 🐛 SOP-10754 包材模块插槽在钉钉不显示的问题 ([fe8b704](https://gitlab.hd123.com/vue/soa/commit/fe8b704c141a83a692860216f520e0516fb55517))

### [2.48.1](https://gitlab.hd123.com/vue/soa/compare/v2.48.0...v2.48.1) (2024-08-09)


### Features

* ✨ SOP-10678 独立订货商品分类面板支持上拉下拉切换分类 ([bd47a2c](https://gitlab.hd123.com/vue/soa/commit/bd47a2cf6f551ff1e6d175035d5716a7d71f96df))
* ✨ SOP-10678 独立订货商品分类面板支持上拉下拉切换分类改进 ([e4d2055](https://gitlab.hd123.com/vue/soa/commit/e4d2055f2061ce11efffb8ee44fe758115ee0263))
* ✨ SOP-10754 新增包材调拨模块 ([7d197b5](https://gitlab.hd123.com/vue/soa/commit/7d197b52a9f59304d7ea2da5dac1c23993828066))
* ✨ SOP-10853 调整获取门店运营系统设置中门店助手字段控制的接口 ([d6c6eb2](https://gitlab.hd123.com/vue/soa/commit/d6c6eb2d4a178d7f8a95bbbef7705f6d17678395))
* ✨ SOP-10855 集成移动报表H5 ([9939af0](https://gitlab.hd123.com/vue/soa/commit/9939af07c0abaa610b657b7b7bce2f06f124d97d))
* ✨ SOP-10855 修复BIURL可能为空 ([80d837f](https://gitlab.hd123.com/vue/soa/commit/80d837f80856f5fe31dea9fcc887be02a5bf5094))
* ✨ SOP-10864 调拨申请调整调出单作废时不限制发起方为当前门店 ([b7f972e](https://gitlab.hd123.com/vue/soa/commit/b7f972e86dfabaaeb55ed2a975494b5b664eece7))
* ✨ SOP-10867 将node-sass迁移到sass解决node-sass的安装困难问题 ([f641a6f](https://gitlab.hd123.com/vue/soa/commit/f641a6f08da35454dafd9b5e932af0e838a7145b))
* ✨ SOP-10867 将node-sass迁移到sass修复sass问题 ([b9cd091](https://gitlab.hd123.com/vue/soa/commit/b9cd091d5f228dc52518e6dfbd4edb4066b7067b))
* ✨ SOP-10869 盘点模块商品录入弹框统一既支持覆盖也支持累加 ([d44049e](https://gitlab.hd123.com/vue/soa/commit/d44049edf2fcc0b56bcb9c12d46970cfcef8f35d))
* ✨ SOP-10869 盘点模块商品录入弹框统一既支持覆盖也支持累加 ([1a9290f](https://gitlab.hd123.com/vue/soa/commit/1a9290fe50fb93532fb9dc7aa57da3d69ee59688))
* ✨ SOP-10873 加盟费用-新增页-选择日期  ios无法兼容只传年和月 ([4629a8f](https://gitlab.hd123.com/vue/soa/commit/4629a8ff12a2331bfe8bc83caf458a63170e47fa))
* ✨ sop-10873 加盟费用的记账类目去掉（费用卡片组件） ([258f8ce](https://gitlab.hd123.com/vue/soa/commit/258f8cef19dca10a1b37816b979883fa7821c59f))
* ✨ SOP-10873 加盟费用的记账类目直接去掉 ([72cab7c](https://gitlab.hd123.com/vue/soa/commit/72cab7c6848570bb675a2bbcf3ff5edeec7b439b))
* ✨ SOP-10876 调查类任务.提交增加批量保存接口 ([763e6a8](https://gitlab.hd123.com/vue/soa/commit/763e6a8a9d395e4c49d148dc15d672df194d5165))
* ✨ SOP-10754 冲突合并 ([bc475f6](https://gitlab.hd123.com/vue/soa/commit/bc475f60679c35a196ecf0ad0b3feb67ec5e6699))
* ✨ SOP-10754 新增包材调拨模块 ([7d197b5](https://gitlab.hd123.com/vue/soa/commit/7d197b52a9f59304d7ea2da5dac1c23993828066))
* ✨ SOP-10754 新增包材调拨模块 ([9c452fe](https://gitlab.hd123.com/vue/soa/commit/9c452feeee03554db93d1a9ea9b415e9b3c0ebb0))
* ✨ SOP-10754 新增包材调拨模块 ([ecdb48a](https://gitlab.hd123.com/vue/soa/commit/ecdb48a53b757921445dc0001a6e5e544a143af1))
* ✨ SOP-10754 新增包材调拨模块 ([568ab00](https://gitlab.hd123.com/vue/soa/commit/568ab005d8ac4600b61bb37447beffe424957dec))
* ✨ SOP-10754 新增包材调拨模块 ([bf09da2](https://gitlab.hd123.com/vue/soa/commit/bf09da26c37c99e19118fbbc765192378d6e724e))
* ✨ SOP-10754 新增包材调拨模块开发 ([2aa8377](https://gitlab.hd123.com/vue/soa/commit/2aa8377e218aca8df603d88f3d0f4df2e49b2509))
* ✨ SOP-10754 新增包材调拨模块修改金额位数以及收货修改数量金额 ([94c3103](https://gitlab.hd123.com/vue/soa/commit/94c310306e2b9c18a455b542588c5916b3d47556))
* ✨ SOP-10782 增加摊主认证和提现功能 ([edfa2d3](https://gitlab.hd123.com/vue/soa/commit/edfa2d341f29fb07ac201313270643726d601f60))
* ✨ SOP-10864 调拨申请调整调出单作废时不限制发起方为当前门店 ([dbbd601](https://gitlab.hd123.com/vue/soa/commit/dbbd601e3eb2f2d1ddd6f136b5f42f75290cffd0))


### Bug Fixes

* 🐛 SOP-10754 包材模块插槽在钉钉不显示的问题 ([bcb836b](https://gitlab.hd123.com/vue/soa/commit/bcb836be76d88c4af114c438356e5030845bfd8f))
* 🐛 SOP-10862 调整调拨详情的页面底部展示问题 ([3006407](https://gitlab.hd123.com/vue/soa/commit/3006407883da09eea4eaa2efb8f5aac66ef7c527))
* 🐛 SOP-10863 包材调拨模块详情没有包材时去掉tab栏位 ([04a389c](https://gitlab.hd123.com/vue/soa/commit/04a389cc5c427e2ef59c2fd7a690764b40f93ed8))
* 🐛 SOP-10869 对于非散称的商品只能输入整数。 ([399e92b](https://gitlab.hd123.com/vue/soa/commit/399e92bcd78e843b42dcc6096626e03dad4edd79))
* 🐛 SOP-10875 自主盘点录入数量既支持增量也支持覆盖.操作优化 ([b6b1b46](https://gitlab.hd123.com/vue/soa/commit/b6b1b46c8042e62ec072d98da54aa5ce20f93591))
* 🐛 SOP-10876 任务模块修改类目显示 ([c62ac88](https://gitlab.hd123.com/vue/soa/commit/c62ac88ff68b132e8fa834fff1fbdf32c745e2de))
* 🐛 SOP-10877 盘点模块商品录入弹框要求非散称商品录入盘点数量时只能输入整数 ([25e2e28](https://gitlab.hd123.com/vue/soa/commit/25e2e28a48bf282b7e93e3f7e7d3afccbca84e45))
* 🐛 SOP-10862 调整调拨详情的页面底部展示问题 ([013c3c8](https://gitlab.hd123.com/vue/soa/commit/013c3c8f81f1aa1c6dfc0abd7101d043c8686090))
* 🐛 SOP-10862 新增包材调拨模块.页面显示问题 ([5104773](https://gitlab.hd123.com/vue/soa/commit/5104773c630caeab741da97fcb1c8557d4bf5172))
* 🐛 SOP-10862 新增包材调拨模块.页面显示问题 ([0619f93](https://gitlab.hd123.com/vue/soa/commit/0619f93f1b1ef06bd94f5341babfc0159a78811e))
* 🐛 SOP-10863 包材调拨模块详情没有包材时去掉tab栏位 ([eef0fe4](https://gitlab.hd123.com/vue/soa/commit/eef0fe4e8586f7b6693839131ee4c2db89989f09))
* 🐛 SOP-10863 新增包材调拨模块.页面操作优化 ([5ce8c24](https://gitlab.hd123.com/vue/soa/commit/5ce8c24c19d4c8130c03365fa4c43bac2e2ab49a))

## [2.48.0](https://gitlab.hd123.com/vue/soa/compare/v2.47.1...v2.48.0) (2024-08-02)


### Features

* ✨ 信誉分模块增加首页入口 ([b74bb60](https://gitlab.hd123.com/vue/soa/commit/b74bb6001b98500b4e8c682e5db0bda19518d462))
* ✨ SOP-10582 门店助手.质量反馈优化调整 ([26aa96b](https://gitlab.hd123.com/vue/soa/commit/26aa96ba7bc8d118495720fb6de2a69934bbb3a6))
* ✨ SOP-10673 钉钉小程序调价申请模块兼容性问题 ([f86de08](https://gitlab.hd123.com/vue/soa/commit/f86de089c677a9c1a7ff2adec9b2c027c8a74aa4))
* ✨ SOP-10751 自主盘点录入数量既支持增量也支持覆盖 ([2a26e96](https://gitlab.hd123.com/vue/soa/commit/2a26e96e8c042062e2d3fc357e097e86645f99b4))
* ✨ SOP-10751 自主盘点录入数量既支持增量也支持覆盖 ([11ff5f5](https://gitlab.hd123.com/vue/soa/commit/11ff5f53eaef377af16a2063bbdb419417af6cc2))
* ✨ SOP-10753 配货差异申请增加已拒绝状态 ([3310bf0](https://gitlab.hd123.com/vue/soa/commit/3310bf031cf2f3842a70331c51f172d9aaad2c6d))
* ✨ SOP-10755 自主盘点编辑页面切换模板时对模板进行校验 ([2c8bb78](https://gitlab.hd123.com/vue/soa/commit/2c8bb78ca1c553d0ab3c0c46475fdc4ede7e00f5))
* ✨ SOP-10755 自主盘点增加选择模板时的校验 ([90157c7](https://gitlab.hd123.com/vue/soa/commit/90157c7d650e91c7e799a0eb3d85dfd76c0069cc))
* ✨ SOP-10757 独立订货和标准订货如果订货时间没配置则直接隐藏 ([8ac0766](https://gitlab.hd123.com/vue/soa/commit/8ac0766398bfbcdaefa776daaa839f1b7763f8b8))
* ✨ SOP-10757 独立订货和标准订货如果订货时间没配置则直接隐藏 ([be3cc77](https://gitlab.hd123.com/vue/soa/commit/be3cc7770c671a0bd84273d5041c4fa8c7d1ef32))
* ✨ SOP-10758 调拨单增加展示调拨申请单号 ([aa99750](https://gitlab.hd123.com/vue/soa/commit/aa997508861555ae539f9d3ca88d964d5cdce20a))
* ✨ SOP-10763 调拨申请支持修改调拨价 ([1925b57](https://gitlab.hd123.com/vue/soa/commit/1925b57472a01f83ff8e726ec9a71623c981c329))
* ✨ SOP-10763 调拨申请支持修改调拨价 ([107cc2d](https://gitlab.hd123.com/vue/soa/commit/107cc2d2b8659b583b228a0bd2447738621cb8de))
* ✨ SOP-10763 调拨申请支持修改调拨价 ([eca9889](https://gitlab.hd123.com/vue/soa/commit/eca98891305fd405308ac80ea545f85f646faa59))
* ✨ SOP-10763 修改调拨价总额显示 ([df3e4e1](https://gitlab.hd123.com/vue/soa/commit/df3e4e129a18fb5c609180a240972cb7c1354ace))
* ✨ SOP-10785 统计类数字输入框显示门店导入的默认值 ([d401299](https://gitlab.hd123.com/vue/soa/commit/d4012999faed7a9b8523d75e837f7e79cb6cfe1f))
* ✨ SOP-10785 统计类数字输入框显示门店导入的默认值 ([fb1291b](https://gitlab.hd123.com/vue/soa/commit/fb1291b7d5ccea4cfeaaaeab95f603fa3f23afd0))
* ✨ SOP-10785 统计类数字输入框显示门店导入的默认值 ([23908d1](https://gitlab.hd123.com/vue/soa/commit/23908d1c4225f0aad7ea971bcaef85d7d6d0ec9f))
* ✨ SOP-10791 店务商品查询新增支持控制资质图片等信息的展示权限 ([82a6b5c](https://gitlab.hd123.com/vue/soa/commit/82a6b5cd01ffdf22d4c230c3ae09159ccf63c0fb))
* ✨ SOP-10794 上传图片组件增加是否启用服务端配置提示的选项 ([044c257](https://gitlab.hd123.com/vue/soa/commit/044c257d963e2c05b59c6d1cb2e8b886d55048e8))
* ✨ SOP-10794 上传图片组件增加展示门店运营平台配置的图片和视频上传要求 ([caa0f57](https://gitlab.hd123.com/vue/soa/commit/caa0f574c0b22130df2fdeeb17b15fd43ebfbee0))
* ✨ SOP-10804 周边门店库存优化 ([4ed303f](https://gitlab.hd123.com/vue/soa/commit/4ed303f84e1c96bdeb190f0a0b7782615475ff80))
* ✨ SOP-10804 周边门店库存优化 门店搜索返回的门店信息gid为number类型导致未勾选的问题修复 ([03b4c35](https://gitlab.hd123.com/vue/soa/commit/03b4c35c51ac8cf91892756b76c9df112aa1c8c5))
* ✨ SOP-10811 店务商品查询商品的售价不受系统设置salePrice的控制 ([ba23802](https://gitlab.hd123.com/vue/soa/commit/ba2380290c70b2b93a3b0a373cbb9dc69ab1027e))
* ✨ SOP-10813 试吃模块-黑名单提示框滑动条优化 ([7940a65](https://gitlab.hd123.com/vue/soa/commit/7940a6517fb29b30c404fe77ce9b86edea3639f6))
* ✨ SOP-10817 调拨申请单增加可用库存列 ([0feec6f](https://gitlab.hd123.com/vue/soa/commit/0feec6f690a62423eb149a5ab69c0fbf6e2320cc))
* ✨ SOP-10817 修复调拨申请扫码添加的批次行显示生产日期包含时分秒的问题 ([3751c58](https://gitlab.hd123.com/vue/soa/commit/3751c582b700c4815409f7a5e108a97701f7b2df))
* ✨ SOP-10823 调整获取盘点筛选弹框顶部位置的逻辑 ([a8bb8c3](https://gitlab.hd123.com/vue/soa/commit/a8bb8c30e9645bc5e47c48cc54e3eed3205b7378))
* ✨ SOP-10823 盘点详情点击展开筛选条件时重新获取高度 ([42fe852](https://gitlab.hd123.com/vue/soa/commit/42fe852f1611f5cdc98db00396d47ec857cd73e7))
* ✨ SOP-10823 小程序.门店助手.盘点盈亏页面进行中的看不到盘盈数据 ([76f81ba](https://gitlab.hd123.com/vue/soa/commit/76f81baccc74e32e99a958c499589c262db89023))
* ✨ SOP-10823 增加getRect工具方法 ([cd7d806](https://gitlab.hd123.com/vue/soa/commit/cd7d806836a45f830ffe44357746a16f09bee1c2))
* ✨ SOP-10828 标准订货不存在订货时间配置时，无法新增订货 ([86c10cb](https://gitlab.hd123.com/vue/soa/commit/86c10cb5d543b0535bd3a006950ae325730bd2aa))
* ✨ SOP-10839 账单支付失败后根据get接口返回状态控制是否重新支付 ([56536c9](https://gitlab.hd123.com/vue/soa/commit/56536c978ab83d4998286dc0af02a884328b2e39))
* ✨ SOP-10839 账单支付失败最后一次要更新单据状态 ([e51c6ff](https://gitlab.hd123.com/vue/soa/commit/e51c6ff14065d697202f80d18f758670fb5da1e1))


### Bug Fixes

* 🐛 修复钉钉获取不到rect的问题 ([326fba4](https://gitlab.hd123.com/vue/soa/commit/326fba433cf7b77ed4fded522aba0f38e9cfeeea))
* 🐛 修复收货模块清点模式显示异常的问题 ([4dde742](https://gitlab.hd123.com/vue/soa/commit/4dde7421ad47cf2ede974d53aeaa4e80aa52b671))
* 🐛 修改初盘详情的结束盘点传参 ([c22ee7e](https://gitlab.hd123.com/vue/soa/commit/c22ee7e6412562937f21994503e0c5d9f7aadee2))
* 🐛 SOP-10763 修复调拨价输入取消未清空的问题 ([0fc1b43](https://gitlab.hd123.com/vue/soa/commit/0fc1b4332f4964c6c14d6367b180173bce959395))
* 🐛 SOP-10794 修复视频提示取的图片配置的问题 ([e70dc14](https://gitlab.hd123.com/vue/soa/commit/e70dc14deb1fc5ce38bac2f478f39404cfcc2d84))
* 🐛 SOP-10803 修复质量反馈的重量的申请原因以及最大值限制 ([25e9495](https://gitlab.hd123.com/vue/soa/commit/25e9495ff62e9fc2dc378e4cc6e4809e92622675))
* 🐛 SOP-10803 质量反馈优化调整.问题修复 ([c1773d2](https://gitlab.hd123.com/vue/soa/commit/c1773d2204a5a1290c9fceeb97be013d22412479))
* 🐛 SOP-10803 质量反馈增加重量提交 ([d5a41fc](https://gitlab.hd123.com/vue/soa/commit/d5a41fc7f23e0358f971fa15b96506d7854456ed))
* 🐛 SOP-10806 修改反馈标识不可反馈提示 ([f1284ca](https://gitlab.hd123.com/vue/soa/commit/f1284cafb33ba76499b97a998472af1b80436a68))
* 🐛 SOP-10806 修改反馈数量小数位 ([65a8812](https://gitlab.hd123.com/vue/soa/commit/65a8812b811af216ddb3de63cd03ceb915495add))
* 🐛 SOP-10806 增加不需要质量反馈的提示 ([b5798a8](https://gitlab.hd123.com/vue/soa/commit/b5798a81cbc0c18c1e53af5b62304074ddda2f7d))
* 🐛 SOP-10806 增加重量的提交以及金额校验 ([f46fe7a](https://gitlab.hd123.com/vue/soa/commit/f46fe7a9f778c1b34f2cfd77978e400ecd54467f))
* 🐛 SOP-10817 调拨申请详情不展示库存 ([7224b77](https://gitlab.hd123.com/vue/soa/commit/7224b77bd1ef9eeb48e7fb872d658a0b79ebecab))
* 🐛 SOP-10817 调拨详情去掉溯源码展示 ([d510b1c](https://gitlab.hd123.com/vue/soa/commit/d510b1c89d6bad6d8343f56aa125ef7c12cf4021))
* 🐛 SOP-10817 修复调拨申请修改效期日期库存值不变的问题 ([72dd40a](https://gitlab.hd123.com/vue/soa/commit/72dd40a7ae2123da3e8b7f8c42d951fc31144511))
* 🐛 SOP-10817 修复调拨详情页未展示库存数的问题 ([ae20fad](https://gitlab.hd123.com/vue/soa/commit/ae20fad120e410c50a2556b4c2cd992866aee0fc))
* 🐛 SOP-10820 调拨申请编辑页面商品数据样式修复 ([b694954](https://gitlab.hd123.com/vue/soa/commit/b694954b604127c5face8e72ab4188de58ff0ca4))
* 🐛 SOP-10822 督导打回重新填写增加“督导驳回”操作记录显示 ([0e66db1](https://gitlab.hd123.com/vue/soa/commit/0e66db19e77ec96ac385626b736b30c0f954f08f))
* 🐛 SOP-10823 盘点盈亏页面进行中的看不到盘盈数据 ([910e2ff](https://gitlab.hd123.com/vue/soa/commit/910e2ffdcc8200bde3ec3ffd916981612f890391))
* 🐛 SOP-10829 修复自主盘点的数据检查与提醒需求.返回warning时未弹出提示弹窗 ([d9c0315](https://gitlab.hd123.com/vue/soa/commit/d9c0315aebe30e63dc4db7a471599d54014597eb))
* 🐛 SOP-10833 修复调拨申请气调商品扫非溯源码时未清空查询商品返回的lines导致提交异常的问题 ([bff8c9b](https://gitlab.hd123.com/vue/soa/commit/bff8c9b64be936289965a82c3492d0f1da6d8ef5))
* 🐛 SOP-10839 修复账单模块付款时不能立即获得支付成功状态的问题 ([2a9a539](https://gitlab.hd123.com/vue/soa/commit/2a9a539154c9d276938ba55b40c5d4a49a4dcb9a))
* 🐛 SOP-10843 修复退货申请配置项enableReasonValidDate未打开时能手动添加效期的问题 ([47e390e](https://gitlab.hd123.com/vue/soa/commit/47e390edab675583afe95d8dd0ce7cc0dba4134d))
* 🐛 SOP-10843 修复退货申请配置项enableReasonValidDate未开启但是商品无法输入数量的问题 ([08c234f](https://gitlab.hd123.com/vue/soa/commit/08c234fb1b3323f1ae8f33f980ade08d906e2363))
* 🐛 SOP-10844 收货获取单据是否需要评价受配置homePage控制 ([561f055](https://gitlab.hd123.com/vue/soa/commit/561f05511458e456aa0d9df3ae03dc58701d1347))
* 🐛 SOP-10849 打开模版选择前检查 ([54204fb](https://gitlab.hd123.com/vue/soa/commit/54204fbd81c6a4a9c2ec18ade6105b164bee9a93))
* 🐛 SOP-10849 修复盘点数目前不能输入0且输入小数点看不见 ([db5ea98](https://gitlab.hd123.com/vue/soa/commit/db5ea98b67e1faaca4f924739d638077a9e5e563))
* 🐛 SOP-10851 调拨单详情页无"申请单号"字段 ([42d351c](https://gitlab.hd123.com/vue/soa/commit/42d351cd1a905d7ea0294c48fb4dd31a7ec5997e))

### [2.47.1](https://gitlab.hd123.com/vue/soa/compare/v2.47.0...v2.47.1) (2024-07-04)


### Features

* ✨ SOP-10766 正常订货爆品商品订货上限向下取整，保存单据时根据qpcQty重算total和qty ([19f679f](https://gitlab.hd123.com/vue/soa/commit/19f679fa31df31345d1fe9d529721d6c3f741c46))
* ✨ SOP-10766 正常订货爆品商品上限向下取整，保存单据时根据qpcQty重算total和qty ([e6f52a2](https://gitlab.hd123.com/vue/soa/commit/e6f52a24117af2ce68534037b6ebaad29c8ea9c9))


### Bug Fixes

* 🐛 诚信志远订货首页增加爆品价的逻辑 ([1bb7995](https://gitlab.hd123.com/vue/soa/commit/1bb79954d28280d644ddd3003cbdfac84b16b55f))

## [2.47.0](https://gitlab.hd123.com/vue/soa/compare/v2.46.0...v2.47.0) (2024-07-01)


### Features

* ✨ 标价签打印模块获取预览信息接口报错优化 ([74fdad2](https://gitlab.hd123.com/vue/soa/commit/74fdad2da169af8b7adfe42794403e78cf9ceb25))
* ✨ 差异申请详情增加展示订货模板 ([a1568f4](https://gitlab.hd123.com/vue/soa/commit/a1568f4714da3e2f64a90f38caa7caf63e855866))
* ✨ 调拨申请模块编辑权限取值更改 ([cee244d](https://gitlab.hd123.com/vue/soa/commit/cee244d1048b0ea5ebfb6c56de1070c6ae5c0a72))
* ✨ 新增this.$openDocument方法全局解决uni.openDocument方法的问题 ([a5f4de8](https://gitlab.hd123.com/vue/soa/commit/a5f4de8e8db258820659553cf6fb83dcd5af6b01))
* ✨ 应用搜索页面-试吃模式选择弹窗补充 ([315bfe5](https://gitlab.hd123.com/vue/soa/commit/315bfe5511029ccca42b8751dd1acf853b5b0250))
* ✨ SOP-10401 诚信志远订货新界面改造 ([59b0637](https://gitlab.hd123.com/vue/soa/commit/59b063714838071043fc321239c4740f5c2c3f3a))
* ✨ SOP-10401 商品详情页增加爆品样式展示；加购交互调整 ([27a0685](https://gitlab.hd123.com/vue/soa/commit/27a068537ebb80d6206daf0595a39e1d8ff06755))
* ✨ SOP-10401 投放订货模块 分类和列表页迁移 ([c1004fe](https://gitlab.hd123.com/vue/soa/commit/c1004fe00eefc84ea04066b350a6c0389f4ec328))
* ✨ SOP-10401 投放活动页加购联调，爆品标签接入 ([ab9d73f](https://gitlab.hd123.com/vue/soa/commit/ab9d73f1aca631b5730e56f320b030461c7dcd51))
* ✨ SOP-10401 投放活动页面搭建完成 ([72f255d](https://gitlab.hd123.com/vue/soa/commit/72f255dc56d118c0ae2e518e02f6b0e05a223a55))
* ✨ SOP-10401 投放商品详情模块添加与联调 ([dffec85](https://gitlab.hd123.com/vue/soa/commit/dffec855376415b6591f1fc937496ccbc5f189b0))
* ✨ SOP-10401 投放商品详情页搭建 ([0e7a02e](https://gitlab.hd123.com/vue/soa/commit/0e7a02e237cb9dda69301d45b12698db0e8f3ffc))
* ✨ SOP-10401 投放首页和投放活动页，活动展示增加时间和门店限制 ([5db2f5b](https://gitlab.hd123.com/vue/soa/commit/5db2f5bf4c0e11ba9cd6fb09ef0d37cbc09b9ead))
* ✨ SOP-10401 投放首页联调 ([d9b4f94](https://gitlab.hd123.com/vue/soa/commit/d9b4f942eb9f53ac08dada6474b8e9d27d02df05))
* ✨ SOP-10401 投放首页商品跳转详情逻辑调整；商品详情页价格显示逻辑调整 ([97f281a](https://gitlab.hd123.com/vue/soa/commit/97f281a1328c660be6755e26da095b1f054926cf))
* ✨ SOP-10401 投放页面部分问题调整 ([8a49237](https://gitlab.hd123.com/vue/soa/commit/8a4923701c4f19382cac2b86514a7be7d1d35fa5))
* ✨ SOP-10401 投放页数量限制逻辑调整 ([aed57dc](https://gitlab.hd123.com/vue/soa/commit/aed57dc8e66358d59ed9b611c8d052b6774f70e6))
* ✨ SOP-10448 订单评价提交时增加评分传参，rate无需实际逻辑 ([988994f](https://gitlab.hd123.com/vue/soa/commit/988994f72ed0bcb57d99dc559f357fbd8ed4b37c))
* ✨ SOP-10448 新增订单评价页面 ([570b44b](https://gitlab.hd123.com/vue/soa/commit/570b44b8392feb81ee078f7d9c09256adf8db2c7))
* ✨ Sop-10512 门店考评 附件为文件时 UI更改 ([fc61ade](https://gitlab.hd123.com/vue/soa/commit/fc61ade47224eb1f35714d275f94fb4c42f56c3f))
* ✨ SOP-10512 门店考评模块更改 ([7699a56](https://gitlab.hd123.com/vue/soa/commit/7699a566d560ebdca916e7abeab3cebbfbc972f5))
* ✨ SOP-10512 门店考评模块兼容负分 ([c486453](https://gitlab.hd123.com/vue/soa/commit/c486453d09c5d16567b6507b48d26f468da97f92))
* ✨ SOP-10512 门店考评模块增加分值变化 时间倒序排列 ([1d16302](https://gitlab.hd123.com/vue/soa/commit/1d16302f15da1d7969811dd81ca6cfe331d90276))
* ✨ SOP-10512 门店考评模块增加文件模式 ([499e08f](https://gitlab.hd123.com/vue/soa/commit/499e08f18aa8b1cc086996ba868922ab496c46d8))
* ✨ SOP-10512 门店考评搜索页完善 ([9c818ce](https://gitlab.hd123.com/vue/soa/commit/9c818ce55d7b758247138c9f751246e796ee77d8))
* ✨ SOP-10512 门店考评页面兼容附件携带的文件 ([c72587f](https://gitlab.hd123.com/vue/soa/commit/c72587f8f7baf5f3698ad38d49c5f75ddad3aa97))
* ✨ SOP-10512 门店考评canvas兼容钉钉 ([ff65dfa](https://gitlab.hd123.com/vue/soa/commit/ff65dfaec88f58dc1951be1a28e17efd22b7fade))
* ✨ SOP-10517 支持钉钉和微信小程序连接HM-Z3便携式蓝牙打印机打印标签，使用CPCL指令 ([ac1fa32](https://gitlab.hd123.com/vue/soa/commit/ac1fa323468ab364a94f74091f92c810cce15de8))
* ✨ SOP-10544 独立叫货支持展示周销量与7日日均销量 ([a4dd2c0](https://gitlab.hd123.com/vue/soa/commit/a4dd2c09ec7ed38f712960c7b48cec1c7c70a9d0))
* ✨ SOP-10544 独立叫货支持展示周销量与7日日均销量 ([c15f17f](https://gitlab.hd123.com/vue/soa/commit/c15f17f4b44666a6c69a1d43d17135b845ec6918))
* ✨ SOP-10544 更改平均日销量字段 ([ab1ab1b](https://gitlab.hd123.com/vue/soa/commit/ab1ab1b6570d4546720e8974653bed81eeadcda2))
* ✨ SOP-10579 加盟费用的记账类目支持选填 ([24789c0](https://gitlab.hd123.com/vue/soa/commit/24789c03723dfaa64fa386370623364559bec535))
* ✨ SOP-10580 小程序.门店助手.账户流水中增加清分结算流水 ([31faa46](https://gitlab.hd123.com/vue/soa/commit/31faa465dd25f55fed9148296fde6a3936536393))
* ✨ SOP-10580 小程序.门店助手.账户流水中增加清分结算流水 ([8fbb3d8](https://gitlab.hd123.com/vue/soa/commit/8fbb3d8315275afab00a59cb072fe2de543d1dc8))
* ✨ SOP-10581 小程序.门店助手.盘点优化调整 ([0eb1841](https://gitlab.hd123.com/vue/soa/commit/0eb184198c3b8dded6acb9e8a024d20bb50f5029))
* ✨ SOP-10581 小程序.门店助手.盘点优化调整 ([46bab8a](https://gitlab.hd123.com/vue/soa/commit/46bab8a244ffa69a1cb5bffd6d0f2a49b3d13c04))
* ✨ SOP-10585 侧边栏支持显示二级分类 ([76fb26c](https://gitlab.hd123.com/vue/soa/commit/76fb26c19b123a46a712c2185520910eec9adef2))
* ✨ SOP-10585 商品查询支持显示说明和资质图片 ([2ba6f66](https://gitlab.hd123.com/vue/soa/commit/2ba6f66b7120f16bd1d7cef702bffac47133be77))
* ✨ SOP-10585 移植零食有鸣分支店务商品查询 ([733c07b](https://gitlab.hd123.com/vue/soa/commit/733c07bb0049a3db4745159ef2d48b853a35e8cb))
* ✨ SOP-10589 评论页面上传图片样式调整；投放订货列表页增加筛选条件 ([6c54755](https://gitlab.hd123.com/vue/soa/commit/6c54755810b31005bc606a5d0fdef4c877eabd31))
* ✨ SOP-10599 评论页面增加交互 ([1da9f9c](https://gitlab.hd123.com/vue/soa/commit/1da9f9cf81746c7d64a6f1e7d38e49e80a82d37d))
* ✨ SOP-10604 商品叫货自动带出关联商品 ([3215e13](https://gitlab.hd123.com/vue/soa/commit/3215e132cfa077382940fcfcb181b9bd33504ee0))
* ✨ SOP-10644 不停业盘点调整盘点编辑页面扫码按钮支持拖拽 ([d8f73d7](https://gitlab.hd123.com/vue/soa/commit/d8f73d73ca36474ad7f53852a449d732ab2782e6))
* ✨ SOP-10653 质量反馈支持显示损耗率和上传视频 ([0c18b7c](https://gitlab.hd123.com/vue/soa/commit/0c18b7c45418503f09acef6349dded95a290e082))
* ✨ SOP-10657 乐豆家需求调查类任务调整 ([4d34d87](https://gitlab.hd123.com/vue/soa/commit/4d34d87cbaec31a42aecd6fcc49962cd599c1d46))
* ✨ SOP-10657 任务详情描述增加视频，修改视频和图片上传最大数量 ([6c23fc2](https://gitlab.hd123.com/vue/soa/commit/6c23fc29fa52234d83a3089f23cccd6fc160321a))
* ✨ SOP-10657 修改乐豆家需求调查类任务调整.页面操作优化 ([a78eb61](https://gitlab.hd123.com/vue/soa/commit/a78eb61c819106273f873a6ccf9bd293dbacbc79))
* ✨ SOP-10677 收货模块指定商品左滑不允许进行质量反馈标记 ([a97e7ad](https://gitlab.hd123.com/vue/soa/commit/a97e7ad3e557043c96c260ea154aeb2bfcc73aed))
* ✨ SOP-10705 调拨申请入口权限优化 ([494e68b](https://gitlab.hd123.com/vue/soa/commit/494e68b253121703173e279d05ed8cc22809c313))
* ✨ SOP-10708 试吃黑名单增加线号 ([6d8c346](https://gitlab.hd123.com/vue/soa/commit/6d8c346df74910ffd945114135edbac12c8e8874))
* ✨ SOP-10711 周黑鸭订货减量单和差异申请单显示订货模板 ([03a9e47](https://gitlab.hd123.com/vue/soa/commit/03a9e474e1afd7921c4ce97e2ad0a1a68da7a625))
* ✨ SOP-10722 乐豆家需求调查类任务调整.页面操作优化 ([7058dde](https://gitlab.hd123.com/vue/soa/commit/7058dde13530f532aa202fad156643d38faaf122))


### Bug Fixes

* 🐛 冲突合并 ([bec0721](https://gitlab.hd123.com/vue/soa/commit/bec0721687cbe6e34552960347add2c5b9c8cc0d))
* 🐛 分支合并 ([d0dadd5](https://gitlab.hd123.com/vue/soa/commit/d0dadd5bd7b696b1770e1c4f9bd4b1c7ec272a0b))
* 🐛 乐豆家需求调查类任务调整.页面显示问题2 ([dacf5b1](https://gitlab.hd123.com/vue/soa/commit/dacf5b1feb959cf0ca087a803db1bbb99f69581e))
* 🐛 删除重复的sysConfig字段 ([8e0c765](https://gitlab.hd123.com/vue/soa/commit/8e0c7650488bdc6f83e2489802578935627eb756))
* 🐛 修复调价申请搜索商品选择时无效的问题 ([7cdb5b6](https://gitlab.hd123.com/vue/soa/commit/7cdb5b6084edbad1beb9e1a23ff15fb1cb4582e7))
* 🐛 修复钉钉小程序订货购物车提交跳转详情页错误的问题 ([ed1ef38](https://gitlab.hd123.com/vue/soa/commit/ed1ef38fa75f6ad4f718b45370eb565a541c2416))
* 🐛 修复订货编辑页面会将辅料分类全都加入购物车的问题 ([44824b6](https://gitlab.hd123.com/vue/soa/commit/44824b662f48170d31e0f59aa7b536a5d8fb3d60))
* 🐛 修复正常订货就多肉多模式下数量计算错误的问题 ([351e78a](https://gitlab.hd123.com/vue/soa/commit/351e78a09b8fa03619f4cdb4bf4c3d4ce2d532de))
* 🐛 修复质量反馈损耗率未显示值的问题 ([a3b6682](https://gitlab.hd123.com/vue/soa/commit/a3b668217702f8e0b5882b9ced6ff64bfbe7e2e7))
* 🐛 质量反馈保存时增加lossRate ([d0341c7](https://gitlab.hd123.com/vue/soa/commit/d0341c70c10ecbf2ea3f8aaafce694d14465920c))
* 🐛 SOP-10401订货投放增加加购弹窗 ([3671d9a](https://gitlab.hd123.com/vue/soa/commit/3671d9ad7e9a70902bdbde939f2f055e831adfe2))
* 🐛 SOP-10544 删掉跳转商品详情页方法 ([b59dbeb](https://gitlab.hd123.com/vue/soa/commit/b59dbebb356b9121f95577ab72f05c926153fe61))
* 🐛 SOP-10580 冲突合并 ([ee01cf7](https://gitlab.hd123.com/vue/soa/commit/ee01cf72d2f0d78ef957142276ba7198074bd09b))
* 🐛 SOP-10580 冲突合并 ([61029d7](https://gitlab.hd123.com/vue/soa/commit/61029d7e818cc2ddc7e40ecf756968f29ff595ec))
* 🐛 SOP-10580 云资金修改入参以及默认排序 ([8a9a55d](https://gitlab.hd123.com/vue/soa/commit/8a9a55d911d64a68bc3824bfd1ea59626ac0e4db))
* 🐛 SOP-10580 账户流水中增加清分结算流水修改流水月度返回 ([7a3bcdf](https://gitlab.hd123.com/vue/soa/commit/7a3bcdf123db0089a0fb6e943b9d3b4bf52c4491))
* 🐛 SOP-10581 增加盘点权限 ([85c3173](https://gitlab.hd123.com/vue/soa/commit/85c3173f929cc79a06af8d251483c589d13d2845))
* 🐛 SOP-10596、SOP-10597 投放订货页面部分问题修复 ([113763a](https://gitlab.hd123.com/vue/soa/commit/113763a9c2f83f3253a2b9240050c2ce941ab951))
* 🐛 SOP-10597 订货投放页面问题修复 ([e4f7340](https://gitlab.hd123.com/vue/soa/commit/e4f7340eabdb0e02584ae5d278cc41ed7dd1a49a))
* 🐛 SOP-10597 投放订货页面部分显示问题调整 ([6ac69e3](https://gitlab.hd123.com/vue/soa/commit/6ac69e329e9089a486b48c5e372328c01b012f57))
* 🐛 SOP-10597 投放界面问题调整 ([0ed5176](https://gitlab.hd123.com/vue/soa/commit/0ed51769211ac88a1737259f2549ff01a0dba7f5))
* 🐛 SOP-10597 投放页面修复 ([325ea96](https://gitlab.hd123.com/vue/soa/commit/325ea96f6068c4079cbb6f35ed60578a70461570))
* 🐛 SOP-10598 订货分类页面问题修复 ([290d87a](https://gitlab.hd123.com/vue/soa/commit/290d87ac653d37fbfc770af8ffdc241d671f5e16))
* 🐛 SOP-10600 商品评论列表查看页面图片展示调整 ([c445cbd](https://gitlab.hd123.com/vue/soa/commit/c445cbd7ccaeab6ab9909e1eb53a083ee3f1a43b))
* 🐛 SOP-10607 商品评论列表查看页面部分问题修复 ([55e21ec](https://gitlab.hd123.com/vue/soa/commit/55e21ec3013fafabce00edd4af80903de76c2b53))
* 🐛 SOP-10657 单项选择题增加图片视频上传功能 ([43910c5](https://gitlab.hd123.com/vue/soa/commit/43910c58b4045c7d1b7a781bd25325b97247f1a9))
* 🐛 SOP-10657 视屏播放修改 ([7a0bc41](https://gitlab.hd123.com/vue/soa/commit/7a0bc41506b51b9563ca9fd882297ebcf57a1575))
* 🐛 SOP-10657 修改调查类反馈传参 ([7bcb0d5](https://gitlab.hd123.com/vue/soa/commit/7bcb0d515ad6592ef9b830ae1c1db72da91c91c6))
* 🐛 SOP-10677 收获模块不等于0能滑动 ([aa4d18b](https://gitlab.hd123.com/vue/soa/commit/aa4d18ba222f9271ce92c9117e13a406ccbcd168))
* 🐛 SOP-10724 乐豆家需求调查类任务调整.页面显示问题2 ([79e7e1f](https://gitlab.hd123.com/vue/soa/commit/79e7e1f3a71d1bdfe41f5ccb0cdf389ad9788028))
* 🐛 SOP-10736 账户流水中增加清分结算流水，空字段展示为null ([24522f5](https://gitlab.hd123.com/vue/soa/commit/24522f584a0f26b3de418760f32393ea0b62d8b4))
* 🐛 SOP-10749 盘点优化调整，盘点详情页面字段未展示 ([b03632e](https://gitlab.hd123.com/vue/soa/commit/b03632e97b00160fa98560ccaeb6da42d70b1340))

### [2.46.1](https://gitlab.hd123.com/vue/soa/compare/v2.46.0...v2.46.1) (2024-06-17)


### Features

* ✨ 差异申请详情增加展示订货模板 ([af34f33](https://gitlab.hd123.com/vue/soa/commit/af34f330dabb64c54be934d4b6ec57a85ce9c46a))
* ✨ 调拨申请模块编辑权限取值更改 ([7ffdf71](https://gitlab.hd123.com/vue/soa/commit/7ffdf718f1af311f8832961846cfc42695078d68))
* ✨ SOP-10705 调拨申请入口权限优化 ([c7cc5b3](https://gitlab.hd123.com/vue/soa/commit/c7cc5b3f0a02689702c12d51cc4b828fa58402c5))
* ✨ SOP-10708 试吃黑名单增加线号 ([7f8976b](https://gitlab.hd123.com/vue/soa/commit/7f8976bf86a1c1df50c16e91c6adcd8019f6c52d))
* ✨ SOP-10711 周黑鸭订货减量单和差异申请单显示订货模板 ([71d3df8](https://gitlab.hd123.com/vue/soa/commit/71d3df808a6aa123a8f1ab75fc526f1222cddbc4))

## [2.46.0](https://gitlab.hd123.com/vue/soa/compare/v2.45.1...v2.46.0) (2024-06-11)


### Features

* ✨ 标签打印模块权限限制实现 ([e97d6f5](https://gitlab.hd123.com/vue/soa/commit/e97d6f5cb992f3f2067548bd70b968ac4c2b146c))
* ✨ 标签打印入口图标显示 ([e5daee8](https://gitlab.hd123.com/vue/soa/commit/e5daee8f2f8e2a65b58e0ab3e2c0eef84624fa04))
* ✨ 调拨驳回按钮增加对starterId和当前门店id是否相等的判断 ([a54e464](https://gitlab.hd123.com/vue/soa/commit/a54e464d14e2de916fcc3e47bcccd715d73a0b87))
* ✨ 调拨申请-新增调除和调入权限以及权限控制 ([9560b3d](https://gitlab.hd123.com/vue/soa/commit/9560b3df312067e7729532805a6079489f9d0912))
* ✨ 订货已批准调整为总部批准 ([359b14e](https://gitlab.hd123.com/vue/soa/commit/359b14ee3f860883662a8391a37043f769ec89e0))
* ✨ 门店周边库存  商品搜索页添加取消事件 ([c30d795](https://gitlab.hd123.com/vue/soa/commit/c30d795293bfbbccea4950a74ff522249bdb18e5))
* ✨ 门店周边库存-门店筛选条件UI优化 ([40216c9](https://gitlab.hd123.com/vue/soa/commit/40216c93d4468c9b4c9975ad8bfbecadc449843d))
* ✨ 模板订货和减量订货增加扫码逻辑 ([34b44fa](https://gitlab.hd123.com/vue/soa/commit/34b44fa54af8a37734ddbff5ce2b6debf762d5c6))
* ✨ 模板订货提交时处理已经不在模板内的商品 ([cdd2b32](https://gitlab.hd123.com/vue/soa/commit/cdd2b32ea470e7c31ed4630cfab2aea80c3d75fc))
* ✨ 试吃负库存优化 ([81a6e8d](https://gitlab.hd123.com/vue/soa/commit/81a6e8d43db33f3eadc13406fb2c3699ae630e6f))
* ✨ 试吃活动-商品校验 ([c055867](https://gitlab.hd123.com/vue/soa/commit/c055867e1d36e267d19cbe42f1a7140fcc95c4bb))
* ✨ 试吃模块 负库存校验逻辑更改 ([2707796](https://gitlab.hd123.com/vue/soa/commit/2707796efd8b4aa97dac03401e3f249d55e68d18))
* ✨ 试吃模块-试吃记录页面更改 ([4a0d8ca](https://gitlab.hd123.com/vue/soa/commit/4a0d8cac80b456c5733f6bf422c0b3aec5a0be53))
* ✨ 试吃模块-试吃记录页面更改 ([c7b937e](https://gitlab.hd123.com/vue/soa/commit/c7b937e24de6bd3b86b4cb39c9898b3c020b0a45))
* ✨ 试吃模块记录提交时 指明是试吃还是赠送 ([fa4b9c2](https://gitlab.hd123.com/vue/soa/commit/fa4b9c28ecfd0e72347a12dddb7cbee121371211))
* ✨ 试吃模块记录提交时 指明是试吃还是赠送 ([5ea622f](https://gitlab.hd123.com/vue/soa/commit/5ea622f594ae88585a81da5497513a7e60684c0f))
* ✨ 试吃模块入口权限调整为试吃模块查看权限 ([bd9b950](https://gitlab.hd123.com/vue/soa/commit/bd9b950c5e02bc6d04761e541b64436504f6ad75))
* ✨ 退货申请批次号格式从服务端配置中取 ([6f1d88d](https://gitlab.hd123.com/vue/soa/commit/6f1d88db7314ac841d2932b650d40d88646db011))
* ✨ 新增标签打印模块查看权限和调拨申请作废权限 ([1800352](https://gitlab.hd123.com/vue/soa/commit/1800352ab865e6b536fc5d85d7c8cf5a0c530165))
* ✨ 新增门店周边库存 ([57d0fad](https://gitlab.hd123.com/vue/soa/commit/57d0faddabccbcfa86e35123bdea3b30f5bb2485))
* ✨ 新增试吃模块入口 ([ea46f19](https://gitlab.hd123.com/vue/soa/commit/ea46f19cdac18c633c9191d8b8a3c5fb4c4f7da2))
* ✨ 新增支持周黑鸭模板订货 ([0e33e81](https://gitlab.hd123.com/vue/soa/commit/0e33e8190ec8136f6222f1f62c7633729286d3ab))
* ✨ 账单v2支持类型的筛选条件 ([ffbd006](https://gitlab.hd123.com/vue/soa/commit/ffbd00663bb66240186b3bd34dde757ecc34ef58))
* ✨ 账户流水详情是否展示余额受showBalance配置控制 ([2e9df96](https://gitlab.hd123.com/vue/soa/commit/2e9df962dd22b6792bfffd6af214c468ae4df0e4))
* ✨ 支持订货单发送media类型的消息 ([cc37f76](https://gitlab.hd123.com/vue/soa/commit/cc37f769d9f186de0a9d53c5bf45c96d1653bfd4))
* ✨ 支持username@租户的方式登录 ([14da751](https://gitlab.hd123.com/vue/soa/commit/14da75118e0bfeee3ba64c96b27214b7df62ab25))
* ✨ 自主盘点 盈亏明细效期 数量和金额逻辑变更 ([5279d8a](https://gitlab.hd123.com/vue/soa/commit/5279d8adda0c17a107f0db30e09109d568f78991))
* ✨ 自主盘点-模板盘点-新增效期 日期选择器禁用大于今天的日期 ([17d2f77](https://gitlab.hd123.com/vue/soa/commit/17d2f77401735cb1594b3faf8eab9e80f4aed303))
* ✨ 自主盘点-模板盘点-新增效期 日期选择器禁用大于今天的日期 ([ab8453d](https://gitlab.hd123.com/vue/soa/commit/ab8453d70d4880e56af2c70032e2b59b33267209))
* ✨ 自主盘点空值过滤以及样式优化 ([5d1e095](https://gitlab.hd123.com/vue/soa/commit/5d1e095ca957b021c098b5a3a3fccd82f7af2d0c))
* ✨ SOP-10481 小程序.门店助手.新增试吃模块 ([fa476bd](https://gitlab.hd123.com/vue/soa/commit/fa476bdde4e9da6305384a766f1204caea29d614))
* ✨ SOP-10481 新增试吃模块 ([bf90649](https://gitlab.hd123.com/vue/soa/commit/bf90649d8213f377e8389f6ed39c7e79a3a0c74d))
* ✨ SOP-10482 门店周边库存 商品过滤条件枚举变更 ([fec4ea1](https://gitlab.hd123.com/vue/soa/commit/fec4ea189a57df1fdbfc2391a981b269db73c179))
* ✨ SOP-10482 新增门店周边库存模块 ([017f005](https://gitlab.hd123.com/vue/soa/commit/017f005b8b97f576448257a1795568e2472939ca))
* ✨ SOP-10482 周边门店库存模块提交 ([f8e562b](https://gitlab.hd123.com/vue/soa/commit/f8e562bdbe1d3f20ef2b465708c02ed93bfae84a))
* ✨ SOP-10483 批次号调整模块 钉钉小程序页面样式兼容调整 ([44e5b34](https://gitlab.hd123.com/vue/soa/commit/44e5b34d61db5cdf4c7774bc83c7f06b0a890bec))
* ✨ SOP-10483 批次号调整模块，生成的批次格式取值调整 ([6d4c6ab](https://gitlab.hd123.com/vue/soa/commit/6d4c6ab9f371082dd56d14a24209e95a37478f2c))
* ✨ SOP-10483 批次号调整模块联调 ([10af78b](https://gitlab.hd123.com/vue/soa/commit/10af78b7fb38ac08960310dcffa283364d5dfd16))
* ✨ SOP-10483 批次号调整模块联调完成 ([fc4ef83](https://gitlab.hd123.com/vue/soa/commit/fc4ef83f2345fb73947cde4501ac81148738089a))
* ✨ SOP-10483 批次号调整模块页面搭建完成 ([d9f60de](https://gitlab.hd123.com/vue/soa/commit/d9f60de6c268cb43942be291d447622f7d302b72))
* ✨ SOP-10483 批次号调整申请编辑模块逻辑补充 ([3297ab4](https://gitlab.hd123.com/vue/soa/commit/3297ab4ab7aeee82f3149481aeb38c1de9cc1211))
* ✨ SOP-10489 批次号调整模块部分逻辑调整 ([4ae399b](https://gitlab.hd123.com/vue/soa/commit/4ae399b339afa3207acf942100f451e476f49b3e))
* ✨ SOP-10492 调拨调整改动暂存 ([bcf3899](https://gitlab.hd123.com/vue/soa/commit/bcf389950d67f3b656176ec466b9da39ca7e2aa8))
* ✨ SOP-10492 调拨调整申请模块增加溯源码和效期控制 ([8599563](https://gitlab.hd123.com/vue/soa/commit/8599563609ab1b2e93c7c3db69a8ee3d94ce2171))
* ✨ SOP-10492 调拨调整选择效期，日期选择器禁用今天之后的日期 ([e232c73](https://gitlab.hd123.com/vue/soa/commit/e232c7341d45ff2b2432093af1dd9e6d18a52885))
* ✨ SOP-10492 调拨模块调整 - 添加批次展示；当前门店代发货界面样式调整；调拨模块禁止编辑 ([6bfe7b7](https://gitlab.hd123.com/vue/soa/commit/6bfe7b7eb3a893dbc9ebd93913eedbb7a918596b))
* ✨ SOP-10492 调拨模块发货权限控制支持配置 ([e82d270](https://gitlab.hd123.com/vue/soa/commit/e82d270ffbd84c4421429c5d76b8a2cf3ef7abf4))
* ✨ SOP-10492 调拨模块效期编辑控制逻辑调整 ([944dcb8](https://gitlab.hd123.com/vue/soa/commit/944dcb8895d81a96aef77bc07539db2bbddce42c))
* ✨ SOP-10492 调拨申请编辑页，商品卡片样式调整 ([703ec61](https://gitlab.hd123.com/vue/soa/commit/703ec618427137e228ee647802a9b497e213753a))
* ✨ SOP-10492 调拨申请与调拨模块 - 效期控制字段调整 ([0024271](https://gitlab.hd123.com/vue/soa/commit/002427183111fd73c176be9cfe4aa39858f4d061))
* ✨ SOP-10493 收货支持周黑鸭信任模式 ([c815c15](https://gitlab.hd123.com/vue/soa/commit/c815c15d35e3cbbd6374d993aa7b5b88335cb698))
* ✨ SOP-10494 配货差异部分逻辑调整 ([1a043fd](https://gitlab.hd123.com/vue/soa/commit/1a043fdf15c2ee98e1a610f93b2a3edcb0ed8b09))
* ✨ SOP-10494 配货差异调整 已作废枚举修改 ([836cc0e](https://gitlab.hd123.com/vue/soa/commit/836cc0e3af57b764bf4b3d1615b4656706aeb46b))
* ✨ SOP-10494 配货差异模块商品行申请数量逻辑调整 ([b1a79ab](https://gitlab.hd123.com/vue/soa/commit/b1a79abaa6232444803e29b4ac524e7602db6c82))
* ✨ SOP-10494 配货差异申请编辑页部分逻辑补充 ([19f290c](https://gitlab.hd123.com/vue/soa/commit/19f290c636c1bb9ee36363341659b2aa059200b6))
* ✨ SOP-10494 配货差异申请编辑页部分逻辑补充 ([3c72f6c](https://gitlab.hd123.com/vue/soa/commit/3c72f6c15a6f2659bfa7731598ae0738e0b2cd4e))
* ✨ SOP-10494 配货差异申请部分显示问题调整 ([22fa7ea](https://gitlab.hd123.com/vue/soa/commit/22fa7eadb2a7952a239223430bd056b936b281b5))
* ✨ SOP-10494 配货差异申请单据搜索页查询条件调整 ([f869a10](https://gitlab.hd123.com/vue/soa/commit/f869a10cae2d20be35a96995c725b174f5dad552))
* ✨ SOP-10494 配货差异申请模块部分联调 ([f85593d](https://gitlab.hd123.com/vue/soa/commit/f85593d03bb0d5b437a68e6b5ccff30883c5be66))
* ✨ SOP-10494 配货差异申请模块部分联调 ([3bfc70c](https://gitlab.hd123.com/vue/soa/commit/3bfc70c325076d00bfc00373f59fd7099fb13afa))
* ✨ SOP-10494 配货差异申请模块部分问题修复 ([28c50a1](https://gitlab.hd123.com/vue/soa/commit/28c50a1d807a5dd64d956fd8966f4adb708bf04a))
* ✨ SOP-10494 配货差异申请模块查询商品分类添加id传参 ([f77be4d](https://gitlab.hd123.com/vue/soa/commit/f77be4d72760f67bc66a14d7a117cd6b53e81e4c))
* ✨ SOP-10494 配货差异申请模块搭建 ([d60f86a](https://gitlab.hd123.com/vue/soa/commit/d60f86aa2289aede31be80a29f41ec26825847a9))
* ✨ SOP-10494 配货差异申请模块调整 ([a33ffc2](https://gitlab.hd123.com/vue/soa/commit/a33ffc2f29c53761a44955e2152c09c9e2506e86))
* ✨ SOP-10494 配货差异申请模块联调 ([c30e9a3](https://gitlab.hd123.com/vue/soa/commit/c30e9a33aee3a9345388cf2f4ad37658547c4aeb))
* ✨ SOP-10494 配货差异申请模块列表页和详情页增加已作废状态 ([9e85abe](https://gitlab.hd123.com/vue/soa/commit/9e85abe9c22a1d9105c528c522abd2f0fc048fe7))
* ✨ SOP-10494 配货差异申请模块权限定义 ([c66d4f4](https://gitlab.hd123.com/vue/soa/commit/c66d4f46935051fb900a29641d525d11379716e7))
* ✨ SOP-10494 配货差异申请模块详情页展示批次信息 ([a6d8bdf](https://gitlab.hd123.com/vue/soa/commit/a6d8bdf11df93ef891b23cb6827904b5323af73c))
* ✨ SOP-10494 配货差异申请模块移动到库存管理模块 ([cfc23f3](https://gitlab.hd123.com/vue/soa/commit/cfc23f345f919044854ac3341162d6ff8146909c))
* ✨ SOP-10496 退货申请支持扫溯源码和录入批次 ([58a8262](https://gitlab.hd123.com/vue/soa/commit/58a8262aa3996610bb84afedce0c8221c5e758cc))
* ✨ SOP-10496 移除退货申请单头原因必填的限制 ([259f5bf](https://gitlab.hd123.com/vue/soa/commit/259f5bfc4753e91671290849c73ba3072aac464b))
* ✨ SOP-10512 新增门店考评模块 ([4bf4423](https://gitlab.hd123.com/vue/soa/commit/4bf4423a7a7cf69c030f53a34b9781beef841d8e))
* ✨ SOP-10512 新增门店考评模块 ([12b1b26](https://gitlab.hd123.com/vue/soa/commit/12b1b261e32750da6b92a0cfc0bd72db07bc5605))
* ✨ SOP-10516 新增标签打印模块 ([2c98f7f](https://gitlab.hd123.com/vue/soa/commit/2c98f7f6ec7ed0483bb2ee12846bc23f264219c4))
* ✨ SOP-10531 试吃气调商品增加一小时过期校验 ([f6f637b](https://gitlab.hd123.com/vue/soa/commit/f6f637b44a17407e381e8e72fb5fbae1461df4f9))
* ✨ SOP-10536 调整独立订货和订货到同一个新分包 ([3f10b59](https://gitlab.hd123.com/vue/soa/commit/3f10b59856cb4b99662b5f316da8b8fce0574a02))
* ✨ SOP-10536 使用big.js替换mathjs优化主包大小 ([ed4ed56](https://gitlab.hd123.com/vue/soa/commit/ed4ed56d6d4e2a0b348af6f957bb9d3eb1188ff8))
* ✨ SOP-10542 配货差异申请详情页商品样式调整 ([2b5e0ae](https://gitlab.hd123.com/vue/soa/commit/2b5e0aef962975737b51b3963f45fc68b4434863))
* ✨ SOP-10556 新增盘点模板 ([4e0cffb](https://gitlab.hd123.com/vue/soa/commit/4e0cffb070c9bbf71d906008300680d84ceb2d67))
* ✨ SOP-10564 配货差异申请模块，编辑页面，数量编辑时滚动问题修复 ([ec07822](https://gitlab.hd123.com/vue/soa/commit/ec07822479fa6533d3e1002a6f504ab44807621e))
* ✨ SOP-10564 配货差异申请验收问题调整 ([a472aa5](https://gitlab.hd123.com/vue/soa/commit/a472aa56be548050660aad83f68054492c95971d))
* ✨ SOP-10564 批次号调整商品搜索页增加确定按钮 ([6807d3c](https://gitlab.hd123.com/vue/soa/commit/6807d3c8f5dd77951e651dbdf507531de50335eb))
* ✨ SOP-10571 模板订货计算预计到货时间不添加偏移天数 ([fe7c726](https://gitlab.hd123.com/vue/soa/commit/fe7c72600eb86102f7a5396499d5b86bf237407d))
* ✨ SOP-10574 新增订货单直接弹出选择模板弹窗 ([e2eec76](https://gitlab.hd123.com/vue/soa/commit/e2eec76f45f373b39859959b326a7bf6c72315fe))
* ✨ SOP-10576 ([d05c6e3](https://gitlab.hd123.com/vue/soa/commit/d05c6e389e23c925f79aeb3855cf5957b72617c4))
* ✨ SOP-10576 盘点模板-售价和规格位置调整 ([7759206](https://gitlab.hd123.com/vue/soa/commit/7759206bf54ac348d760f8fabdadbb2fb382d2c1))
* ✨ SOP-10576 盘点模块新增代码 ([bb3c121](https://gitlab.hd123.com/vue/soa/commit/bb3c12133f3c4f5acc6444992ebbedfd985b1af5))
* ✨ SOP-10576 新增盘点模板-P3 ([0cf391d](https://gitlab.hd123.com/vue/soa/commit/0cf391da1c79a802581ea5986f5256582d1ddc56))
* ✨ SOP-10576 自主盘点-模板盘点逻辑变更 ([2eb2243](https://gitlab.hd123.com/vue/soa/commit/2eb2243edf91190360b8c6a7d2d68cbd408c8554))
* ✨ SOP-10577 试吃活动区分试吃和赠送 ([7d8b9f9](https://gitlab.hd123.com/vue/soa/commit/7d8b9f91d44a896818e6630eee8e624702ad05fd))
* ✨ SOP-10577 试吃模块区分试吃和赠送调整 ([23aa27a](https://gitlab.hd123.com/vue/soa/commit/23aa27ad46f643bff737f05a74e6f7efccf4f6a5))
* ✨ SOP-10586 新增售价改价模块 ([b21b819](https://gitlab.hd123.com/vue/soa/commit/b21b819bff6bc547d08c45eb0739709652a1a7c0))
* ✨ SOP-10601 配货差异申请选择单据接口增加排序条件 ([ac83025](https://gitlab.hd123.com/vue/soa/commit/ac8302535663e70572dc155fcb2efa813d1ca302))
* ✨ SOP-10619 批次号调整 - 调整记录增加筛选条件 ([231d474](https://gitlab.hd123.com/vue/soa/commit/231d474f08e027b1a834d93192c990c9f9c7665b))
* ✨ SOP-10619 批次号验收问题调整 ([9152e9d](https://gitlab.hd123.com/vue/soa/commit/9152e9d64545e3e3e215b134b10cc180800b731c))
* ✨ SOP-10620 试吃模块新增 负库存校验和记录页面改动 ([eaac262](https://gitlab.hd123.com/vue/soa/commit/eaac2621441dbc0468caa84b55a0f5fc5c3864aa))
* ✨ SOP-10627 批次号调整模块选择商品界面逻辑调整 ([9a04065](https://gitlab.hd123.com/vue/soa/commit/9a04065fae24fe4f38fd653080c6d51ae2f710a1))
* ✨ SOP-10628 收货相关模块展示收货单号字段调整 ([9cab5c7](https://gitlab.hd123.com/vue/soa/commit/9cab5c7449138678a59907922ce4201778e0ed97))
* ✨ SOP-10630 收货散称商品支持隐藏件数字段 ([4534c2e](https://gitlab.hd123.com/vue/soa/commit/4534c2ebbac56a297116622aecef01ce5b67913b))
* ✨ SOP-10633 展示待支付账单和业务流水 ([d8c69c6](https://gitlab.hd123.com/vue/soa/commit/d8c69c6129779a99ef19ec59395a454b60320391))
* ✨ SOP-10641 调拨申请增加提交校验 ([34d803d](https://gitlab.hd123.com/vue/soa/commit/34d803d0a214e74ab2a346c6dc4bd553d42f6d85))
* ✨ SOP-10641 周黑鸭调拨申请调整优化 ([e57d554](https://gitlab.hd123.com/vue/soa/commit/e57d55462fb4eef7bca4d0a344ba6e70be3e22fb))
* ✨ SOP-10644 调拨支持记录到效期 ([5bceb70](https://gitlab.hd123.com/vue/soa/commit/5bceb701a74a90fc0102134f1ac877c53f4a00b0))
* ✨ SOP-10645 试吃-黑名单提示优化 ([176a2b6](https://gitlab.hd123.com/vue/soa/commit/176a2b6d70913699af4e5b12b370181c2feb1fa3))
* ✨ SOP-10645 试吃黑名单优化 提示icon更改 ([222140b](https://gitlab.hd123.com/vue/soa/commit/222140b17376cb9f533e2640d4306fb171cbc4a7))
* ✨ sop-10645 试吃黑名单优化 icon更改 ([d0fd807](https://gitlab.hd123.com/vue/soa/commit/d0fd807161d1f709ba1beaaa2fdce0b4810eec82))
* ✨ SOP-10646 把收货列表关键字重新改回num字段 ([4d60299](https://gitlab.hd123.com/vue/soa/commit/4d6029985bdbaacc81147bbb286c0e5dea5da0ff))
* ✨ SOP-10646 店务互通-界面显示单号统一 ([b666651](https://gitlab.hd123.com/vue/soa/commit/b666651a37d4b2a2aa8fb2bc82317c54f98837fa))
* ✨ SOP-10646 店务互通-界面显示单号统一 ([37c5acc](https://gitlab.hd123.com/vue/soa/commit/37c5acc3321edc1566f472434c2170b7e22f7a47))
* ✨ SOP-10646 店务互通-界面显示单号统一 ([f521a90](https://gitlab.hd123.com/vue/soa/commit/f521a9029f4de307e5e1d4ec7d52627bf21536aa))
* ✨ SOP-10646 店务互通-界面显示单号统一 ([77816ae](https://gitlab.hd123.com/vue/soa/commit/77816ae5a61d2bd448a838ab982bb4c8a6f7d88e))
* ✨ SOP-10647 调拨申请支持作废权限限制实现 ([2c54e26](https://gitlab.hd123.com/vue/soa/commit/2c54e264aacc1858ca3cb4d8f26aeb0ade484e25))
* ✨ SOP-10647 调拨支持驳回申请 ([2696b4d](https://gitlab.hd123.com/vue/soa/commit/2696b4d93155624a5c53896a2b9c3cbcb1bc288f))
* ✨ SOP-10650 试吃/赠送模块支持记录到效期 ([556e292](https://gitlab.hd123.com/vue/soa/commit/556e292c68ffd3dd8a5caf47fbaefb0e74811884))
* ✨ SOP-10652 收货单和订货单列表支持显示模板名称 ([b1605e0](https://gitlab.hd123.com/vue/soa/commit/b1605e0ebc0c18ae1436341dae83fa2a4b7ccb38))
* ✨ SOP-10657 调拨申请支持驳回申请 ([ea66607](https://gitlab.hd123.com/vue/soa/commit/ea666071a27dbaf7b79f5e3608b37fb1fb4d8ffb))
* ✨ SOP-10672 店务单号统一兼容历史数据 ([160d4b1](https://gitlab.hd123.com/vue/soa/commit/160d4b1c0ae2b09831975304bdd7c6b6149d695b))
* ✨ SOP-10696 减量订货源单据显示字段调整 ([3aa7c63](https://gitlab.hd123.com/vue/soa/commit/3aa7c6384244df9782c1512999e172dfcdd2e970))
* ✨ SOP-10697 调拨申请新增作废权限以及权限控制 ([e51db82](https://gitlab.hd123.com/vue/soa/commit/e51db8292e5efb4d73d579b24b2576c87c9bfee4))


### Bug Fixes

* 🐛 标签打印模块问题更改 ([5f36b4c](https://gitlab.hd123.com/vue/soa/commit/5f36b4ce116b58f0e69bfe38a1feca4c018de3c0))
* 🐛 订货支持已作废状态 ([7404601](https://gitlab.hd123.com/vue/soa/commit/7404601be1207b94d0a8ff860c57b514f09047a6))
* 🐛 减量订货增加减字样 ([4bccdf2](https://gitlab.hd123.com/vue/soa/commit/4bccdf26cfec9ee815fdc5abe87f97638da0d338))
* 🐛 模板订货删除不在模板内的商品后重新query获取信息 ([cb45d78](https://gitlab.hd123.com/vue/soa/commit/cb45d787f78d82ffb095246c24a691512b42ff74))
* 🐛 批次号生成默认格式更改 ([c5ba492](https://gitlab.hd123.com/vue/soa/commit/c5ba492d5fb1825aaecaf8938a85642bacb03eb5))
* 🐛 试吃模块 气调商品扫码添加后无溯源码解决 ([fff4854](https://gitlab.hd123.com/vue/soa/commit/fff4854226baec4d9fe12dfb25c559f4d4c70f9b))
* 🐛 试吃模块 气调商品溯源码删除错误问题解决 ([61e28af](https://gitlab.hd123.com/vue/soa/commit/61e28af246972fcf40b53104307f90a97a99b842))
* 🐛 试吃模块 扫码添加溯源码  购物车和列表商品数据不统一问题修复 ([c8370e0](https://gitlab.hd123.com/vue/soa/commit/c8370e069fb4d17ea4c6c50c043265d36bd29395))
* 🐛 试吃模块 搜索添加商品 数量回显问题 ([f435477](https://gitlab.hd123.com/vue/soa/commit/f435477970cfe2dece57108f2da44996a0309140))
* 🐛 试吃模块 搜索完商品 重置搜索page ([d38fe34](https://gitlab.hd123.com/vue/soa/commit/d38fe34a868adda117c5c4b3e7fab54e046439cb))
* 🐛 试吃模块-非气调商品删除异常问题 ([75c5758](https://gitlab.hd123.com/vue/soa/commit/75c5758025a6332b01bfc682652a188b59576aab))
* 🐛 试吃模块-商品数量减到1之后，列表数据没处理 ([dd11c9a](https://gitlab.hd123.com/vue/soa/commit/dd11c9ad80228ad9324eff4f335eebc572b7d11f))
* 🐛 试吃模块搜索框暗纹修改和试吃商品总价保留两位小数 ([98018b3](https://gitlab.hd123.com/vue/soa/commit/98018b364e57e294c7c15d2f78f0fe120ccccc61))
* 🐛 修复调拨申请保存时部分商品没有传validPeriod的问题 ([f1dc139](https://gitlab.hd123.com/vue/soa/commit/f1dc1395d7ea6c555fb34babb041323d612dbe09))
* 🐛 修复调拨申请门店显示异常的问题 ([36e0668](https://gitlab.hd123.com/vue/soa/commit/36e0668a097951c9aad622247d166e4a36e6987f))
* 🐛 修复订货编辑页面购物车数量显示异常的问题 ([33cd766](https://gitlab.hd123.com/vue/soa/commit/33cd766b2e1e3a0cfad7a126f8193cfa985177c9))
* 🐛 修复订货撤回后显示逻辑不对的问题 ([f7e79f3](https://gitlab.hd123.com/vue/soa/commit/f7e79f36d4fdd8c20900bfb15cfc9aaea6e2d641))
* 🐛 修复订货单据列表点击失效的问题 ([af2e030](https://gitlab.hd123.com/vue/soa/commit/af2e030bddcab673ccbc60514b91dd70303701c0))
* 🐛 修复订货单搜索页面未支持模板订货的问题 ([117a37c](https://gitlab.hd123.com/vue/soa/commit/117a37c3970db7e353df94000b4c42e1ec2e98e8))
* 🐛 修复订货非必定不允许编辑为0的问题 ([5dec855](https://gitlab.hd123.com/vue/soa/commit/5dec855975bb98bacf51132875fdc2fbaa88ba91))
* 🐛 修复订货消息无法跳转的问题 ([4297ddf](https://gitlab.hd123.com/vue/soa/commit/4297ddf77ef16ca913e4c5fa6a199d0df517bd6a))
* 🐛 修复减量草稿无效的问题 ([f04fd5a](https://gitlab.hd123.com/vue/soa/commit/f04fd5a22885cb0e72ac26c907a4102b87870297))
* 🐛 修复减量订货未传目标单据的问题 ([04de9e9](https://gitlab.hd123.com/vue/soa/commit/04de9e9e955f71c6ba8c52aa91bad7e61bafc1e3))
* 🐛 修复减量订货详情商品列表显示的标题不对的问题 ([edb01b7](https://gitlab.hd123.com/vue/soa/commit/edb01b7a3c37584b3cb53c5bfb21c1891be5c9a2))
* 🐛 修复减量订货修改数量无效的问题 ([152c682](https://gitlab.hd123.com/vue/soa/commit/152c682c86c44e3e8f22502c30d774e296166e45))
* 🐛 修复模板订货草稿仍需保存的问题 ([76108e5](https://gitlab.hd123.com/vue/soa/commit/76108e557966b1d19235fe7174ddc4baaa94e5da))
* 🐛 修复模板订货加量详情显示加单订货的问题 ([bf78171](https://gitlab.hd123.com/vue/soa/commit/bf781713626d2c26195bbb145a6a243301650f26))
* 🐛 修复模板订货仍显示分类的问题 ([6b3d746](https://gitlab.hd123.com/vue/soa/commit/6b3d746303510aca62a9538e723f82c72aec2199))
* 🐛 修复模板订货未将商品名做2行省略的问题 ([e609739](https://gitlab.hd123.com/vue/soa/commit/e6097397aa13f1cda91966eb4d7d5c558fd6e4d9))
* 🐛 修复模板订货详情未显示减量来源单据 ([411cdaf](https://gitlab.hd123.com/vue/soa/commit/411cdafc1d7cd36af9147914c605681b03115a45))
* 🐛 修复模板订货详情未显示审批金额的问题 ([224d254](https://gitlab.hd123.com/vue/soa/commit/224d254e36facaeb9c739867fac09c13edcb8140))
* 🐛 修复模板订货选择模板底部空白区域大的问题 ([dc09b12](https://gitlab.hd123.com/vue/soa/commit/dc09b127f25e94cfb5c11f804f1ad8ae873e3e02))
* 🐛 修复模板订货展示预计到货时间不对的问题 ([4d8534f](https://gitlab.hd123.com/vue/soa/commit/4d8534fd9c2689edc3d52f2ea0e6f396edee7de8))
* 🐛 修复批次号调整自动带出的批次号可以调整日期得问题 ([0929124](https://gitlab.hd123.com/vue/soa/commit/0929124dab8bde216fcf87b793fe44eb41d85d32))
* 🐛 修复收货单列表显示订货单号没有优先取reqOrdDisplayNum的问题 ([b311bfa](https://gitlab.hd123.com/vue/soa/commit/b311bfa440ccc2e2fc74c16ec5aab0bd34e46daf))
* 🐛 修复通知退货单显示金额数量异常的问题 ([bdab90b](https://gitlab.hd123.com/vue/soa/commit/bdab90b68fff9b1e67b82c06edd6474c2a8f05e5))
* 🐛 修复退货申请拖拽按钮导致点击无法穿透的问题 ([b0946db](https://gitlab.hd123.com/vue/soa/commit/b0946db1206bfb6877124e436226f40cf323a154))
* 🐛 修复消息通知中心差异申请单无法跳转的问题 ([e1aa22f](https://gitlab.hd123.com/vue/soa/commit/e1aa22fe17aaa499acd908b9e9aef5da31f61eb1))
* 🐛 修复直送退货计算了alcSinglePrice的问题 ([f52c43a](https://gitlab.hd123.com/vue/soa/commit/f52c43a1ed22d7a8313ee6801094b62ad38d41ce))
* 🐛 修复周黑鸭退货申请详情显示效期行数量和原因行数量对不上的问题 ([61e36a0](https://gitlab.hd123.com/vue/soa/commit/61e36a0b801cef4c6ad6c2e76e24053b5e8ae31b))
* 🐛 修复drag-button改动后影响slot的点击 ([5373df1](https://gitlab.hd123.com/vue/soa/commit/5373df1deeebc4ffb3f66e6963ac282f0872cb36))
* 🐛 自主盘点 盈亏明细效期 数量和金额逻辑变更 ([7e47193](https://gitlab.hd123.com/vue/soa/commit/7e47193ffc28f01ceb98e3f773029212527d0611))
* 🐛 自主盘点-模板商品提交问题解决 ([429eafd](https://gitlab.hd123.com/vue/soa/commit/429eafd77342e27fced77c5bdba3987213f48c40))
* 🐛 自主盘点空模板问题修复 ([e46e042](https://gitlab.hd123.com/vue/soa/commit/e46e042f60599c09f7dd0c427afc40d47bb6d49b))
* 🐛 SOP-10485 盘点单详情和盈亏屏蔽非气调商品的批号 ([077e87f](https://gitlab.hd123.com/vue/soa/commit/077e87f7565ca583469a5d3952be1d0345505b10))
* 🐛 SOP-10531 ([8b0ff28](https://gitlab.hd123.com/vue/soa/commit/8b0ff2836c3021eb7de622d97ebab1125d1fbbe6))
* 🐛 SOP-10531 过期提示样式更改 ([ac2562f](https://gitlab.hd123.com/vue/soa/commit/ac2562fcec513d6f53a77e56f489462743513b46))
* 🐛 SOP-10531 气调商品三小时内过期，也可以加入购物车 ([1f9ad98](https://gitlab.hd123.com/vue/soa/commit/1f9ad98cc06e2c5d8a652928f75dddb7f3d1ca0a))
* 🐛 SOP-10531 试吃模块界面问题汇总 ([134838d](https://gitlab.hd123.com/vue/soa/commit/134838d162b1435427187167a89a668a5b4f3883))
* 🐛 SOP-10531 试吃模块问题汇总 ([780a290](https://gitlab.hd123.com/vue/soa/commit/780a290c4b88de9e1fd0f0688b7722bb7432d8cd))
* 🐛 SOP-10531 toast提示icon更改 ([87f13e7](https://gitlab.hd123.com/vue/soa/commit/87f13e7a1ff309254143eb2417fd8ccf8d1703e0))
* 🐛 SOP-10532 批次号调整模块部分问题修复 ([2ed77b0](https://gitlab.hd123.com/vue/soa/commit/2ed77b08f20476c149de6f47da8e51fb1925646e))
* 🐛 SOP-10532 批次号调整模块查看权限绑定调整 ([63a4240](https://gitlab.hd123.com/vue/soa/commit/63a42405eb2b071dbfad7b53439e436507103168))
* 🐛 SOP-10536 修复独立订货提交时跳转详情页面传参错误的问题 ([ae3f58a](https://gitlab.hd123.com/vue/soa/commit/ae3f58a253217f54f4ddb0643819ddfac0a2eabe))
* 🐛 SOP-10537 修复模板订货测试问题 ([e7e8ba7](https://gitlab.hd123.com/vue/soa/commit/e7e8ba73078ea449771f095b0da3c70988824135))
* 🐛 SOP-10542 配货差异模块数值变化逻辑取消toast ([ddb3100](https://gitlab.hd123.com/vue/soa/commit/ddb3100e499e3041793ec512644881e7f90df78f))
* 🐛 SOP-10542 配货差异申请模块部分问题修复 ([eed4536](https://gitlab.hd123.com/vue/soa/commit/eed4536bcd5c614430ca0c07927d7073410244a2))
* 🐛 SOP-10542 配货差异申请模块加购为0时交互调整 ([e42c19e](https://gitlab.hd123.com/vue/soa/commit/e42c19e3b5f6652691ea243e1fa2fe244807d5b6))
* 🐛 SOP-10542 配货差异详情页商品展示调整 ([e8d4618](https://gitlab.hd123.com/vue/soa/commit/e8d461828ecc25cb87b6a15a4c69c2cc62276451))
* 🐛 SOP-10549 修复订货列表已提交仍展示更新人的问题 ([1348981](https://gitlab.hd123.com/vue/soa/commit/1348981d380f646ddbdee922b7aa1c967b7ac112))
* 🐛 SOP-10561 ([ef0d83c](https://gitlab.hd123.com/vue/soa/commit/ef0d83c61e9b72c896e5747ffd296a03c820a362))
* 🐛 SOP-10561 ([144ed0d](https://gitlab.hd123.com/vue/soa/commit/144ed0d24cdd7e0502f8a0603903684ef150d825))
* 🐛 SOP-10561 自主盘点模块问题修改 ([89342f3](https://gitlab.hd123.com/vue/soa/commit/89342f34071b3dbb10b4727d916062145debef2d))
* 🐛 SOP-10602 订货支持撤回 ([eb730ef](https://gitlab.hd123.com/vue/soa/commit/eb730ef34bc7dd1466616dfddefb34888c28b45e))
* 🐛 SOP-10606 试吃模块商品校验接口参数更改以及记录增加条码代码 ([4a6e541](https://gitlab.hd123.com/vue/soa/commit/4a6e541ba2d64fe1c09f1c61b079981c8bf99640))
* 🐛 SOP-10616 修复订货撤回相关问题 ([3bf9414](https://gitlab.hd123.com/vue/soa/commit/3bf9414272374325837b59876b161d655a37cdaf))
* 🐛 SOP-10624 控制退货申请添加批次的日期为今天及之前 ([91b29a8](https://gitlab.hd123.com/vue/soa/commit/91b29a84bfeae49e3565f862acbd06b52ceb87c1))
* 🐛 SOP-10625 退货手动添加的效期不允许重复，扫码的可以 ([eeebc3c](https://gitlab.hd123.com/vue/soa/commit/eeebc3ca9c95e04885984f481fe5fe2740186b0c))
* 🐛 SOP-10638 修复保存后的退货单再次添加气调商品展示异常的问题 ([b668771](https://gitlab.hd123.com/vue/soa/commit/b6687715d175bceaba2385dd7aee49fb7389acec))
* 🐛 SOP-10639 修复退货申请操作效期行时未区分扫码和手工录入的问题 ([1f80a53](https://gitlab.hd123.com/vue/soa/commit/1f80a53fa709a95a4e0d75d692a0ff8e10bc15bf))
* 🐛 SOP-10640 修复资金流水相关问题 ([9e2abf3](https://gitlab.hd123.com/vue/soa/commit/9e2abf3e99b295790b9cb1eed7eae79b4d78b0d6))
* 🐛 SOP-10663 标签打印模块 商品售价取值变更 ([d81019d](https://gitlab.hd123.com/vue/soa/commit/d81019d4fccf33ac80b0d9b89b177ee58bee81bf))
* 🐛 SOP-10663 标签打印模块页面显示问题 ([666110a](https://gitlab.hd123.com/vue/soa/commit/666110ab8bb52a9d5f48a4953e71cbc694654484))
* 🐛 SOP-10663 标签打印模块页面显示问题修复 ([a4c5b1c](https://gitlab.hd123.com/vue/soa/commit/a4c5b1c42bd694f18f717bc04e0dd3067e9108db))
* 🐛 SOP-10670 修复调价申请模块钉钉小程序白屏的问题 ([f977796](https://gitlab.hd123.com/vue/soa/commit/f977796c55ab2114e9ea10c14bc5a29166d77caa))
* 🐛 SOP-10699 调拨申请详情点击查看更多时也需要展示批次号 ([0ca316b](https://gitlab.hd123.com/vue/soa/commit/0ca316b6349d58e5d5e84b7c296b6b76f8e1b134))

### [2.45.1](https://gitlab.hd123.com/vue/soa/compare/v2.45.0...v2.45.1) (2024-04-09)


### Features

* ✨ SOP-10480 订货支持控制展示商品保质期天数与到效期 ([5077224](https://gitlab.hd123.com/vue/soa/commit/5077224818febb9559afdfdbf11b983b242cfeb5))

## [2.45.0](https://gitlab.hd123.com/vue/soa/compare/v2.44.1...v2.45.0) (2024-04-01)


### Features

* ✨ SOP-10280 报损/报溢模块查原因的接口增加reasonCls参数 ([1c4ce6d](https://gitlab.hd123.com/vue/soa/commit/1c4ce6d40f6be2884feb665d878dc0f2aeba7a7e))
* ✨ SOP-10369 收货及退货支持隐藏配货价和配货金额 ([f0bb134](https://gitlab.hd123.com/vue/soa/commit/f0bb134b2532a80a8bb26e3a1c493f03934f1bd2))
* ✨ SOP-10406 复盘盘点计划商品面板支持只展示盘点范围内商品的分类 ([854e925](https://gitlab.hd123.com/vue/soa/commit/854e92527bf48d5bdaf5c4ea658fa753748ae700))


### Bug Fixes

* 🐛 SOP-10369 退仓、退公司、退供应商详情页显示优化 ([f298595](https://gitlab.hd123.com/vue/soa/commit/f298595aaa4078d52a665f63af4ec673c33d01d8))
* 🐛 SOP-10369 退货申请详情页调整 ([d96659a](https://gitlab.hd123.com/vue/soa/commit/d96659ae76dc401711a01010b7115fc088082e90))

### [2.44.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.44.0...v2.44.1) (2024-03-27)


### Features

* ✨ SOP-10450 任务模块，点击添加图片按钮交互失效修复 ([09b3cb9](https://gitlab.hd123.com:20022/vue/soa/commit/09b3cb95ac9176af18057c60fab8e708158cc42f))
* ✨ SOP-10450 任务模块传参调整 ([368cc01](https://gitlab.hd123.com:20022/vue/soa/commit/368cc0181feba6caafc4bb3fc6c8b161eac74c80))
* ✨ SOP-10450 任务日期显示逻辑调整 ([e15d09c](https://gitlab.hd123.com:20022/vue/soa/commit/e15d09c0a3a31597f72d9b0d2088f1281db1c8cd))
* ✨ SOP-10450 任务日期显示优化 ([1ad7380](https://gitlab.hd123.com:20022/vue/soa/commit/1ad7380a02fe8b63670be8725b45f2f7a5eaedf4))
* ✨ SOP-10450 上传组件过滤器oss拼接参数特殊处理 ([3bc4206](https://gitlab.hd123.com:20022/vue/soa/commit/3bc4206018ab4c05941c5f429e91f5a685bce71b))
* ✨ SOP-10450 上传组件拼接参数调整 ([574d568](https://gitlab.hd123.com:20022/vue/soa/commit/574d56861adf775cbc3766ebbd29ece77e0ef90f))
* ✨ SOP-10450 温岭三和.任务日期格式显示优化 ([84e8a85](https://gitlab.hd123.com:20022/vue/soa/commit/84e8a856ad251c5340f0ed29d12cbb7a4fec1bcb))
* ✨ SOP-10450 增加控制判断 ([7637c12](https://gitlab.hd123.com:20022/vue/soa/commit/7637c12dec4d6f83454cb28d6c9f006067e5be45))

## [2.44.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.43.3...v2.44.0) (2024-03-19)


### Features

* ✨ SOP-10403 账单管理显示商家实收金额 ([107f58e](https://gitlab.hd123.com:20022/vue/soa/commit/107f58e8e8a3dc7c8b976791e06f7f3d84374fec))
* ✨ SOP-10404 门店助手独立叫货增加商品体积显示 ([959c994](https://gitlab.hd123.com:20022/vue/soa/commit/959c99458c5d1c7ba6aa20dc9c258a851bcd9838))
* ✨ SOP-10411 温岭三和 任务模块描述支持全部字数展示 ([2e79085](https://gitlab.hd123.com:20022/vue/soa/commit/2e7908524099b31e40ec824506cc0d169410059b))
* ✨ SOP-10411 温岭三和 任务模块优化需求 ([352cb63](https://gitlab.hd123.com:20022/vue/soa/commit/352cb636bf2c6c0c9710adcad09a7245ae7364e7))
* SOP-10366 计划盘点限制只允许编辑自己的初盘单 ([5ba10f0](https://gitlab.hd123.com:20022/vue/soa/commit/5ba10f0e733b5546f81a204e8c10ba30454b71ab))


### Bug Fixes

* 🐛 盘点结束人取值调整 ([7ce411f](https://gitlab.hd123.com:20022/vue/soa/commit/7ce411fc926143b289c87824a2a2bef9a1325a67))
* 🐛 SOP-10366 盘点编辑别人的单据交互优化 ([d90a6c7](https://gitlab.hd123.com:20022/vue/soa/commit/d90a6c717db2a739152fa46c3b8de6ee497757ff))
* 🐛 SOP-10366 盘点结束盘点前需要登录人 ([c379150](https://gitlab.hd123.com:20022/vue/soa/commit/c3791509c78a5bd8f6208d2ea081a44ae184a34a))
* 🐛 SOP-10366 盘点人取值优化 ([0e5a493](https://gitlab.hd123.com:20022/vue/soa/commit/0e5a4934fc988517af9231b6702ff045a0f7fe8c))
* 🐛 SOP-10366 盘点人字段取值调整，盈亏月增加盘点人弹窗 ([5263cd3](https://gitlab.hd123.com:20022/vue/soa/commit/5263cd3d8d85e5753a1c7bab3ccbc8b6f72a55d9))
* 🐛 SOP-10446 复盘页面优化 ([c7eb970](https://gitlab.hd123.com:20022/vue/soa/commit/c7eb97021f8997da091e83c629368065d87c7bf8))
* 🐛 SOP-10446 划盘点只允许编辑自己创建的单据.页面取值优化 ([4542d6d](https://gitlab.hd123.com:20022/vue/soa/commit/4542d6ddcdab5b442de66be26b56f1a72a47db82))
* SOP-10404 建议叫货增加商品体积显示 ([7fad6d9](https://gitlab.hd123.com:20022/vue/soa/commit/7fad6d990ccc6c7b0113a73678700cf8ad486698))
* SOP-10404 门店助手独立叫货增加商品体积显示 ([92ca713](https://gitlab.hd123.com:20022/vue/soa/commit/92ca7130a16c260bc73fdcc1fd6e249a7ce19fb2))

### [2.43.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.43.2...v2.43.3) (2024-03-13)


### Bug Fixes

* 🐛 修复标准订货非新品仍展示新标签的问题 ([a9c9c89](https://gitlab.hd123.com:20022/vue/soa/commit/a9c9c89f2c244a161fc909e7b9ad82f0959f2912))
* 🐛 HB-87683 独立叫货配货建议单提交不成功 ([3b02a0b](https://gitlab.hd123.com:20022/vue/soa/commit/3b02a0bad964d60a026c7cca1352d0cbf432b7af))
* 🐛 SOP-10429 独立订货建议叫货兼容库存金额和超出上下限 ([7d98348](https://gitlab.hd123.com:20022/vue/soa/commit/7d98348a59ee999ab2c96c901d8df70c826b82bd))

### [2.43.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.43.1...v2.43.2) (2024-03-08)


### Bug Fixes

* 🐛 🐛 SOP-10415 修复订货商品名在某些iOS手机上商品名称只可以展示一行的问题 ([e20890d](https://gitlab.hd123.com:20022/vue/soa/commit/e20890d407bb2900abc32611564b2a012ad9ac76))
* 🐛 商品效期查询时间区间调整为前后各2年 ([e5eb2d2](https://gitlab.hd123.com:20022/vue/soa/commit/e5eb2d2fe7ea30fba820deeed1a53332917ab6ed))
* 🐛 SOP-10414 商品效期日期选择商品显示问题定位 ([9737d6f](https://gitlab.hd123.com:20022/vue/soa/commit/9737d6fe9de64e457e1194cda200c2bbb7c259ca))
* 🐛 SOP-10414 商品效期样式优化 ([634711d](https://gitlab.hd123.com:20022/vue/soa/commit/634711d10d3b43ac5f93a144d8d6382079d337f5))
* 🐛 SOP-10415 修复订货商品名在某些iOS手机上商品名称只可以展示一行的问题 ([55883e2](https://gitlab.hd123.com:20022/vue/soa/commit/55883e2fa4f477102bbb62425c0e4110b2ac2199))

### [2.43.1](https://gitlab.hd123.com/vue/soa/compare/v2.43.0...v2.43.1) (2024-03-01)


### Features

* ✨ SOP-10373 异常商品模块部分显示内容调整 ([08e27bf](https://gitlab.hd123.com/vue/soa/commit/08e27bfccc866fd5ed881ab96a46c55801d65d5b))
* ✨ SOP-10373 异常商品模块处置页面日期格式调整 ([4ea2bab](https://gitlab.hd123.com/vue/soa/commit/4ea2bab1fdb0a6205e8e9018b7628cd00c5520d8))

## [2.43.0](https://gitlab.hd123.com/vue/soa/compare/v2.42.1...v2.43.0) (2024-02-28)


### Features

* ✨ SOP-10232 删除多余图片 ([af20752](https://gitlab.hd123.com/vue/soa/commit/af20752d608226d8a24a7f9302e8db293ef9a798))
* ✨ SOP-10232 异常商品模块框架搭建 ([3852509](https://gitlab.hd123.com/vue/soa/commit/3852509797c03d56f87d01147c56d232e129ac6a))
* ✨ SOP-10233 异常商品列表页增加商品条码字段 ([3aabaf2](https://gitlab.hd123.com/vue/soa/commit/3aabaf211614b24e8f34a9c28490bfed6a604f88))
* ✨ SOP-10233 异常商品模块，页面名称修改 ([8e4c03a](https://gitlab.hd123.com/vue/soa/commit/8e4c03a743a546a48cc7345c8012f14234f68797))
* ✨ SOP-10233 增加异常商品模块 ([3f2ef71](https://gitlab.hd123.com/vue/soa/commit/3f2ef710d3929c4a89c0aed85b6f11ee2cc4f3ae))
* ✨ SOP-10278 独立订货 按门店类型配置订货价,前端调整接口模型 ([9eae5d7](https://gitlab.hd123.com/vue/soa/commit/9eae5d7c73b670c1fc41f1249baa01b02a6820ab))
* ✨ SOP-10279 调拨和报损模块 价格字段增加权限控制 ([ef4e772](https://gitlab.hd123.com/vue/soa/commit/ef4e772ea8299b0d3418b1c7c46fcec593ba6b55))
* ✨ SOP-10279 调拨和报损模块价格支持配置控制逻辑调整 ([48cddd6](https://gitlab.hd123.com/vue/soa/commit/48cddd685c00255e72fdf32d5717d006e600b07e))
* ✨ SOP-10279 调拨模块调整 ([b426925](https://gitlab.hd123.com/vue/soa/commit/b426925dc438790ac29ab9c67a232e20670d0327))
* ✨ SOP-10279 调拨模块售价总额展示逻辑调整 ([1c87d2f](https://gitlab.hd123.com/vue/soa/commit/1c87d2f7d2e1dca98b53915ee9a5fe2a45c5bef2))
* ✨ SOP-10316 直配收货支持录入生产日期、到效日期 ([d77e52d](https://gitlab.hd123.com/vue/soa/commit/d77e52d436f9c1653bbe02ab58f5e2dc6df96183))
* ✨ SOP-10333 异常商品模块显示调整 ([388f25f](https://gitlab.hd123.com/vue/soa/commit/388f25fc6e98e14999e3a9c1e327dd0cb471e4e6))
* ✨ SOP-10342 异常商品门店名称过长显示调整 ([4867c52](https://gitlab.hd123.com/vue/soa/commit/4867c52c7f95adbefacb037c3e6f8218d0abb9cd))
* SOP-10286 标准版独立订货支持叫货上下限控制功能 ([cecde70](https://gitlab.hd123.com/vue/soa/commit/cecde70b2a24b94d031d064579e9eb5a6f4f18b8))
* SOP-10317 标准订货页面支持展示实物库存和在途库存和未发货库存 ([0733315](https://gitlab.hd123.com/vue/soa/commit/073331563efcd8ec23ef649b6c59da465b275ec0))
* SOP-10318 独立订货增加库存金额控制 ([99615d9](https://gitlab.hd123.com/vue/soa/commit/99615d9f4956454f893e6c12f52acb8dbf69ede7))


### Bug Fixes

* 🐛 修复收货编辑生产日期始终赋值到第一个商品的问题 ([6ddd9ff](https://gitlab.hd123.com/vue/soa/commit/6ddd9ff5063c281bd997ee07e2da103c04d71c84))
* 🐛 SOP-10278 独立订货增加配货总金额计算逻辑 ([8e52481](https://gitlab.hd123.com/vue/soa/commit/8e5248161d60683510c59de01fa5f0e4863de458))
* 🐛 SOP-10279 调拨申请搜索添加商品增加售价传参 ([1f5361d](https://gitlab.hd123.com/vue/soa/commit/1f5361d205977fb32e412546019dfb3afa5d1115))
* 🐛 SOP-10332 样式问题调整 ([5ff2e6a](https://gitlab.hd123.com/vue/soa/commit/5ff2e6adff206a2a613bdbb2c1231c1f4b9d5ed0))
* 🐛 SOP-10332 异常商品模块，处置弹窗名称显示样式兼容 ([ddb3dbc](https://gitlab.hd123.com/vue/soa/commit/ddb3dbc65a55806272923555a47ee8b9ea5dfd06))
* 🐛 SOP-10332 异常商品模块兼容钉钉 ([8564fc0](https://gitlab.hd123.com/vue/soa/commit/8564fc05cc0534c3d3634f7f8095f860f7b4db92))
* 🐛 SOP-10332 异常商品模块问题修复 ([572de45](https://gitlab.hd123.com/vue/soa/commit/572de452538f9f08f298723bbfb82ff597f01ede))
* 🐛 SOP-10342 异常商品列表页全选逻辑调整 ([0bd6c38](https://gitlab.hd123.com/vue/soa/commit/0bd6c386cb608c2d540bec5d469a476b0c41eb0b))
* 🐛 SOP-10342 异常商品模块问题修复；调拨模块提交时售价传参调整 ([632f5df](https://gitlab.hd123.com/vue/soa/commit/632f5dffdd1173a5bc706cc323d1c480020fd678))
* 🐛 SOP-10342 异常商品模块页面刷新逻辑调整 ([938f77f](https://gitlab.hd123.com/vue/soa/commit/938f77f92ee9edbfbd9361f3dfa104ab4a95fd26))
* 🐛 SOP-10342 异常商品模块支持通知栏跳转到对应门店日期下的异常页面 ([2bf398c](https://gitlab.hd123.com/vue/soa/commit/2bf398c392aae5e3f0f980f1758b62f4a4b6e14d))
* 🐛 SOP-10342 异常商品批量处置按钮增加禁用状态 ([fe468dc](https://gitlab.hd123.com/vue/soa/commit/fe468dcd615aea8fec76d3c1101743f15a8a3aef))
* 🐛 SOP-10342 异常商品批量处置按钮增加禁用状态调整 ([5337d7e](https://gitlab.hd123.com/vue/soa/commit/5337d7e454345586f172d66fcf55b9e514e37f77))
* 🐛 SOP-10342 异常商品详情弹窗单品补差额计算逻辑调整 ([7aef196](https://gitlab.hd123.com/vue/soa/commit/7aef1967953dbafc3099f88f48de56a2f74e94b4))
* 🐛 SOP-10342 异常商品详情页单品补差额计算调整 ([86bf0d1](https://gitlab.hd123.com/vue/soa/commit/86bf0d1117eb6f9d1289676baf43ace502eef42c))
* 🐛 SOP-10364 异常商品模块问题修复 ([623be12](https://gitlab.hd123.com/vue/soa/commit/623be129e33dd94ac79b984a652d71f8b1ad5f88))
* SOP-10317 订货页面支持按配置展示实物库存和在途库存和未发货库存 ([dd0566d](https://gitlab.hd123.com/vue/soa/commit/dd0566d8730c17e98d0dbd5fac835fb8884c8471))

### [2.42.1](https://gitlab.hd123.com/vue/soa/compare/v2.42.0...v2.42.1) (2024-01-25)


### Bug Fixes

* 🐛 修复独立订货存在跳转到商品的箭头而实际上未实现该功能的问题-移除箭头 ([cf087e3](https://gitlab.hd123.com/vue/soa/commit/cf087e3e0a5b7fe813d98bff9898084623166348))
* 🐛 SOP-10305 增加商品效期模块的权限 ([8170dcc](https://gitlab.hd123.com/vue/soa/commit/8170dcc46ae72f47d81228b6bfd3242baeb22ad9))

## [2.42.0](https://gitlab.hd123.com/vue/soa/compare/v2.41.0...v2.42.0) (2024-01-22)


### Features

* ✨ SOP-10211 零食有鸣支持校验校验员工是否允许登陆 ([914fc02](https://gitlab.hd123.com/vue/soa/commit/914fc021b3918fa5e5f684cf370f7a42ed7a032b))
* ✨ SOP-10262 调查类任务图片支持预览 ([c22409a](https://gitlab.hd123.com/vue/soa/commit/c22409a15ca7aa985435ce48c58ce378a58d855b))
* ✨ SOP-10262 调查类任务新增图片展示 ([e9b2299](https://gitlab.hd123.com/vue/soa/commit/e9b2299a42688046466e8d58f456ac1010d67190))

## [2.41.0](https://gitlab.hd123.com/vue/soa/compare/v2.40.1...v2.41.0) (2024-01-10)


### Features

* ✨ SOP-10159 支持限制门店短时间内不可叫货 ([4282c83](https://gitlab.hd123.com/vue/soa/commit/4282c83d29de94f448e85831538cc9a70fc01ded))

### [2.40.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.40.0...v2.40.1) (2023-12-28)


### Bug Fixes

* 🐛 订货吱口令报错提示优化 ([806088a](https://gitlab.hd123.com:20022/vue/soa/commit/806088a3d9477bfc305a7b1dd4b78676410545c6))
* 🐛 sop-10063订货选择自提支持复制单据自提方式 ([8baf57f](https://gitlab.hd123.com:20022/vue/soa/commit/8baf57fd55de93137cca54c6587f39eedae86db2))
* 🐛 SOP-10114 标准订货吱口令支付优化 ([fbd4d08](https://gitlab.hd123.com:20022/vue/soa/commit/fbd4d08adf746865bd7595e6a027aa5d5acd15db))
* 🐛 SOP-10114 标准订货吱口令支付优化 ([acce51c](https://gitlab.hd123.com:20022/vue/soa/commit/acce51c513c934a9d19c01fd43ba2abc53329a4b))
* 🐛 SOP-10114 订货吱口令支付后优化 ([ab523cb](https://gitlab.hd123.com:20022/vue/soa/commit/ab523cb49958cc19effd3629991cdd04929299bb))

## [2.40.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.38.3...v2.40.0) (2023-12-20)


### Features

* ✨ SOP-10018 爆品创建普通行的明细商品时，price和total字段取值逻辑调整 ([2748b6e](https://gitlab.hd123.com:20022/vue/soa/commit/2748b6e5d9251fc0f589d66f2098da136247669b))
* ✨ SOP-10018 爆品可购数量与加购数量取值比较调整 ([d753605](https://gitlab.hd123.com:20022/vue/soa/commit/d753605a5a73d79cb4a21a03adfeb6b213064d1d))
* ✨ SOP-10018 爆品可叫货数变化后价格计算位置调整 ([14522bf](https://gitlab.hd123.com:20022/vue/soa/commit/14522bf73fddfeaf2916434dcdcf51a9a8f4e026))
* ✨ SOP-10018 爆品提交时价格变动和可购数量变动校验提示取值调整 ([7cf20cf](https://gitlab.hd123.com:20022/vue/soa/commit/7cf20cf72604b7b5144137bc8817c952eb13cf29))
* ✨ SOP-10018 爆品提交时价格变动后改价逻辑调整 ([ea55565](https://gitlab.hd123.com:20022/vue/soa/commit/ea55565045a04989f6d539b67accaa96f1e26377))
* ✨ SOP-10018 订货商品报名数字段调整，叫货购物车页面已订数量字段取值调整 ([9b17987](https://gitlab.hd123.com:20022/vue/soa/commit/9b17987c680407b127a0fe19588ca12cfe88e853))
* ✨ SOP-10018 购物车提交时爆品参数构建字段取值调整 ([17da8d0](https://gitlab.hd123.com:20022/vue/soa/commit/17da8d062dda5868769924a28cc82a148993e31d))
* ✨ SOP-10018 恢复报名数，已定数和可订数的展示 ([d4ecf7f](https://gitlab.hd123.com:20022/vue/soa/commit/d4ecf7f17b2a41d488ca75f508656904f240ddbe))
* ✨ SOP-10057 标准订货吱口令支付 ([e7dc628](https://gitlab.hd123.com:20022/vue/soa/commit/e7dc628e4e37421f76882b6a6b6ab0e5c21c9505))
* ✨ SOP-10058 直送收货提交增加二次确认与新增商品置顶 ([32f76a0](https://gitlab.hd123.com:20022/vue/soa/commit/32f76a0cd0ddbe606b1ef1269dfc981defcefa90))
* ✨ SOP-10063 门店叫货支持仓库自提 ([5dfb4ce](https://gitlab.hd123.com:20022/vue/soa/commit/5dfb4cec24e8d83fc147783a0763b368dafe89bd))


### Bug Fixes

* 🐛 标准订货待处理单据显示修复 ([10497eb](https://gitlab.hd123.com:20022/vue/soa/commit/10497ebeeb951e84e9cb7cfaae7cf505a43af2ff))
* 🐛 吱口令余额按后端返回字段显示 ([8d96769](https://gitlab.hd123.com:20022/vue/soa/commit/8d96769baaff26f9d9463ea3f42aea9cb6c04f13))
* 🐛 SOP-10053 门店调拨单未收货完成不允许盘点 ([bb67bae](https://gitlab.hd123.com:20022/vue/soa/commit/bb67baeb0744a37ac1a9994b8fda83bdf553baa7))
* 🐛 SOP-10057 标准订货吱口令支付显示优化 ([c6f37eb](https://gitlab.hd123.com:20022/vue/soa/commit/c6f37ebf40cb84af95ca37a71203c2acb076aeef))
* 🐛 SOP-10063 仓库自提购物车修改需要同步到购物车 ([d784284](https://gitlab.hd123.com:20022/vue/soa/commit/d78428421fcc67eba37a1d9dd4528ae864e3d7c3))
* 🐛 SOP-10063 未显示自提则传不自提 ([9ae0bdc](https://gitlab.hd123.com:20022/vue/soa/commit/9ae0bdc7f453459705c4fc44d1b8e9d0b46ac555))
* 🐛 SOP-10114 标准订货吱口令支付优化 ([5a6fd8c](https://gitlab.hd123.com:20022/vue/soa/commit/5a6fd8cd29aa9719dbe5bf35f8b224c231b93500))
* 🐛 SOP-10114 标准订货吱口令支付优化 ([3a3d043](https://gitlab.hd123.com:20022/vue/soa/commit/3a3d043a23a75a69fded728b206f351561d4161f))

## [2.39.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.38.2...v2.39.0) (2023-12-05)


### Features

* ✨ SOP-10007 初盘界面展示待盘商品需求兼容实现 ([16c2324](https://gitlab.hd123.com:20022/vue/soa/commit/16c2324bac4c2fb71b20a5aa1455de461e8f7a1f))
* ✨ SOP-10007 初盘界面展示待盘商品需求兼容实现 ([21aba44](https://gitlab.hd123.com:20022/vue/soa/commit/21aba44abb4e5da7c8b16901c392e92fa26d1e7f))
* ✨ SOP-10007、SOP-9976 爆品订货与调拨申请部分问题修复 ([d14d11a](https://gitlab.hd123.com:20022/vue/soa/commit/d14d11abcc19b66bba2243658e63d561efb4c828))
* ✨ SOP-10018 爆品保存草稿时价格字段构建 ([570840e](https://gitlab.hd123.com:20022/vue/soa/commit/570840e5a9089a242c986223a370c85309dc8c40))
* ✨ SOP-10018 爆品订货保存接口商品行增加子明细字段传参 ([d0474b3](https://gitlab.hd123.com:20022/vue/soa/commit/d0474b35f4ca2315e83dd95e36317d961d5289da))
* ✨ SOP-10018 爆品活动价格为0是也算作爆品 ([fd96051](https://gitlab.hd123.com:20022/vue/soa/commit/fd96051dda6fe883fc1714afeb8b7ee4656dfa1b))
* ✨ SOP-10018 修改爆品可订数量相关字段 ([aa9f4f1](https://gitlab.hd123.com:20022/vue/soa/commit/aa9f4f1993397b434ff4f9eecbdcb8af118e56b9))
* ✨ SOP-3217 爆品活动弹窗state限制允许CONFIRMED状态活动的展示 ([a26458e](https://gitlab.hd123.com:20022/vue/soa/commit/a26458ebde6b2dd4a6015103cc5f90d47802d5ea))
* ✨ SOP-9945 爆品活动弹窗展示逻辑调整 ([7a16321](https://gitlab.hd123.com:20022/vue/soa/commit/7a163216280da8fbf48b711841f9c703f9245fa5))
* ✨ SOP-9945 爆品活动订货提交后库存不足弹窗商品显示的价格取值逻辑调整 ([8caa4c9](https://gitlab.hd123.com:20022/vue/soa/commit/8caa4c90a96ead6852f26c1711ccbe988f7f3db4))
* ✨ SOP-9945 爆品活动价格计算逻辑调整 ([1a762c6](https://gitlab.hd123.com:20022/vue/soa/commit/1a762c68f91cbc0f2015bfa85c3a5c398dc6d704))
* ✨ SOP-9945 爆品活动展示逻辑调整 ([6553efc](https://gitlab.hd123.com:20022/vue/soa/commit/6553efc550a9eaa11998be3937a434a546c9a84a))
* ✨ SOP-9945 订货单区分爆品活动单据显示 ([52e59bf](https://gitlab.hd123.com:20022/vue/soa/commit/52e59bfa6933a0c1106bc3ab2bf89a3407c09330))
* ✨ SOP-9976 调拨模块的商品总金额字段取值主分支逻辑同步 ([a3f9853](https://gitlab.hd123.com:20022/vue/soa/commit/a3f985356e7586f9b730f0d1d90aa5ab247f6485))
* ✨ SOP-9976 调拨模块计算金额字段逻辑从主分支同步 ([494e51f](https://gitlab.hd123.com:20022/vue/soa/commit/494e51f373593f7505d2cc6c274743c53fce4108))
* ✨ SOP-9976 调拨模块商品总额显示字段调整 ([bfd9114](https://gitlab.hd123.com:20022/vue/soa/commit/bfd9114a0fb7014810fe2dfdcb5676f2f54c790c))
* ✨ SOP-9976 调拨模块商品总额显示字段调整 ([3993fce](https://gitlab.hd123.com:20022/vue/soa/commit/3993fce43f71b7389765179546219010884a8946))
* ✨ SOP-9976 调拨模块显示商品单价和金额 ([11766f5](https://gitlab.hd123.com:20022/vue/soa/commit/11766f5fa72dc5ed8d53a8eb2057e2c2eebc309c))
* ✨ SOP-9976 调拨模块显示商品单价和金额 ([2ec1c60](https://gitlab.hd123.com:20022/vue/soa/commit/2ec1c6095a71e9f81d2cc205f62f53d7884d2d4b))
* ✨ SOP-9976 调拨模块主分支代码同步 ([abddcde](https://gitlab.hd123.com:20022/vue/soa/commit/abddcde57c25be848e45210afca7dcd7f27ea4d9))

## [2.39.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.38.2...v2.39.0) (2023-12-05)


### Features

* ✨ SOP-10007 初盘界面展示待盘商品需求兼容实现 ([16c2324](https://gitlab.hd123.com:20022/vue/soa/commit/16c2324bac4c2fb71b20a5aa1455de461e8f7a1f))
* ✨ SOP-9976 调拨模块的商品总金额字段取值主分支逻辑同步 ([a3f9853](https://gitlab.hd123.com:20022/vue/soa/commit/a3f985356e7586f9b730f0d1d90aa5ab247f6485))
* ✨ SOP-9976 调拨模块计算金额字段逻辑从主分支同步 ([494e51f](https://gitlab.hd123.com:20022/vue/soa/commit/494e51f373593f7505d2cc6c274743c53fce4108))
* ✨ SOP-9976 调拨模块商品总额显示字段调整 ([bfd9114](https://gitlab.hd123.com:20022/vue/soa/commit/bfd9114a0fb7014810fe2dfdcb5676f2f54c790c))
* ✨ SOP-9976 调拨模块显示商品单价和金额 ([11766f5](https://gitlab.hd123.com:20022/vue/soa/commit/11766f5fa72dc5ed8d53a8eb2057e2c2eebc309c))
* ✨ SOP-9976 调拨模块主分支代码同步 ([abddcde](https://gitlab.hd123.com:20022/vue/soa/commit/abddcde57c25be848e45210afca7dcd7f27ea4d9))

### [2.38.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.38.1...v2.38.2) (2023-11-22)


### Bug Fixes

* 🐛 标准订货商品列表图片压缩 ([1e19b09](https://gitlab.hd123.com:20022/vue/soa/commit/1e19b094be72fd148596587118c3f7c241d6aad9))

### [2.38.1](https://gitlab.hd123.com/vue/soa/compare/v2.38.0...v2.38.1) (2023-10-30)


### Features

* ✨ SOP-9855 盘点录入商品倒序排序 ([f4601d7](https://gitlab.hd123.com/vue/soa/commit/f4601d73fe9e7880b51378cdfb8c9ff906f4ec57))
* ✨ SOP-9856 直送收货规格规格只能为贸易协议规格，赠品只能录入1*1 ([ce593b9](https://gitlab.hd123.com/vue/soa/commit/ce593b971a4b6f66079e6ff54b728a478a7efdf7))

## [2.38.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.37.1...v2.38.0) (2023-10-23)


### Features

* ✨ SOP-9808 独立订货支持总部库存，销量等按配置显示 ([452113c](https://gitlab.hd123.com:20022/vue/soa/commit/452113c0b121f6dd91b68ee88377ffca0006a413))


### Bug Fixes

* 🐛 SOP-9799 知识库传参优化&消息中心显示优化 ([4a23180](https://gitlab.hd123.com:20022/vue/soa/commit/4a23180e2e147364e1d7c1743f3f2b4847071b8d))

### [2.37.1](https://gitlab.hd123.com/vue/soa/compare/v2.37.0...v2.37.1) (2023-10-17)


### Bug Fixes

* 🐛 SOP-9789 盘点支持按配置存储盘点规格 ([58635e2](https://gitlab.hd123.com/vue/soa/commit/58635e22b80a3f317ef42133af0c7644e6412e91))
* 🐛 SOP-9789 盘点支持按配置存储盘点规格 ([594df65](https://gitlab.hd123.com/vue/soa/commit/594df653ed17bbd083d719bf2425d3d8d2e3cde0))
* 🐛 SOP-9789 盘点支持按配置存储盘点规格 ([5566faa](https://gitlab.hd123.com/vue/soa/commit/5566faa01a435f238009e46a1e3767939f16669f))

## [2.37.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.36.5...v2.37.0) (2023-10-12)

### [2.36.5](https://gitlab.hd123.com:20022/vue/soa/compare/v2.36.4...v2.36.5) (2023-09-26)


### Bug Fixes

* 🐛 标准订货列表页修复列表页删除无效 ([40d063b](https://gitlab.hd123.com:20022/vue/soa/commit/40d063bff65fc3890ae838f88706f0fd46189f6c))

### [2.36.4](https://gitlab.hd123.com:20022/vue/soa/compare/v2.36.3...v2.36.4) (2023-09-12)


### Features

* ✨ SOP-9647 标准订货拆单交互逻辑调整 ([d3c3e0f](https://gitlab.hd123.com:20022/vue/soa/commit/d3c3e0f6af93d200aab623db404d953563b7b77d))

### [2.36.3](https://gitlab.hd123.com/vue/soa/compare/v2.36.1...v2.36.3) (2023-09-05)


### Features

* ✨ 主分支增加隐私保护指引的逻辑 ([4bc2108](https://gitlab.hd123.com/vue/soa/commit/4bc2108e3b63c4d66948aa53ef96601deeb433e6))


### Bug Fixes

* 🐛 SOP-9619 直送收货直送退货进货价和退货价计算精度问题处理 ([89c86c4](https://gitlab.hd123.com/vue/soa/commit/89c86c47e5e9e33781787ffec7aa941dc04a9208))

### [2.36.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.36.1...v2.36.2) (2023-09-02)


### Bug Fixes

* 🐛 SOP-9602 开始盘点传参调整 ([9ebb877](https://gitlab.hd123.com:20022/vue/soa/commit/9ebb877b8188135fe76858d1c504768a2df21d04))

### [2.36.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.36.0...v2.36.1) (2023-08-25)


### Bug Fixes

* 🐛 合单收货合单页只支持合单提交 ([7559821](https://gitlab.hd123.com:20022/vue/soa/commit/75598210005734a5b0ae900ed327676205ac06e7))
* 普通收获页开启合单配置时隐藏保存提交按钮 ([abb5c7d](https://gitlab.hd123.com:20022/vue/soa/commit/abb5c7d35d1f2ee5da5fbc0a060f860a8a6f555f))

## [2.36.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.35.3...v2.36.0) (2023-08-25)


### Features

* ✨ SOP-9403 增加自主盘点 ([938c205](https://gitlab.hd123.com:20022/vue/soa/commit/938c205f7615692bec6fb6e79372520d74501c16))


### Bug Fixes

* 🐛 自主盘点界面优化。新增权限 ([1b86d89](https://gitlab.hd123.com:20022/vue/soa/commit/1b86d89386bebb5215b9c2f0f174ff90a8285aa2))

### [2.35.3](https://gitlab.hd123.com/vue/soa/compare/v2.35.1...v2.35.3) (2023-08-14)


### Bug Fixes

* 🐛 盘点已失效判断调整为判断天数 ([0454e27](https://gitlab.hd123.com/vue/soa/commit/0454e27ec6029f1eed4db56f026b4e3eec60e572))
* 🐛 SOP-9463 盘点录入批次时0和null默认回显为空 ([918237f](https://gitlab.hd123.com/vue/soa/commit/918237f2f9f86a2c2bbb41bcd8d50b675a90954c))
* 🐛 SOP-9463 修复九多肉多盘点默认批次号为null的问题 ([0b5f648](https://gitlab.hd123.com/vue/soa/commit/0b5f6481fc72619d2663da918b9d09cea6380224))
* 🐛 SOP-9463 修复九多肉多盘点默认批次号为null的问题 ([1363bd1](https://gitlab.hd123.com/vue/soa/commit/1363bd1d401de1530ea7b8d3c414a65f0b9ab6a5))

### [2.35.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.35.1...v2.35.2) (2023-08-09)


### Bug Fixes

* 🐛 盘点已失效判断调整为判断天数 ([0454e27](https://gitlab.hd123.com:20022/vue/soa/commit/0454e27ec6029f1eed4db56f026b4e3eec60e572))
* 🐛 SOP-9463 修复九多肉多盘点默认批次号为null的问题 ([1363bd1](https://gitlab.hd123.com:20022/vue/soa/commit/1363bd1d401de1530ea7b8d3c414a65f0b9ab6a5))

### [2.35.1](https://gitlab.hd123.com/vue/soa/compare/v2.35.0...v2.35.1) (2023-08-02)

## [2.35.0](https://gitlab.hd123.com/vue/soa/compare/v2.34.1...v2.35.0) (2023-07-27)


### Features

* ✨ SOP-9055 门店自采单据流程状态调整 ([ea0f6dc](https://gitlab.hd123.com/vue/soa/commit/ea0f6dc9dae747dc3d4b0886521b77045fe523bf))
* ✨ SOP-9057 新增周转筐管理【回收模块】 ([937ffb4](https://gitlab.hd123.com/vue/soa/commit/937ffb440934a1012b00de59a191958a239de081))
* ✨ SOP-9057 周转筐管理【回收模块联调】 ([c5e54a5](https://gitlab.hd123.com/vue/soa/commit/c5e54a51072441850e9645e3b21903c00977d7f0))
* ✨ SOP-9086、SOP-9068 新增文件返回登记模块和叫货目录页面 ([4b8fd95](https://gitlab.hd123.com/vue/soa/commit/4b8fd9530791b8fd700243341a89eb6b690f7605))
* ✨ SOP-9089 新增质量反馈模块 ([f85e184](https://gitlab.hd123.com/vue/soa/commit/f85e1847a5df267da9a07bcb02dc378f691048aa))
* ✨ SOP-9089 新增质量反馈模块 ([3319a7f](https://gitlab.hd123.com/vue/soa/commit/3319a7fa4c07f5f6c0575a143d592067e4480a4b))
* ✨ SOP-9089 新增质量反馈模块 ([2d98502](https://gitlab.hd123.com/vue/soa/commit/2d985024f499184d516356c0a47ad0d140355aee))
* ✨ SOP-9112 订货模块新增预报货功能 ([d562182](https://gitlab.hd123.com/vue/soa/commit/d56218233416d01793d55849fc0c9d2b2b81b51c))
* ✨ SOP-9126 小程序.门店助手.新增周转筐管理【盘点模块】 ([674d4e9](https://gitlab.hd123.com/vue/soa/commit/674d4e94c3d1602b29e6cd6aed3d3a7bf750905b))
* ✨ SOP-9127 新增周转筐管理【收货模块】 ([c695071](https://gitlab.hd123.com/vue/soa/commit/c6950719628d1c8a2f6f4b794df3bf86117eb085))
* ✨ SOP-9127 新增周转筐管理【收货模块】 ([8351e86](https://gitlab.hd123.com/vue/soa/commit/8351e862d8c6856e4feb00e345db669895ab9e33))
* ✨ SOP-9127 周转筐管理【收货模块】联调 ([9b58baa](https://gitlab.hd123.com/vue/soa/commit/9b58baa8b15587b7cea2ab3a09c0d248126c6cca))
* ✨ SOP-9152 小程序.门店助手.新增商品效期管理模块 ([c35f798](https://gitlab.hd123.com/vue/soa/commit/c35f7980f74a35c50fca58b55386a2d55ac37074))
* ✨ SOP-9205 门店收货支持多单合并收货及收货差异处理 ([aeffe93](https://gitlab.hd123.com/vue/soa/commit/aeffe938e0741c7bb30077bd81a56cd23633529e))
* ✨ SOP-9233 订货支持多场景 ([14e035f](https://gitlab.hd123.com/vue/soa/commit/14e035f714a32689eba6a1bd19750eb2a07db8fe))
* ✨ SOP-9236 门店助手公文对接 ([d85da95](https://gitlab.hd123.com/vue/soa/commit/d85da95b0ccafceb10d7915e4ccc6912e98e9b01))
* ✨ SOP-9243 门店助手&督导助手知识库优化 ([ca063f5](https://gitlab.hd123.com/vue/soa/commit/ca063f557983f131a179c0664841fa55eb639570))
* ✨ SOP-9274 订货界面金额要加上辅料的金额 ([75c8082](https://gitlab.hd123.com/vue/soa/commit/75c8082bf7ae117aeefd8ace095c57f02d8b6262))
* ✨ SOP-9281 购物车和订单详情页统一由前端计算预计到货时间 ([e683a22](https://gitlab.hd123.com/vue/soa/commit/e683a222c2c8847f7550fbfbf11f1285bb02334b))
* ✨ SOP-9295 加单订货根据配置区分打开要货面板或默认样式 ([94456b0](https://gitlab.hd123.com/vue/soa/commit/94456b0435546ab5dc5763b789d0c26f332f9404))
* ✨ SOP-9303 订货规则如果没有对应订货模式的规则，则取-1模式的规则 ([4a55cda](https://gitlab.hd123.com/vue/soa/commit/4a55cdab9de019fe23a5bf827ee364f8f2df795d))
* ✨ SOP-9317 初盘增加手动录入批次号的逻辑 ([26a29f6](https://gitlab.hd123.com/vue/soa/commit/26a29f6e5ebfe823b46ea2283ef57ac1393afbe4))
* ✨ SOP-9321 复盘页面支持扫码录入批次更换查询商品列表的接口 ([3c9539a](https://gitlab.hd123.com/vue/soa/commit/3c9539afa27e1b4859b07aa6500b41948e3eb7f5))
* ✨ SOP-9355 门店领用单申请数量受审核数量限制 ([507a042](https://gitlab.hd123.com/vue/soa/commit/507a042b6e5fdd8c81eddc992fb23d1b08478052))
* ✨ SOP-9358 订货增加上下限和强控提醒配置 ([01734f4](https://gitlab.hd123.com/vue/soa/commit/01734f417b16122b4bee10d3460a647aa59037db))
* ✨ SOP-9360 订货单价计算辅料金额 ([15147fc](https://gitlab.hd123.com/vue/soa/commit/15147fcbb25971e05d8277e410090aa6df8271af))
* ✨ SOP-9368 新建文件返回登记.文件类型显示为弹窗样式优化 ([76dd6fc](https://gitlab.hd123.com/vue/soa/commit/76dd6fca351bcfb9a7d9aaf78135dd50bb12d022))
* ✨ SOP-9376 新增质量反馈.问题汇总 ([75bf537](https://gitlab.hd123.com/vue/soa/commit/75bf5370264b637e08df79662e4e2c42f698ec3e))
* ✨ SOP-9390 盘点扫批次码支持跨月识别月份日期 ([43b03e0](https://gitlab.hd123.com/vue/soa/commit/43b03e083db8333fd8bddf5f336c577527ba162a))
* ✨ SOP-9391 盘点商品到效期改为批次加上保质期计算 ([423ce10](https://gitlab.hd123.com/vue/soa/commit/423ce10861dd2927853789ffc1a8a02a27dc148b))
* ✨ SOP-9392 质量反馈商品模式、门店领用增加审批流程 ([7c19deb](https://gitlab.hd123.com/vue/soa/commit/7c19debef9ac7529194a0a2c2d0b30592727c645))
* ✨ SOP-9392 质量反馈商品模式增加审批流程 ([57f5914](https://gitlab.hd123.com/vue/soa/commit/57f5914dac650e40cd13f2728d453e98c8bbb4e3))
* ✨ SOP-9397 合单收货录入数量优化 ([ae8ec50](https://gitlab.hd123.com/vue/soa/commit/ae8ec509868cae34bb7d0d127fc0de2d3a84b3a7))
* ✨ SOP-9410 标准叫货界面叫货价取值传参调整 ([b1fbe03](https://gitlab.hd123.com/vue/soa/commit/b1fbe032584587a13a5e5721b552aeeeebf9db84))
* ✨ SOP-9434 合单收货容差判断逻辑调整 ([e1b6801](https://gitlab.hd123.com/vue/soa/commit/e1b6801c76d33a272da5e4961c4761231ee4a769))
* ✨ SOP-9453 盘点增加展示实盘包数的权限 ([e677fd3](https://gitlab.hd123.com/vue/soa/commit/e677fd38ebd55a249ad3b1368dbd69a2dca00c3e))


### Bug Fixes

* 🐛 0620收货bug修复 ([a71a430](https://gitlab.hd123.com/vue/soa/commit/a71a4300ae77e482a10b1f42d2a6aee3a45a0d95))
* 🐛 差异数显示条件修改 ([0efab2a](https://gitlab.hd123.com/vue/soa/commit/0efab2ab5e91a78e00d7c6a1e51ebb3163b23d73))
* 🐛 合单收货扫批次码支持跨月识别月份日期 ([0542cb2](https://gitlab.hd123.com/vue/soa/commit/0542cb2c4a0a63c85e35c806b237c00cc0873f1e))
* 🐛 盘点差异数精度修改 ([57c7b6f](https://gitlab.hd123.com/vue/soa/commit/57c7b6f66dc8535db099c969eac10daace58f507))
* 🐛 商品效期差异数修改 ([a27683d](https://gitlab.hd123.com/vue/soa/commit/a27683dc1e01825398b66741bf94c6af4062af6b))
* 🐛 通过模块进入商品质量反馈策略优化 ([f043c3a](https://gitlab.hd123.com/vue/soa/commit/f043c3a7d123164e41bcc6dec86d7959eba8c833))
* 🐛 效期商品参数修改 ([a9e3137](https://gitlab.hd123.com/vue/soa/commit/a9e313733c4e8066e8d2adcff75f33d83b716a49))
* 🐛 新增门店领用功能 ([6c0a018](https://gitlab.hd123.com/vue/soa/commit/6c0a018f70198bef2d51446974416876aae8462f))
* 🐛 修复门店领用详情页面商品明细行没有图片的问题 ([d75f202](https://gitlab.hd123.com/vue/soa/commit/d75f20201e4b4df213681fb64b13dd44c9615261))
* 🐛 SOP-9155 修复文件登记模块文件类型为其他时无法输入自定义类型的问题 ([0439fee](https://gitlab.hd123.com/vue/soa/commit/0439fee5fe8cd50c6f61010ba34f15622393db97))
* 🐛 SOP-9155 修复文件返回登记开发中产生的bug ([4e37c11](https://gitlab.hd123.com/vue/soa/commit/4e37c1122d59313a19b15132330a425e1658c304))
* 🐛 SOP-9156 修复文件返回登记模块编辑页面删除按钮过大的问题 ([aa6a86d](https://gitlab.hd123.com/vue/soa/commit/aa6a86d7ad045da9c75f6a59f74cc16f15add767))
* 🐛 SOP-9158 周转筐管理增加权限 ([a5b9b17](https://gitlab.hd123.com/vue/soa/commit/a5b9b17dc5bd6af07309ef62729888fb4a83ed3b))
* 🐛 SOP-9238 门店助手.周转筐盘点操作时间、详情页实拣数量展示问题 ([9330587](https://gitlab.hd123.com/vue/soa/commit/933058784bde208c46008699424e52942cf48a97))
* 🐛 SOP-9244 门店助手.周转筐盘点模块差异数变化 ([d5e3f0b](https://gitlab.hd123.com/vue/soa/commit/d5e3f0b32448c79c533e923c90132dc8ee1652a1))
* 🐛 SOP-9250 小程序.门店助手.商品效期管理模块.问题汇总 ([0f75fc5](https://gitlab.hd123.com/vue/soa/commit/0f75fc5a8551f09a20bd8496706f208725e45baa))
* 🐛 SOP-9269 修复订货支持多场景bug ([72fabdc](https://gitlab.hd123.com/vue/soa/commit/72fabdcbaddbd0387bef8ffda9c4aebfc513984e))
* 🐛 SOP-9273 修复提交时loading不隐藏的问题 ([39bfbc5](https://gitlab.hd123.com/vue/soa/commit/39bfbc59652a9d002da387d6adb7c04404fa883e))
* 🐛 SOP-9278 订货详情页面增加显示含料包标签 ([b8c0a0b](https://gitlab.hd123.com/vue/soa/commit/b8c0a0b46c634ea5acc46abedad88679146113fe))
* 🐛 SOP-9352 收货合并收货散货扫码录入批次优化 ([9aa84fd](https://gitlab.hd123.com/vue/soa/commit/9aa84fdb52a178c573d602dd94257307b83cbf84))
* 🐛 SOP-9360 订货单价保留四位小数 ([77f340e](https://gitlab.hd123.com/vue/soa/commit/77f340ecee62261783c8b58ad27e0942fcbbc595))
* 🐛 SOP-9362 修复扫码录入批次时 编辑批次行后变成始终编辑某一行批次的问题 ([df9d19b](https://gitlab.hd123.com/vue/soa/commit/df9d19bec1cd783756e4ab1edb7c5d6e4b15ab72))
* 🐛 SOP-9371 商品质量反馈模块跨月工时记录 ([6144205](https://gitlab.hd123.com/vue/soa/commit/6144205e9a06aa159ea1930865399c612d15e4fa))
* 🐛 SOP-9376 新增质量反馈.问题汇总 ([c0d914b](https://gitlab.hd123.com/vue/soa/commit/c0d914b99c0f578a051bb214b45f45d8ddd0d3b2))
* 🐛 SOP-9381、SOP-9382 质量反馈模块.页面显示优化 ([8761292](https://gitlab.hd123.com/vue/soa/commit/8761292cc0d872412b2f0f19357bb59a96b4f730))
* 🐛 SOP-9393 门店叫货根据本次叫货金额计算展示可用余额 ([ab35dd3](https://gitlab.hd123.com/vue/soa/commit/ab35dd32e0bc3298625108375e38dcc425f40798))
* 🐛 SOP-9417 新增预报货页面和列表数量展示问题 ([1749f4a](https://gitlab.hd123.com/vue/soa/commit/1749f4af9fb5f5c6f6f7f9dea95a31d1db5ad950))
* 🐛 SOP-9428 合并收货详情页显示参考重量单品数优化 ([18d70ab](https://gitlab.hd123.com/vue/soa/commit/18d70ab28064f9a25f0b6098df367ac2f3428914))
* 🐛 SOP-9433 合单收货扫码录入问题修复 ([d4e2b60](https://gitlab.hd123.com/vue/soa/commit/d4e2b60b1ad14376c3fb9d5a607b1d6807dd3013))

### [2.34.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.34.0...v2.34.1) (2023-06-29)

## [2.34.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.33.1...v2.34.0) (2023-06-16)


### Features

* ✨ SOP-9237 盘点优化 ([4e18846](https://gitlab.hd123.com:20022/vue/soa/commit/4e18846b56c2b680ea9fe19367424a9d0b8fd577))
* ✨ SOP-9243 门店助手&督导助手知识库优化 ([497bee5](https://gitlab.hd123.com:20022/vue/soa/commit/497bee566b4dfa1391537536a3cb483f1f3f3df4))


### Bug Fixes

* 🐛 知识库视频播放进度条拖动调整 ([8abe796](https://gitlab.hd123.com:20022/vue/soa/commit/8abe79659e0bfd2f250322a0f57c58ba479abc03))
* 🐛 SOP-9187 直送收货连续调了两次modifyAndSubmit接口 ([c2c0983](https://gitlab.hd123.com:20022/vue/soa/commit/c2c09832f3c536765464d1cbdb6e8a91b9b7d69f))

### [2.33.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.33.0...v2.33.1) (2023-05-24)

## [2.33.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.32.1...v2.33.0) (2023-05-19)


### Features

* ✨ SOP-8975 陈列任务支持自动生成货架位调整单 ([d326445](https://gitlab.hd123.com:20022/vue/soa/commit/d326445e806d7a5dabd733031a9273edf3858b64))
* ✨ SOP-8994 停业盘点支持多端操作 ([1869c80](https://gitlab.hd123.com:20022/vue/soa/commit/1869c804c0c83a3a5c29a3cb860f3ae25ade5cab))

### [2.32.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.32.0...v2.32.1) (2023-05-09)

## [2.32.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.31.2...v2.32.0) (2023-04-28)


### Bug Fixes

* 🐛 SOP-9009 增加文件存储权限未开提示和视频禁止拖拽适配 ([e980562](https://gitlab.hd123.com:20022/vue/soa/commit/e980562f214ca3d05e1016602666b88c09f1f04f))
* 🐛 SOP-9011 台账任务陈列项需要传分值给服务端\ ([f06b703](https://gitlab.hd123.com:20022/vue/soa/commit/f06b70391005db1911e3d320da816d4f46c730d0))

### [2.31.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.31.1...v2.31.2) (2023-04-23)


### Bug Fixes

* 🐛 SOP-9018 盘点盈亏排序修改 ([abac021](https://gitlab.hd123.com:20022/vue/soa/commit/abac02172022230e41ef67e542efd989f69e68d1))

### [2.31.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.31.0...v2.31.1) (2023-04-23)

## [2.31.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.30.3...v2.31.0) (2023-04-14)


### Features

* ✨ SOP-8918 盘点增加复盘预览 ([9ae7c61](https://gitlab.hd123.com:20022/vue/soa/commit/9ae7c617c29ce6333dab9656d74d84bc33950b6a))
* ✨ SOP-8920 任务巡检支持保存到草稿 ([f9d3b81](https://gitlab.hd123.com:20022/vue/soa/commit/f9d3b816f512d3403606b082e9d544e887de2137))

### [2.30.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.30.2...v2.30.3) (2023-04-10)

### [2.30.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.30.1...v2.30.2) (2023-04-10)


### Bug Fixes

* 🐛 SOP-8922 任务巡检优化 ([7c704c2](https://gitlab.hd123.com:20022/vue/soa/commit/7c704c2c1a319b8e87b8136d355cf09603b813d8))

### [2.30.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.6...v2.30.1) (2023-04-07)


### Features

* ✨ SOP-8827 任务巡检优化 ([db75bca](https://gitlab.hd123.com:20022/vue/soa/commit/db75bca9a1ff45d646cd2c0a0e80043a309c138c))


### Bug Fixes

* 🐛 任务详情优化 ([b4dee44](https://gitlab.hd123.com:20022/vue/soa/commit/b4dee447418b3d14fd44c436d8161f1f19c161ff))
* 🐛 SOP-8841 SOP-8906 修复历史样式问题 ([021d9e3](https://gitlab.hd123.com:20022/vue/soa/commit/021d9e350335eca879c97dd562dfe10af90b6a37))
* 🐛 SOP-8841 SOP-8906 修复历史样式问题 ([960a70e](https://gitlab.hd123.com:20022/vue/soa/commit/960a70e18cba99c6f03fe5bf1179bee66e469630))
* 🐛 SOP-8872 暂时移出对于upload组件上传图片是否真实存在的校验 ([d96ba87](https://gitlab.hd123.com:20022/vue/soa/commit/d96ba87772ab37701894f4cbc54ad9b42604cf14))

### [2.29.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.29.1...v2.29.2) (2023-03-25)


### Features

* ✨ SOP-8836 订单查询默认筛选当前门店的定单 ([0e532fe](https://gitlab.hd123.com:20022/vue/soa/commit/0e532fe6fd85ba01d627248b4bb4b66a0421417c))

### [2.29.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.29.0...v2.29.1) (2023-03-22)


### Features

* ✨ SOP-8818 上传图片到oos后不显示图片问题解决 ([5896b9f](https://gitlab.hd123.com:20022/vue/soa/commit/5896b9fa547a3543fd147acfc8662d9b4125e05c))


### Bug Fixes

* 🐛 SOP-8813 商品查询- 近28日时段分析和店均对比为空 ([a6aa978](https://gitlab.hd123.com:20022/vue/soa/commit/a6aa9782b4580af4367fc6fd3725562ce75f6ad6))

## [2.29.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.28.2...v2.29.0) (2023-03-21)


### Features

* ✨ SOP-8806 订单查询功能界面全选优化 ([7a75698](https://gitlab.hd123.com:20022/vue/soa/commit/7a756984123f4e14ef84978e2f9a8e1498e58e52))
* ✨ SOP-8808 线上商品模块新增按平台授予改价格权限的逻辑 ([49b8eb2](https://gitlab.hd123.com:20022/vue/soa/commit/49b8eb2f29ecc19eaa9f8acb01886d9ddf71e056))


### Bug Fixes

* 🐛 订单查询限制在当前门店，订单选择热区优化 ([c722d01](https://gitlab.hd123.com:20022/vue/soa/commit/c722d01ab7d83a9b5b8af6b192ca3fb5baa12d31))

### [2.28.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.5...v2.28.2) (2023-03-19)


### Features

* ✨ SOP-8771 新增上传图片前获取oss签名的逻辑 ([06a37ee](https://gitlab.hd123.com:20022/vue/soa/commit/06a37ee4986690259e8bec44f54102bb88e1e904))

### [2.28.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.4...v2.28.1) (2023-03-18)


### Features

* ✨ SOP-8764 线上商品模块调整部分校验及展示规则 ([1f9f748](https://gitlab.hd123.com:20022/vue/soa/commit/1f9f748c347f7e7787b0842306a052fd7c19f5d4))
* ✨ SOP-8771 上传图片或视频新增校验图片或视频是否存在的逻辑 ([ce28747](https://gitlab.hd123.com:20022/vue/soa/commit/ce2874714c0d52e817daa0378815297bd307c4d7))

## [2.28.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.27.3...v2.28.0) (2023-03-03)


### Bug Fixes

* 🐛 SOP-8728 标准订货获取单据详情接口调整 ([392a27c](https://gitlab.hd123.com:20022/vue/soa/commit/392a27ceaba4ecf977d2be7d11a80da6a7b113b2))

### [2.27.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.3...v2.27.3) (2023-03-02)


### Features

* ✨ SOP-8558 复盘需要显示初盘差异数 ([3119f38](https://gitlab.hd123.com:20022/vue/soa/commit/3119f388667166f02c7515709b5ba4771139ab62))


### Bug Fixes

* 🐛 加盟费用状态增加已完成对应图片 ([33e287b](https://gitlab.hd123.com:20022/vue/soa/commit/33e287b6b2b92c2616bb894c17ff3013b36cd7b6))
* 🐛 SOP-8657 门店费用上报模块兼容app ([a82bde6](https://gitlab.hd123.com:20022/vue/soa/commit/a82bde690083a7f51c83a1540fe88e12bf8d0748))
* 🐛 SOP-8693 标准订货单据重复问题优化 ([b03b4ea](https://gitlab.hd123.com:20022/vue/soa/commit/b03b4eaa039d7d8bb92837b7064eccd9ef616360))
* 🐛 SOP-8717 修复调查类任务详情编辑无效的问题 ([93e603f](https://gitlab.hd123.com:20022/vue/soa/commit/93e603f54c8e20dee2ceb357a6a8a9691042c705))

### [2.27.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.27.0...v2.27.1) (2023-02-20)


### Bug Fixes

* 🐛 SOP-8562 加工模块提交和保存前将qpcQty按qpc计算qty ([6e2d3ef](https://gitlab.hd123.com:20022/vue/soa/commit/6e2d3ef3cf0aaa5b6cc802f90cf8cafcef9c36e3))

## [2.27.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.26.0...v2.27.0) (2023-02-20)


### Features

* ✨ SOP-8498 租户许可证二维码增加base64加密解密的逻辑 ([09c0a53](https://gitlab.hd123.com:20022/vue/soa/commit/09c0a53ae5262c5f61978a181d4c65ea40eff969))
* ✨ SOP-8559 支付取消禁用需求 ([c60c86f](https://gitlab.hd123.com:20022/vue/soa/commit/c60c86f9f7676b809961c6206c0e4b2cc00c1886))
* ✨ SOP-8572 新增线上商品模块 ([e26aa8b](https://gitlab.hd123.com:20022/vue/soa/commit/e26aa8b735f79d29643afd7c38102d506c6380e8))
* ✨ SOP-8575 新增门店费用上报模块 ([72fefb9](https://gitlab.hd123.com:20022/vue/soa/commit/72fefb906df50e4429dabf08ba3305a84e951835))


### Bug Fixes

* 🐛 SOP-8619 门店费用上报：页面问题 ([1053ff6](https://gitlab.hd123.com:20022/vue/soa/commit/1053ff6125d00c7af0b1972c8bf1dd65867ced30))

## [2.26.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.2...v2.26.0) (2023-02-03)


### Features

* ✨ SOP-8438 价签申请模块增加标价签维度创建申请的模式 ([4e14826](https://gitlab.hd123.com:20022/vue/soa/commit/4e14826a687b57a43bfe058db757c200ca4c8377))
* ✨ SOP-8523 价格带模块查询商品详情接口增加sku_id参数 ([1b3e84f](https://gitlab.hd123.com:20022/vue/soa/commit/1b3e84f8ea015714f24235c64d9844b51cd798d3))
* ✨ SOP-8560 价签申请增加价签类型功能优化 ([57cc2f4](https://gitlab.hd123.com:20022/vue/soa/commit/57cc2f4946edf68363d4b5dbaa9f222735a8ab93))
* ✨ SOP-8561 APP欢迎页面增加人性化提示 ([acee64d](https://gitlab.hd123.com:20022/vue/soa/commit/acee64da1eb2f666c107bb5ce75549e256bcf4fb))


### Bug Fixes

* 🐛 SOP-8522 收货搜索页逻辑调整 ([d15d49f](https://gitlab.hd123.com:20022/vue/soa/commit/d15d49ff45aedf585abe711d9c6098d74b6d8db7))

## [2.25.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.3.5...v2.25.0) (2023-01-10)


### Bug Fixes

* 🐛 SOP-8471、SOP-8474、SOP-8476 ([460c239](https://gitlab.hd123.com:20022/vue/soa/commit/460c239681b0611f73f96c75511b7e8257d4bafe))
* 🐛 SOP-8472 库存上下限调整：新增页面界面问题 ([c371afd](https://gitlab.hd123.com:20022/vue/soa/commit/c371afd6dc9f7c15acb9a04ec0ffdf1c7ca4e9b1))
* 🐛 SOP-8475 输入的商品上下限不符合限制时，界面没有提示 ([c474dd8](https://gitlab.hd123.com:20022/vue/soa/commit/c474dd8fe2ece0d0364b9478698cb78ec4fb2a0d))

### [2.3.5](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.1...v2.3.5) (2023-01-05)


### Features

* ✨ SOP-8439 新增库存上下限调整功能 ([99aa500](https://gitlab.hd123.com:20022/vue/soa/commit/99aa5004924132e5c532cbd1102a024c56ea6f69))
* ✨ SOP-8440 直送收货、退货模块待处理界面-增加删除功能 ([5fd4825](https://gitlab.hd123.com:20022/vue/soa/commit/5fd4825278290e55e82201a8533f6aa2d4947d8f))
* ✨ SOP-8448 增加埋点信息 ([e15eff3](https://gitlab.hd123.com:20022/vue/soa/commit/e15eff3ce3bcc18924ef5e70a31def86253a25db))
* ✨ SOP-8461 收货清点扫码问题修复 ([33f473e](https://gitlab.hd123.com:20022/vue/soa/commit/33f473e27f895fe2f7b145dc25f8ee7714150889))


### Bug Fixes

* 🐛 SOP-8461 收货清点扫码问题修复 ([eddfffb](https://gitlab.hd123.com:20022/vue/soa/commit/eddfffbee73bb402b622fa5152096fbcdf17f5a1))

## [2.30.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.6...v2.30.0) (2023-04-06)


### Features

* ✨ SOP-8827 任务巡检优化 ([db75bca](https://gitlab.hd123.com:20022/vue/soa/commit/db75bca9a1ff45d646cd2c0a0e80043a309c138c))


### Bug Fixes

* 🐛 任务详情优化 ([b4dee44](https://gitlab.hd123.com:20022/vue/soa/commit/b4dee447418b3d14fd44c436d8161f1f19c161ff))
* 🐛 SOP-8841 SOP-8906 修复历史样式问题 ([021d9e3](https://gitlab.hd123.com:20022/vue/soa/commit/021d9e350335eca879c97dd562dfe10af90b6a37))
* 🐛 SOP-8841 SOP-8906 修复历史样式问题 ([960a70e](https://gitlab.hd123.com:20022/vue/soa/commit/960a70e18cba99c6f03fe5bf1179bee66e469630))
* 🐛 SOP-8872 暂时移出对于upload组件上传图片是否真实存在的校验 ([d96ba87](https://gitlab.hd123.com:20022/vue/soa/commit/d96ba87772ab37701894f4cbc54ad9b42604cf14))

### [2.29.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.29.1...v2.29.2) (2023-03-25)


### Features

* ✨ SOP-8836 订单查询默认筛选当前门店的定单 ([0e532fe](https://gitlab.hd123.com:20022/vue/soa/commit/0e532fe6fd85ba01d627248b4bb4b66a0421417c))

### [2.29.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.29.0...v2.29.1) (2023-03-22)


### Features

* ✨ SOP-8818 上传图片到oos后不显示图片问题解决 ([5896b9f](https://gitlab.hd123.com:20022/vue/soa/commit/5896b9fa547a3543fd147acfc8662d9b4125e05c))


### Bug Fixes

* 🐛 SOP-8813 商品查询- 近28日时段分析和店均对比为空 ([a6aa978](https://gitlab.hd123.com:20022/vue/soa/commit/a6aa9782b4580af4367fc6fd3725562ce75f6ad6))

## [2.29.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.28.2...v2.29.0) (2023-03-21)


### Features

* ✨ SOP-8806 订单查询功能界面全选优化 ([7a75698](https://gitlab.hd123.com:20022/vue/soa/commit/7a756984123f4e14ef84978e2f9a8e1498e58e52))
* ✨ SOP-8808 线上商品模块新增按平台授予改价格权限的逻辑 ([49b8eb2](https://gitlab.hd123.com:20022/vue/soa/commit/49b8eb2f29ecc19eaa9f8acb01886d9ddf71e056))


### Bug Fixes

* 🐛 订单查询限制在当前门店，订单选择热区优化 ([c722d01](https://gitlab.hd123.com:20022/vue/soa/commit/c722d01ab7d83a9b5b8af6b192ca3fb5baa12d31))

### [2.28.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.5...v2.28.2) (2023-03-19)


### Features

* ✨ SOP-8771 新增上传图片前获取oss签名的逻辑 ([06a37ee](https://gitlab.hd123.com:20022/vue/soa/commit/06a37ee4986690259e8bec44f54102bb88e1e904))

### [2.28.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.4...v2.28.1) (2023-03-18)


### Features

* ✨ SOP-8764 线上商品模块调整部分校验及展示规则 ([1f9f748](https://gitlab.hd123.com:20022/vue/soa/commit/1f9f748c347f7e7787b0842306a052fd7c19f5d4))
* ✨ SOP-8771 上传图片或视频新增校验图片或视频是否存在的逻辑 ([ce28747](https://gitlab.hd123.com:20022/vue/soa/commit/ce2874714c0d52e817daa0378815297bd307c4d7))

## [2.28.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.27.3...v2.28.0) (2023-03-03)


### Bug Fixes

* 🐛 SOP-8728 标准订货获取单据详情接口调整 ([392a27c](https://gitlab.hd123.com:20022/vue/soa/commit/392a27ceaba4ecf977d2be7d11a80da6a7b113b2))

### [2.27.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.3...v2.27.3) (2023-03-02)


### Features

* ✨ SOP-8558 复盘需要显示初盘差异数 ([3119f38](https://gitlab.hd123.com:20022/vue/soa/commit/3119f388667166f02c7515709b5ba4771139ab62))


### Bug Fixes

* 🐛 加盟费用状态增加已完成对应图片 ([33e287b](https://gitlab.hd123.com:20022/vue/soa/commit/33e287b6b2b92c2616bb894c17ff3013b36cd7b6))
* 🐛 SOP-8657 门店费用上报模块兼容app ([a82bde6](https://gitlab.hd123.com:20022/vue/soa/commit/a82bde690083a7f51c83a1540fe88e12bf8d0748))
* 🐛 SOP-8693 标准订货单据重复问题优化 ([b03b4ea](https://gitlab.hd123.com:20022/vue/soa/commit/b03b4eaa039d7d8bb92837b7064eccd9ef616360))
* 🐛 SOP-8717 修复调查类任务详情编辑无效的问题 ([93e603f](https://gitlab.hd123.com:20022/vue/soa/commit/93e603f54c8e20dee2ceb357a6a8a9691042c705))

### [2.27.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.27.0...v2.27.1) (2023-02-20)


### Bug Fixes

* 🐛 SOP-8562 加工模块提交和保存前将qpcQty按qpc计算qty ([6e2d3ef](https://gitlab.hd123.com:20022/vue/soa/commit/6e2d3ef3cf0aaa5b6cc802f90cf8cafcef9c36e3))

## [2.27.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.26.0...v2.27.0) (2023-02-20)


### Features

* ✨ SOP-8498 租户许可证二维码增加base64加密解密的逻辑 ([09c0a53](https://gitlab.hd123.com:20022/vue/soa/commit/09c0a53ae5262c5f61978a181d4c65ea40eff969))
* ✨ SOP-8559 支付取消禁用需求 ([c60c86f](https://gitlab.hd123.com:20022/vue/soa/commit/c60c86f9f7676b809961c6206c0e4b2cc00c1886))
* ✨ SOP-8572 新增线上商品模块 ([e26aa8b](https://gitlab.hd123.com:20022/vue/soa/commit/e26aa8b735f79d29643afd7c38102d506c6380e8))
* ✨ SOP-8575 新增门店费用上报模块 ([72fefb9](https://gitlab.hd123.com:20022/vue/soa/commit/72fefb906df50e4429dabf08ba3305a84e951835))


### Bug Fixes

* 🐛 SOP-8619 门店费用上报：页面问题 ([1053ff6](https://gitlab.hd123.com:20022/vue/soa/commit/1053ff6125d00c7af0b1972c8bf1dd65867ced30))

## [2.26.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.2...v2.26.0) (2023-02-03)


### Features

* ✨ SOP-8438 价签申请模块增加标价签维度创建申请的模式 ([4e14826](https://gitlab.hd123.com:20022/vue/soa/commit/4e14826a687b57a43bfe058db757c200ca4c8377))
* ✨ SOP-8523 价格带模块查询商品详情接口增加sku_id参数 ([1b3e84f](https://gitlab.hd123.com:20022/vue/soa/commit/1b3e84f8ea015714f24235c64d9844b51cd798d3))
* ✨ SOP-8560 价签申请增加价签类型功能优化 ([57cc2f4](https://gitlab.hd123.com:20022/vue/soa/commit/57cc2f4946edf68363d4b5dbaa9f222735a8ab93))
* ✨ SOP-8561 APP欢迎页面增加人性化提示 ([acee64d](https://gitlab.hd123.com:20022/vue/soa/commit/acee64da1eb2f666c107bb5ce75549e256bcf4fb))


### Bug Fixes

* 🐛 SOP-8522 收货搜索页逻辑调整 ([d15d49f](https://gitlab.hd123.com:20022/vue/soa/commit/d15d49ff45aedf585abe711d9c6098d74b6d8db7))

## [2.25.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.3.5...v2.25.0) (2023-01-10)


### Bug Fixes

* 🐛 SOP-8471、SOP-8474、SOP-8476 ([460c239](https://gitlab.hd123.com:20022/vue/soa/commit/460c239681b0611f73f96c75511b7e8257d4bafe))
* 🐛 SOP-8472 库存上下限调整：新增页面界面问题 ([c371afd](https://gitlab.hd123.com:20022/vue/soa/commit/c371afd6dc9f7c15acb9a04ec0ffdf1c7ca4e9b1))
* 🐛 SOP-8475 输入的商品上下限不符合限制时，界面没有提示 ([c474dd8](https://gitlab.hd123.com:20022/vue/soa/commit/c474dd8fe2ece0d0364b9478698cb78ec4fb2a0d))

### [2.3.5](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.1...v2.3.5) (2023-01-05)


### Features

* ✨ SOP-8439 新增库存上下限调整功能 ([99aa500](https://gitlab.hd123.com:20022/vue/soa/commit/99aa5004924132e5c532cbd1102a024c56ea6f69))
* ✨ SOP-8440 直送收货、退货模块待处理界面-增加删除功能 ([5fd4825](https://gitlab.hd123.com:20022/vue/soa/commit/5fd4825278290e55e82201a8533f6aa2d4947d8f))
* ✨ SOP-8448 增加埋点信息 ([e15eff3](https://gitlab.hd123.com:20022/vue/soa/commit/e15eff3ce3bcc18924ef5e70a31def86253a25db))
* ✨ SOP-8461 收货清点扫码问题修复 ([33f473e](https://gitlab.hd123.com:20022/vue/soa/commit/33f473e27f895fe2f7b145dc25f8ee7714150889))


### Bug Fixes

* 🐛 SOP-8461 收货清点扫码问题修复 ([eddfffb](https://gitlab.hd123.com:20022/vue/soa/commit/eddfffbee73bb402b622fa5152096fbcdf17f5a1))

### [2.29.2](https://gitlab.hd123.com/vue/soa/compare/v2.29.1...v2.29.2) (2023-03-25)


### Features

* ✨ SOP-8836 订单查询默认筛选当前门店的定单 ([0e532fe](https://gitlab.hd123.com/vue/soa/commit/0e532fe6fd85ba01d627248b4bb4b66a0421417c))

### [2.29.1](https://gitlab.hd123.com/vue/soa/compare/v2.29.0...v2.29.1) (2023-03-22)


### Features

* ✨ SOP-8818 上传图片到oos后不显示图片问题解决 ([5896b9f](https://gitlab.hd123.com/vue/soa/commit/5896b9fa547a3543fd147acfc8662d9b4125e05c))

## [2.29.0](https://gitlab.hd123.com/vue/soa/compare/v2.28.2...v2.29.0) (2023-03-21)


### Features

* ✨ SOP-8764 线上商品模块调整部分校验及展示规则 ([1f9f748](https://gitlab.hd123.com/vue/soa/commit/1f9f748c347f7e7787b0842306a052fd7c19f5d4))
* ✨ SOP-8808 线上商品模块新增按平台授予改价格权限的逻辑 ([49b8eb2](https://gitlab.hd123.com/vue/soa/commit/49b8eb2f29ecc19eaa9f8acb01886d9ddf71e056))

### [2.28.2](https://gitlab.hd123.com/vue/soa/compare/v2.28.1...v2.28.2) (2023-03-19)


### Features

* ✨ SOP-8771 新增上传图片前获取oss签名的逻辑 ([06a37ee](https://gitlab.hd123.com/vue/soa/commit/06a37ee4986690259e8bec44f54102bb88e1e904))

### [2.28.1](https://gitlab.hd123.com/vue/soa/compare/v2.28.0...v2.28.1) (2023-03-18)


### Features

* ✨ SOP-8771 上传图片或视频新增校验图片或视频是否存在的逻辑 ([ce28747](https://gitlab.hd123.com/vue/soa/commit/ce2874714c0d52e817daa0378815297bd307c4d7))

## [2.28.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.27.3...v2.28.0) (2023-03-03)


### Features

* ✨ SOP-8558 复盘需要显示初盘差异数 ([3119f38](https://gitlab.hd123.com:20022/vue/soa/commit/3119f388667166f02c7515709b5ba4771139ab62))


### Bug Fixes

* 🐛 SOP-8657 门店费用上报模块兼容app ([a82bde6](https://gitlab.hd123.com:20022/vue/soa/commit/a82bde690083a7f51c83a1540fe88e12bf8d0748))
* 🐛 SOP-8693 标准订货单据重复问题优化 ([b03b4ea](https://gitlab.hd123.com:20022/vue/soa/commit/b03b4eaa039d7d8bb92837b7064eccd9ef616360))
* 🐛 SOP-8728 标准订货获取单据详情接口调整 ([392a27c](https://gitlab.hd123.com:20022/vue/soa/commit/392a27ceaba4ecf977d2be7d11a80da6a7b113b2))

### [2.27.3](https://gitlab.hd123.com/vue/soa/compare/v2.27.1...v2.27.3) (2023-03-02)


### Bug Fixes

* 🐛 加盟费用状态增加已完成对应图片 ([33e287b](https://gitlab.hd123.com/vue/soa/commit/33e287b6b2b92c2616bb894c17ff3013b36cd7b6))
* 🐛 SOP-8717 修复调查类任务详情编辑无效的问题 ([93e603f](https://gitlab.hd123.com/vue/soa/commit/93e603f54c8e20dee2ceb357a6a8a9691042c705))

### [2.27.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.27.1...v2.27.2) (2023-02-22)


### Bug Fixes

* 🐛 加盟费用状态增加已完成对应图片 ([33e287b](https://gitlab.hd123.com:20022/vue/soa/commit/33e287b6b2b92c2616bb894c17ff3013b36cd7b6))

### [2.27.1](https://gitlab.hd123.com/vue/soa/compare/v2.27.0...v2.27.1) (2023-02-20)


### Bug Fixes

* 🐛 SOP-8562 加工模块提交和保存前将qpcQty按qpc计算qty ([6e2d3ef](https://gitlab.hd123.com/vue/soa/commit/6e2d3ef3cf0aaa5b6cc802f90cf8cafcef9c36e3))

## [2.27.0](https://gitlab.hd123.com/vue/soa/compare/v2.26.0...v2.27.0) (2023-02-20)


### Features

* ✨ SOP-8498 租户许可证二维码增加base64加密解密的逻辑 ([09c0a53](https://gitlab.hd123.com/vue/soa/commit/09c0a53ae5262c5f61978a181d4c65ea40eff969))
* ✨ SOP-8559 支付取消禁用需求 ([c60c86f](https://gitlab.hd123.com/vue/soa/commit/c60c86f9f7676b809961c6206c0e4b2cc00c1886))
* ✨ SOP-8560 价签申请增加价签类型功能优化 ([57cc2f4](https://gitlab.hd123.com/vue/soa/commit/57cc2f4946edf68363d4b5dbaa9f222735a8ab93))
* ✨ SOP-8561 APP欢迎页面增加人性化提示 ([acee64d](https://gitlab.hd123.com/vue/soa/commit/acee64da1eb2f666c107bb5ce75549e256bcf4fb))
* ✨ SOP-8572 新增线上商品模块 ([e26aa8b](https://gitlab.hd123.com/vue/soa/commit/e26aa8b735f79d29643afd7c38102d506c6380e8))
* ✨ SOP-8575 新增门店费用上报模块 ([72fefb9](https://gitlab.hd123.com/vue/soa/commit/72fefb906df50e4429dabf08ba3305a84e951835))


### Bug Fixes

* 🐛 SOP-8619 门店费用上报：页面问题 ([1053ff6](https://gitlab.hd123.com/vue/soa/commit/1053ff6125d00c7af0b1972c8bf1dd65867ced30))

## [2.26.0](https://gitlab.hd123.com/vue/soa/compare/v2.25.0...v2.26.0) (2023-02-03)


### Features

* ✨ SOP-8438 价签申请模块增加标价签维度创建申请的模式 ([4e14826](https://gitlab.hd123.com/vue/soa/commit/4e14826a687b57a43bfe058db757c200ca4c8377))
* ✨ SOP-8523 价格带模块查询商品详情接口增加sku_id参数 ([1b3e84f](https://gitlab.hd123.com/vue/soa/commit/1b3e84f8ea015714f24235c64d9844b51cd798d3))


### Bug Fixes

* 🐛 SOP-8522 收货搜索页逻辑调整 ([d15d49f](https://gitlab.hd123.com/vue/soa/commit/d15d49ff45aedf585abe711d9c6098d74b6d8db7))

## [2.25.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.3.5...v2.25.0) (2023-01-10)


### Features

* ✨ SOP-8439 新增库存上下限调整功能 ([99aa500](https://gitlab.hd123.com:20022/vue/soa/commit/99aa5004924132e5c532cbd1102a024c56ea6f69))
* ✨ SOP-8440 直送收货、退货模块待处理界面-增加删除功能 ([5fd4825](https://gitlab.hd123.com:20022/vue/soa/commit/5fd4825278290e55e82201a8533f6aa2d4947d8f))
* ✨ SOP-8448 增加埋点信息 ([e15eff3](https://gitlab.hd123.com:20022/vue/soa/commit/e15eff3ce3bcc18924ef5e70a31def86253a25db))


### Bug Fixes

* 🐛 SOP-8471、SOP-8474、SOP-8476 ([460c239](https://gitlab.hd123.com:20022/vue/soa/commit/460c239681b0611f73f96c75511b7e8257d4bafe))
* 🐛 SOP-8472 库存上下限调整：新增页面界面问题 ([c371afd](https://gitlab.hd123.com:20022/vue/soa/commit/c371afd6dc9f7c15acb9a04ec0ffdf1c7ca4e9b1))
* 🐛 SOP-8475 输入的商品上下限不符合限制时，界面没有提示 ([c474dd8](https://gitlab.hd123.com:20022/vue/soa/commit/c474dd8fe2ece0d0364b9478698cb78ec4fb2a0d))

### [2.24.2](https://gitlab.hd123.com/vue/soa/compare/v2.24.1...v2.24.2) (2023-01-10)


### Features

* ✨ SOP-8523 价格带模块查询商品详情接口增加sku_id参数 ([fade6cf](https://gitlab.hd123.com/vue/soa/commit/fade6cfb78fd449f05af3f149b6da32377327b31))

### [2.24.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.0...v2.24.1) (2022-12-21)


### Bug Fixes

* 🐛 SOP-8378 全部应用.应用过多时会出现重叠现象 ([12ee341](https://gitlab.hd123.com:20022/vue/soa/commit/12ee3418f2a2454ec9d000c6d2cb986bec5a1c9f))
* 🐛 SOP-8386 标准订货：商品列表--商品的日销弹窗显示不友好 ([6a0fdc9](https://gitlab.hd123.com:20022/vue/soa/commit/6a0fdc92a03a78e5ec053a371fecc26d08875122))
* 🐛 SOP-8396 当前商品扫码查询扫描商品条码调用接口是 超市版逻辑 ([29b36dd](https://gitlab.hd123.com:20022/vue/soa/commit/29b36dd483bb84abacfb6c9831108d4dbbf36dd8))

## [2.24.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.23.0...v2.24.0) (2022-12-09)


### Features

* ✨ SOP-8302 门店助手.兼容PDA ([9cecbfc](https://gitlab.hd123.com:20022/vue/soa/commit/9cecbfc8ebb27bb2fd876be56eb7828325692fc7))
* ✨ SOP-8304 PDA扫码适配需求 ([c8afaef](https://gitlab.hd123.com:20022/vue/soa/commit/c8afaeff0c69722f0dce4c891ad87701deb5a7a0))
* ✨ SOP-8338 知识库视频按配置不允许拖拽 ([1341ab2](https://gitlab.hd123.com:20022/vue/soa/commit/1341ab2eae00545df5e14bcbca5c93c6025e7837))


### Bug Fixes

* 🐛 SOP-8274 任务中心列表页搜索页卡片显示优化 ([74eac7e](https://gitlab.hd123.com:20022/vue/soa/commit/74eac7e9da9fb2280129e6ec262d560c13629425))
* 🐛 SOP-8317 -质量反馈选择11月份没有日期 ([1182b2a](https://gitlab.hd123.com:20022/vue/soa/commit/1182b2ae145d636bf5b56ce2850589662f748e3d))
* 🐛 SOP-8318 两个订货模块，新建订货，添加商品页面，商品名称较长时，展示不友好 ([ac7a3ad](https://gitlab.hd123.com:20022/vue/soa/commit/ac7a3ad1b426684117ea58fc32eed28d83ecd138))
* 🐛 SOP-8334 收货模块和设备认证扫码兼容PDA ([75cccbc](https://gitlab.hd123.com:20022/vue/soa/commit/75cccbca05bea92fd9a89c80ac5e0d0cf6d7d0da))
* 🐛 SOP-8345 学习中心优化：视频配置不允许拖动，全屏播放视频，进度条可以拖动 ([bc276a2](https://gitlab.hd123.com:20022/vue/soa/commit/bc276a22eddc06febb33bffea37bb20b2fa24a02))
* 🐛 SOP-8349 知识库资料查询，选择一级分类，二级分类选择全部，查不到一级分类下的资料 ([0c392c3](https://gitlab.hd123.com:20022/vue/soa/commit/0c392c3a5ef7acd577573667068928fad218b3f1))

## [2.23.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.22.3...v2.23.0) (2022-11-25)


### Features

* ✨ SOP-8193 小程序端增加租户管理的逻辑 ([1765e63](https://gitlab.hd123.com:20022/vue/soa/commit/1765e63defcc573e815deb88406f083f8d9d7c3c))


### Bug Fixes

* 🐛 SOP-8188 任务模块，领取任务，提交或评价后，页面未更新 ([aed42ad](https://gitlab.hd123.com:20022/vue/soa/commit/aed42ad7038e4b9ddde18dd648c91ad47a52509e))
* 🐛 SOP-8211 历史评价，跳转页面，数据展示不友好，无法点击 ([0dca03d](https://gitlab.hd123.com:20022/vue/soa/commit/0dca03d3fc960fbe31312124a58564a701e6a22d))

### [2.22.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.22.2...v2.22.3) (2022-11-15)


### Bug Fixes

* 🐛 SOP-8223 账单门店费用代扣逻辑调整 ([d97110e](https://gitlab.hd123.com:20022/vue/soa/commit/d97110e104f7815cd8ceb5f8a2fecc8fa27e7550))

### [2.22.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.22.1...v2.22.2) (2022-11-14)

### [2.22.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.22.0...v2.22.1) (2022-11-11)


### Bug Fixes

* 🐛 SOP-8206 订货活动.已报名的活动，修改报名数之后，页面的剩余可订数未发生变化 ([8d938fb](https://gitlab.hd123.com:20022/vue/soa/commit/8d938fb3a9ee357f7115539bfe7fa64614f0a2d0))

## [2.22.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.21.1...v2.22.0) (2022-11-11)


### Features

* ✨ SOP-8121 叫货对接云资金支付优化 ([919efd8](https://gitlab.hd123.com:20022/vue/soa/commit/919efd80bb9cbf0618c6a01c4f39861f8daba116))
* ✨ SOP-8127 门店助手盘点操作优化 ([62af7b1](https://gitlab.hd123.com:20022/vue/soa/commit/62af7b1843f07d043f6ae38919ca151546d4c976))
* ✨ SOP-8128 提成绩效移动端展示 ([3f0372f](https://gitlab.hd123.com:20022/vue/soa/commit/3f0372ffc8c6dbed8e9980aa106ae9ec5e70e35b))


### Bug Fixes

* 🐛 APP 修复清仓促销、组合折扣创建页面输入框占位文字字体过小的问题 ([7d48190](https://gitlab.hd123.com:20022/vue/soa/commit/7d4819082e82ff6628f66104fc1d6a2717e38b9b))
* 🐛 SOP-7583 APP修复正常订货-日销折线图在APP端文字过大的问题 ([347edaf](https://gitlab.hd123.com:20022/vue/soa/commit/347edaf5950a7f94a412462a6060478f139fa8d0))
* 🐛 SOP-8001 UNI 接入uni时将获取岗位的接口调整到首页调用 ([d46958a](https://gitlab.hd123.com:20022/vue/soa/commit/d46958a264c4f3ef0c7464283c2d762931eafbb9))
* 🐛 SOP-8127 复盘抽盘不需要显示全选 ([b19614b](https://gitlab.hd123.com:20022/vue/soa/commit/b19614b75c43198a8208b3cae63c99d51109dcc3))
* 🐛 SOP-8163 直送收货/退货支持展示供应商代码+名称.页面显示问题汇总 ([3128fb5](https://gitlab.hd123.com:20022/vue/soa/commit/3128fb50b47415564bb4470412fd56acb8edbb90))
* 🐛 SOP-8182 门店助手盘点操作优化_问题汇总 ([7de5fac](https://gitlab.hd123.com:20022/vue/soa/commit/7de5fac0daa77c91aa71f145a605d4ca08ef1d6f))
* 🐛 SOP-8182 门店助手盘点全选操作优化 ([c81624c](https://gitlab.hd123.com:20022/vue/soa/commit/c81624ce961e2953412e42ec2aecb29269a53672))

### [2.21.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.21.0...v2.21.1) (2022-11-01)


### Features

* ✨ SOP-8119 直送收货/退货支持展示供应商代码+名称 ([165c0dc](https://gitlab.hd123.com:20022/vue/soa/commit/165c0dcd52e0969028c51abf961a1d8b9b7f8161))
* ✨ SOP-8120 直送收货商品单价保留4位小数 ([0c914c5](https://gitlab.hd123.com:20022/vue/soa/commit/0c914c54a8c7dff54a5e1cf1a27deae65a6640b8))
* ✨ SOP-8131 钉钉打包解决重复key的报错 ([0700e45](https://gitlab.hd123.com:20022/vue/soa/commit/0700e45b475446c41d6a14833303978bf946fe55))

## [2.21.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.20.1...v2.21.0) (2022-10-31)


### Features

* ✨ SOP-7327 水印门店信息从显示id改为显示code ([403e555](https://gitlab.hd123.com:20022/vue/soa/commit/403e555a084c5e403584292a82134d9faf5778fc))
* ✨ SOP-7994 SOP、TAS、UAS接口地址替换为v2_5 ([b5dd0c5](https://gitlab.hd123.com:20022/vue/soa/commit/b5dd0c5c9f66244c0bd4b987864a6797777d72c2))
* ✨ SOP-8015 调拨申请单增加deliveryType字段 ([10a0519](https://gitlab.hd123.com:20022/vue/soa/commit/10a0519f5b5f76c91db56b2bc3f59fc0fcd41eeb))
* ✨ SOP-8090 资金流水详情页面 备注取值从note字段调整为srcNote字段 ([931b2c5](https://gitlab.hd123.com:20022/vue/soa/commit/931b2c592f7aae154c031c3d84023277dd7cd8c6))


### Bug Fixes

* 🐛 SOP-8041 调拨申请单.配送模式显示优化 ([b163043](https://gitlab.hd123.com:20022/vue/soa/commit/b163043493d36d4c2fd907f4a4dee4f340324e69))

### [2.20.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.20.0...v2.20.1) (2022-10-17)


### Features

* ✨ SOP-7980 便利版支持取输入框输入租户的逻辑 ([4b77338](https://gitlab.hd123.com:20022/vue/soa/commit/4b77338cc990c875491035466ed5cf1bb9b16875))

## [2.20.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.16.4...v2.20.0) (2022-10-14)


### Features

* ✨ SOP-7552 提供HD-UNI小程序编译方案和接入uni的方案 ([6c44dae](https://gitlab.hd123.com:20022/vue/soa/commit/6c44dae010c80f613a45877801ac8cab05c8467c))
* ✨ SOP-7552 支持与UNI主应用通信 ([65e05c9](https://gitlab.hd123.com:20022/vue/soa/commit/65e05c9dd096ffc04b5098b600e7a8f5bec79ad9))


### Bug Fixes

* 🐛 将价签调整任务显示价格改为调整后价格 ([f993468](https://gitlab.hd123.com:20022/vue/soa/commit/f993468ff99e220708b53eb9f12556ca79fc1ad9))
* 🐛 SOP-7054 修复整单满赠折扣为0时没有校验数据合法性的问题 ([a2fc340](https://gitlab.hd123.com:20022/vue/soa/commit/a2fc34004fe0dc16956324b008f04c70593f28b8))
* 🐛 SOP-7483 修复满额换购与单品换购 换购商品数值统计错误的问题 ([b51e7a2](https://gitlab.hd123.com:20022/vue/soa/commit/b51e7a272fdadb933ccdc1be2d4bf551c1e670ef))
* 🐛 SOP-7545 门店助手APP：线路调整-下拉日期选择框，重置后再点击门店，门店消失 ([8403a1a](https://gitlab.hd123.com:20022/vue/soa/commit/8403a1ae551e507c931328a64d9aee51ffa587ef))
* 🐛 SOP-7553 小程序&APP.标准订货/独立订货，数量编辑存在问题 ([3cea1fc](https://gitlab.hd123.com:20022/vue/soa/commit/3cea1fc32a55d1d72f000ca790a82a824d0b6c65))
* 🐛 SOP-7555 页面编辑了数量点击左上角返回没有二次确认提示框 ([36171ed](https://gitlab.hd123.com:20022/vue/soa/commit/36171ed00606b1da32039de925bbd9f34bc302ed))
* 🐛 SOP-7556 进入订货模块，温馨提示滑动速度过快 ([39d4caa](https://gitlab.hd123.com:20022/vue/soa/commit/39d4caa73b2a4528720abae7e1552f914932ff9e))
* 🐛 SOP-7565 叫货建议单搜索页面，删除商品列表刷新，接口数据正确，前端显示重复 ([ec91e64](https://gitlab.hd123.com:20022/vue/soa/commit/ec91e64b43fd459c2c8d38d38b2958f85256422b))
* 🐛 SOP-7735 门店助手APP：盘点模块，设置复盘商品，弹框显示异常问题 ([b2d6dad](https://gitlab.hd123.com:20022/vue/soa/commit/b2d6dad2daddc0b7ad6cf539ead1b097ed2093f2))
* 🐛 SOP-7735 盘点模块，设置复盘商品，弹框显示异常问题 ([61a968a](https://gitlab.hd123.com:20022/vue/soa/commit/61a968afe4d151ebd3e591ca063a42eb5dc19043))
* 🐛 SOP-7827 APP修复设置复盘商品时一级分类滚动范围小的问题 ([ee14172](https://gitlab.hd123.com:20022/vue/soa/commit/ee141725be9c61f144a3a376323a7d580d07953f))
* 🐛 SOP-7835 修复app端台账任务历史评价内容展示不全的问题 ([3c9d554](https://gitlab.hd123.com:20022/vue/soa/commit/3c9d5543ac8263d5607f85d0edc362d10d77f130))
* 🐛 SOP-7836 修复台账任务查看父反馈内容超出没有滚动的问题 ([368f0dd](https://gitlab.hd123.com:20022/vue/soa/commit/368f0dd7632b245b86956fb433534e72f2e15596))
* 🐛 SOP-7837 修复APP端任务详情选择图片触发onShow时更新任务详情数据导致填写内容消失的问题 ([74d1363](https://gitlab.hd123.com:20022/vue/soa/commit/74d1363033774b611209d6da8be548e16275613d))
* 🐛 SOP-7837 APP修复台账任务详情编辑时无法添加图片的问题 ([fdd439b](https://gitlab.hd123.com:20022/vue/soa/commit/fdd439bfa885907f0822ddbad10654af88ae8a52))
* 🐛 SOP-7846 修复资料模块搜索无空状态及文件调整引起的分包问题 ([f7f602e](https://gitlab.hd123.com:20022/vue/soa/commit/f7f602ecfe819d0f29be209fcb2d49cd5c330822))
* 🐛 SOP-7847、SOP-7849 ([f4f498c](https://gitlab.hd123.com:20022/vue/soa/commit/f4f498c56b0c96d63b2def9b73ca7c20dc1935cd))
* 🐛 SOP-7890 直送收退货货保存后未提交再编辑时无法修改价格 ([2a708fb](https://gitlab.hd123.com:20022/vue/soa/commit/2a708fb884bdde5e2aa933fbb9708dea75f375bf))
* 🐛 SOP-7920 账户流水，筛选弹窗点击金额输入框，弹出的键盘将“重置”“确定”按钮遮盖了 ([a2a87e2](https://gitlab.hd123.com:20022/vue/soa/commit/a2a87e2d2e5e3541e73b394155b909b151e776e7))
* 🐛 SOP-7924 任务详情页有多个图片时，点击非第一张图片，展示的都是第一张图片 ([a358abd](https://gitlab.hd123.com:20022/vue/soa/commit/a358abd40046804edf9cd3f3dd4402767aac1507))
* 🐛 SOP-7924 任务详情页有多个图片时，点击非第一张图片，展示的都是第一张图片 ([fa7ee59](https://gitlab.hd123.com:20022/vue/soa/commit/fa7ee5999cd8ec2612cba9a44837b92866551680))

### [2.16.4](https://gitlab.hd123.com:20022/vue/soa/compare/v2.19.0...v2.16.4) (2022-09-30)


### Features

* ✨ SOP-7742 优化盘点模块在APP端的表现 ([15db6a8](https://gitlab.hd123.com:20022/vue/soa/commit/15db6a8c53c6131a43198c1d5f04d24c8f1ee287))
* ✨ SOP-7876 直送退货保存后未提交再编辑时无法修改价格 ([690e8aa](https://gitlab.hd123.com:20022/vue/soa/commit/690e8aa89f862832d3afe45a35df216866a9a57f))


### Bug Fixes

* 🐛 SOP-7543 线路调整-下拉日期选择框，toast提示结束日期不能早于开始日期/所选日期不能晚于当天 显示了 ([d4fb696](https://gitlab.hd123.com:20022/vue/soa/commit/d4fb6961e3691bc0024a7dd29fd507c12a65781d))
* 🐛 SOP-7547 线路调整-订单详情-分类名称过长显示不友好 ([b7ff409](https://gitlab.hd123.com:20022/vue/soa/commit/b7ff409ba61cca2fe6b467cc63aae065a5aba921))
* 🐛 SOP-7570、SOP-7558 ([3c39eeb](https://gitlab.hd123.com:20022/vue/soa/commit/3c39eebbdd78fb55766db668a71cd217fff81943))
* 🐛 SOP-7827 盘点选择商品页面商品滚动区域高度调整 ([6c87e4c](https://gitlab.hd123.com:20022/vue/soa/commit/6c87e4cb9f21a7257bf523feb38a36243e67e00a))
* 🐛 SOP-7853、SOP-7854 ([8374be0](https://gitlab.hd123.com:20022/vue/soa/commit/8374be04a5c1536e2396e1b895d425f800fcc106))

## [2.19.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.18.2...v2.19.0) (2022-09-23)


### Features

* ✨ SOP-7771 新增知识库模块 ([c613456](https://gitlab.hd123.com:20022/vue/soa/commit/c6134564de8edfae0f1a789a5ec9cad0790d0439))

### [2.18.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.18.1...v2.18.2) (2022-09-16)


### Bug Fixes

* 🐛 SOP-7701 修复了任务完成清空后tab徽标还存在的问题 ([822942a](https://gitlab.hd123.com:20022/vue/soa/commit/822942aa9bac74c89a57e3451fab5d3f4519bb4e))

### [2.18.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.18.0...v2.18.1) (2022-09-13)

## [2.18.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.17.0...v2.18.0) (2022-09-13)


### Features

* ✨ SOP-7418 台账任务反馈评价改造 ([80a135c](https://gitlab.hd123.com:20022/vue/soa/commit/80a135c4aea3338662b4436e43082dfa040ee47d))
* ✨ SOP-7552 UNI-PORTAL传入参数结构调整 ([ef48e3d](https://gitlab.hd123.com:20022/vue/soa/commit/ef48e3db67ba6f01ba9c502e316cc637c0f6b475))
* ✨ SOP-7604 退货通知数量交互优化 ([4f683fe](https://gitlab.hd123.com:20022/vue/soa/commit/4f683fe2e7d2bad033b75b1de198872fa5fb9bb2))


### Bug Fixes

* 🐛 修复了不合格tag展示靠后的问题 ([a6e3074](https://gitlab.hd123.com:20022/vue/soa/commit/a6e30740276762858b064dcc25e2e758289ee1f7))
* 🐛 修复了单模板台账未显示历史评价的问题 ([732dd7a](https://gitlab.hd123.com:20022/vue/soa/commit/732dd7a9b61f7d8697c435e688a8316966802907))
* 🐛 SOP-7526 修复了整改任务没有tag的问题 ([a127635](https://gitlab.hd123.com:20022/vue/soa/commit/a1276351e74eeffe3d3e214385cc7caab19d5c29))
* 🐛 SOP-7526 SOP-7519 SOP-7517 SOP-7484 台账问题修复 ([8ab7658](https://gitlab.hd123.com:20022/vue/soa/commit/8ab7658d82bd90a2531e217819b3b0ec0b5e5fdc))
* 🐛 SOP-7534 修复在接入UNI时仍检查APP更新的问题 ([0e2b4b4](https://gitlab.hd123.com:20022/vue/soa/commit/0e2b4b4cfaaca6fde86ea48d774ccc56b366c698))
* 🐛 SOP-7544 线路调整门店搜索按name搜索 ([f1006df](https://gitlab.hd123.com:20022/vue/soa/commit/f1006df72d2b6c94d23df1276af98ac6ea456757))
* 🐛 SOP-7562 修复了模板名称过长时不合格样式展示的问题 ([787e3ce](https://gitlab.hd123.com:20022/vue/soa/commit/787e3ce3ba4756e5279a27d6e141291899c3d5c3))
* 🐛 SOP-7567 APP端盘点白屏问题处理 ([3ebcd75](https://gitlab.hd123.com:20022/vue/soa/commit/3ebcd75263e10e8fea5ffff3297f849004689916))
* 🐛 SOP-7572 修复了台账任务提交反馈提交评价字数限制未生效的问题 ([d64e2cf](https://gitlab.hd123.com:20022/vue/soa/commit/d64e2cfd5a2ac12c7c6a5f2727193bf088b2f4ce))
* 🐛 SOP-7574 修复了台账任务历史评价无内容的问题 ([d799261](https://gitlab.hd123.com:20022/vue/soa/commit/d799261d8f7ec79c73254efe90f46663cc30d69b))
* 🐛 SOP-7665 退货通知限量标签显示 ([5a081b1](https://gitlab.hd123.com:20022/vue/soa/commit/5a081b18c684f09ba5b5c750c83f04ad32ea1dd1))
* 🐛 SOP-7665 退货通知限量标签显示 ([96797d8](https://gitlab.hd123.com:20022/vue/soa/commit/96797d83e532287892ee8561f952fc26a8028e8b))
* 🐛 SOP-7666 修复了台账反馈和评价流程的问题 ([63e793c](https://gitlab.hd123.com:20022/vue/soa/commit/63e793cebcf43de1ea91c6deaa4298efeb4f248d))
* 🐛 SOP-7666 修复了台账任务完成反馈评价的流程问题 ([acad9ca](https://gitlab.hd123.com:20022/vue/soa/commit/acad9ca60b20f841375bea9fbb6ffa866c35cdce))

## [2.17.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.16.3...v2.17.0) (2022-08-26)


### Features

* ✨ SOP-7278 质量反馈隐藏赔付比例 ([fa828e7](https://gitlab.hd123.com:20022/vue/soa/commit/fa828e796be3b8fdd00ac72cfb96cdbed8f2be3c))
* ✨ SOP-7283 增加门店资金账户查看权限 ([9126410](https://gitlab.hd123.com:20022/vue/soa/commit/9126410adb34aaf63eaa69799580ab43dd885050))
* ✨ SOP-7283 增加门店资金账户相关查看功能 ([d042c8e](https://gitlab.hd123.com:20022/vue/soa/commit/d042c8e67fdf79cac71b415c722e523df73d4d3f))
* ✨ SOP-7418 新增台账任务改造 ([99cebee](https://gitlab.hd123.com:20022/vue/soa/commit/99cebee3980cd79c2c5119172dd051d5b38ed1bb))


### Bug Fixes

* 🐛 SOP-7035 修复图片视频上传组件在钉钉平台预览视频播放器透明的问题 ([eb3e6c6](https://gitlab.hd123.com:20022/vue/soa/commit/eb3e6c695466b17652cb7085dde6751db8c09e2e))
* 🐛 SOP-7038 修复APP端营销玩法列表上拉加载无法触发的问题 ([1e941db](https://gitlab.hd123.com:20022/vue/soa/commit/1e941db18940e934781c836a8a4be0bdbc7c5992))
* 🐛 SOP-7040 修复钉钉平台收货列表卡片的电商标记与其他tag没有间距的问题 ([18430cf](https://gitlab.hd123.com:20022/vue/soa/commit/18430cfa897ad83592d7df37bcadc13393868a47))
* 🐛 SOP-7043 修复创建促销价活动页面备注输入框被提交按钮遮挡的问题 ([791962c](https://gitlab.hd123.com:20022/vue/soa/commit/791962c2c191e60387cfd3339d71f65e6b8ca43b))
* 🐛 SOP-7044 修复标价签申请选择商品页面勾选移除后徽标数值不减少的问题 ([292bb1c](https://gitlab.hd123.com:20022/vue/soa/commit/292bb1cb63d7cdd83f5106a1c581a9d308fd72d4))
* 🐛 SOP-7045 收货详情页面一件确认行样式字数优化 ([6fd8a32](https://gitlab.hd123.com:20022/vue/soa/commit/6fd8a329303547baa05b19eb0e1076ca4de46f01))
* 🐛 SOP-7046 收货.清点模式下，未确认状态的商品列表，散称商品联动逻辑不正确 ([8d40ad3](https://gitlab.hd123.com:20022/vue/soa/commit/8d40ad3193f94f0e4bd508b1ee4d6de05d7cbed8))
* 🐛 SOP-7047 订货活动-点击立即报名，提交报名后，弹层提示商品种类数、金额为0，数值错误 ([35f2b4c](https://gitlab.hd123.com:20022/vue/soa/commit/35f2b4c8ce8c85da1784680888bb0b4824aa84aa))
* 🐛 SOP-7048 修复收货提交错误重试弹框重试多次消失的问题 ([6bb60e4](https://gitlab.hd123.com:20022/vue/soa/commit/6bb60e4fb53ebe792362470fc8a40fb1a42ddc1a))
* 🐛 SOP-7049 数据tab页uCharts类型提取兼容APP ([72935de](https://gitlab.hd123.com:20022/vue/soa/commit/72935de05b229f6d944c925d82a50230b9002a89))
* 🐛 SOP-7051 修复营销玩法创建促销折扣的折扣输入框placeholder文字很小的问题 ([8b4943a](https://gitlab.hd123.com:20022/vue/soa/commit/8b4943aed5c7b798901fcf0a640c0fcd21f85e51))
* 🐛 SOP-7052 小程序：订货活动点击活动跳转详情页，未加载出数据时，时间展示为null ([5e73f6e](https://gitlab.hd123.com:20022/vue/soa/commit/5e73f6e6474e29618a1a0ed313a99577d4ad324d))
* 🐛 SOP-7053 修复APP端数据tab页面环比数据颜色未生效的问题 ([4bc26f2](https://gitlab.hd123.com:20022/vue/soa/commit/4bc26f2474a294f6be909cb87ae172d3cf889edf))
* 🐛 SOP-7054 修复营销玩法新建活动时折扣或门槛为0时可以提交的问题 ([b44b791](https://gitlab.hd123.com:20022/vue/soa/commit/b44b791542ce95b57216cd6f614ad1adfc4b4ca4))
* 🐛 SOP-7055 修复营销玩法模块输入框不展示以及无法选择时段折扣起止时间的问题 ([a41ac1a](https://gitlab.hd123.com:20022/vue/soa/commit/a41ac1a41f27d12befb38db314914b280fee10ca))
* 🐛 SOP-7056 修复APP端收货容差弹出框重复弹出时第二次不弹出的问题 ([64d2071](https://gitlab.hd123.com:20022/vue/soa/commit/64d20716f55df04b60bf332aa9c34eeb3c14ed83))
* 🐛 SOP-7058 修复营销玩法-阶梯折扣单品满件时保存按钮的生效未将适用商品数量纳入判断 ([1dd5168](https://gitlab.hd123.com:20022/vue/soa/commit/1dd5168dedc09e78cc6440e0afda35fd710df452))
* 🐛 SOP-7059  修复营销玩法详情页面多个卡片同时显示时标题会被下一个卡片继承的问题 ([b606c7a](https://gitlab.hd123.com:20022/vue/soa/commit/b606c7afc80cc133210b48f18dda509fb6794f97))
* 🐛 SOP-7060 修复APP端营销玩法-换购玩法换购商品因为key值不存在无法显示的问题 ([81bf65c](https://gitlab.hd123.com:20022/vue/soa/commit/81bf65cb79848639a5dd6beee6859d134530fa3d))
* 🐛 SOP-7063 修复自采模块编辑页面上下滚动时部分低层级元素透过高层级元素展示的问题 ([cd69845](https://gitlab.hd123.com:20022/vue/soa/commit/cd69845b3a66ddbe673cf88152ac2611d61819a0))
* 🐛 SOP-7063 修复自采模块使用text标签包裹其他标签的问题 ([d065ce9](https://gitlab.hd123.com:20022/vue/soa/commit/d065ce93765250b7078899a93a4dcf724c7719b6))
* 🐛 SOP-7064 修复APP端自采模块编辑时自裁费用无输入框的问题 ([2e9f368](https://gitlab.hd123.com:20022/vue/soa/commit/2e9f368cf55b9123218c538eddec9cd8acbf634b))
* 🐛 SOP-7077 修复营销玩法满额换购与单品是否可以换购选择商品未进行控制的问题 ([57bb505](https://gitlab.hd123.com:20022/vue/soa/commit/57bb5052929c8b869905b035545a10b168eb3571))
* 🐛 SOP-7082 门店助手店务点击“取消”按钮，无反应 ([a90e664](https://gitlab.hd123.com:20022/vue/soa/commit/a90e6645f30c875442e3e0c000c7ccdccf3606a3))
* 🐛 SOP-7099 修复订单查询模块toast提示重复的问题 ([a4d135c](https://gitlab.hd123.com:20022/vue/soa/commit/a4d135c15f3a680e150451f789a070e423316b29))
* 🐛 SOP-7101 修复门店助手APP/小程序.调拨申请库存不显示 ([bacd2d4](https://gitlab.hd123.com:20022/vue/soa/commit/bacd2d4adc05f49f567a32916c7cda345a63c4ac))
* 🐛 SOP-7106 商品查询页面，title字体颜色与背景色一致，显示不友好 ([ec1ca56](https://gitlab.hd123.com:20022/vue/soa/commit/ec1ca564bc7f32d829eb8884d32ded14dec0a000))
* 🐛 SOP-7109 修复商品查询报错提示未展示的问题 ([1fdae89](https://gitlab.hd123.com:20022/vue/soa/commit/1fdae894ca411dc94d878e48c18a4a15d5f38d90))
* 🐛 SOP-7431 账户流水页界面问题 ([8ac9400](https://gitlab.hd123.com:20022/vue/soa/commit/8ac940057f1a7bf48b94550bc9f34bef10d21a7f))
* 🐛 SOP-7434 账户流水：最低金额输入0后进行筛选，筛选弹窗中对应值为空 ([2bf8081](https://gitlab.hd123.com:20022/vue/soa/commit/2bf808136ee69a1134bfe21f83581b9306fd7c73))
* 🐛 SOP-7437 账户流水：流水明细页界面问题 ([c93d013](https://gitlab.hd123.com:20022/vue/soa/commit/c93d013fbdc5f5ba60aca65b833120892d1baf03))

### [2.16.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.16.2...v2.16.3) (2022-08-17)


### Bug Fixes

* 🐛 SOP-7393 价格带模块位置调整 ([b7eeccf](https://gitlab.hd123.com:20022/vue/soa/commit/b7eeccfef5ec8a9b523cd1c050962d8691079774))

### [2.16.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.15.2...v2.16.2) (2022-08-15)


### Features

* ✨ SOP-7311 增加权限申请逻辑 ([77feed4](https://gitlab.hd123.com:20022/vue/soa/commit/77feed4b3bf5c92655d137e4db011730fa3c371e))


### Bug Fixes

* 🐛 SOP-7103、SOP-7096 ([6f1dac5](https://gitlab.hd123.com:20022/vue/soa/commit/6f1dac5d7a966dd927017f2d0334cd943ac60ae2))

### [2.15.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.16.0...v2.15.2) (2022-08-12)

## [2.16.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.15.1...v2.16.0) (2022-08-12)


### Features

* ✨ SOP-7282 线路调整.督导管理门店范围权限通过数据授权门店关系实现 ([8ae33e9](https://gitlab.hd123.com:20022/vue/soa/commit/8ae33e984546f55fe3510f82cb3d0edae520ea88))
* ✨ SOP-7312 门店价格带优化 自定义价格显示优化 ([554be36](https://gitlab.hd123.com:20022/vue/soa/commit/554be3677acc00988ad18551aa49ce58d398b613))


### Bug Fixes

* 🐛 SOP-7034 报损/报溢模块-报损单详情-点击查看全部-图片遮挡了头部文字 ([44fc9e0](https://gitlab.hd123.com:20022/vue/soa/commit/44fc9e0aed1323d256e5509bbe85ad40268e4d6a))
* 🐛 SOP-7326 移除任务模块组件引用hd-loading、hd-toast和hd-modal组件 ([43b8773](https://gitlab.hd123.com:20022/vue/soa/commit/43b87736b43b3a00ecf2a308b7c0ddfb4862bc9d))
* 🐛 SOP-7332 移除完成反馈弹出框组件内的loading组件 ([456f166](https://gitlab.hd123.com:20022/vue/soa/commit/456f16686d6e2642ef1ee6a8602e7c5a907962e3))
* 🐛 SOP-7348 修复了订货提醒任务提交后任务状态没有改变，修复了店长看到督导可评价的任务后也存在评价按钮的问题 ([10eb8a2](https://gitlab.hd123.com:20022/vue/soa/commit/10eb8a2f076e1bcb51551b45384bcbc18c4c41be))
* 🐛 SOP-7349 申请退货-新增退货申请-选择退货原因提示框-名字超过一行时没有省略显示 ([7beb4cf](https://gitlab.hd123.com:20022/vue/soa/commit/7beb4cfca3bee91c3032fd1e59a41fa99cace88b))
* 🐛 SOP-7365 退货装箱模块切换分包，并且模块跳转采用Route ([deb16f4](https://gitlab.hd123.com:20022/vue/soa/commit/deb16f4255880e5f959352027ee500ba576c1460))
* 🐛 SOP-7367 价格带详情页，切换门店和区域价格带tab后，列表显示数据范围重复 ([c849a78](https://gitlab.hd123.com:20022/vue/soa/commit/c849a783991391d52675b6972ddf196c70f8679d))
* 🐛 SOP-7371 价格方案标题输入框问题，编辑完成后回到搜索页面未刷新 ([bef311d](https://gitlab.hd123.com:20022/vue/soa/commit/bef311db713b170375e0341081e43270b5b18ff7))
* 🐛 SOP-7371 搜索页调整 ([a651fae](https://gitlab.hd123.com:20022/vue/soa/commit/a651faef3fcd520e8fa927404fa51c1ea4773018))
* 🐛 SOP-7372 价格带商品详情页添加删除弹窗显示问题 ([d7882b9](https://gitlab.hd123.com:20022/vue/soa/commit/d7882b95c063193ccfaa75c99e8a7c10b0db0fbe))
* 🐛 SOP-7374 价格带列表需要分开显示区域商品和门店商品数量 ([1e8331b](https://gitlab.hd123.com:20022/vue/soa/commit/1e8331b24ca232ec7ec46771818fec36f7356055))
* 🐛 SOP-7375 价格带列表未加载更多 ([6abb53b](https://gitlab.hd123.com:20022/vue/soa/commit/6abb53b2ec3b582d19b2d2295adb86e3a766e765))

### [2.15.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.15.0...v2.15.1) (2022-08-11)


### Features

* ✨ SOP-7282 线路调整.督导管理门店范围权限通过数据授权门店关系实现 ([39a9522](https://gitlab.hd123.com:20022/vue/soa/commit/39a952254670fe74beb39b2e540d1a8b51dcfb16))
* ✨ SOP-7312 门店价格带优化 ([df564bb](https://gitlab.hd123.com:20022/vue/soa/commit/df564bbed9d6c7eae08ce642e4133420f6dc1e45))
* ✨ SOP-7312 门店价格带优化 ([d63069a](https://gitlab.hd123.com:20022/vue/soa/commit/d63069adc35d0b5c58bffb37daee584ca5d2132d))
* ✨ SOP-7312 门店价格带优化 ([3291231](https://gitlab.hd123.com:20022/vue/soa/commit/3291231d45210453ed5ca9ba04b7b53cde374af2))
* ✨ SOP-7347 将质量反馈可上传图片上限改为6张 ([850e553](https://gitlab.hd123.com:20022/vue/soa/commit/850e5533b1727c6f0da1f32079057039f7dd5716))


### Bug Fixes

* 🐛 APP支持两次侧滑返回 ([b5ced25](https://gitlab.hd123.com:20022/vue/soa/commit/b5ced25dce732a5432774fb88b0dc420b703ff84))
* 🐛 SOP-7024 上传图片视频组件兼容APP-PLUS平台 ([08d92ac](https://gitlab.hd123.com:20022/vue/soa/commit/08d92ac0db2b73eda506501a5d6a75122dbabb29))
* 🐛 SOP-7027 合并2.15.0后无限打印错误提示问题修复 ([beebfb2](https://gitlab.hd123.com:20022/vue/soa/commit/beebfb2afd12a8eee0c14cc2e2008501f8b3d71d))
* 🐛 SOP-7261 商品查询筛选条件未变成全部仓位 ([e6b3827](https://gitlab.hd123.com:20022/vue/soa/commit/e6b3827b3bbe352e202d5c234f461be5ae7caf82))
* 🐛 SOP-7263 ([23a9956](https://gitlab.hd123.com:20022/vue/soa/commit/23a9956bf5e90efcde81865720423a679bb85b4e))
* 🐛 SOP-7267 商品查询：历史搜索，数据超过两行，页面显示不友好 ([ab704af](https://gitlab.hd123.com:20022/vue/soa/commit/ab704af1721476db25a3e2423827205621856389))
* 🐛 SOP-7267 商品查询：历史搜索，数据超过两行，页面显示不友好 ([7e4870b](https://gitlab.hd123.com:20022/vue/soa/commit/7e4870b442a7fa96962c2382760032de8fcaa8c2))
* 🐛 SOP-7271 .箱码明细页面，退货数量修改弹框问题汇总 ([a45ecd1](https://gitlab.hd123.com:20022/vue/soa/commit/a45ecd19b7f28b1a927288917362380bd99bcae6))
* 🐛 SOP-7277 商品查询：商品详情页，商品基础信息，供应商未展示代码 ([1be93cf](https://gitlab.hd123.com:20022/vue/soa/commit/1be93cf2c48e8126cd0f6716297e08aa05002392))
* 🐛 SOP-7280 箱码明细页面，修改商品的箱码后，页面数据未刷新 ([2083a58](https://gitlab.hd123.com:20022/vue/soa/commit/2083a58bc04a512210969d759ce1f3413cf3182b))
* 🐛 SOP-7284 门店助手APP：商品查询页面，搜索框输入品类/品牌/仓位/标签，传参错误 ([a937579](https://gitlab.hd123.com:20022/vue/soa/commit/a9375799614dff3c0f95daa6c3efed0979a77703))
* 🐛 SOP-7284 门店助手APP：商品查询页面，搜索框输入品类/品牌/仓位/标签，传参错误 ([9c1f493](https://gitlab.hd123.com:20022/vue/soa/commit/9c1f493059152b2950810730382d79d0eff95766))
* 🐛 SOP-7285 箱码明细页面，切换箱码列表存在问题 ([5632570](https://gitlab.hd123.com:20022/vue/soa/commit/56325707a2ffbee58d8d540a2d822914bb5c0b99))
* 🐛 SOP-7285、SOP-7305 ([2eba40b](https://gitlab.hd123.com:20022/vue/soa/commit/2eba40b853ce7e74db0ec54ab002a452488671ee))
* 🐛 SOP-7288、SOP-7301、 ([acdeb8f](https://gitlab.hd123.com:20022/vue/soa/commit/acdeb8f74e9b23d994983ecdf813b70b83d246db))
* 🐛 SOP-7289 “已装箱”状态的箱码明细页面，允许修改箱数 ([caa7801](https://gitlab.hd123.com:20022/vue/soa/commit/caa78016f0ff50b40934f8a70b036e5c3d0a40b7))
* 🐛 SOP-7292 退货装箱.复制单据.复制单据的时候校验需要和新增一致 ([c0bb4be](https://gitlab.hd123.com:20022/vue/soa/commit/c0bb4be2b7ba2c276a5266a31d763901615ceb51))
* 🐛 SOP-7295 退货装箱.新建：有数据变更后，点击返回按钮，没有二次弹窗提示 ([522c0c3](https://gitlab.hd123.com:20022/vue/soa/commit/522c0c30412ae29d8562d6904e6fafa547b5a843))
* 🐛 SOP-7305、SOP-7305 ([cafa698](https://gitlab.hd123.com:20022/vue/soa/commit/cafa698116be6ba5b54fd2924de1c7c525f84a6d))
* 🐛 SOP-7306 退货装箱.新建：扫描同样供应商商品，前端仍然分配了新的箱码 ([76b5c58](https://gitlab.hd123.com:20022/vue/soa/commit/76b5c5811d128f440373afb6b676f23c6cb31c11))
* 🐛 SOP-7308 门店助手：商品查询详情页：默认展示全部仓位，点击仓位切换某一个仓位，再切换为全部仓位，接口报错 ([13f6e12](https://gitlab.hd123.com:20022/vue/soa/commit/13f6e1246815bf15d7d2d490b7d524c51d9c65a9))
* 🐛 SOP-7309、SOP-7295 ([4ec1a25](https://gitlab.hd123.com:20022/vue/soa/commit/4ec1a25a0c8cb75d3bec3fa9f0840c52e6343e34))
* 🐛 SOP-7313 退货装箱.新建：存在未装箱商品进行提交，弹窗提示缺少未装箱商品code ([c0cda28](https://gitlab.hd123.com:20022/vue/soa/commit/c0cda281db5133dff1def47884091e76fdd7c5eb))
* 🐛 SOP-7315 箱码--全部tab下，只显示了未装箱的数据 ([e1b7fbb](https://gitlab.hd123.com:20022/vue/soa/commit/e1b7fbbabca8e091a8fe21bcc9f349b09a962a5b))
* 🐛 SOP-7320 重点商品检查APP兼容优化 ([a7b7a89](https://gitlab.hd123.com:20022/vue/soa/commit/a7b7a894abddd3547362fd9e2a2a6d5baadbc015))
* 🐛 SOP-7330 超市门店退货装箱退货申请单字段调整 ([b313ad9](https://gitlab.hd123.com:20022/vue/soa/commit/b313ad9b78b7040d9ca82886a8eb5c531eba1cbd))
* 🐛 SOP-7338 编辑页汇总信息显示优化 ([8139aca](https://gitlab.hd123.com:20022/vue/soa/commit/8139aca2efd438f70c8e39a09402224e3d35a71e))
* 🐛 SOP-7338 退货装箱.编辑页汇总信息显示优化 ([2ba2f38](https://gitlab.hd123.com:20022/vue/soa/commit/2ba2f389f2f34731a24780837e2ffe54a5016ff6))
* 🐛 SOP-7340 超市版兼容.账单单据模块.详情页面点击【立即支付】，页面报错问题 ([fa7f577](https://gitlab.hd123.com:20022/vue/soa/commit/fa7f57744e35c2b06f6c779605a9bdcf3e518e2b))

## [2.15.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.14.6...v2.15.0) (2022-08-01)


### Bug Fixes

* 🐛 SOP-7253 超市门店退货装箱业务.单据搜索列表，商品名称过长时显示不正确 ([60e89ec](https://gitlab.hd123.com:20022/vue/soa/commit/60e89ec137957dd9b51b0cbcc0ace61fd2979801))
* 🐛 SOP-7271 箱码明细页面，退货数量修改弹框问题汇总 ([fefda01](https://gitlab.hd123.com:20022/vue/soa/commit/fefda019a838a6accefce334dbfa1a88df34d342))
* 🐛 SOP-7273 超市门店退货装箱箱码获取调整 ([c03a7aa](https://gitlab.hd123.com:20022/vue/soa/commit/c03a7aac2730000972adb279de6c05c9dd8ec991))
* 🐛 SOP-7274 箱码明细页面，页面中商品删除成功后，箱数会恢复初始状态 ([8054694](https://gitlab.hd123.com:20022/vue/soa/commit/8054694b14315959879472f6a5cb072cca706b31))

### [2.14.6](https://gitlab.hd123.com:20022/vue/soa/compare/v2.14.5...v2.14.6) (2022-07-31)


### Features

* ✨ SOP-6478 小程序.门店助手.商品查询超市版改造（商品搜索页面） ([d85d50e](https://gitlab.hd123.com:20022/vue/soa/commit/d85d50e74beffe1a54b71ff0513962a70f4a9b47))
* ✨ SOP-6478 小程序.门店助手.商品查询超市版改造（商品详情页面） ([5f113d6](https://gitlab.hd123.com:20022/vue/soa/commit/5f113d6ab0eebcaddadf992fea14c34242ad58a3))
* ✨ SOP-6479 超市门店退货装箱业务 ([f012e6f](https://gitlab.hd123.com:20022/vue/soa/commit/f012e6f892bcb53ed976933be854c7cf1580cf3a))
* ✨ SOP-7098 直送退货支持价格改高 ([6699240](https://gitlab.hd123.com:20022/vue/soa/commit/66992406a98711e42c704b0f901763e58e79bed9))
* ✨ SOP-7100 订货支持下拉滚动切换上一分类 ([4d2daa2](https://gitlab.hd123.com:20022/vue/soa/commit/4d2daa29bf1d9de5640e646503c3614debebec25))
* ✨ SOP-7116 修改了质量反馈中申请原因描述的提示文案 ([c0d7bd7](https://gitlab.hd123.com:20022/vue/soa/commit/c0d7bd7b69ef67727cfd7626cfd7965ff0962ab0))
* ✨ SOP-7124 新增重点商品检查类任务 ([c0ea26a](https://gitlab.hd123.com:20022/vue/soa/commit/c0ea26afdbc133475419a60882d1b6f8e0f31ddb))
* ✨ SOP-7221 超市门店退货装箱增加权限和配置 ([76fdf8e](https://gitlab.hd123.com:20022/vue/soa/commit/76fdf8e9b542dae3c556191ac5b79460b66f78f9))
* ✨ SOP-7239 质量反馈增加上传图片上限，无上传视频 ([fe3b45b](https://gitlab.hd123.com:20022/vue/soa/commit/fe3b45b5125145dec4c3a7fab68af668faa7a5ca))
* ✨ SOP-7239 质量反馈增加图片上传数量上限 ([126f4b8](https://gitlab.hd123.com:20022/vue/soa/commit/126f4b8d74580f7ace16898c2ae822332967a188))


### Bug Fixes

* 🐛 退货装箱分配原因 ([61f98fb](https://gitlab.hd123.com:20022/vue/soa/commit/61f98fba04ef195887e544d4db41547dd4f05ba0))
* 🐛 退货装箱箱码获取调整 ([1b5ee48](https://gitlab.hd123.com:20022/vue/soa/commit/1b5ee48526e3300af9af0945151d2c6e53971061))
* 🐛 退货装箱箱码明细 ([36c8dff](https://gitlab.hd123.com:20022/vue/soa/commit/36c8dff557c7190ec4130398e4bcde67be4c9845))
* 🐛 退货装箱箱码明细界面 ([1b35efe](https://gitlab.hd123.com:20022/vue/soa/commit/1b35efe161e08cea8682889afc6c9ba9f9169476))
* 🐛 退货装箱详情 ([6bb217a](https://gitlab.hd123.com:20022/vue/soa/commit/6bb217ad15976b4d33c27ca3310b6f94b453deb6))
* 🐛 修复了父任务反馈图片展示问题 ([cb71577](https://gitlab.hd123.com:20022/vue/soa/commit/cb71577a9b76694ef3ae50188ae845fc1b6f5c6c))
* 🐛 修复了上传图片上限后，提示上传没有隐藏的问题 ([d59e8c0](https://gitlab.hd123.com:20022/vue/soa/commit/d59e8c01ff53cbc5fc8c8d07b8803700b01266ff))
* 🐛 修复了重点商品陈列任务整改任务待评价查看父任务反馈缺少完成反馈的问题 ([132c6f6](https://gitlab.hd123.com:20022/vue/soa/commit/132c6f6968cd295287df5cc7c99d567116589692))
* 🐛 SOP-6479 超市门店退货装箱业务 查看全部页面 ([38933f1](https://gitlab.hd123.com:20022/vue/soa/commit/38933f1eba21ffbbaf0e9dd0b8a5d1d203c51490))
* 🐛 SOP-6479 超市门店退货装箱业务搜索页调整 ([38384bd](https://gitlab.hd123.com:20022/vue/soa/commit/38384bd36f698f1a529a690017b11908ef5fead7))
* 🐛 SOP-7151 获取OSS签名优化使用uni默认toast组件 ([e78f743](https://gitlab.hd123.com:20022/vue/soa/commit/e78f74327c4244f309ae42ace726cdd1e15ac41f))
* 🐛 SOP-7167 修复了图标透明的样式；SOP-7174修复了整改任务单的展示;SOP-7191修复了领取人错误 ([80b6fa3](https://gitlab.hd123.com:20022/vue/soa/commit/80b6fa309e078f84927d5cd9a654cc1c5081272c))
* 🐛 SOP-7187 修复了重点商品查看父任务反馈未展示完成反馈的问题，修复了转交退回后领取人唯一的问题 ([37067a8](https://gitlab.hd123.com:20022/vue/soa/commit/37067a8a640867665882a9ad83048acccd9f623b))
* 🐛 SOP-7223 超市门店退货装箱业务.新增退货装箱页面，页面中退货原因需要必填 ([03f3851](https://gitlab.hd123.com:20022/vue/soa/commit/03f3851acea01966f86be4bb9035965edac55321))
* 🐛 SOP-7228 退货装箱详情-已提交页面，显示问题汇总 ([7ee4931](https://gitlab.hd123.com:20022/vue/soa/commit/7ee4931d3e5c5f2b592baad4e72f6d200176bad8))
* 🐛 SOP-7228、SOP-7238、SOP-7222 ([cbc6694](https://gitlab.hd123.com:20022/vue/soa/commit/cbc669456ede82a147b81fd0efb9bee7299e99a8))
* 🐛 SOP-7240 退货商品列表存在数据时；切换页面业务类型，页面实现问题 ([4a6f3a6](https://gitlab.hd123.com:20022/vue/soa/commit/4a6f3a62f4fe039ac1279d988cd452f76858353d))
* 🐛 SOP-7240 退货装箱.新增页面.页面实现问题 ([9a53d38](https://gitlab.hd123.com:20022/vue/soa/commit/9a53d38f4ea572e5b5444d52e0ee7c1ebcbed03a))
* 🐛 SOP-7241 退货装箱详情页面，商品列表合计显示问题汇总 ([57bb66f](https://gitlab.hd123.com:20022/vue/soa/commit/57bb66f970177db58060c9d32808fd09fd603931))
* 🐛 SOP-7243 退货装箱.新增页面，页面退货原因实现问题 ([4431b4d](https://gitlab.hd123.com:20022/vue/soa/commit/4431b4da7110b6d95d81a595841f96083ad7328b))
* 🐛 SOP-7246 退货商品列表与箱码；列表显示问题 ([d150794](https://gitlab.hd123.com:20022/vue/soa/commit/d150794ecad495ed2561db72ff52f16bbee2f37a))
* 🐛 SOP-7248 门店助手增加历史评价 ([f529286](https://gitlab.hd123.com:20022/vue/soa/commit/f5292869b8fcfb01de5cc3f00745c2f80507be87))
* 🐛 SOP-7250 箱码明细页面，无法修改退货数量 ([6ee2641](https://gitlab.hd123.com:20022/vue/soa/commit/6ee2641889477b9848bcdfd109e8a935b014c625))
* 🐛 SOP-7251 超市门店退货装箱业务.退货装箱详情页面，商品列表显示问题汇总 ([d4a80f8](https://gitlab.hd123.com:20022/vue/soa/commit/d4a80f80e630531270bc7392e1b7c093aae598e1))
* 🐛 SOP-7253 门店退货装箱业务.单据搜索列表，商品名称过长时显示不正确 ([966c131](https://gitlab.hd123.com:20022/vue/soa/commit/966c1315327d26bf6cf8b527beab662ec12791fe))
* 🐛 SOP-7254 退货装箱.新增页面.接口加载中，页面仓位显示不正确 ([251f428](https://gitlab.hd123.com:20022/vue/soa/commit/251f428cca0d9e47f84ad01881bb640d54d3fb0a))
* 🐛 SOP-7255 退货装箱.商品搜索页面，商品“当前仓位库存”字段显示问题 ([e7885d9](https://gitlab.hd123.com:20022/vue/soa/commit/e7885d9ba38d8500c96742d80fb7dad356334386))
* 🐛 SOP-7260 加工模块乱码问题 ([7c27e55](https://gitlab.hd123.com:20022/vue/soa/commit/7c27e55a2eee93dd93c75f53ba226d8b277402d7))

### [2.14.5](https://gitlab.hd123.com:20022/vue/soa/compare/v2.14.4...v2.14.5) (2022-07-23)


### Bug Fixes

* 🐛 SOP-7151 获取OSS签名优化 ([92bf8b7](https://gitlab.hd123.com:20022/vue/soa/commit/92bf8b7c05d34449d97bde81a8ddf78695fd5f3a))
* 🐛 SOP-7151 获取OSS签名优化 ([4defa32](https://gitlab.hd123.com:20022/vue/soa/commit/4defa32db586f04c597b3581aadc4120e85dfcd2))
* 🐛 SOP-7158 首页查询组织变更结果微信平台兼容自定义tabbar ([26bdf0c](https://gitlab.hd123.com:20022/vue/soa/commit/26bdf0c4e63125d1b470713925d2688e4f5a3875))

### [2.14.4](https://gitlab.hd123.com:20022/vue/soa/compare/v2.14.3...v2.14.4) (2022-07-19)

### [2.14.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.14.0...v2.14.3) (2022-07-18)


### Features

* ✨ SOP-7104 微信端增加自定义tabbar支持将数据模块调整至其他分包 ([2c82ff5](https://gitlab.hd123.com:20022/vue/soa/commit/2c82ff50c15740b5b31c15dbcaab48df36723104))


### Bug Fixes

* 🐛 SOP-7023 修复任务查询页面引入组件名为带扩展名的问题 ([50aad4f](https://gitlab.hd123.com:20022/vue/soa/commit/50aad4ffa12e6327d3db5547bf2e0af720dc9634))
* 🐛 SOP-7104 Tabbar组件支持设置徽标 ([575a867](https://gitlab.hd123.com:20022/vue/soa/commit/575a8674d267fbe1d466d8412b1fad26040cf18d))
* 🐛 SOP-7117 Tabbar组件修复重新进入小程序后无法定位到对应页面的问题 ([93dfdc2](https://gitlab.hd123.com:20022/vue/soa/commit/93dfdc2ba700ef5747385bbeb288b14d5350d92e))

## [2.14.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.13.0...v2.14.0) (2022-07-15)


### Features

* ✨ SOP-6479 超市门店退货装箱业务 ([d8bd2e7](https://gitlab.hd123.com:20022/vue/soa/commit/d8bd2e7d5d5edf482b0d4ce60e755b86ad2dad59))
* ✨ SOP-6973 水印颜色调整为较浅 ([3e2e855](https://gitlab.hd123.com:20022/vue/soa/commit/3e2e855dc986953bdf19043d5ebd9b54cf9dd40a))
* ✨ SOP-6973 支持显示门店信息水印 ([aece7be](https://gitlab.hd123.com:20022/vue/soa/commit/aece7be250f897c4a4c3af6c2ca060d634aeb752))
* ✨ SOP-6973 支持显示门店信息水印 ([131916e](https://gitlab.hd123.com:20022/vue/soa/commit/131916e4f28b0e40e1de43cfa46b84c943fdd9cb))
* ✨ SOP-7014 巡检整改任务的完成反馈根据点检项配置决定哪些是必填内容 ([854ec81](https://gitlab.hd123.com:20022/vue/soa/commit/854ec81bd05aa306f0ba7fcc00c953a5504c9ecb))
* ✨ SOP-7019 门店费用代扣优化 ([136ed30](https://gitlab.hd123.com:20022/vue/soa/commit/136ed30fd0dfc6ee0c26daa5b5d9d77b1b97c2c8))


### Bug Fixes

* 🐛 sop-6971 出货备注取note ([8c1ce9c](https://gitlab.hd123.com:20022/vue/soa/commit/8c1ce9ce65e3bab15f2a6f3f7fecf9f79a7cdb36))
* 🐛 SOP-6971 两个备注在界面上下合并显示 ([6d51878](https://gitlab.hd123.com:20022/vue/soa/commit/6d5187886fde2da539ac8df414a50420608372d6))
* 🐛 SOP-7080 独立订货loading优化 ([e08f2f5](https://gitlab.hd123.com:20022/vue/soa/commit/e08f2f52047e78f3c92b27bfbbf09fcf942a5a03))

## [2.13.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.12.0...v2.13.0) (2022-07-06)


### Features

* ✨ 首页增加用户组织校验，变动则引导退出登录 ([c1acdfd](https://gitlab.hd123.com:20022/vue/soa/commit/c1acdfdc74d59e7ca3855522986c47e667ba07b1))
* ✨ SOP-6147 新增账单管理模块 ([55970d5](https://gitlab.hd123.com:20022/vue/soa/commit/55970d5773c4f89f045c6ffbef0e76ad833b2f50))
* ✨ SOP-6502 增加线路调整模块 ([90752d2](https://gitlab.hd123.com:20022/vue/soa/commit/90752d21f13022e816ea7113d1b44a9355c31da2))
* ✨ SOP-6770 退货业务支持录入商品的生产日期判断是否允许退货 ([78104e6](https://gitlab.hd123.com:20022/vue/soa/commit/78104e6e0e31807e35ea8765263c605240ee5359))
* ✨ SOP-6776 新增店务和任务巡检模块兼容app的逻辑 ([5a2d80b](https://gitlab.hd123.com:20022/vue/soa/commit/5a2d80b95c2a97d365df82eba1a8e6ab091fb76a))
* ✨ SOP-6874 门店助手新增待评价功能相关改造 ([d631bb8](https://gitlab.hd123.com:20022/vue/soa/commit/d631bb8d9fc88f40357bf87f2a03b6d5e868034f))
* ✨ SOP-6874 门店助手新增待评价任务模块接收人展示详情问题修复 ([fd53536](https://gitlab.hd123.com:20022/vue/soa/commit/fd5353633a982050cbac791a6d7cf39d402eec85))
* ✨ SOP-6874 详情页按钮权限调整 ([c602b1c](https://gitlab.hd123.com:20022/vue/soa/commit/c602b1c2b18301740b291d1021f1492057494cfe))
* ✨ SOP-6889 新增不适用功能中整改任务查看父任务的详情不适用不属实图片修改 ([13d4696](https://gitlab.hd123.com:20022/vue/soa/commit/13d469672da0be6e970e67196f233b99e822d698))
* ✨ SOP-6889 新增点检项任务处理评价的是否使用是否属实功能 ([61ae0d3](https://gitlab.hd123.com:20022/vue/soa/commit/61ae0d3b0f76c92e7e009b816dfc9aed519e9f6a))
* ✨ SOP-6891 门店提交点检项提交评价逻辑优化 ([830d45b](https://gitlab.hd123.com:20022/vue/soa/commit/830d45bb5135d0641c86265f2a7723c7e57576ef))
* ✨ SOP-6971 收货支持查看商品备注 ([bc61b8e](https://gitlab.hd123.com:20022/vue/soa/commit/bc61b8e3c950a6101089942744e049f39eedbc77))
* ✨ SOP-7001 质量反馈历史反馈拒绝原因与备注显示逻辑调整 ([4fcb1a5](https://gitlab.hd123.com:20022/vue/soa/commit/4fcb1a5dac1b536f4e9e89f1ed9ff68b40a157b7))


### Bug Fixes

* 🐛 线路调整门店按组织过滤 ([7cc6efd](https://gitlab.hd123.com:20022/vue/soa/commit/7cc6efd5ebc022c81a3624b4b614d88c5007b151))
* 🐛 修复了点击查看点检项小项选择时层级问题 ([62887ca](https://gitlab.hd123.com:20022/vue/soa/commit/62887cab591478288b74035ca31c54a8504848ee))
* 🐛 SOP-6863 标准订货显示商品规格数前端取整 ([8e54efe](https://gitlab.hd123.com:20022/vue/soa/commit/8e54efe4b8037e65d68338b8e660d95b446b8286))
* 🐛 SOP-6874 修复了门店助手详情页退回按钮展示的条件还有列表的气泡问题 ([f2bd112](https://gitlab.hd123.com:20022/vue/soa/commit/f2bd1124a4533d978ede63863838e1b0111868d5))
* 🐛 SOP-6874 修复了派发岗位引入的门店助手不同任务类型待评价详情页提交等问题 ([2ff5a48](https://gitlab.hd123.com:20022/vue/soa/commit/2ff5a48befae3d8d4277f0fb99fe4a42d2938577))
* 🐛 SOP-6874 修复了退回领取按钮的样式不生效问题 ([e4df387](https://gitlab.hd123.com:20022/vue/soa/commit/e4df387739f0652a498d52db6cda4f7a4e1a130e))
* 🐛 SOP-6874 修复了转交人在待领取和待处理不同情况的过滤 ([e2702a5](https://gitlab.hd123.com:20022/vue/soa/commit/e2702a510023dc4c3439827b2343f78e40e50f59))
* 🐛 SOP-6874 转交人选择中选择岗位后再次选择岗位，默认选中上次选择的岗位 ([d06fdaf](https://gitlab.hd123.com:20022/vue/soa/commit/d06fdaf183423990d96f86e8cd678ff20f0bdd73))
* 🐛 SOP-6891 修复了提交提示出来是toast仍然弹出的问题 ([f3f73e7](https://gitlab.hd123.com:20022/vue/soa/commit/f3f73e72aca3e4bd29fe6d804194d0665a118c41))
* 🐛 SOP-6896 退仓模块增加运费信息的计算、展示 ([d2faaa4](https://gitlab.hd123.com:20022/vue/soa/commit/d2faaa418ee2a1eaebf2961c7af2217e2db61c7b))
* 🐛 SOP-6899 标准订货切换下一类别优化 ([f598e4e](https://gitlab.hd123.com:20022/vue/soa/commit/f598e4eb6b6e1e870301ffa3f67f93525f0e3b07))
* 🐛 SOP-6901 退货业务支持录入商品的生产日期判断是否允许退货.返回商品页面，生产日期/到效日期需要保留 ([df375fa](https://gitlab.hd123.com:20022/vue/soa/commit/df375fa204d59f7b2898c4818d1bef276a2b9bec))
* 🐛 SOP-6908 退货业务支持录入商品的生产日期判断是否允许退货.编辑退货申请.返回商品列表页面，退货方式会显示 ([29f2c71](https://gitlab.hd123.com:20022/vue/soa/commit/29f2c710f914ee6ac9be906a6d232fd48c80caaa))
* 🐛 SOP-6949 线路调整-移动门店提示框和添加门店修改下路提示框样式与UI不符 ([3eb6bd2](https://gitlab.hd123.com:20022/vue/soa/commit/3eb6bd2ce581964f4b9e0ea3d22c080a60ef0b55))
* 🐛 SOP-6950 线路调整-门店只有一条线路，只展示【移动】按钮，实际展示了移动和删除 ([fa92370](https://gitlab.hd123.com:20022/vue/soa/commit/fa92370149203c58f17d54361f922b8f086d6fb9))
* 🐛 SOP-6958、SOP-6960 修复了转交后接收人展示问题和非店长看不到转交按钮的问题 ([5bcec0e](https://gitlab.hd123.com:20022/vue/soa/commit/5bcec0e366ce78b89537740cf7641e65378ea034))
* 🐛 SOP-6975 修复了待评价执行人不正确的问题 ([7d12c2f](https://gitlab.hd123.com:20022/vue/soa/commit/7d12c2f2b2b11a8810ce3d82e3782d900d39b67f))
* 🐛 SOP-6997 修复了巡检整改单待处理中仍有是否适用选项的问题 ([a259727](https://gitlab.hd123.com:20022/vue/soa/commit/a259727fe4589fca9e3807560c7c9dc6956499c4))

## [2.12.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.11.2...v2.12.0) (2022-06-17)


### Features

* ✨ SOP-6250 订货在线支付增加运费信息的计算、展示 ([2cc3335](https://gitlab.hd123.com:20022/vue/soa/commit/2cc3335395935100d05760471bef13b23a4b1c04))
* ✨ SOP-6863 标准订货显示商品规格数 ([af085e3](https://gitlab.hd123.com:20022/vue/soa/commit/af085e30817e356acafca013774d508b6bac78f2))


### Bug Fixes

* 🐛 订货运费取值错误 ([819a3ed](https://gitlab.hd123.com:20022/vue/soa/commit/819a3ed05eb70807b274a011f3e87c9d8594cf7f))
* 🐛 SOP-6834 直送收货和直送退货模块.配置"useGift": "false"时，页面优化 ([9d6baf0](https://gitlab.hd123.com:20022/vue/soa/commit/9d6baf0368a302a75317acff5e6e5c6c70101c17))
* 🐛 SOP-6849 退仓模块.详情页面.运费展示问题 ([7ff2a0b](https://gitlab.hd123.com:20022/vue/soa/commit/7ff2a0b2a38e5197f1c308f6907e1aa5fb821c6a))
* 🐛 SOP-6860 修复了任务中心领取任务前清空列表，领取任务失败后没有重新获取列表的问题 ([6047222](https://gitlab.hd123.com:20022/vue/soa/commit/60472223461bb1d8ad5f8b9f3c0c1ec115f8592c))

### [2.11.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.11.1...v2.11.2) (2022-06-15)


### Features

* ✨ SOP-6772 收货与收货明细界面展示效期 ([82f8e03](https://gitlab.hd123.com:20022/vue/soa/commit/82f8e03c271004f225192d8471be6e7bf5d9bf10))
* ✨ SOP-6774 针对捷强客户增加隐藏部分功能的逻辑 ([c8afb64](https://gitlab.hd123.com:20022/vue/soa/commit/c8afb64db9f65314b0d5c65cd2f364a5abedf084))

### [2.11.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.11.0...v2.11.1) (2022-06-10)


### Bug Fixes

* 🐛 订货再来一单草稿id错误 ([871f898](https://gitlab.hd123.com:20022/vue/soa/commit/871f898a46d246c1f0b3b1aedd06e4757b40f637))
* 🐛 SOP-6736 商品效期数据刷新 ([6e5329f](https://gitlab.hd123.com:20022/vue/soa/commit/6e5329f08b716d5d33e348c52877f6de64509363))
* 🐛 SOP-6744 门店叫货优化.商品列表，存在商品价格变化，问题汇总 ([2c0c922](https://gitlab.hd123.com:20022/vue/soa/commit/2c0c92282423c6f29d017472aa364454e1b05092))
* 🐛 SOP-6764 效期订货.库存不足弹窗，修改商品加购数量，/order/draft/save接口传参问题 ([59652a9](https://gitlab.hd123.com:20022/vue/soa/commit/59652a9d3f3c9384e01556bb98468ac52807e188))
* 🐛 SOP-6767 门店叫货优化.购物车页面，商品数量编辑为0，问题汇总 ([754c9e0](https://gitlab.hd123.com:20022/vue/soa/commit/754c9e09eae9c63deb0314a40f29caa62fe6006b))
* 🐛 SOP-6779 效期订货.效期库存提示弹窗问题汇总 ([063b9ed](https://gitlab.hd123.com:20022/vue/soa/commit/063b9ed94f29f10848ac45bfafb9bc92ee0f3618))
* 🐛 SOP-6780 小程序.门店助手.编辑页面.分类列表页面加购商品数据展示问题 ([330ae8d](https://gitlab.hd123.com:20022/vue/soa/commit/330ae8d3977253c29ad1b21fab53e9d9abe24957))
* 🐛 SOP-6783 门店叫货优化.分类商品列表页面，分类获取接口返回为空/失败，页面显示不正确 ([023da16](https://gitlab.hd123.com:20022/vue/soa/commit/023da16e9c62f18d2703dd16180086f97cf5c193))
* 🐛 SOP-6784 分类点击显示问题 ([c637c37](https://gitlab.hd123.com:20022/vue/soa/commit/c637c3737731d9a15f575594470e78df0b2fbfe2))
* 🐛 SOP-6784 门店叫货优化.分类商品列表页面，切换分类过程中，不允许再点击其他分类 ([43c6a33](https://gitlab.hd123.com:20022/vue/soa/commit/43c6a33b8e34248b7d0b89b2f975cfa8584a0836))
* 🐛 SOP-6786 效期订货.编辑页面进入购物车；购物车页面相关问题 ([26f1840](https://gitlab.hd123.com:20022/vue/soa/commit/26f184079aa4ee959f13290b69fa1af607f0f418))
* 🐛 SOP-6786 效期订货.编辑页面进入购物车；购物车页面相关问题 ([046318b](https://gitlab.hd123.com:20022/vue/soa/commit/046318b68540dd9feafe0893f742f27ad751e462))
* 🐛 SOP-6786 效期订货.编辑页面进入购物车；购物车页面相关问题 ([2d6f715](https://gitlab.hd123.com:20022/vue/soa/commit/2d6f715034016e4f625db358fdf27562f2301157))
* 🐛 SOP-6787 门店叫货优化.分类商品列表页面，存在商品编辑下切换分类，调用save接口时页面没有显示load ([fabda56](https://gitlab.hd123.com:20022/vue/soa/commit/fabda56e0a158bef3edf29c72c114143f64bdfda))
* 🐛 SOP-6788 门店叫货优化.分类商品列表页面，分页切换查看列表问题汇总 ([d7cb31c](https://gitlab.hd123.com:20022/vue/soa/commit/d7cb31c906dd78507928942521d544835d199b36))
* 🐛 SOP-6790 效期订货.再来一单.单据中存在的已加购商品.数量相关问题 ([2032493](https://gitlab.hd123.com:20022/vue/soa/commit/20324935e7c61b01e927f07021f7dfc6c4b2f6c0))
* 🐛 SOP-6798 门店叫货优化.商品列表返回分类列表，需要调用接口更新分类角标 ([9964b81](https://gitlab.hd123.com:20022/vue/soa/commit/9964b816bc39e0a53b4d905ba9602d04f0d7cdba))
* 🐛 SOP-6798 门店叫货优化.商品列表返回分类列表，需要调用接口更新分类角标 ([0fc4d83](https://gitlab.hd123.com:20022/vue/soa/commit/0fc4d8382d9cd824b69ce62216dd9cc3a1a78d67))

## [2.11.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.10.0...v2.11.0) (2022-06-02)


### Features

* ✨ 修改了新增标价签申请界面扫码进入搜索商品时的传参获取、搜索框文案修改 ([cf899e9](https://gitlab.hd123.com:20022/vue/soa/commit/cf899e9977d6194d0e41105adaffb3c7a4cc523f))
* ✨ cli 升级3.4.7 ([a6b1fb0](https://gitlab.hd123.com:20022/vue/soa/commit/a6b1fb002ab8152b14cf8ceee7225743d97f6cf9))
* ✨ SOP-6251 订货界面展示优化 ([482ce17](https://gitlab.hd123.com:20022/vue/soa/commit/482ce17d2497d2448ae09741ac4e0f2fa3ecbcae))
* ✨ SOP-6500 公司间退货单和配货出货退货单显示退款标识 ([2762465](https://gitlab.hd123.com:20022/vue/soa/commit/27624651a3deb0bf1e3dada1aca36247ecae5ee3))
* ✨ SOP-6522 超市版增加激活码逻辑 ([372cd01](https://gitlab.hd123.com:20022/vue/soa/commit/372cd01ce9090f0810b2732089948176c58a7ede))
* ✨ SOP-6522 超市版增加激活码逻辑 ([9fc1bbb](https://gitlab.hd123.com:20022/vue/soa/commit/9fc1bbb17d8768dcd95ed7ee524bb43219a79e6b))
* ✨ SOP-6522 超市版增加激活码逻辑 -todo ([f9d4e5d](https://gitlab.hd123.com:20022/vue/soa/commit/f9d4e5d6af23eaa85719ac494bfb440b3398c6d2))
* ✨ SOP-6525 标价签申请 ([3aa28e6](https://gitlab.hd123.com:20022/vue/soa/commit/3aa28e66840d7bb940958dd8d3e1b1417a512d99))
* ✨ SOP-6525 标价签申请编辑界面刷新时机修改 ([6891f0e](https://gitlab.hd123.com:20022/vue/soa/commit/6891f0ec6aefac0e7f15f5e6d5395e13a1c323bc))
* ✨ SOP-6525 标价签申请方法备注 ([be6a402](https://gitlab.hd123.com:20022/vue/soa/commit/be6a4024300ed95cf58c93a1616de2eff7c242da))
* ✨ SOP-6525 标价签申请改动 ([e78de01](https://gitlab.hd123.com:20022/vue/soa/commit/e78de017f8482ad67990577c4193cbd2b6efbb71))
* ✨ SOP-6525 标价签申请列表增加分页器 ([18fe816](https://gitlab.hd123.com:20022/vue/soa/commit/18fe816e159842c8b9af24aeeef4b3d7b369f5a3))
* ✨ SOP-6525 标价签申请商品数量展示调整 ([0fcba4f](https://gitlab.hd123.com:20022/vue/soa/commit/0fcba4f8fe4b0efc1071cdaa90fcb42b6db9fdf2))
* ✨ SOP-6525 标价签申请添加权限定义 ([ee77338](https://gitlab.hd123.com:20022/vue/soa/commit/ee77338f893f43b7012d38ff77979d0a82808e57))
* ✨ SOP-6525 标价签申请新增等权限添加 ([56cdc98](https://gitlab.hd123.com:20022/vue/soa/commit/56cdc98fc5772f482f3413c30bcb5f4d2a0fa96b))
* ✨ SOP-6525 标价签申请新增商品搜索页面 ([104e25f](https://gitlab.hd123.com:20022/vue/soa/commit/104e25f221cc643cf4585b65605d2f7d342ae233))
* ✨ SOP-6525 标价签申请样式修改，部分方法名备注 ([f508545](https://gitlab.hd123.com:20022/vue/soa/commit/f50854537bada655175d191f6f38a5099c311a56))
* ✨ SOP-6525 价签申请按钮权限添加 ([ef6c870](https://gitlab.hd123.com:20022/vue/soa/commit/ef6c87050f0da645fccba50efe81ceb2cb992dff))
* ✨ SOP-6525 价签申请按钮权限修改 ([7a41906](https://gitlab.hd123.com:20022/vue/soa/commit/7a419069c992930e729d4441da319fe3eb204633))
* ✨ SOP-6525 新增标价签叫货模块 ([9e65da0](https://gitlab.hd123.com:20022/vue/soa/commit/9e65da0cfe01582da9aedfc1995415648b2988d8))
* ✨ SOP-6525 新增标价签叫货模块 ([ecc6d99](https://gitlab.hd123.com:20022/vue/soa/commit/ecc6d9970f8d3d42621d9bfb417a77159e4e883a))
* ✨ SOP-6525 新增标价签申请模块修改 ([046c563](https://gitlab.hd123.com:20022/vue/soa/commit/046c56336a7e52f439a02d211d750065e38ce968))
* ✨ SOP-6680 门店效期订货走草稿模式 ([8f117fc](https://gitlab.hd123.com:20022/vue/soa/commit/8f117fcfae8a392f6f73bc1604b3f2316aa3eff2))
* ✨ SOP-6689 订货模块.可订货金额点击热区扩大优化 ([0fe4452](https://gitlab.hd123.com:20022/vue/soa/commit/0fe445212db4fd0d3cbab7802b4dd244f692b25f))
* ✨ SOP-6710 激活码页面样式优化 ([f64b8db](https://gitlab.hd123.com:20022/vue/soa/commit/f64b8dba24d584dc8ef314c855e8913881242edc))


### Bug Fixes

* 🐛 当从编辑或新建的单据提交后的详情页返回，需要多返回一级 ([e184276](https://gitlab.hd123.com:20022/vue/soa/commit/e184276597fa4166798e98d9281d7af4186b4273))
* 🐛 订货点击步进器时切换了下一分类 ([69f3bb4](https://gitlab.hd123.com:20022/vue/soa/commit/69f3bb4eb6a47527bc7a17831193673da8a39a85))
* 🐛 二次确认是否删除商品行时，操作后需要重新获取草稿内容 ([8bf3821](https://gitlab.hd123.com:20022/vue/soa/commit/8bf38210b88e917e365a2e5877e96c3ee3063e2f))
* 🐛 添加标价签申请编辑权限 ([7688595](https://gitlab.hd123.com:20022/vue/soa/commit/7688595c60baa0fc09fdbf4f52147c8d4de35d8d))
* 🐛 SOP-6251 订货无商品时也支持切换分类 ([647e8e6](https://gitlab.hd123.com:20022/vue/soa/commit/647e8e6353836f1260283388ef3597abc9ecdf4b))
* 🐛 SOP-6576 购物车页面，删除商品后获取列表接口传参问题汇总 ([6385f59](https://gitlab.hd123.com:20022/vue/soa/commit/6385f5931fcae89cfe0bb1ffdcb457de6c632913))
* 🐛 SOP-6576 购物车页面，删除商品后获取列表接口传参问题汇总 ([e6a5842](https://gitlab.hd123.com:20022/vue/soa/commit/e6a5842b02b55c3d76558ea26033c1500fd2834f))
* 🐛 SOP-6590 门店叫货.草稿模式兼容再来一单 ([f752ea3](https://gitlab.hd123.com:20022/vue/soa/commit/f752ea3dfaa71b41abcfa204efb3a8ff9803d112))
* 🐛 SOP-6592 订货优化.初始进入页面显示loading问题汇总 ([b7fee9e](https://gitlab.hd123.com:20022/vue/soa/commit/b7fee9e5c8f1aeab29cafa94a0d331c1e99d20f8))
* 🐛 SOP-6592 门店叫货加盟订货优化.初始进入页面显示loading问题汇总 ([6b8315c](https://gitlab.hd123.com:20022/vue/soa/commit/6b8315c2b8f3f3f81e2e7f8fd74393fab7010b67))
* 🐛 SOP-6606 建议叫货，页面中保存按钮在某种情况下显示不正确 ([c86e67e](https://gitlab.hd123.com:20022/vue/soa/commit/c86e67e2d7cd8e1e32c914e1f0cac84353abfeaa))
* 🐛 SOP-6618 门店叫货加盟订货优化.商品列表查询加载实现优化 ([e7c9abf](https://gitlab.hd123.com:20022/vue/soa/commit/e7c9abf63611c23f55a5c373bd7c4a99a188f847))
* 🐛 SOP-6641 加盟订货优化.分类商品列表，未存在商品数量编辑，切换分类存在问题 ([66a5c66](https://gitlab.hd123.com:20022/vue/soa/commit/66a5c66d0093970041e1b9dc9e4bc94cd3ae5492))
* 🐛 SOP-6645 门店叫货优化.提交订货，接口调用和页面显示问题 ([1b44404](https://gitlab.hd123.com:20022/vue/soa/commit/1b4440439f6dcb70b055479487658796884b9f7d))
* 🐛 SOP-6646 门店叫货优化.非订货时间内，点击弹框中【保存并返回】按钮，接口调用和页面显示问题 ([e94d05b](https://gitlab.hd123.com:20022/vue/soa/commit/e94d05b0bf02e4d71856e9d5dbbd1c17ea52ba56))
* 🐛 SOP-6649 门店叫货加盟订货优化.编辑单据页面，保存提示优化 ([2d062e4](https://gitlab.hd123.com:20022/vue/soa/commit/2d062e4d8c51811f3b7065246d83c1a424cc1515))
* 🐛 SOP-6651 建议叫货，商品列表删除商品，问题汇总 ([aa068a3](https://gitlab.hd123.com:20022/vue/soa/commit/aa068a3d498526a1fb8213063a7a263ed044fbdf))
* 🐛 SOP-6652 建议叫货，商品列表中非自主添加的商品，不允许删除 ([84ebf05](https://gitlab.hd123.com:20022/vue/soa/commit/84ebf0516c349114638c51b29f03a8ccc1c66752))
* 🐛 SOP-6652、SOP-6653 建议叫货bug修复 ([b37edf8](https://gitlab.hd123.com:20022/vue/soa/commit/b37edf869c34664e09d38bb3950005d9b0f28511))
* 🐛 SOP-6655 建议叫货，商品列表中非自主添加的商品，商品数量为0后save接口传参错误 ([73beff1](https://gitlab.hd123.com:20022/vue/soa/commit/73beff1131ff1912449257a01cc7e22d8366a8b8))
* 🐛 SOP-6656 建议叫货，点击保存按钮，页面显示问题汇总 ([8efa729](https://gitlab.hd123.com:20022/vue/soa/commit/8efa7297d0be4fe4f999cb1fc851213d51982d08))
* 🐛 SOP-6660 订货优化.页面点击返回按钮，弹框问题显示汇总 ([7c68bc4](https://gitlab.hd123.com:20022/vue/soa/commit/7c68bc4c7cd07723a4778b43eae8d0e5b2d178ed))
* 🐛 SOP-6663 建议叫货，点击备注弹框中提交按钮，问题汇总 ([5258210](https://gitlab.hd123.com:20022/vue/soa/commit/52582103bd84b1537d8b9313c9cf4736585082de))
* 🐛 SOP-6666 门店叫货加盟订货优化.新增正常订货，页面报“未指定草稿标识” ([43b3963](https://gitlab.hd123.com:20022/vue/soa/commit/43b39634909f6c355707f481ef8e3783210d0949))
* 🐛 SOP-6667 门店叫货加盟订货优化.商品列表查询加载问题汇总 ([e4cf573](https://gitlab.hd123.com:20022/vue/soa/commit/e4cf573a3201ab9893428d3ce134134ec6f1de9d))
* 🐛 SOP-6681 建议叫货，点击页面返回按钮，问题汇总 ([418deea](https://gitlab.hd123.com:20022/vue/soa/commit/418deeaa033ce859836844593b0673acd6cbe16c))
* 🐛 SOP-6686 门店叫货优化.购物车页面，调用save接口时页面显示问题 ([f9c83ef](https://gitlab.hd123.com:20022/vue/soa/commit/f9c83ef22f8f6c611bd71b6ba8db9e3d22f36738))
* 🐛 SOP-6690 门店叫货优化.提交可订数不足时接口调用，页面显示存在问题 ([ecaf787](https://gitlab.hd123.com:20022/vue/soa/commit/ecaf787cb5a2bf42621e106c996eb6cc8a3b2c44))
* 🐛 SOP-6691 门店叫货优化.点击可订数不足弹框的关闭按钮，返回页面，商品数据显示不正确 ([ecb5aac](https://gitlab.hd123.com:20022/vue/soa/commit/ecb5aacd51efd0f59d328e6d4dbaf804b31a7a43))
* 🐛 SOP-6694 门店叫货加盟订货优化.购物车页面，向上加载商品列表时，期望页面显示loading ([f5a4d6b](https://gitlab.hd123.com:20022/vue/soa/commit/f5a4d6b12080ed44739af824c98996d924171a97))
* 🐛 SOP-6697 门店叫货优化.点击可订数不足弹框的关闭按钮，返回页面合计金额显示不正确 ([2f9e933](https://gitlab.hd123.com:20022/vue/soa/commit/2f9e933a20c6eda23d5f0529f142e5555f63c7f2))
* 🐛 SOP-6698 门店叫货优化.存在商品可订数变为0，页面问题汇总 ([bd88498](https://gitlab.hd123.com:20022/vue/soa/commit/bd88498a02880f699866ec3f866db01ee945600a))
* 🐛 SOP-6701 ([f6939dc](https://gitlab.hd123.com:20022/vue/soa/commit/f6939dc2273036a6cd1f7967e5b934c5f07a4a40))
* 🐛 SOP-6705 门店叫货优化.商品搜索页面点击提交按钮，存在商品下架，商品搜索列表未更新成功 ([3f1eaaf](https://gitlab.hd123.com:20022/vue/soa/commit/3f1eaaf9364d051022ed255efc4e44c70e0cf923))
* 🐛 SOP-6708 购物车页面，商品步进器按钮问题 ([7d338c5](https://gitlab.hd123.com:20022/vue/soa/commit/7d338c53b38ebfb1cd3ff3ad0416b051baa1f1a8))
* 🐛 SOP-6711 购物车页面，新增的时候点击【保存/提交】按钮，调用的接口不正确 ([5c891aa](https://gitlab.hd123.com:20022/vue/soa/commit/5c891aab9dd9725581b4cab9bd0cf8cabe4e7a4c))
* 🐛 SOP-6715 门店叫货优化.商品搜索页面，存在下架商品，弹出的弹框中订货品项数和订货金额不正确 ([c9d675f](https://gitlab.hd123.com:20022/vue/soa/commit/c9d675fffc91da350a02df94e144e412336dc4f9))
* 🐛 SOP-6719 修复绑定失效后未跳转回激活页面的问题 ([f98e3c5](https://gitlab.hd123.com:20022/vue/soa/commit/f98e3c576bb9a08ab12e81e665f5c8e72bc615a5))
* 🐛 SOP-6719 修复绑定失效后未跳转回激活页面的问题 ([c512935](https://gitlab.hd123.com:20022/vue/soa/commit/c512935eccc218a2eb446e639fcabce49ab83570))
* 🐛 SOP-6719 修复跳转到激活码页面展示modal的问题 ([d600b99](https://gitlab.hd123.com:20022/vue/soa/commit/d600b99b1b3cfc7ebf36ba260c4a74086237ea66))
* 🐛 SOP-6725 效期订货.商品搜索页面.商品效期行步进器+/-按钮问题 ([a85e8b6](https://gitlab.hd123.com:20022/vue/soa/commit/a85e8b680919d5b0a67eb16737fb52d8b68132c6))
* 🐛 SOP-6728 门店叫货优化.购物车页面，余额不足时返回购物车页面，页面中一直显示loading ([7ef5219](https://gitlab.hd123.com:20022/vue/soa/commit/7ef52192bec5bea4ca1f9eba5898631c9a2676ab))
* 🐛 SOP-6731 门店叫货优化.购物车页面，弹出的可订数不足弹框显示不正确 ([347e60f](https://gitlab.hd123.com:20022/vue/soa/commit/347e60f1972f9eab96db5db97966b6f65cb78c25))
* 🐛 SOP-6736 效期订货.商品效期库存不足点击【提交】按钮，页面没有弹出效期库存不足提示弹窗 ([5d76057](https://gitlab.hd123.com:20022/vue/soa/commit/5d76057f4919a2eaccf26b9812ce7913a114bcb4))
* 🐛 SOP-6744 门店叫货优化.商品列表，存在商品价格变化，问题汇总 ([0032851](https://gitlab.hd123.com:20022/vue/soa/commit/00328518a9102d15f06d0455c9f1009b7d192ec9))
* 🐛 SOP-6751 门店效期订货.效期商品调用/draft/line/remove接口成功后，页面步进器显示问题 ([68e99c0](https://gitlab.hd123.com:20022/vue/soa/commit/68e99c088fcd2c6951dd11ebe684c7a93eaddffe))
* 🐛 SOP-6753 门店效期订货.订货页面底部加购商品金额合计问题 ([f1be3ac](https://gitlab.hd123.com:20022/vue/soa/commit/f1be3ac334cc77e0311c6127ab55b659b9ea22fc))
* 🐛 SOP-6754 门店叫货优化.购物车页面，分类过多页面显示不正确 ([3019fc3](https://gitlab.hd123.com:20022/vue/soa/commit/3019fc3c73bd988b28882f7ee46b7283788d4489))
* 🐛 SOP-6756 商品选择的标价签类型不在该商品类别中提交不能成功，添加提示 ([7c0c01a](https://gitlab.hd123.com:20022/vue/soa/commit/7c0c01afaf3cc226ca262d18ae25c3433681847e))

## [2.10.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.9.2...v2.10.0) (2022-05-20)


### Features

* ✨ SOP-6177 门店标准订货走草稿模式 ([5b22f4d](https://gitlab.hd123.com:20022/vue/soa/commit/5b22f4d7a80cde50ec898827efd6486aa3b13e92))
* ✨ SOP-6397 质量反馈支持审批功能 ([8b6c36d](https://gitlab.hd123.com:20022/vue/soa/commit/8b6c36dffc2409d1b972810cbffc8794b972c20d))
* ✨ SOP-6476 叫货界面金额旁边显示本单总件数 ([71d7f57](https://gitlab.hd123.com:20022/vue/soa/commit/71d7f57504a6f49b89a269ed284e718e3c1eeaa4))
* ✨ SOP-6521 超市版兼容app ([a4c3bb0](https://gitlab.hd123.com:20022/vue/soa/commit/a4c3bb09635cceb0708add003e08628a490e6b5c))
* ✨ SOP-6521 超市版兼容app ([fab6f68](https://gitlab.hd123.com:20022/vue/soa/commit/fab6f681bad0390bf987342f582a3fcc16747647))
* ✨ SOP-6521 兼容app改造 ([f15fb08](https://gitlab.hd123.com:20022/vue/soa/commit/f15fb08cbe30d08ffa0dc13c6361d6c1ee0a8a92))


### Bug Fixes

* 🐛 SOP-6473 购物车页面，没有分类的商品未显示 ([fb513fb](https://gitlab.hd123.com:20022/vue/soa/commit/fb513fbf3c48dbbcb4281ef18ef9935703d80fa2))
* 🐛 SOP-6546 加盟订货优化.购物车页面，删除商品列表显示优化 ([cb7dbcd](https://gitlab.hd123.com:20022/vue/soa/commit/cb7dbcdb880adc58ac56f80273299cd2385f2a57))
* 🐛 SOP-6556 门店叫货加盟订货优化.购物车页面，分页器显示问题汇总 ([0939c44](https://gitlab.hd123.com:20022/vue/soa/commit/0939c4426bcbb6553aabe1649dce2cfccd0e8e39))
* 🐛 SOP-6561 商品删除remove接口调用问题汇总 ([5bce522](https://gitlab.hd123.com:20022/vue/soa/commit/5bce522b6a5a0a3c2bfcfab4d6b196ebfd2157d6))
* 🐛 SOP-6564 门店叫货加盟订货优化.点击提交按钮，接口调用问题汇总 ([30b4633](https://gitlab.hd123.com:20022/vue/soa/commit/30b4633e37f6e1f567f638bc06aa8438908fb0b8))
* 🐛 SOP-6565 库存不足弹框中，商品全部删除成功，应该不能提交成功 ([cdd5754](https://gitlab.hd123.com:20022/vue/soa/commit/cdd57549f68f42387e67d195681e0178f40fdcbb))
* 🐛 SOP-6566 购物车保存单据，保存接口传参错误 ([60f21e9](https://gitlab.hd123.com:20022/vue/soa/commit/60f21e9c7f9f427379a002125963db8144d83178))
* 🐛 SOP-6578 商品搜索页面，点击取消按钮，调用的save接口传参错误 ([21716f9](https://gitlab.hd123.com:20022/vue/soa/commit/21716f90f672b5d71c293490e6971a5a3fe5944e))
* 🐛 SOP-6582 订货购物车逻辑调整 ([fda0471](https://gitlab.hd123.com:20022/vue/soa/commit/fda047159ae65f52f756ac859559f189c4afcf9d))
* 🐛 SOP-6594 质量反馈.时间选择器在某种情况下，会报错 ([691da13](https://gitlab.hd123.com:20022/vue/soa/commit/691da1387b7bd07a64c59bc6d73bf94f242092b9))
* 🐛 SOP-6608 建议叫货，分类商品列表，勾选商品行，save接口传参错误 ([4922622](https://gitlab.hd123.com:20022/vue/soa/commit/4922622d884ca1c6b4fac39596c2e2dcbee90296))
* 🐛 SOP-6609 添加商品页面返回商品列表页面，商品列表页面未刷新 ([3064d03](https://gitlab.hd123.com:20022/vue/soa/commit/3064d031bc8e77dcff7d36a67b9c077b588e28db))
* 🐛 SOP-6631 门店叫货优化.提交订货，提交接口报错（传参错 ([3e1a63e](https://gitlab.hd123.com:20022/vue/soa/commit/3e1a63e8319d1c4b5346455663ab0a9e0d4f5557))

### [2.9.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.9.1...v2.9.2) (2022-05-12)


### Features

* ✨ SOP-6424 门店叫货加盟订货走草稿模式 ([d922781](https://gitlab.hd123.com:20022/vue/soa/commit/d9227815f551e485b141abf52a69f66f62e6c363))


### Bug Fixes

* 🐛 食惠邦订货传参调整 ([64bda14](https://gitlab.hd123.com:20022/vue/soa/commit/64bda141170b5a276b1d4214e61672add4bd42da))
* 🐛 SOP-6489、SOP-6490 ([1bbb2fa](https://gitlab.hd123.com:20022/vue/soa/commit/1bbb2fa5e412d43f92f8c630e68c8a2ffd136528))
* 🐛 SOP-6506 食惠邦订货界面库存不足优化 ([182570f](https://gitlab.hd123.com:20022/vue/soa/commit/182570f3a1b168a170f84a2b0fb2dc3ad74d66ce))
* 🐛 SOP-6510 商品启用效期，单所有效期均没有库存时，效期内容显示优化 ([f2cac86](https://gitlab.hd123.com:20022/vue/soa/commit/f2cac8698650a9ec6eff80c4402f1bcb1d56893c))
* 🐛 SOP-6539 门店叫货加盟订货优化.商品搜索列表返回分类列表，分页器显示错误 ([a78844a](https://gitlab.hd123.com:20022/vue/soa/commit/a78844a1d09ea9a1756e4ba70b72f2dd9fc00e94))
* 🐛 SOP-6543 门店叫货加盟订货优化.商品搜索列表，点击【取消】按钮实现错误 ([afa7585](https://gitlab.hd123.com:20022/vue/soa/commit/afa75854b9b3241292ca88092f246acc456de716))

### [2.9.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.9.0...v2.9.1) (2022-05-06)


### Features

* ✨ SOP-6245 食惠邦门店订货流程优化 ([3c9815b](https://gitlab.hd123.com:20022/vue/soa/commit/3c9815b06ce843e33317b5f143aab6110765e695))


### Bug Fixes

* 🐛 SOP-6483 新增订货页面.存在效期库存的商品，添加问题 ([9107bed](https://gitlab.hd123.com:20022/vue/soa/commit/9107bed72c1197451ae5f88f5fbce4a680b8bbe4))
* 🐛 SOP-6485 食惠邦订货.商品效期内容展示问题 ([9716eb8](https://gitlab.hd123.com:20022/vue/soa/commit/9716eb8b455596ea3bdb81683d66182491f603b5))

## [2.9.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.8.0...v2.9.0) (2022-04-29)


### Features

* ✨ SOP-6396 质量反馈功能优化 ([a91f0e2](https://gitlab.hd123.com:20022/vue/soa/commit/a91f0e2b965f920880b7598f43ad36a5e0ad1edf))
* ✨ SOP-6424 门店叫货加盟订货优化 ([091fe8b](https://gitlab.hd123.com:20022/vue/soa/commit/091fe8b85f3901d7ac1a9278131ae6f48c1ab46e))
* ✨ SOP-6429 盘点优化 ([f019eab](https://gitlab.hd123.com:20022/vue/soa/commit/f019eabee55c30ba9cb91c7610f7e7313d388bb1))


### Bug Fixes

* 🐛 标准订货可订数全部为0时回显错误 ([e448ced](https://gitlab.hd123.com:20022/vue/soa/commit/e448ced04df68adb9e223a7b9d90cc2134ede981))
* 🐛 独立订货库存不足时页面信息更新 ([c9b9ad2](https://gitlab.hd123.com:20022/vue/soa/commit/c9b9ad27ca03dfa38a2e6dac2dafcf63e850b061))
* 🐛 修复标准订货可订数为0时影响提交 ([21c52a9](https://gitlab.hd123.com:20022/vue/soa/commit/21c52a9bcddbb68e551126d242900564b3865826))
* 🐛 SOP-6310 任务中心.搜索页面【领取】任务报错问题 ([7969c56](https://gitlab.hd123.com:20022/vue/soa/commit/7969c56335698f95268259b73283acdb284d1ddb))
* 🐛 SOP-6431 营销玩法促销价玩法修复滑动删除商品下标错误 ([61ea2f9](https://gitlab.hd123.com:20022/vue/soa/commit/61ea2f9e1307220e1439e780b633aa08118f01e6))
* 🐛 SOP-6434 选项卡切换时屏蔽上个tab的请求数据 ([175ec66](https://gitlab.hd123.com:20022/vue/soa/commit/175ec6618bc9a109e3af6703c69586781f0aa203))
* 🐛 SOP-6448 阶梯满减没满减商品无法滑动删除修复 ([746d2a0](https://gitlab.hd123.com:20022/vue/soa/commit/746d2a0682b87cca4a68b77788fbc65d8ac13c9d))
* 🐛 SOP-6449 退公司选项卡切换时屏蔽上个tab的请求数据 ([4f3ffbd](https://gitlab.hd123.com:20022/vue/soa/commit/4f3ffbd29956b4e0a43ea48f0bcb7fca49a90cbd))
* 🐛 SOP-6451 门店叫货加盟订货优化.请选择页数弹框，默认定位显示不正确 ([65e003e](https://gitlab.hd123.com:20022/vue/soa/commit/65e003e46e607c9a727465d687ac42fbba303dbd))
* 🐛 SOP-6454 质量反馈.历史反馈记录搜索页面，商品质量反馈信息展示问题 ([3ecdc94](https://gitlab.hd123.com:20022/vue/soa/commit/3ecdc942e092045245983e1471aa86aa49bc232e))
* 🐛 SOP-6456 加盟订货优化.修改商品数量后，合计的商品件数显示不正确 ([9ab690b](https://gitlab.hd123.com:20022/vue/soa/commit/9ab690ba21ec1aaee00a0e61191da5ab902b6387))

## [2.8.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.6...v2.8.0) (2022-04-22)


### Features

* ✨ SOP-6147 新增账单管理模块 ([c15a63a](https://gitlab.hd123.com:20022/vue/soa/commit/c15a63a911a10fb022177e1fed83fcd93bafcfbc))
* ✨ SOP-6244 报损报溢添加图片 ([2ddb117](https://gitlab.hd123.com:20022/vue/soa/commit/2ddb1171df33faae12a1444dc2abe93cb39706c6))
* ✨ SOP-6247 订货叫货，展示加盟资金余额 ([34bcc0b](https://gitlab.hd123.com:20022/vue/soa/commit/34bcc0b47bccb9fd868e5c8f7a8a733c4f89f10a))


### Bug Fixes

* 🐛 SOP-6129 修复订货模块提交按钮是否可以点击判断错误的问题 ([0e1ff48](https://gitlab.hd123.com:20022/vue/soa/commit/0e1ff4863dcb2a2a552c6f911005e10d61914e3e))
* 🐛 SOP-6312 全局配置sysConfig获取时序调整 ([6845e2d](https://gitlab.hd123.com:20022/vue/soa/commit/6845e2d23e7ef9c48247320dc1f40118f479a54e))
* 🐛 SOP-6314 全局页面跳转携带中文时保持不乱码 ([70eda85](https://gitlab.hd123.com:20022/vue/soa/commit/70eda85c4f603acc25cd93be982da2ce1131b354))
* 🐛 SOP-6314 全局页面跳转携带中文时保持不乱码 ([ec4f298](https://gitlab.hd123.com:20022/vue/soa/commit/ec4f2984a2d2b2b5ee9d5a19ad4725d45d57e110))
* 🐛 SOP-6314、SOP-6337 直送收退编辑页面进货金额合计金额与apos不一致 ([05d9187](https://gitlab.hd123.com:20022/vue/soa/commit/05d9187f938191bdfbc79393e5940fef92f2a409))
* 🐛 SOP-6333、SOP-6334报损报溢添加图片问题 ([7978712](https://gitlab.hd123.com:20022/vue/soa/commit/7978712b89cf7c2eb64b753afa7303beb62b7a6d))
* 🐛 SOP-6338 报损报溢添加图片.查看视频优化 ([2165cd8](https://gitlab.hd123.com:20022/vue/soa/commit/2165cd804396af9ea0011c762152b258586a351a))
* 🐛 SOP-6340 报损报溢传参问题 ([81c9037](https://gitlab.hd123.com:20022/vue/soa/commit/81c9037b1ae36e73d9768dfe2be8bc4fdc18ddc3))
* 🐛 SOP-6346 报损报溢添加图片.单据详情页面，图片实现不正确 ([f4ae376](https://gitlab.hd123.com:20022/vue/soa/commit/f4ae376819aa46423fc0055a081f1daa76efe494))
* 🐛 SOP-6360 叫货单展示商品件数和商品规格数.单据列表合计需要修改 ([b72fd5d](https://gitlab.hd123.com:20022/vue/soa/commit/b72fd5da4bec2506c60357b5cb2cf7aa0be7f194))
* 🐛 SOP-6363 订货展示加盟资金余额.新增/编辑订货页面，页面展示的可订货金额与UI不符 ([c2c184c](https://gitlab.hd123.com:20022/vue/soa/commit/c2c184ca2ba14fa1428785535c8e0cea9028dbd2))
* 🐛 SOP-6370、SOP-6371 ([5f0c2ea](https://gitlab.hd123.com:20022/vue/soa/commit/5f0c2ea57e790db667954af5d86e371d90f40b56))

### [2.4.6](https://gitlab.hd123.com:20022/vue/soa/compare/v2.7.0...v2.4.6) (2022-04-14)


### Bug Fixes

* 🐛 SOP-6312 全局配置sysConfig获取时序调整 ([739ebdc](https://gitlab.hd123.com:20022/vue/soa/commit/739ebdc15f60ad7171f0ed351adeb0b3394f9a8a))

## [2.7.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.6.1...v2.7.0) (2022-04-13)


### Features

* ✨ SOP-6038 门店叫货单保存时控制叫货包装数量为整数 ([4e96444](https://gitlab.hd123.com:20022/vue/soa/commit/4e96444b9691b2c64fc30de006b5227f04b725c7))
* ✨ SOP-6060 新增调查类任务 ([ff6683b](https://gitlab.hd123.com:20022/vue/soa/commit/ff6683b1222de749976b4e72b60f54c96ea80776))
* ✨ SOP-6060 新增调查类任务 ([607618f](https://gitlab.hd123.com:20022/vue/soa/commit/607618f9d09ed4ae858764a954a95a656be110c9))
* ✨ SOP-6060 新增调查类任务 ([1be050c](https://gitlab.hd123.com:20022/vue/soa/commit/1be050cda42bd98b1ed02a509cde18b5b458aa87))
* ✨ SOP-6060 新增调查类任务 ([83b7abd](https://gitlab.hd123.com:20022/vue/soa/commit/83b7abdcb14b2bbbf618d7f00caf4e4bad5926a6))
* ✨ SOP-6148 新增订单查询模块 ([7dd5ec8](https://gitlab.hd123.com:20022/vue/soa/commit/7dd5ec89712864b8b1721a1dffa2816156557a93))
* ✨ SOP-6226 调查类任务详情页面相关问题 ([f17728e](https://gitlab.hd123.com:20022/vue/soa/commit/f17728e3124cc37b27d4d80c1290380bcd97e0dd))


### Bug Fixes

* 🐛 调整任务【待处理】状态到【待评价】状态，反馈信息消失问题 ([cecdb26](https://gitlab.hd123.com:20022/vue/soa/commit/cecdb26e0ab8249e209d219858c153d5c280a7e3))
* 🐛 SOP-6193 标准订货模块.商品数量框输入数据时.页面提示问题 ([7850d90](https://gitlab.hd123.com:20022/vue/soa/commit/7850d900527256b5c41e24c4d3434120cb43cf0e))
* 🐛 SOP-6204 订单查询页面基础样式问题汇总 ([ccc6db0](https://gitlab.hd123.com:20022/vue/soa/commit/ccc6db067c72fe71b595c9e466a9514f959929c6))
* 🐛 SOP-6205 订单查询模块.订单状态选择框实现优化 ([71420db](https://gitlab.hd123.com:20022/vue/soa/commit/71420dbc5ffbdbba465b329f3f123f8bfef10da8))
* 🐛 SOP-6209 订单查询模块.订单列表问题优化 ([1961734](https://gitlab.hd123.com:20022/vue/soa/commit/1961734d5cd7dd0fe0b8521257db81f8d3f7b41e))
* 🐛 SOP-6218 订单查询模块.商品清单页面.商品查询传参优化 ([4bd6ccd](https://gitlab.hd123.com:20022/vue/soa/commit/4bd6ccda0650f0fd21919f8923a4b23241833ffd))
* 🐛 SOP-6240 调查任务.任务列表按钮显示问题 ([a6a35f4](https://gitlab.hd123.com:20022/vue/soa/commit/a6a35f45a37abb6ce83cfadbfe661a6883373687))
* 🐛 SOP-6240 调查任务.任务列表按钮显示问题 ([46b914f](https://gitlab.hd123.com:20022/vue/soa/commit/46b914f1b0e6d3c0c7c2200b40a6f88fd2f72a38))
* 🐛 SOP-6240 调查任务.任务列表按钮显示问题 ([6643975](https://gitlab.hd123.com:20022/vue/soa/commit/66439758b1b22c6730e040a2fe204ee67b691a57))
* 🐛 SOP-6264 门店助手图片上传优化 ([1b856a2](https://gitlab.hd123.com:20022/vue/soa/commit/1b856a2a7653cf8bb3350cd242c9e7182933e6b4))
* 🐛 SOP-6266 调查类任务.【提交】按钮逻辑问题 ([fc368d6](https://gitlab.hd123.com:20022/vue/soa/commit/fc368d6d0b618df59103536422af463758865e1e))
* 🐛 SOP-6268 调查类任务.【待复评】状态的任务详情页面展示问题 ([1f1e198](https://gitlab.hd123.com:20022/vue/soa/commit/1f1e19842d25e3b81c5eda6fce1ca4468bede161))

### [2.6.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.6.0...v2.6.1) (2022-03-30)


### Bug Fixes

* 🐛 修复ShopSkuEdit页面CSS语法错误导致微信编译报错 ([6d28506](https://gitlab.hd123.com:20022/vue/soa/commit/6d28506b0b3fcedb6e8fa9d5a1b12924110ad4ca))

## [2.6.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.5.2...v2.6.0) (2022-03-28)


### Features

* ✨ SOP-5910 后端适配后，输入框展示权限控制还原 ([622e0d3](https://gitlab.hd123.com:20022/vue/soa/commit/622e0d38813986cb80164fa0f0c5933119dcfd46))
* ✨ SOP-6035 自定义价格带和促销标签编辑页和详情页 ([eae1dee](https://gitlab.hd123.com:20022/vue/soa/commit/eae1dee3e302996ac4bc7bd1ad14ffc9a288e677))
* ✨ SOP-6035 自定义价格带和促销标签初始页和搜索页 ([fecee94](https://gitlab.hd123.com:20022/vue/soa/commit/fecee949721de8c63c7d64687216f8863730acbf))
* ✨ SOP-6056 任务列表增加父子任务关联展示 ([f0d7ef2](https://gitlab.hd123.com:20022/vue/soa/commit/f0d7ef2f8059a967a1cc15a1d7e106ff8e084f11))
* ✨ SOP-6061 畅销品缺货订货适配任务，尚未适配ERP交货单 ([c660ad5](https://gitlab.hd123.com:20022/vue/soa/commit/c660ad59b3e8d677bc2b28c0eaca109285248c04))
* ✨ SOP-6061 接通ERP叫货后前端调整订货模块跳转 ([f2ed10e](https://gitlab.hd123.com:20022/vue/soa/commit/f2ed10eb6574675fed0c83e9dfa19f3443f402ab))
* ✨ SOP-6118 订货权限控制优化 ([06ef20b](https://gitlab.hd123.com:20022/vue/soa/commit/06ef20b652ab17bde19d0bd3c3440afafe79f8d5))
* ✨ SOP-6118 订货权限控制优化 ([d10b13e](https://gitlab.hd123.com:20022/vue/soa/commit/d10b13ebf503c95da0d744b5fdfd5cbfc2bedf7e))
* ✨ SOP-6118 订货权限控制优化 ([3f36d3f](https://gitlab.hd123.com:20022/vue/soa/commit/3f36d3fa40ff10df32ac69f6e180de1adb05d514))
* ✨ SOP-6118 订货权限控制优化 ([7eccb84](https://gitlab.hd123.com:20022/vue/soa/commit/7eccb84358ff99238d2d397d527aa51fda238d64))
* ✨ SOP-6139 畅销品适配执行人接受人改动后转交按钮展示逻辑优化 ([be6aa51](https://gitlab.hd123.com:20022/vue/soa/commit/be6aa5126f3094c7d91bf5b0c0683f4c9edd17ba))
* ✨ SOP-6141 多店长需求 ([516b979](https://gitlab.hd123.com:20022/vue/soa/commit/516b9794f7d14771a2b0b7078b9c82dfd58b8e79))


### Bug Fixes

* 🐛 价格带获取标签接口修改 ([0aaffcd](https://gitlab.hd123.com:20022/vue/soa/commit/0aaffcdd75623222f00a882827a2373db4c2c83a))
* 🐛 SOP-6127 列表关联任务，点击未跳转至详情页 ([21d3a55](https://gitlab.hd123.com:20022/vue/soa/commit/21d3a555f58b408e67d9686cc44f790811f84f99))
* 🐛 SOP-6156 多店长需求.转交后页面按钮显示不正确 ([f5b5b5f](https://gitlab.hd123.com:20022/vue/soa/commit/f5b5b5f2059c5a6f7df8597b826b5c6332fb18a1))

### [2.5.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.5...v2.5.2) (2022-03-18)


### Features

* ✨ 待办功能支持店务模块 ([b47513a](https://gitlab.hd123.com:20022/vue/soa/commit/b47513ac32ec18f8041ff6f518a62f7b05f9cade))

### [2.4.5](https://gitlab.hd123.com:20022/vue/soa/compare/v2.5.0...v2.4.5) (2022-03-17)


### Features

* ✨ SOP-5621、SOP-5494我的待办改造 ([7ef6988](https://gitlab.hd123.com:20022/vue/soa/commit/7ef69888a984eb02e304c68d49ebf27760cbedc4))


### Bug Fixes

* 🐛 修复标准订货备注有默认值 ([e6255a8](https://gitlab.hd123.com:20022/vue/soa/commit/e6255a8a425d637ec25b6318fe94b7e10fe20334))
* 🐛 SOP-6082 店务待办界面问题修复 ([dd058a7](https://gitlab.hd123.com:20022/vue/soa/commit/dd058a722c1555aa2516818c871cbaa2d72d0995))
* 🐛 SOP-6082 店务待办任务tag展示问题修复 ([3afc889](https://gitlab.hd123.com:20022/vue/soa/commit/3afc889564060c1f307539b8627676992e8c7ae5))
* 🐛 SOP-6082 界面展示问题修复 ([3c2cb9c](https://gitlab.hd123.com:20022/vue/soa/commit/3c2cb9c10cf63fba144134329fb6186b4b4b8dcb))

## [2.5.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.4...v2.5.0) (2022-03-11)


### Features

* ✨ SOP-5603 价签调整接口联调 ([8d6d55b](https://gitlab.hd123.com:20022/vue/soa/commit/8d6d55b079886a84b33727d78c809489d2e4e0ef))
* ✨ SOP-5823 营销玩法.选择商品部分逻辑优化 ([e8962e4](https://gitlab.hd123.com:20022/vue/soa/commit/e8962e40fda4a8e0d017829022760f79b53bc45a))
* ✨ SOP-5867 标价签接口联调修改 ([84f0b54](https://gitlab.hd123.com:20022/vue/soa/commit/84f0b5439a25e21fa5bb1bec22b7b4d301ff4cb0))
* ✨ SOP-5867 价签调整任务真机调试接口问题 ([2ae2402](https://gitlab.hd123.com:20022/vue/soa/commit/2ae2402a4666e44d577e07dc903338df1abfe52e))
* ✨ SOP-5888 盘点数量输入框数字键盘弹出数字键盘 ([bc0ad0e](https://gitlab.hd123.com:20022/vue/soa/commit/bc0ad0e0714ab4fb728f9dfc1e9529fced16d7ff))
* ✨ SOP-5909 点检类任务提交图片、价签任务完成反馈图片提交增加水印 ([f9c000a](https://gitlab.hd123.com:20022/vue/soa/commit/f9c000aed66741c619df8c1c9cb902216ceb6656))
* ✨ SOP-5910 文字必填与非必填样式修改 ([dadeedf](https://gitlab.hd123.com:20022/vue/soa/commit/dadeedf4408ac800110cbd8675060e12277a3353))
* ✨ SOP-5911 任务支持多次转交 ([1ce70d5](https://gitlab.hd123.com:20022/vue/soa/commit/1ce70d5683a671bbf68f1b9366263ac90a91dc83))
* ✨ SOP-5912 支持任务级别控制是否可以从相册中选取视频/图片 ([9ef9649](https://gitlab.hd123.com:20022/vue/soa/commit/9ef9649d3aa27a59eb49f83ef9e2264c8215d9ba))
* ✨ SOP-5977 支持单独配置直配收货不允许多收 ([ea28f46](https://gitlab.hd123.com:20022/vue/soa/commit/ea28f46cd943ed77b4fab9b2995075096d173b9f))
* ✨ SOP-5977 支持单独配置直配收货不允许多增加收容差弹窗限制 ([652aac0](https://gitlab.hd123.com:20022/vue/soa/commit/652aac049168ac9073a19efb982299e1fa45cf7b))
* ✨ SOP-5978 门店日结增加提醒字样 ([ce37c01](https://gitlab.hd123.com:20022/vue/soa/commit/ce37c01a2056f098c38059a3af5740cac8faa9ea))
* ✨ SOP-5985 不重要请求loading延时200毫秒优化体验 ([039b455](https://gitlab.hd123.com:20022/vue/soa/commit/039b455fb0674e2ad5f30fd4880d5269ee807bed))
* ✨ SOP-5985 Toast和Loading组件替换为fant-mini组件 ([f3af0c4](https://gitlab.hd123.com:20022/vue/soa/commit/f3af0c47a4f2b20e5afb18e1fa0870033bba4b07))


### Bug Fixes

* 🐛 营销玩法促销换购价格换购折扣保存 ([ebd77a4](https://gitlab.hd123.com:20022/vue/soa/commit/ebd77a41f171762b5f1e5de25e32602a466aba29))
* 🐛 增加获取用户岗位信息 ([8128019](https://gitlab.hd123.com:20022/vue/soa/commit/8128019d798c44d5d29bf63f698745956658cc26))
* 🐛 SOP-5911 支持多次转交问题修改 ([fb65400](https://gitlab.hd123.com:20022/vue/soa/commit/fb65400ef58793e3020e48936602e842c4535115))
* 🐛 SOP-5946 直送收货&直送退货.供应商列表显示不正确 ([26d169d](https://gitlab.hd123.com:20022/vue/soa/commit/26d169d9231a1106e61da3b77b4b57539087b124))
* 🐛 SOP-5947 、SOP-5948、SOP-5949 供应商直送/直推bug修复 ([8ad4a7e](https://gitlab.hd123.com:20022/vue/soa/commit/8ad4a7ef930592e25395a8689163d39d46db807e))
* 🐛 SOP-5948 直送收货&直送退货.重新点击供应商弹框数据需要清空 ([32c986d](https://gitlab.hd123.com:20022/vue/soa/commit/32c986d27c48951b41d51fe30bd6db150062885c))
* 🐛 SOP-5956 复盘-抽盘-搜索结果页面商品操作错误 ([942ae6b](https://gitlab.hd123.com:20022/vue/soa/commit/942ae6bb890be90ec2051577f8122953a17d248a))
* 🐛 SOP-5980 文案字数限制问题修改 ([9708ee1](https://gitlab.hd123.com:20022/vue/soa/commit/9708ee188e53d80aba1587ab5e63b3523235a219))
* 🐛 SOP-5980 文字超出限制 ([cdde99f](https://gitlab.hd123.com:20022/vue/soa/commit/cdde99f7447cfb0637a080fa7d8932c1cf4f056c))
* 🐛 SOP-5986 盘点数量编辑框问题汇总 ([174d403](https://gitlab.hd123.com:20022/vue/soa/commit/174d4035baca519541443995b2ac0bfeb052b815))
* 🐛 SOP-5997 价签调整任务显示转交问题修改 ([d86cf23](https://gitlab.hd123.com:20022/vue/soa/commit/d86cf232306f2a1ef346c002adfb8e6a29bc570a))
* 🐛 SOP-6012 门店日结增加提醒字样.当日日结任务完成，切换tab时实现错误 ([30301c9](https://gitlab.hd123.com:20022/vue/soa/commit/30301c92ff9af180e64fed8bc51cdcca98d81586))
* 🐛 SOP-6013 直配收货显示提示语和按钮，在商品搜索下显示不正确 ([773fbc1](https://gitlab.hd123.com:20022/vue/soa/commit/773fbc1ace4f2df8ac640d30e9dcd587770885db))
* 🐛 SOP-6015 Modal组件兼容多次弹出不出现的问题 ([83e50f4](https://gitlab.hd123.com:20022/vue/soa/commit/83e50f41234abf27a30d993fda1d3b5456e1b38d))

### [2.4.4](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.3...v2.4.4) (2022-03-02)


### Bug Fixes

* 🐛 点检项编辑页切换增加防抖 ([23162a7](https://gitlab.hd123.com:20022/vue/soa/commit/23162a7cebdbdaf3e29f03fb05ebc515ecc9ae01))
* 🐛 盘点去掉保存/删除草稿弹窗 ([7490315](https://gitlab.hd123.com:20022/vue/soa/commit/7490315b045fb8285ca172e3a675eed9a0d48cd6))
* 🐛 去掉自采保存提示、修复商品搜索名称不显示 ([be99209](https://gitlab.hd123.com:20022/vue/soa/commit/be992099c11cda09133650ae6bdf1d047f7d172b))
* 🐛 SOP-5887 不存在“开始准备”权限的时候，仍然可以进行此操作 ([b4a4dcf](https://gitlab.hd123.com:20022/vue/soa/commit/b4a4dcfb28c96f8d68f8d07b9f2f04c6ca39d342))

### [2.4.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.2...v2.4.3) (2022-03-02)


### Features

* ✨ SOP-5867 价签任务接口联调修改 ([ffca6e3](https://gitlab.hd123.com:20022/vue/soa/commit/ffca6e35bdc6ac589908701cd561229d83aa0c01))


### Bug Fixes

* 🐛 SOP-5935 首页-大数据接口门店id传参错误，目前为固定传参 ([41bf3f1](https://gitlab.hd123.com:20022/vue/soa/commit/41bf3f184e784a7757ad84dee2fb54af5b526200))

### [2.4.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.1...v2.4.2) (2022-03-02)


### Features

* ✨ feat: ✨ SOP-5867 价签任务接口联调修改 ([7d65011](https://gitlab.hd123.com:20022/vue/soa/commit/7d65011b9f793c7dc906525dd782fa1f1c909af8))


### Bug Fixes

* 🐛 标准订货loading不隐藏问题 ([aa0e337](https://gitlab.hd123.com:20022/vue/soa/commit/aa0e3373396e90fabbe774d89e323f5318c3d6be))
* 🐛 SOP-5913 上传图片、视频时过程中切换上一项，会导致上传失败,增加loading组件 ([50681c9](https://gitlab.hd123.com:20022/vue/soa/commit/50681c9a63ab521a43e6c619d782b80040d9d0a5))

### [2.4.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.0...v2.4.1) (2022-03-02)


### Features

* ✨ SOP-5822、SOP-5884、SOP-5887 盘点模块优化 ([aba4e5a](https://gitlab.hd123.com:20022/vue/soa/commit/aba4e5acfba2c8117638ca0bb7112beed65c7e3f))
* ✨ SOP-5867 价签任务接口联调 ([51ad4a7](https://gitlab.hd123.com:20022/vue/soa/commit/51ad4a79ac07307d15430ee9723cde8c70b5a1ed))
* ✨ SOP-5867 价签任务接口联调 ([f454e60](https://gitlab.hd123.com:20022/vue/soa/commit/f454e60a5e5f9c96e822c27dd843267ee447c3bb))
* ✨ SOP-5883 直送收货和直送退货选择供应商增加筛选条件 ([6378b2f](https://gitlab.hd123.com:20022/vue/soa/commit/6378b2f0f270a39094db11c96f21428429325056))
* ✨ SOP-5931 门店助手分包 ([c631525](https://gitlab.hd123.com:20022/vue/soa/commit/c63152506b8a02f8160833c88f770db1107ffda6))

## [2.4.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.3.4...v2.4.0) (2022-02-25)


### Features

* ✨ SOP-5604订货活动报名优化 ([56bf005](https://gitlab.hd123.com:20022/vue/soa/commit/56bf00503d53a3a9cbfd7fd23a8fa9e2c42e1e3f))
* ✨ SOP-5619质量反馈支持直接添加商品反馈按店务配置 ([0ccd7dc](https://gitlab.hd123.com:20022/vue/soa/commit/0ccd7dc50a8bc9950ee61fba7bac4a65ac4aafe0))
* ✨ SOP-5619质量反馈支持直接添加添加商品反馈 ([dd28dbc](https://gitlab.hd123.com:20022/vue/soa/commit/dd28dbc1afeabd41157034c5870721e33bbcab0d))
* ✨ SOP-5715自采模块商品检索优化 ([a5184bc](https://gitlab.hd123.com:20022/vue/soa/commit/a5184bc107e87e220278893ceca9b1c59ae10a83))
* ✨ SOP-5726自采模块增加商品查询和选择页面 ([d46a006](https://gitlab.hd123.com:20022/vue/soa/commit/d46a0066e7dba05131fad8c08dbb8a909f2ce9cd))
* ✨ SOP-5757营销玩法排除促销品发起促销时生效 ([49d8f4d](https://gitlab.hd123.com:20022/vue/soa/commit/49d8f4d639bcc6757bd6bc160822fcb05f3e5359))
* ✨ SOP-5758 任务模块无权限的处理 ([628d3a6](https://gitlab.hd123.com:20022/vue/soa/commit/628d3a6b34fa9a63934af5ca9dacd6718055389d))
* ✨ SOP-5800 去掉登录岗位判断 ([f5bc63f](https://gitlab.hd123.com:20022/vue/soa/commit/f5bc63f89219a2dab37e6ee60b17d95994f9d66a))
* ✨ SOP-5826 排除商品选择其他商品.其他商品列表展示优化 ([72c8522](https://gitlab.hd123.com:20022/vue/soa/commit/72c85223766c21a6ed602099719f6f9d82efad7e))


### Bug Fixes

* 🐛 SOP-5796 自采页面扫描添加商品返回问题 ([abb408f](https://gitlab.hd123.com:20022/vue/soa/commit/abb408fef72f71899289dcfcb1024e54173d1849))
* 🐛 SOP-5797 门店自采.选择商品页面实现问题 ([3072f5b](https://gitlab.hd123.com:20022/vue/soa/commit/3072f5b80f9c8cf4aa9db87fc6165afa96de2f29))
* 🐛 SOP-5813质量反馈配置传参问题 ([d20c315](https://gitlab.hd123.com:20022/vue/soa/commit/d20c3151c214230abb54a9f6a56853866b608a21))
* 🐛 SOP-5825 普通折扣.点击【保存并生效】按钮传参问题 ([16443f9](https://gitlab.hd123.com:20022/vue/soa/commit/16443f9ecf029b8df9f992d6a29269fa8ffa20c3))
* 🐛 SOP-5833 营销玩法.新增页面排除商品选项显示问题 ([55db8ee](https://gitlab.hd123.com:20022/vue/soa/commit/55db8eefa476a30a96ed8279f42f0fdd11c187e3))
* 🐛 SOP-5835 普通折扣无配置时默认显示优化 ([07bc82e](https://gitlab.hd123.com:20022/vue/soa/commit/07bc82ec0f06372382ef1b66f01a53b965d6ce53))
* 🐛 SOP-5835 营销玩法.普通折扣显示优化 ([f444e1c](https://gitlab.hd123.com:20022/vue/soa/commit/f444e1c94b576ebbda0f2aae66fe3fdd642803d6))
* 🐛 SOP-5835营销玩法.中台关闭supportExcludePrm和supportExcludeSku配置,此 ([ea160cf](https://gitlab.hd123.com:20022/vue/soa/commit/ea160cff97302a9b27bc1ffbd865850fff58c890))
* 🐛 SOP-5861 营销玩法合并到soa ([d47633b](https://gitlab.hd123.com:20022/vue/soa/commit/d47633b2cb045785c420f508ca4e7b50c1c42b16))

### [2.24.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.24.0...v2.24.1) (2022-12-21)


### Bug Fixes

* 🐛 SOP-8378 全部应用.应用过多时会出现重叠现象 ([12ee341](https://gitlab.hd123.com:20022/vue/soa/commit/12ee3418f2a2454ec9d000c6d2cb986bec5a1c9f))
* 🐛 SOP-8386 标准订货：商品列表--商品的日销弹窗显示不友好 ([6a0fdc9](https://gitlab.hd123.com:20022/vue/soa/commit/6a0fdc92a03a78e5ec053a371fecc26d08875122))
* 🐛 SOP-8396 当前商品扫码查询扫描商品条码调用接口是 超市版逻辑 ([29b36dd](https://gitlab.hd123.com:20022/vue/soa/commit/29b36dd483bb84abacfb6c9831108d4dbbf36dd8))

## [2.24.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.23.0...v2.24.0) (2022-12-09)

### Features

- ✨ SOP-8302 门店助手.兼容 PDA ([9cecbfc](https://gitlab.hd123.com:20022/vue/soa/commit/9cecbfc8ebb27bb2fd876be56eb7828325692fc7))
- ✨ SOP-8304 PDA 扫码适配需求 ([c8afaef](https://gitlab.hd123.com:20022/vue/soa/commit/c8afaeff0c69722f0dce4c891ad87701deb5a7a0))
- ✨ SOP-8338 知识库视频按配置不允许拖拽 ([1341ab2](https://gitlab.hd123.com:20022/vue/soa/commit/1341ab2eae00545df5e14bcbca5c93c6025e7837))

### Bug Fixes

- 🐛 SOP-8274 任务中心列表页搜索页卡片显示优化 ([74eac7e](https://gitlab.hd123.com:20022/vue/soa/commit/74eac7e9da9fb2280129e6ec262d560c13629425))
- 🐛 SOP-8317 -质量反馈选择 11 月份没有日期 ([1182b2a](https://gitlab.hd123.com:20022/vue/soa/commit/1182b2ae145d636bf5b56ce2850589662f748e3d))
- 🐛 SOP-8318 两个订货模块，新建订货，添加商品页面，商品名称较长时，展示不友好 ([ac7a3ad](https://gitlab.hd123.com:20022/vue/soa/commit/ac7a3ad1b426684117ea58fc32eed28d83ecd138))
- 🐛 SOP-8334 收货模块和设备认证扫码兼容 PDA ([75cccbc](https://gitlab.hd123.com:20022/vue/soa/commit/75cccbca05bea92fd9a89c80ac5e0d0cf6d7d0da))
- 🐛 SOP-8345 学习中心优化：视频配置不允许拖动，全屏播放视频，进度条可以拖动 ([bc276a2](https://gitlab.hd123.com:20022/vue/soa/commit/bc276a22eddc06febb33bffea37bb20b2fa24a02))
- 🐛 SOP-8349 知识库资料查询，选择一级分类，二级分类选择全部，查不到一级分类下的资料 ([0c392c3](https://gitlab.hd123.com:20022/vue/soa/commit/0c392c3a5ef7acd577573667068928fad218b3f1))

## [2.23.0](https://gitlab.hd123.com/vue/soa/compare/v2.22.3...v2.23.0) (2022-11-25)

### Features

- ✨ SOP-8193 小程序端增加租户管理的逻辑 ([1765e63](https://gitlab.hd123.com/vue/soa/commit/1765e63defcc573e815deb88406f083f8d9d7c3c))

### Bug Fixes

- 🐛 SOP-8188 任务模块，领取任务，提交或评价后，页面未更新 ([aed42ad](https://gitlab.hd123.com/vue/soa/commit/aed42ad7038e4b9ddde18dd648c91ad47a52509e))
- 🐛 SOP-8211 历史评价，跳转页面，数据展示不友好，无法点击 ([0dca03d](https://gitlab.hd123.com/vue/soa/commit/0dca03d3fc960fbe31312124a58564a701e6a22d))

### [2.22.4](https://gitlab.hd123.com/vue/soa/compare/v2.22.3...v2.22.4) (2022-11-25)

### Features

- ✨ SOP-8193 小程序端增加租户管理的逻辑 ([537fe08](https://gitlab.hd123.com/vue/soa/commit/537fe080a13487c42dd8a84e987ed8ee96467506))
- ✨ SOP-8193 APP 端增加租户许可证校验 ([fb3facc](https://gitlab.hd123.com/vue/soa/commit/fb3facc5cb738c35909423126d6b6d6913411b0f))
- ✨ SOP-8266 选择门店增加门店 license 校验 ([bced9bd](https://gitlab.hd123.com/vue/soa/commit/bced9bdf79bb93dbd3825741625fbe8f1fcc929b))

### Bug Fixes

- 🐛 SOP-8193 修复 toast、loading 和 modal 组件在 APP.vue 无法取到路由路径的问题 ([032424f](https://gitlab.hd123.com/vue/soa/commit/032424ff3af5e0f7cbbbbce181e31e99359674c6))

### [2.22.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.22.2...v2.22.3) (2022-11-15)

### Bug Fixes

- 🐛 SOP-8223 账单门店费用代扣逻辑调整 ([d97110e](https://gitlab.hd123.com:20022/vue/soa/commit/d97110e104f7815cd8ceb5f8a2fecc8fa27e7550))

### [2.22.2](https://gitlab.hd123.com/vue/soa/compare/v2.22.1...v2.22.2) (2022-11-14)

### [2.22.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.22.0...v2.22.1) (2022-11-11)

### Bug Fixes

- 🐛 SOP-8206 订货活动.已报名的活动，修改报名数之后，页面的剩余可订数未发生变化 ([8d938fb](https://gitlab.hd123.com:20022/vue/soa/commit/8d938fb3a9ee357f7115539bfe7fa64614f0a2d0))

## [2.22.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.21.1...v2.22.0) (2022-11-11)

### Features

- ✨ SOP-8119 直送收货/退货支持展示供应商代码+名称 ([165c0dc](https://gitlab.hd123.com:20022/vue/soa/commit/165c0dcd52e0969028c51abf961a1d8b9b7f8161))
- ✨ SOP-8120 直送收货商品单价保留 4 位小数 ([0c914c5](https://gitlab.hd123.com:20022/vue/soa/commit/0c914c54a8c7dff54a5e1cf1a27deae65a6640b8))
- ✨ SOP-8121 叫货对接云资金支付优化 ([919efd8](https://gitlab.hd123.com:20022/vue/soa/commit/919efd80bb9cbf0618c6a01c4f39861f8daba116))
- ✨ SOP-8127 门店助手盘点操作优化 ([62af7b1](https://gitlab.hd123.com:20022/vue/soa/commit/62af7b1843f07d043f6ae38919ca151546d4c976))
- ✨ SOP-8128 提成绩效移动端展示 ([3f0372f](https://gitlab.hd123.com:20022/vue/soa/commit/3f0372ffc8c6dbed8e9980aa106ae9ec5e70e35b))

### Bug Fixes

- 🐛 APP 修复清仓促销、组合折扣创建页面输入框占位文字字体过小的问题 ([7d48190](https://gitlab.hd123.com:20022/vue/soa/commit/7d4819082e82ff6628f66104fc1d6a2717e38b9b))
- 🐛 SOP-7583 APP 修复正常订货-日销折线图在 APP 端文字过大的问题 ([347edaf](https://gitlab.hd123.com:20022/vue/soa/commit/347edaf5950a7f94a412462a6060478f139fa8d0))
- 🐛 SOP-8001 UNI 接入 uni 时将获取岗位的接口调整到首页调用 ([d46958a](https://gitlab.hd123.com:20022/vue/soa/commit/d46958a264c4f3ef0c7464283c2d762931eafbb9))
- 🐛 SOP-8127 复盘抽盘不需要显示全选 ([b19614b](https://gitlab.hd123.com:20022/vue/soa/commit/b19614b75c43198a8208b3cae63c99d51109dcc3))
- 🐛 SOP-8163 直送收货/退货支持展示供应商代码+名称.页面显示问题汇总 ([3128fb5](https://gitlab.hd123.com:20022/vue/soa/commit/3128fb50b47415564bb4470412fd56acb8edbb90))
- 🐛 SOP-8182 门店助手盘点操作优化\_问题汇总 ([7de5fac](https://gitlab.hd123.com:20022/vue/soa/commit/7de5fac0daa77c91aa71f145a605d4ca08ef1d6f))
- 🐛 SOP-8182 门店助手盘点全选操作优化 ([c81624c](https://gitlab.hd123.com:20022/vue/soa/commit/c81624ce961e2953412e42ec2aecb29269a53672))

### [2.21.1](https://gitlab.hd123.com/vue/soa/compare/v2.21.0...v2.21.1) (2022-11-01)

### Features

- ✨ SOP-8131 钉钉打包解决重复 key 的报错 ([0700e45](https://gitlab.hd123.com/vue/soa/commit/0700e45b475446c41d6a14833303978bf946fe55))

## [2.21.0](https://gitlab.hd123.com/vue/soa/compare/v2.20.1...v2.21.0) (2022-10-31)

### Features

- ✨ SOP-7327 水印门店信息从显示 id 改为显示 code ([403e555](https://gitlab.hd123.com/vue/soa/commit/403e555a084c5e403584292a82134d9faf5778fc))
- ✨ SOP-7994 SOP、TAS、UAS 接口地址替换为 v2_5 ([b5dd0c5](https://gitlab.hd123.com/vue/soa/commit/b5dd0c5c9f66244c0bd4b987864a6797777d72c2))
- ✨ SOP-8015 调拨申请单增加 deliveryType 字段 ([10a0519](https://gitlab.hd123.com/vue/soa/commit/10a0519f5b5f76c91db56b2bc3f59fc0fcd41eeb))
- ✨ SOP-8090 资金流水详情页面 备注取值从 note 字段调整为 srcNote 字段 ([931b2c5](https://gitlab.hd123.com/vue/soa/commit/931b2c592f7aae154c031c3d84023277dd7cd8c6))

### Bug Fixes

- 🐛 SOP-8041 调拨申请单.配送模式显示优化 ([b163043](https://gitlab.hd123.com/vue/soa/commit/b163043493d36d4c2fd907f4a4dee4f340324e69))

### [2.20.1](https://gitlab.hd123.com/vue/soa/compare/v2.20.0...v2.20.1) (2022-10-17)

### Features

- ✨ SOP-7980 便利版支持取输入框输入租户的逻辑 ([4b77338](https://gitlab.hd123.com/vue/soa/commit/4b77338cc990c875491035466ed5cf1bb9b16875))

## [2.20.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.16.4...v2.20.0) (2022-10-14)

### Features

- ✨ SOP-7552 提供 HD-UNI 小程序编译方案和接入 uni 的方案 ([6c44dae](https://gitlab.hd123.com:20022/vue/soa/commit/6c44dae010c80f613a45877801ac8cab05c8467c))
- ✨ SOP-7552 支持与 UNI 主应用通信 ([65e05c9](https://gitlab.hd123.com:20022/vue/soa/commit/65e05c9dd096ffc04b5098b600e7a8f5bec79ad9))
- ✨ SOP-7742 优化盘点模块在 APP 端的表现 ([15db6a8](https://gitlab.hd123.com:20022/vue/soa/commit/15db6a8c53c6131a43198c1d5f04d24c8f1ee287))

### Bug Fixes

- 🐛 将价签调整任务显示价格改为调整后价格 ([f993468](https://gitlab.hd123.com:20022/vue/soa/commit/f993468ff99e220708b53eb9f12556ca79fc1ad9))
- 🐛 SOP-7054 修复整单满赠折扣为 0 时没有校验数据合法性的问题 ([a2fc340](https://gitlab.hd123.com:20022/vue/soa/commit/a2fc34004fe0dc16956324b008f04c70593f28b8))
- 🐛 SOP-7483 修复满额换购与单品换购 换购商品数值统计错误的问题 ([b51e7a2](https://gitlab.hd123.com:20022/vue/soa/commit/b51e7a272fdadb933ccdc1be2d4bf551c1e670ef))
- 🐛 SOP-7543 线路调整-下拉日期选择框，toast 提示结束日期不能早于开始日期/所选日期不能晚于当天 显示了 ([d4fb696](https://gitlab.hd123.com:20022/vue/soa/commit/d4fb6961e3691bc0024a7dd29fd507c12a65781d))
- 🐛 SOP-7545 门店助手 APP：线路调整-下拉日期选择框，重置后再点击门店，门店消失 ([8403a1a](https://gitlab.hd123.com:20022/vue/soa/commit/8403a1ae551e507c931328a64d9aee51ffa587ef))
- 🐛 SOP-7547 线路调整-订单详情-分类名称过长显示不友好 ([b7ff409](https://gitlab.hd123.com:20022/vue/soa/commit/b7ff409ba61cca2fe6b467cc63aae065a5aba921))
- 🐛 SOP-7553 小程序&APP.标准订货/独立订货，数量编辑存在问题 ([3cea1fc](https://gitlab.hd123.com:20022/vue/soa/commit/3cea1fc32a55d1d72f000ca790a82a824d0b6c65))
- 🐛 SOP-7555 页面编辑了数量点击左上角返回没有二次确认提示框 ([36171ed](https://gitlab.hd123.com:20022/vue/soa/commit/36171ed00606b1da32039de925bbd9f34bc302ed))
- 🐛 SOP-7556 进入订货模块，温馨提示滑动速度过快 ([39d4caa](https://gitlab.hd123.com:20022/vue/soa/commit/39d4caa73b2a4528720abae7e1552f914932ff9e))
- 🐛 SOP-7565 叫货建议单搜索页面，删除商品列表刷新，接口数据正确，前端显示重复 ([ec91e64](https://gitlab.hd123.com:20022/vue/soa/commit/ec91e64b43fd459c2c8d38d38b2958f85256422b))
- 🐛 SOP-7570、SOP-7558 ([3c39eeb](https://gitlab.hd123.com:20022/vue/soa/commit/3c39eebbdd78fb55766db668a71cd217fff81943))
- 🐛 SOP-7735 门店助手 APP：盘点模块，设置复盘商品，弹框显示异常问题 ([b2d6dad](https://gitlab.hd123.com:20022/vue/soa/commit/b2d6dad2daddc0b7ad6cf539ead1b097ed2093f2))
- 🐛 SOP-7735 盘点模块，设置复盘商品，弹框显示异常问题 ([61a968a](https://gitlab.hd123.com:20022/vue/soa/commit/61a968afe4d151ebd3e591ca063a42eb5dc19043))
- 🐛 SOP-7827 盘点选择商品页面商品滚动区域高度调整 ([6c87e4c](https://gitlab.hd123.com:20022/vue/soa/commit/6c87e4cb9f21a7257bf523feb38a36243e67e00a))
- 🐛 SOP-7827 APP 修复设置复盘商品时一级分类滚动范围小的问题 ([ee14172](https://gitlab.hd123.com:20022/vue/soa/commit/ee141725be9c61f144a3a376323a7d580d07953f))
- 🐛 SOP-7835 修复 app 端台账任务历史评价内容展示不全的问题 ([3c9d554](https://gitlab.hd123.com:20022/vue/soa/commit/3c9d5543ac8263d5607f85d0edc362d10d77f130))
- 🐛 SOP-7836 修复台账任务查看父反馈内容超出没有滚动的问题 ([368f0dd](https://gitlab.hd123.com:20022/vue/soa/commit/368f0dd7632b245b86956fb433534e72f2e15596))
- 🐛 SOP-7837 修复 APP 端任务详情选择图片触发 onShow 时更新任务详情数据导致填写内容消失的问题 ([74d1363](https://gitlab.hd123.com:20022/vue/soa/commit/74d1363033774b611209d6da8be548e16275613d))
- 🐛 SOP-7837 APP 修复台账任务详情编辑时无法添加图片的问题 ([fdd439b](https://gitlab.hd123.com:20022/vue/soa/commit/fdd439bfa885907f0822ddbad10654af88ae8a52))
- 🐛 SOP-7846 修复资料模块搜索无空状态及文件调整引起的分包问题 ([f7f602e](https://gitlab.hd123.com:20022/vue/soa/commit/f7f602ecfe819d0f29be209fcb2d49cd5c330822))
- 🐛 SOP-7847、SOP-7849 ([f4f498c](https://gitlab.hd123.com:20022/vue/soa/commit/f4f498c56b0c96d63b2def9b73ca7c20dc1935cd))
- 🐛 SOP-7853、SOP-7854 ([8374be0](https://gitlab.hd123.com:20022/vue/soa/commit/8374be04a5c1536e2396e1b895d425f800fcc106))
- 🐛 SOP-7890 直送收退货货保存后未提交再编辑时无法修改价格 ([2a708fb](https://gitlab.hd123.com:20022/vue/soa/commit/2a708fb884bdde5e2aa933fbb9708dea75f375bf))
- 🐛 SOP-7920 账户流水，筛选弹窗点击金额输入框，弹出的键盘将“重置”“确定”按钮遮盖了 ([a2a87e2](https://gitlab.hd123.com:20022/vue/soa/commit/a2a87e2d2e5e3541e73b394155b909b151e776e7))
- 🐛 SOP-7924 任务详情页有多个图片时，点击非第一张图片，展示的都是第一张图片 ([a358abd](https://gitlab.hd123.com:20022/vue/soa/commit/a358abd40046804edf9cd3f3dd4402767aac1507))
- 🐛 SOP-7924 任务详情页有多个图片时，点击非第一张图片，展示的都是第一张图片 ([fa7ee59](https://gitlab.hd123.com:20022/vue/soa/commit/fa7ee5999cd8ec2612cba9a44837b92866551680))

## [2.19.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.18.2...v2.19.0) (2022-09-23)

### Features

- ✨ SOP-7771 新增知识库模块 ([c613456](https://gitlab.hd123.com:20022/vue/soa/commit/c6134564de8edfae0f1a789a5ec9cad0790d0439))

### [2.18.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.18.1...v2.18.2) (2022-09-16)

### Bug Fixes

- 🐛 SOP-7701 修复了任务完成清空后 tab 徽标还存在的问题 ([822942a](https://gitlab.hd123.com:20022/vue/soa/commit/822942aa9bac74c89a57e3451fab5d3f4519bb4e))

### [2.18.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.18.0...v2.18.1) (2022-09-13)

## [2.18.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.17.0...v2.18.0) (2022-09-13)

### Features

- ✨ SOP-7418 台账任务反馈评价改造 ([80a135c](https://gitlab.hd123.com:20022/vue/soa/commit/80a135c4aea3338662b4436e43082dfa040ee47d))
- ✨ SOP-7552 UNI-PORTAL 传入参数结构调整 ([ef48e3d](https://gitlab.hd123.com:20022/vue/soa/commit/ef48e3db67ba6f01ba9c502e316cc637c0f6b475))
- ✨ SOP-7604 退货通知数量交互优化 ([4f683fe](https://gitlab.hd123.com:20022/vue/soa/commit/4f683fe2e7d2bad033b75b1de198872fa5fb9bb2))

### Bug Fixes

- 🐛 修复了不合格 tag 展示靠后的问题 ([a6e3074](https://gitlab.hd123.com:20022/vue/soa/commit/a6e30740276762858b064dcc25e2e758289ee1f7))
- 🐛 修复了单模板台账未显示历史评价的问题 ([732dd7a](https://gitlab.hd123.com:20022/vue/soa/commit/732dd7a9b61f7d8697c435e688a8316966802907))
- 🐛 SOP-7526 修复了整改任务没有 tag 的问题 ([a127635](https://gitlab.hd123.com:20022/vue/soa/commit/a1276351e74eeffe3d3e214385cc7caab19d5c29))
- 🐛 SOP-7526 SOP-7519 SOP-7517 SOP-7484 台账问题修复 ([8ab7658](https://gitlab.hd123.com:20022/vue/soa/commit/8ab7658d82bd90a2531e217819b3b0ec0b5e5fdc))
- 🐛 SOP-7534 修复在接入 UNI 时仍检查 APP 更新的问题 ([0e2b4b4](https://gitlab.hd123.com:20022/vue/soa/commit/0e2b4b4cfaaca6fde86ea48d774ccc56b366c698))
- 🐛 SOP-7544 线路调整门店搜索按 name 搜索 ([f1006df](https://gitlab.hd123.com:20022/vue/soa/commit/f1006df72d2b6c94d23df1276af98ac6ea456757))
- 🐛 SOP-7562 修复了模板名称过长时不合格样式展示的问题 ([787e3ce](https://gitlab.hd123.com:20022/vue/soa/commit/787e3ce3ba4756e5279a27d6e141291899c3d5c3))
- 🐛 SOP-7567 APP 端盘点白屏问题处理 ([3ebcd75](https://gitlab.hd123.com:20022/vue/soa/commit/3ebcd75263e10e8fea5ffff3297f849004689916))
- 🐛 SOP-7572 修复了台账任务提交反馈提交评价字数限制未生效的问题 ([d64e2cf](https://gitlab.hd123.com:20022/vue/soa/commit/d64e2cfd5a2ac12c7c6a5f2727193bf088b2f4ce))
- 🐛 SOP-7574 修复了台账任务历史评价无内容的问题 ([d799261](https://gitlab.hd123.com:20022/vue/soa/commit/d799261d8f7ec79c73254efe90f46663cc30d69b))
- 🐛 SOP-7665 退货通知限量标签显示 ([5a081b1](https://gitlab.hd123.com:20022/vue/soa/commit/5a081b18c684f09ba5b5c750c83f04ad32ea1dd1))
- 🐛 SOP-7665 退货通知限量标签显示 ([96797d8](https://gitlab.hd123.com:20022/vue/soa/commit/96797d83e532287892ee8561f952fc26a8028e8b))
- 🐛 SOP-7666 修复了台账反馈和评价流程的问题 ([63e793c](https://gitlab.hd123.com:20022/vue/soa/commit/63e793cebcf43de1ea91c6deaa4298efeb4f248d))
- 🐛 SOP-7666 修复了台账任务完成反馈评价的流程问题 ([acad9ca](https://gitlab.hd123.com:20022/vue/soa/commit/acad9ca60b20f841375bea9fbb6ffa866c35cdce))

## [2.17.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.16.3...v2.17.0) (2022-08-26)

### Features

- ✨ cli 升级 3.4.7 ([a6b1fb0](https://gitlab.hd123.com:20022/vue/soa/commit/a6b1fb002ab8152b14cf8ceee7225743d97f6cf9))
- ✨ SOP-6478 小程序.门店助手.商品查询超市版改造（商品搜索页面） ([d85d50e](https://gitlab.hd123.com:20022/vue/soa/commit/d85d50e74beffe1a54b71ff0513962a70f4a9b47))
- ✨ SOP-6478 小程序.门店助手.商品查询超市版改造（商品详情页面） ([5f113d6](https://gitlab.hd123.com:20022/vue/soa/commit/5f113d6ab0eebcaddadf992fea14c34242ad58a3))
- ✨ SOP-6479 超市门店退货装箱业务 ([f012e6f](https://gitlab.hd123.com:20022/vue/soa/commit/f012e6f892bcb53ed976933be854c7cf1580cf3a))
- ✨ SOP-6479 超市门店退货装箱业务 ([d8bd2e7](https://gitlab.hd123.com:20022/vue/soa/commit/d8bd2e7d5d5edf482b0d4ce60e755b86ad2dad59))
- ✨ SOP-6521 超市版兼容 app ([a4c3bb0](https://gitlab.hd123.com:20022/vue/soa/commit/a4c3bb09635cceb0708add003e08628a490e6b5c))
- ✨ SOP-6521 超市版兼容 app ([fab6f68](https://gitlab.hd123.com:20022/vue/soa/commit/fab6f681bad0390bf987342f582a3fcc16747647))
- ✨ SOP-6521 兼容 app 改造 ([f15fb08](https://gitlab.hd123.com:20022/vue/soa/commit/f15fb08cbe30d08ffa0dc13c6361d6c1ee0a8a92))
- ✨ SOP-6522 超市版增加激活码逻辑 ([372cd01](https://gitlab.hd123.com:20022/vue/soa/commit/372cd01ce9090f0810b2732089948176c58a7ede))
- ✨ SOP-6522 超市版增加激活码逻辑 ([9fc1bbb](https://gitlab.hd123.com:20022/vue/soa/commit/9fc1bbb17d8768dcd95ed7ee524bb43219a79e6b))
- ✨ SOP-6522 超市版增加激活码逻辑 -todo ([f9d4e5d](https://gitlab.hd123.com:20022/vue/soa/commit/f9d4e5d6af23eaa85719ac494bfb440b3398c6d2))
- ✨ SOP-6710 激活码页面样式优化 ([f64b8db](https://gitlab.hd123.com:20022/vue/soa/commit/f64b8dba24d584dc8ef314c855e8913881242edc))
- ✨ SOP-6776 新增店务和任务巡检模块兼容 app 的逻辑 ([5a2d80b](https://gitlab.hd123.com:20022/vue/soa/commit/5a2d80b95c2a97d365df82eba1a8e6ab091fb76a))
- ✨ SOP-7221 超市门店退货装箱增加权限和配置 ([76fdf8e](https://gitlab.hd123.com:20022/vue/soa/commit/76fdf8e9b542dae3c556191ac5b79460b66f78f9))
- ✨ SOP-7278 质量反馈隐藏赔付比例 ([fa828e7](https://gitlab.hd123.com:20022/vue/soa/commit/fa828e796be3b8fdd00ac72cfb96cdbed8f2be3c))
- ✨ SOP-7283 增加门店资金账户查看权限 ([9126410](https://gitlab.hd123.com:20022/vue/soa/commit/9126410adb34aaf63eaa69799580ab43dd885050))
- ✨ SOP-7283 增加门店资金账户相关查看功能 ([d042c8e](https://gitlab.hd123.com:20022/vue/soa/commit/d042c8e67fdf79cac71b415c722e523df73d4d3f))
- ✨ SOP-7311 增加权限申请逻辑 ([77feed4](https://gitlab.hd123.com:20022/vue/soa/commit/77feed4b3bf5c92655d137e4db011730fa3c371e))
- ✨ SOP-7418 新增台账任务改造 ([99cebee](https://gitlab.hd123.com:20022/vue/soa/commit/99cebee3980cd79c2c5119172dd051d5b38ed1bb))

### Bug Fixes

- 🐛 退货装箱分配原因 ([61f98fb](https://gitlab.hd123.com:20022/vue/soa/commit/61f98fba04ef195887e544d4db41547dd4f05ba0))
- 🐛 退货装箱箱码获取调整 ([1b5ee48](https://gitlab.hd123.com:20022/vue/soa/commit/1b5ee48526e3300af9af0945151d2c6e53971061))
- 🐛 退货装箱箱码明细 ([36c8dff](https://gitlab.hd123.com:20022/vue/soa/commit/36c8dff557c7190ec4130398e4bcde67be4c9845))
- 🐛 退货装箱箱码明细界面 ([1b35efe](https://gitlab.hd123.com:20022/vue/soa/commit/1b35efe161e08cea8682889afc6c9ba9f9169476))
- 🐛 退货装箱详情 ([6bb217a](https://gitlab.hd123.com:20022/vue/soa/commit/6bb217ad15976b4d33c27ca3310b6f94b453deb6))
- 🐛 APP 支持两次侧滑返回 ([b5ced25](https://gitlab.hd123.com:20022/vue/soa/commit/b5ced25dce732a5432774fb88b0dc420b703ff84))
- 🐛 SOP-6479 超市门店退货装箱业务 查看全部页面 ([38933f1](https://gitlab.hd123.com:20022/vue/soa/commit/38933f1eba21ffbbaf0e9dd0b8a5d1d203c51490))
- 🐛 SOP-6479 超市门店退货装箱业务搜索页调整 ([38384bd](https://gitlab.hd123.com:20022/vue/soa/commit/38384bd36f698f1a529a690017b11908ef5fead7))
- 🐛 SOP-6719 修复绑定失效后未跳转回激活页面的问题 ([f98e3c5](https://gitlab.hd123.com:20022/vue/soa/commit/f98e3c576bb9a08ab12e81e665f5c8e72bc615a5))
- 🐛 SOP-6719 修复绑定失效后未跳转回激活页面的问题 ([c512935](https://gitlab.hd123.com:20022/vue/soa/commit/c512935eccc218a2eb446e639fcabce49ab83570))
- 🐛 SOP-6719 修复跳转到激活码页面展示 modal 的问题 ([d600b99](https://gitlab.hd123.com:20022/vue/soa/commit/d600b99b1b3cfc7ebf36ba260c4a74086237ea66))
- 🐛 SOP-7023 修复任务查询页面引入组件名为带扩展名的问题 ([50aad4f](https://gitlab.hd123.com:20022/vue/soa/commit/50aad4ffa12e6327d3db5547bf2e0af720dc9634))
- 🐛 SOP-7024 上传图片视频组件兼容 APP-PLUS 平台 ([08d92ac](https://gitlab.hd123.com:20022/vue/soa/commit/08d92ac0db2b73eda506501a5d6a75122dbabb29))
- 🐛 SOP-7027 合并 2.15.0 后无限打印错误提示问题修复 ([beebfb2](https://gitlab.hd123.com:20022/vue/soa/commit/beebfb2afd12a8eee0c14cc2e2008501f8b3d71d))
- 🐛 SOP-7034 报损/报溢模块-报损单详情-点击查看全部-图片遮挡了头部文字 ([44fc9e0](https://gitlab.hd123.com:20022/vue/soa/commit/44fc9e0aed1323d256e5509bbe85ad40268e4d6a))
- 🐛 SOP-7035 修复图片视频上传组件在钉钉平台预览视频播放器透明的问题 ([eb3e6c6](https://gitlab.hd123.com:20022/vue/soa/commit/eb3e6c695466b17652cb7085dde6751db8c09e2e))
- 🐛 SOP-7038 修复 APP 端营销玩法列表上拉加载无法触发的问题 ([1e941db](https://gitlab.hd123.com:20022/vue/soa/commit/1e941db18940e934781c836a8a4be0bdbc7c5992))
- 🐛 SOP-7040 修复钉钉平台收货列表卡片的电商标记与其他 tag 没有间距的问题 ([18430cf](https://gitlab.hd123.com:20022/vue/soa/commit/18430cfa897ad83592d7df37bcadc13393868a47))
- 🐛 SOP-7043 修复创建促销价活动页面备注输入框被提交按钮遮挡的问题 ([791962c](https://gitlab.hd123.com:20022/vue/soa/commit/791962c2c191e60387cfd3339d71f65e6b8ca43b))
- 🐛 SOP-7044 修复标价签申请选择商品页面勾选移除后徽标数值不减少的问题 ([292bb1c](https://gitlab.hd123.com:20022/vue/soa/commit/292bb1cb63d7cdd83f5106a1c581a9d308fd72d4))
- 🐛 SOP-7045 收货详情页面一件确认行样式字数优化 ([6fd8a32](https://gitlab.hd123.com:20022/vue/soa/commit/6fd8a329303547baa05b19eb0e1076ca4de46f01))
- 🐛 SOP-7046 收货.清点模式下，未确认状态的商品列表，散称商品联动逻辑不正确 ([8d40ad3](https://gitlab.hd123.com:20022/vue/soa/commit/8d40ad3193f94f0e4bd508b1ee4d6de05d7cbed8))
- 🐛 SOP-7047 订货活动-点击立即报名，提交报名后，弹层提示商品种类数、金额为 0，数值错误 ([35f2b4c](https://gitlab.hd123.com:20022/vue/soa/commit/35f2b4c8ce8c85da1784680888bb0b4824aa84aa))
- 🐛 SOP-7048 修复收货提交错误重试弹框重试多次消失的问题 ([6bb60e4](https://gitlab.hd123.com:20022/vue/soa/commit/6bb60e4fb53ebe792362470fc8a40fb1a42ddc1a))
- 🐛 SOP-7049 数据 tab 页 uCharts 类型提取兼容 APP ([72935de](https://gitlab.hd123.com:20022/vue/soa/commit/72935de05b229f6d944c925d82a50230b9002a89))
- 🐛 SOP-7051 修复营销玩法创建促销折扣的折扣输入框 placeholder 文字很小的问题 ([8b4943a](https://gitlab.hd123.com:20022/vue/soa/commit/8b4943aed5c7b798901fcf0a640c0fcd21f85e51))
- 🐛 SOP-7052 小程序：订货活动点击活动跳转详情页，未加载出数据时，时间展示为 null ([5e73f6e](https://gitlab.hd123.com:20022/vue/soa/commit/5e73f6e6474e29618a1a0ed313a99577d4ad324d))
- 🐛 SOP-7053 修复 APP 端数据 tab 页面环比数据颜色未生效的问题 ([4bc26f2](https://gitlab.hd123.com:20022/vue/soa/commit/4bc26f2474a294f6be909cb87ae172d3cf889edf))
- 🐛 SOP-7054 修复营销玩法新建活动时折扣或门槛为 0 时可以提交的问题 ([b44b791](https://gitlab.hd123.com:20022/vue/soa/commit/b44b791542ce95b57216cd6f614ad1adfc4b4ca4))
- 🐛 SOP-7055 修复营销玩法模块输入框不展示以及无法选择时段折扣起止时间的问题 ([a41ac1a](https://gitlab.hd123.com:20022/vue/soa/commit/a41ac1a41f27d12befb38db314914b280fee10ca))
- 🐛 SOP-7056 修复 APP 端收货容差弹出框重复弹出时第二次不弹出的问题 ([64d2071](https://gitlab.hd123.com:20022/vue/soa/commit/64d20716f55df04b60bf332aa9c34eeb3c14ed83))
- 🐛 SOP-7058 修复营销玩法-阶梯折扣单品满件时保存按钮的生效未将适用商品数量纳入判断 ([1dd5168](https://gitlab.hd123.com:20022/vue/soa/commit/1dd5168dedc09e78cc6440e0afda35fd710df452))
- 🐛 SOP-7059 修复营销玩法详情页面多个卡片同时显示时标题会被下一个卡片继承的问题 ([b606c7a](https://gitlab.hd123.com:20022/vue/soa/commit/b606c7afc80cc133210b48f18dda509fb6794f97))
- 🐛 SOP-7060 修复 APP 端营销玩法-换购玩法换购商品因为 key 值不存在无法显示的问题 ([81bf65c](https://gitlab.hd123.com:20022/vue/soa/commit/81bf65cb79848639a5dd6beee6859d134530fa3d))
- 🐛 SOP-7063 修复自采模块编辑页面上下滚动时部分低层级元素透过高层级元素展示的问题 ([cd69845](https://gitlab.hd123.com:20022/vue/soa/commit/cd69845b3a66ddbe673cf88152ac2611d61819a0))
- 🐛 SOP-7063 修复自采模块使用 text 标签包裹其他标签的问题 ([d065ce9](https://gitlab.hd123.com:20022/vue/soa/commit/d065ce93765250b7078899a93a4dcf724c7719b6))
- 🐛 SOP-7064 修复 APP 端自采模块编辑时自裁费用无输入框的问题 ([2e9f368](https://gitlab.hd123.com:20022/vue/soa/commit/2e9f368cf55b9123218c538eddec9cd8acbf634b))
- 🐛 SOP-7077 修复营销玩法满额换购与单品是否可以换购选择商品未进行控制的问题 ([57bb505](https://gitlab.hd123.com:20022/vue/soa/commit/57bb5052929c8b869905b035545a10b168eb3571))
- 🐛 SOP-7082 门店助手店务点击“取消”按钮，无反应 ([a90e664](https://gitlab.hd123.com:20022/vue/soa/commit/a90e6645f30c875442e3e0c000c7ccdccf3606a3))
- 🐛 SOP-7099 修复订单查询模块 toast 提示重复的问题 ([a4d135c](https://gitlab.hd123.com:20022/vue/soa/commit/a4d135c15f3a680e150451f789a070e423316b29))
- 🐛 SOP-7101 修复门店助手 APP/小程序.调拨申请库存不显示 ([bacd2d4](https://gitlab.hd123.com:20022/vue/soa/commit/bacd2d4adc05f49f567a32916c7cda345a63c4ac))
- 🐛 SOP-7103、SOP-7096 ([6f1dac5](https://gitlab.hd123.com:20022/vue/soa/commit/6f1dac5d7a966dd927017f2d0334cd943ac60ae2))
- 🐛 SOP-7106 商品查询页面，title 字体颜色与背景色一致，显示不友好 ([ec1ca56](https://gitlab.hd123.com:20022/vue/soa/commit/ec1ca564bc7f32d829eb8884d32ded14dec0a000))
- 🐛 SOP-7109 修复商品查询报错提示未展示的问题 ([1fdae89](https://gitlab.hd123.com:20022/vue/soa/commit/1fdae894ca411dc94d878e48c18a4a15d5f38d90))
- 🐛 SOP-7223 超市门店退货装箱业务.新增退货装箱页面，页面中退货原因需要必填 ([03f3851](https://gitlab.hd123.com:20022/vue/soa/commit/03f3851acea01966f86be4bb9035965edac55321))
- 🐛 SOP-7228 退货装箱详情-已提交页面，显示问题汇总 ([7ee4931](https://gitlab.hd123.com:20022/vue/soa/commit/7ee4931d3e5c5f2b592baad4e72f6d200176bad8))
- 🐛 SOP-7228、SOP-7238、SOP-7222 ([cbc6694](https://gitlab.hd123.com:20022/vue/soa/commit/cbc669456ede82a147b81fd0efb9bee7299e99a8))
- 🐛 SOP-7240 退货商品列表存在数据时；切换页面业务类型，页面实现问题 ([4a6f3a6](https://gitlab.hd123.com:20022/vue/soa/commit/4a6f3a62f4fe039ac1279d988cd452f76858353d))
- 🐛 SOP-7240 退货装箱.新增页面.页面实现问题 ([9a53d38](https://gitlab.hd123.com:20022/vue/soa/commit/9a53d38f4ea572e5b5444d52e0ee7c1ebcbed03a))
- 🐛 SOP-7241 退货装箱详情页面，商品列表合计显示问题汇总 ([57bb66f](https://gitlab.hd123.com:20022/vue/soa/commit/57bb66f970177db58060c9d32808fd09fd603931))
- 🐛 SOP-7243 退货装箱.新增页面，页面退货原因实现问题 ([4431b4d](https://gitlab.hd123.com:20022/vue/soa/commit/4431b4da7110b6d95d81a595841f96083ad7328b))
- 🐛 SOP-7246 退货商品列表与箱码；列表显示问题 ([d150794](https://gitlab.hd123.com:20022/vue/soa/commit/d150794ecad495ed2561db72ff52f16bbee2f37a))
- 🐛 SOP-7250 箱码明细页面，无法修改退货数量 ([6ee2641](https://gitlab.hd123.com:20022/vue/soa/commit/6ee2641889477b9848bcdfd109e8a935b014c625))
- 🐛 SOP-7251 超市门店退货装箱业务.退货装箱详情页面，商品列表显示问题汇总 ([d4a80f8](https://gitlab.hd123.com:20022/vue/soa/commit/d4a80f80e630531270bc7392e1b7c093aae598e1))
- 🐛 SOP-7253 超市门店退货装箱业务.单据搜索列表，商品名称过长时显示不正确 ([60e89ec](https://gitlab.hd123.com:20022/vue/soa/commit/60e89ec137957dd9b51b0cbcc0ace61fd2979801))
- 🐛 SOP-7253 门店退货装箱业务.单据搜索列表，商品名称过长时显示不正确 ([966c131](https://gitlab.hd123.com:20022/vue/soa/commit/966c1315327d26bf6cf8b527beab662ec12791fe))
- 🐛 SOP-7254 退货装箱.新增页面.接口加载中，页面仓位显示不正确 ([251f428](https://gitlab.hd123.com:20022/vue/soa/commit/251f428cca0d9e47f84ad01881bb640d54d3fb0a))
- 🐛 SOP-7255 退货装箱.商品搜索页面，商品“当前仓位库存”字段显示问题 ([e7885d9](https://gitlab.hd123.com:20022/vue/soa/commit/e7885d9ba38d8500c96742d80fb7dad356334386))
- 🐛 SOP-7261 商品查询筛选条件未变成全部仓位 ([e6b3827](https://gitlab.hd123.com:20022/vue/soa/commit/e6b3827b3bbe352e202d5c234f461be5ae7caf82))
- 🐛 SOP-7263 ([23a9956](https://gitlab.hd123.com:20022/vue/soa/commit/23a9956bf5e90efcde81865720423a679bb85b4e))
- 🐛 SOP-7267 商品查询：历史搜索，数据超过两行，页面显示不友好 ([ab704af](https://gitlab.hd123.com:20022/vue/soa/commit/ab704af1721476db25a3e2423827205621856389))
- 🐛 SOP-7267 商品查询：历史搜索，数据超过两行，页面显示不友好 ([7e4870b](https://gitlab.hd123.com:20022/vue/soa/commit/7e4870b442a7fa96962c2382760032de8fcaa8c2))
- 🐛 SOP-7271 .箱码明细页面，退货数量修改弹框问题汇总 ([a45ecd1](https://gitlab.hd123.com:20022/vue/soa/commit/a45ecd19b7f28b1a927288917362380bd99bcae6))
- 🐛 SOP-7271 箱码明细页面，退货数量修改弹框问题汇总 ([fefda01](https://gitlab.hd123.com:20022/vue/soa/commit/fefda019a838a6accefce334dbfa1a88df34d342))
- 🐛 SOP-7273 超市门店退货装箱箱码获取调整 ([c03a7aa](https://gitlab.hd123.com:20022/vue/soa/commit/c03a7aac2730000972adb279de6c05c9dd8ec991))
- 🐛 SOP-7274 箱码明细页面，页面中商品删除成功后，箱数会恢复初始状态 ([8054694](https://gitlab.hd123.com:20022/vue/soa/commit/8054694b14315959879472f6a5cb072cca706b31))
- 🐛 SOP-7277 商品查询：商品详情页，商品基础信息，供应商未展示代码 ([1be93cf](https://gitlab.hd123.com:20022/vue/soa/commit/1be93cf2c48e8126cd0f6716297e08aa05002392))
- 🐛 SOP-7280 箱码明细页面，修改商品的箱码后，页面数据未刷新 ([2083a58](https://gitlab.hd123.com:20022/vue/soa/commit/2083a58bc04a512210969d759ce1f3413cf3182b))
- 🐛 SOP-7284 门店助手 APP：商品查询页面，搜索框输入品类/品牌/仓位/标签，传参错误 ([a937579](https://gitlab.hd123.com:20022/vue/soa/commit/a9375799614dff3c0f95daa6c3efed0979a77703))
- 🐛 SOP-7284 门店助手 APP：商品查询页面，搜索框输入品类/品牌/仓位/标签，传参错误 ([9c1f493](https://gitlab.hd123.com:20022/vue/soa/commit/9c1f493059152b2950810730382d79d0eff95766))
- 🐛 SOP-7285 箱码明细页面，切换箱码列表存在问题 ([5632570](https://gitlab.hd123.com:20022/vue/soa/commit/56325707a2ffbee58d8d540a2d822914bb5c0b99))
- 🐛 SOP-7285、SOP-7305 ([2eba40b](https://gitlab.hd123.com:20022/vue/soa/commit/2eba40b853ce7e74db0ec54ab002a452488671ee))
- 🐛 SOP-7288、SOP-7301、 ([acdeb8f](https://gitlab.hd123.com:20022/vue/soa/commit/acdeb8f74e9b23d994983ecdf813b70b83d246db))
- 🐛 SOP-7289 “已装箱”状态的箱码明细页面，允许修改箱数 ([caa7801](https://gitlab.hd123.com:20022/vue/soa/commit/caa78016f0ff50b40934f8a70b036e5c3d0a40b7))
- 🐛 SOP-7292 退货装箱.复制单据.复制单据的时候校验需要和新增一致 ([c0bb4be](https://gitlab.hd123.com:20022/vue/soa/commit/c0bb4be2b7ba2c276a5266a31d763901615ceb51))
- 🐛 SOP-7295 退货装箱.新建：有数据变更后，点击返回按钮，没有二次弹窗提示 ([522c0c3](https://gitlab.hd123.com:20022/vue/soa/commit/522c0c30412ae29d8562d6904e6fafa547b5a843))
- 🐛 SOP-7305、SOP-7305 ([cafa698](https://gitlab.hd123.com:20022/vue/soa/commit/cafa698116be6ba5b54fd2924de1c7c525f84a6d))
- 🐛 SOP-7306 退货装箱.新建：扫描同样供应商商品，前端仍然分配了新的箱码 ([76b5c58](https://gitlab.hd123.com:20022/vue/soa/commit/76b5c5811d128f440373afb6b676f23c6cb31c11))
- 🐛 SOP-7308 门店助手：商品查询详情页：默认展示全部仓位，点击仓位切换某一个仓位，再切换为全部仓位，接口报错 ([13f6e12](https://gitlab.hd123.com:20022/vue/soa/commit/13f6e1246815bf15d7d2d490b7d524c51d9c65a9))
- 🐛 SOP-7309、SOP-7295 ([4ec1a25](https://gitlab.hd123.com:20022/vue/soa/commit/4ec1a25a0c8cb75d3bec3fa9f0840c52e6343e34))
- 🐛 SOP-7313 退货装箱.新建：存在未装箱商品进行提交，弹窗提示缺少未装箱商品 code ([c0cda28](https://gitlab.hd123.com:20022/vue/soa/commit/c0cda281db5133dff1def47884091e76fdd7c5eb))
- 🐛 SOP-7315 箱码--全部 tab 下，只显示了未装箱的数据 ([e1b7fbb](https://gitlab.hd123.com:20022/vue/soa/commit/e1b7fbbabca8e091a8fe21bcc9f349b09a962a5b))
- 🐛 SOP-7320 重点商品检查 APP 兼容优化 ([a7b7a89](https://gitlab.hd123.com:20022/vue/soa/commit/a7b7a894abddd3547362fd9e2a2a6d5baadbc015))
- 🐛 SOP-7326 移除任务模块组件引用 hd-loading、hd-toast 和 hd-modal 组件 ([43b8773](https://gitlab.hd123.com:20022/vue/soa/commit/43b87736b43b3a00ecf2a308b7c0ddfb4862bc9d))
- 🐛 SOP-7330 超市门店退货装箱退货申请单字段调整 ([b313ad9](https://gitlab.hd123.com:20022/vue/soa/commit/b313ad9b78b7040d9ca82886a8eb5c531eba1cbd))
- 🐛 SOP-7332 移除完成反馈弹出框组件内的 loading 组件 ([456f166](https://gitlab.hd123.com:20022/vue/soa/commit/456f16686d6e2642ef1ee6a8602e7c5a907962e3))
- 🐛 SOP-7338 编辑页汇总信息显示优化 ([8139aca](https://gitlab.hd123.com:20022/vue/soa/commit/8139aca2efd438f70c8e39a09402224e3d35a71e))
- 🐛 SOP-7338 退货装箱.编辑页汇总信息显示优化 ([2ba2f38](https://gitlab.hd123.com:20022/vue/soa/commit/2ba2f389f2f34731a24780837e2ffe54a5016ff6))
- 🐛 SOP-7340 超市版兼容.账单单据模块.详情页面点击【立即支付】，页面报错问题 ([fa7f577](https://gitlab.hd123.com:20022/vue/soa/commit/fa7f57744e35c2b06f6c779605a9bdcf3e518e2b))
- 🐛 SOP-7349 申请退货-新增退货申请-选择退货原因提示框-名字超过一行时没有省略显示 ([7beb4cf](https://gitlab.hd123.com:20022/vue/soa/commit/7beb4cfca3bee91c3032fd1e59a41fa99cace88b))
- 🐛 SOP-7365 退货装箱模块切换分包，并且模块跳转采用 Route ([deb16f4](https://gitlab.hd123.com:20022/vue/soa/commit/deb16f4255880e5f959352027ee500ba576c1460))
- 🐛 SOP-7431 账户流水页界面问题 ([8ac9400](https://gitlab.hd123.com:20022/vue/soa/commit/8ac940057f1a7bf48b94550bc9f34bef10d21a7f))
- 🐛 SOP-7434 账户流水：最低金额输入 0 后进行筛选，筛选弹窗中对应值为空 ([2bf8081](https://gitlab.hd123.com:20022/vue/soa/commit/2bf808136ee69a1134bfe21f83581b9306fd7c73))
- 🐛 SOP-7437 账户流水：流水明细页界面问题 ([c93d013](https://gitlab.hd123.com:20022/vue/soa/commit/c93d013fbdc5f5ba60aca65b833120892d1baf03))

## [2.19.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.18.2...v2.19.0) (2022-09-23)

### Features

- ✨ SOP-7771 新增知识库模块 ([c613456](https://gitlab.hd123.com:20022/vue/soa/commit/c6134564de8edfae0f1a789a5ec9cad0790d0439))

### [2.18.2](https://gitlab.hd123.com/vue/soa/compare/v2.18.1...v2.18.2) (2022-09-16)

### Bug Fixes

- 🐛 SOP-7701 修复了任务完成清空后 tab 徽标还存在的问题 ([822942a](https://gitlab.hd123.com/vue/soa/commit/822942aa9bac74c89a57e3451fab5d3f4519bb4e))

### [2.18.1](https://gitlab.hd123.com/vue/soa/compare/v2.18.0...v2.18.1) (2022-09-13)

## [2.18.0](https://gitlab.hd123.com/vue/soa/compare/v2.17.0...v2.18.0) (2022-09-13)

### Features

- ✨ cli 升级 3.4.7 ([a6b1fb0](https://gitlab.hd123.com/vue/soa/commit/a6b1fb002ab8152b14cf8ceee7225743d97f6cf9))
- ✨ SOP-6478 小程序.门店助手.商品查询超市版改造（商品搜索页面） ([d85d50e](https://gitlab.hd123.com/vue/soa/commit/d85d50e74beffe1a54b71ff0513962a70f4a9b47))
- ✨ SOP-6478 小程序.门店助手.商品查询超市版改造（商品详情页面） ([5f113d6](https://gitlab.hd123.com/vue/soa/commit/5f113d6ab0eebcaddadf992fea14c34242ad58a3))
- ✨ SOP-6479 超市门店退货装箱业务 ([f012e6f](https://gitlab.hd123.com/vue/soa/commit/f012e6f892bcb53ed976933be854c7cf1580cf3a))
- ✨ SOP-6479 超市门店退货装箱业务 ([d8bd2e7](https://gitlab.hd123.com/vue/soa/commit/d8bd2e7d5d5edf482b0d4ce60e755b86ad2dad59))
- ✨ SOP-6521 超市版兼容 app ([a4c3bb0](https://gitlab.hd123.com/vue/soa/commit/a4c3bb09635cceb0708add003e08628a490e6b5c))
- ✨ SOP-6521 超市版兼容 app ([fab6f68](https://gitlab.hd123.com/vue/soa/commit/fab6f681bad0390bf987342f582a3fcc16747647))
- ✨ SOP-6521 兼容 app 改造 ([f15fb08](https://gitlab.hd123.com/vue/soa/commit/f15fb08cbe30d08ffa0dc13c6361d6c1ee0a8a92))
- ✨ SOP-6522 超市版增加激活码逻辑 ([372cd01](https://gitlab.hd123.com/vue/soa/commit/372cd01ce9090f0810b2732089948176c58a7ede))
- ✨ SOP-6522 超市版增加激活码逻辑 ([9fc1bbb](https://gitlab.hd123.com/vue/soa/commit/9fc1bbb17d8768dcd95ed7ee524bb43219a79e6b))
- ✨ SOP-6522 超市版增加激活码逻辑 -todo ([f9d4e5d](https://gitlab.hd123.com/vue/soa/commit/f9d4e5d6af23eaa85719ac494bfb440b3398c6d2))
- ✨ SOP-6710 激活码页面样式优化 ([f64b8db](https://gitlab.hd123.com/vue/soa/commit/f64b8dba24d584dc8ef314c855e8913881242edc))
- ✨ SOP-6776 新增店务和任务巡检模块兼容 app 的逻辑 ([5a2d80b](https://gitlab.hd123.com/vue/soa/commit/5a2d80b95c2a97d365df82eba1a8e6ab091fb76a))
- ✨ SOP-7221 超市门店退货装箱增加权限和配置 ([76fdf8e](https://gitlab.hd123.com/vue/soa/commit/76fdf8e9b542dae3c556191ac5b79460b66f78f9))
- ✨ SOP-7311 增加权限申请逻辑 ([77feed4](https://gitlab.hd123.com/vue/soa/commit/77feed4b3bf5c92655d137e4db011730fa3c371e))
- ✨ SOP-7418 台账任务反馈评价改造 ([80a135c](https://gitlab.hd123.com/vue/soa/commit/80a135c4aea3338662b4436e43082dfa040ee47d))
- ✨ SOP-7418 新增台账任务改造 ([99cebee](https://gitlab.hd123.com/vue/soa/commit/99cebee3980cd79c2c5119172dd051d5b38ed1bb))
- ✨ SOP-7552 UNI-PORTAL 传入参数结构调整 ([ef48e3d](https://gitlab.hd123.com/vue/soa/commit/ef48e3db67ba6f01ba9c502e316cc637c0f6b475))
- ✨ SOP-7604 退货通知数量交互优化 ([4f683fe](https://gitlab.hd123.com/vue/soa/commit/4f683fe2e7d2bad033b75b1de198872fa5fb9bb2))

### Bug Fixes

- 🐛 退货装箱分配原因 ([61f98fb](https://gitlab.hd123.com/vue/soa/commit/61f98fba04ef195887e544d4db41547dd4f05ba0))
- 🐛 退货装箱箱码获取调整 ([1b5ee48](https://gitlab.hd123.com/vue/soa/commit/1b5ee48526e3300af9af0945151d2c6e53971061))
- 🐛 退货装箱箱码明细 ([36c8dff](https://gitlab.hd123.com/vue/soa/commit/36c8dff557c7190ec4130398e4bcde67be4c9845))
- 🐛 退货装箱箱码明细界面 ([1b35efe](https://gitlab.hd123.com/vue/soa/commit/1b35efe161e08cea8682889afc6c9ba9f9169476))
- 🐛 退货装箱详情 ([6bb217a](https://gitlab.hd123.com/vue/soa/commit/6bb217ad15976b4d33c27ca3310b6f94b453deb6))
- 🐛 修复了不合格 tag 展示靠后的问题 ([a6e3074](https://gitlab.hd123.com/vue/soa/commit/a6e30740276762858b064dcc25e2e758289ee1f7))
- 🐛 修复了单模板台账未显示历史评价的问题 ([732dd7a](https://gitlab.hd123.com/vue/soa/commit/732dd7a9b61f7d8697c435e688a8316966802907))
- 🐛 APP 支持两次侧滑返回 ([b5ced25](https://gitlab.hd123.com/vue/soa/commit/b5ced25dce732a5432774fb88b0dc420b703ff84))
- 🐛 SOP-6479 超市门店退货装箱业务 查看全部页面 ([38933f1](https://gitlab.hd123.com/vue/soa/commit/38933f1eba21ffbbaf0e9dd0b8a5d1d203c51490))
- 🐛 SOP-6479 超市门店退货装箱业务搜索页调整 ([38384bd](https://gitlab.hd123.com/vue/soa/commit/38384bd36f698f1a529a690017b11908ef5fead7))
- 🐛 SOP-6719 修复绑定失效后未跳转回激活页面的问题 ([f98e3c5](https://gitlab.hd123.com/vue/soa/commit/f98e3c576bb9a08ab12e81e665f5c8e72bc615a5))
- 🐛 SOP-6719 修复绑定失效后未跳转回激活页面的问题 ([c512935](https://gitlab.hd123.com/vue/soa/commit/c512935eccc218a2eb446e639fcabce49ab83570))
- 🐛 SOP-6719 修复跳转到激活码页面展示 modal 的问题 ([d600b99](https://gitlab.hd123.com/vue/soa/commit/d600b99b1b3cfc7ebf36ba260c4a74086237ea66))
- 🐛 SOP-7023 修复任务查询页面引入组件名为带扩展名的问题 ([50aad4f](https://gitlab.hd123.com/vue/soa/commit/50aad4ffa12e6327d3db5547bf2e0af720dc9634))
- 🐛 SOP-7024 上传图片视频组件兼容 APP-PLUS 平台 ([08d92ac](https://gitlab.hd123.com/vue/soa/commit/08d92ac0db2b73eda506501a5d6a75122dbabb29))
- 🐛 SOP-7027 合并 2.15.0 后无限打印错误提示问题修复 ([beebfb2](https://gitlab.hd123.com/vue/soa/commit/beebfb2afd12a8eee0c14cc2e2008501f8b3d71d))
- 🐛 SOP-7034 报损/报溢模块-报损单详情-点击查看全部-图片遮挡了头部文字 ([44fc9e0](https://gitlab.hd123.com/vue/soa/commit/44fc9e0aed1323d256e5509bbe85ad40268e4d6a))
- 🐛 SOP-7035 修复图片视频上传组件在钉钉平台预览视频播放器透明的问题 ([eb3e6c6](https://gitlab.hd123.com/vue/soa/commit/eb3e6c695466b17652cb7085dde6751db8c09e2e))
- 🐛 SOP-7038 修复 APP 端营销玩法列表上拉加载无法触发的问题 ([1e941db](https://gitlab.hd123.com/vue/soa/commit/1e941db18940e934781c836a8a4be0bdbc7c5992))
- 🐛 SOP-7040 修复钉钉平台收货列表卡片的电商标记与其他 tag 没有间距的问题 ([18430cf](https://gitlab.hd123.com/vue/soa/commit/18430cfa897ad83592d7df37bcadc13393868a47))
- 🐛 SOP-7043 修复创建促销价活动页面备注输入框被提交按钮遮挡的问题 ([791962c](https://gitlab.hd123.com/vue/soa/commit/791962c2c191e60387cfd3339d71f65e6b8ca43b))
- 🐛 SOP-7044 修复标价签申请选择商品页面勾选移除后徽标数值不减少的问题 ([292bb1c](https://gitlab.hd123.com/vue/soa/commit/292bb1cb63d7cdd83f5106a1c581a9d308fd72d4))
- 🐛 SOP-7045 收货详情页面一件确认行样式字数优化 ([6fd8a32](https://gitlab.hd123.com/vue/soa/commit/6fd8a329303547baa05b19eb0e1076ca4de46f01))
- 🐛 SOP-7046 收货.清点模式下，未确认状态的商品列表，散称商品联动逻辑不正确 ([8d40ad3](https://gitlab.hd123.com/vue/soa/commit/8d40ad3193f94f0e4bd508b1ee4d6de05d7cbed8))
- 🐛 SOP-7047 订货活动-点击立即报名，提交报名后，弹层提示商品种类数、金额为 0，数值错误 ([35f2b4c](https://gitlab.hd123.com/vue/soa/commit/35f2b4c8ce8c85da1784680888bb0b4824aa84aa))
- 🐛 SOP-7048 修复收货提交错误重试弹框重试多次消失的问题 ([6bb60e4](https://gitlab.hd123.com/vue/soa/commit/6bb60e4fb53ebe792362470fc8a40fb1a42ddc1a))
- 🐛 SOP-7049 数据 tab 页 uCharts 类型提取兼容 APP ([72935de](https://gitlab.hd123.com/vue/soa/commit/72935de05b229f6d944c925d82a50230b9002a89))
- 🐛 SOP-7051 修复营销玩法创建促销折扣的折扣输入框 placeholder 文字很小的问题 ([8b4943a](https://gitlab.hd123.com/vue/soa/commit/8b4943aed5c7b798901fcf0a640c0fcd21f85e51))
- 🐛 SOP-7052 小程序：订货活动点击活动跳转详情页，未加载出数据时，时间展示为 null ([5e73f6e](https://gitlab.hd123.com/vue/soa/commit/5e73f6e6474e29618a1a0ed313a99577d4ad324d))
- 🐛 SOP-7053 修复 APP 端数据 tab 页面环比数据颜色未生效的问题 ([4bc26f2](https://gitlab.hd123.com/vue/soa/commit/4bc26f2474a294f6be909cb87ae172d3cf889edf))
- 🐛 SOP-7054 修复营销玩法新建活动时折扣或门槛为 0 时可以提交的问题 ([b44b791](https://gitlab.hd123.com/vue/soa/commit/b44b791542ce95b57216cd6f614ad1adfc4b4ca4))
- 🐛 SOP-7055 修复营销玩法模块输入框不展示以及无法选择时段折扣起止时间的问题 ([a41ac1a](https://gitlab.hd123.com/vue/soa/commit/a41ac1a41f27d12befb38db314914b280fee10ca))
- 🐛 SOP-7056 修复 APP 端收货容差弹出框重复弹出时第二次不弹出的问题 ([64d2071](https://gitlab.hd123.com/vue/soa/commit/64d20716f55df04b60bf332aa9c34eeb3c14ed83))
- 🐛 SOP-7058 修复营销玩法-阶梯折扣单品满件时保存按钮的生效未将适用商品数量纳入判断 ([1dd5168](https://gitlab.hd123.com/vue/soa/commit/1dd5168dedc09e78cc6440e0afda35fd710df452))
- 🐛 SOP-7059 修复营销玩法详情页面多个卡片同时显示时标题会被下一个卡片继承的问题 ([b606c7a](https://gitlab.hd123.com/vue/soa/commit/b606c7afc80cc133210b48f18dda509fb6794f97))
- 🐛 SOP-7060 修复 APP 端营销玩法-换购玩法换购商品因为 key 值不存在无法显示的问题 ([81bf65c](https://gitlab.hd123.com/vue/soa/commit/81bf65cb79848639a5dd6beee6859d134530fa3d))
- 🐛 SOP-7063 修复自采模块编辑页面上下滚动时部分低层级元素透过高层级元素展示的问题 ([cd69845](https://gitlab.hd123.com/vue/soa/commit/cd69845b3a66ddbe673cf88152ac2611d61819a0))
- 🐛 SOP-7063 修复自采模块使用 text 标签包裹其他标签的问题 ([d065ce9](https://gitlab.hd123.com/vue/soa/commit/d065ce93765250b7078899a93a4dcf724c7719b6))
- 🐛 SOP-7064 修复 APP 端自采模块编辑时自裁费用无输入框的问题 ([2e9f368](https://gitlab.hd123.com/vue/soa/commit/2e9f368cf55b9123218c538eddec9cd8acbf634b))
- 🐛 SOP-7077 修复营销玩法满额换购与单品是否可以换购选择商品未进行控制的问题 ([57bb505](https://gitlab.hd123.com/vue/soa/commit/57bb5052929c8b869905b035545a10b168eb3571))
- 🐛 SOP-7082 门店助手店务点击“取消”按钮，无反应 ([a90e664](https://gitlab.hd123.com/vue/soa/commit/a90e6645f30c875442e3e0c000c7ccdccf3606a3))
- 🐛 SOP-7099 修复订单查询模块 toast 提示重复的问题 ([a4d135c](https://gitlab.hd123.com/vue/soa/commit/a4d135c15f3a680e150451f789a070e423316b29))
- 🐛 SOP-7101 修复门店助手 APP/小程序.调拨申请库存不显示 ([bacd2d4](https://gitlab.hd123.com/vue/soa/commit/bacd2d4adc05f49f567a32916c7cda345a63c4ac))
- 🐛 SOP-7103、SOP-7096 ([6f1dac5](https://gitlab.hd123.com/vue/soa/commit/6f1dac5d7a966dd927017f2d0334cd943ac60ae2))
- 🐛 SOP-7106 商品查询页面，title 字体颜色与背景色一致，显示不友好 ([ec1ca56](https://gitlab.hd123.com/vue/soa/commit/ec1ca564bc7f32d829eb8884d32ded14dec0a000))
- 🐛 SOP-7109 修复商品查询报错提示未展示的问题 ([1fdae89](https://gitlab.hd123.com/vue/soa/commit/1fdae894ca411dc94d878e48c18a4a15d5f38d90))
- 🐛 SOP-7223 超市门店退货装箱业务.新增退货装箱页面，页面中退货原因需要必填 ([03f3851](https://gitlab.hd123.com/vue/soa/commit/03f3851acea01966f86be4bb9035965edac55321))
- 🐛 SOP-7228 退货装箱详情-已提交页面，显示问题汇总 ([7ee4931](https://gitlab.hd123.com/vue/soa/commit/7ee4931d3e5c5f2b592baad4e72f6d200176bad8))
- 🐛 SOP-7228、SOP-7238、SOP-7222 ([cbc6694](https://gitlab.hd123.com/vue/soa/commit/cbc669456ede82a147b81fd0efb9bee7299e99a8))
- 🐛 SOP-7240 退货商品列表存在数据时；切换页面业务类型，页面实现问题 ([4a6f3a6](https://gitlab.hd123.com/vue/soa/commit/4a6f3a62f4fe039ac1279d988cd452f76858353d))
- 🐛 SOP-7240 退货装箱.新增页面.页面实现问题 ([9a53d38](https://gitlab.hd123.com/vue/soa/commit/9a53d38f4ea572e5b5444d52e0ee7c1ebcbed03a))
- 🐛 SOP-7241 退货装箱详情页面，商品列表合计显示问题汇总 ([57bb66f](https://gitlab.hd123.com/vue/soa/commit/57bb66f970177db58060c9d32808fd09fd603931))
- 🐛 SOP-7243 退货装箱.新增页面，页面退货原因实现问题 ([4431b4d](https://gitlab.hd123.com/vue/soa/commit/4431b4da7110b6d95d81a595841f96083ad7328b))
- 🐛 SOP-7246 退货商品列表与箱码；列表显示问题 ([d150794](https://gitlab.hd123.com/vue/soa/commit/d150794ecad495ed2561db72ff52f16bbee2f37a))
- 🐛 SOP-7250 箱码明细页面，无法修改退货数量 ([6ee2641](https://gitlab.hd123.com/vue/soa/commit/6ee2641889477b9848bcdfd109e8a935b014c625))
- 🐛 SOP-7251 超市门店退货装箱业务.退货装箱详情页面，商品列表显示问题汇总 ([d4a80f8](https://gitlab.hd123.com/vue/soa/commit/d4a80f80e630531270bc7392e1b7c093aae598e1))
- 🐛 SOP-7253 超市门店退货装箱业务.单据搜索列表，商品名称过长时显示不正确 ([60e89ec](https://gitlab.hd123.com/vue/soa/commit/60e89ec137957dd9b51b0cbcc0ace61fd2979801))
- 🐛 SOP-7253 门店退货装箱业务.单据搜索列表，商品名称过长时显示不正确 ([966c131](https://gitlab.hd123.com/vue/soa/commit/966c1315327d26bf6cf8b527beab662ec12791fe))
- 🐛 SOP-7254 退货装箱.新增页面.接口加载中，页面仓位显示不正确 ([251f428](https://gitlab.hd123.com/vue/soa/commit/251f428cca0d9e47f84ad01881bb640d54d3fb0a))
- 🐛 SOP-7255 退货装箱.商品搜索页面，商品“当前仓位库存”字段显示问题 ([e7885d9](https://gitlab.hd123.com/vue/soa/commit/e7885d9ba38d8500c96742d80fb7dad356334386))
- 🐛 SOP-7261 商品查询筛选条件未变成全部仓位 ([e6b3827](https://gitlab.hd123.com/vue/soa/commit/e6b3827b3bbe352e202d5c234f461be5ae7caf82))
- 🐛 SOP-7263 ([23a9956](https://gitlab.hd123.com/vue/soa/commit/23a9956bf5e90efcde81865720423a679bb85b4e))
- 🐛 SOP-7267 商品查询：历史搜索，数据超过两行，页面显示不友好 ([ab704af](https://gitlab.hd123.com/vue/soa/commit/ab704af1721476db25a3e2423827205621856389))
- 🐛 SOP-7267 商品查询：历史搜索，数据超过两行，页面显示不友好 ([7e4870b](https://gitlab.hd123.com/vue/soa/commit/7e4870b442a7fa96962c2382760032de8fcaa8c2))
- 🐛 SOP-7271 .箱码明细页面，退货数量修改弹框问题汇总 ([a45ecd1](https://gitlab.hd123.com/vue/soa/commit/a45ecd19b7f28b1a927288917362380bd99bcae6))
- 🐛 SOP-7271 箱码明细页面，退货数量修改弹框问题汇总 ([fefda01](https://gitlab.hd123.com/vue/soa/commit/fefda019a838a6accefce334dbfa1a88df34d342))
- 🐛 SOP-7273 超市门店退货装箱箱码获取调整 ([c03a7aa](https://gitlab.hd123.com/vue/soa/commit/c03a7aac2730000972adb279de6c05c9dd8ec991))
- 🐛 SOP-7274 箱码明细页面，页面中商品删除成功后，箱数会恢复初始状态 ([8054694](https://gitlab.hd123.com/vue/soa/commit/8054694b14315959879472f6a5cb072cca706b31))
- 🐛 SOP-7277 商品查询：商品详情页，商品基础信息，供应商未展示代码 ([1be93cf](https://gitlab.hd123.com/vue/soa/commit/1be93cf2c48e8126cd0f6716297e08aa05002392))
- 🐛 SOP-7280 箱码明细页面，修改商品的箱码后，页面数据未刷新 ([2083a58](https://gitlab.hd123.com/vue/soa/commit/2083a58bc04a512210969d759ce1f3413cf3182b))
- 🐛 SOP-7284 门店助手 APP：商品查询页面，搜索框输入品类/品牌/仓位/标签，传参错误 ([a937579](https://gitlab.hd123.com/vue/soa/commit/a9375799614dff3c0f95daa6c3efed0979a77703))
- 🐛 SOP-7284 门店助手 APP：商品查询页面，搜索框输入品类/品牌/仓位/标签，传参错误 ([9c1f493](https://gitlab.hd123.com/vue/soa/commit/9c1f493059152b2950810730382d79d0eff95766))
- 🐛 SOP-7285 箱码明细页面，切换箱码列表存在问题 ([5632570](https://gitlab.hd123.com/vue/soa/commit/56325707a2ffbee58d8d540a2d822914bb5c0b99))
- 🐛 SOP-7285、SOP-7305 ([2eba40b](https://gitlab.hd123.com/vue/soa/commit/2eba40b853ce7e74db0ec54ab002a452488671ee))
- 🐛 SOP-7288、SOP-7301、 ([acdeb8f](https://gitlab.hd123.com/vue/soa/commit/acdeb8f74e9b23d994983ecdf813b70b83d246db))
- 🐛 SOP-7289 “已装箱”状态的箱码明细页面，允许修改箱数 ([caa7801](https://gitlab.hd123.com/vue/soa/commit/caa78016f0ff50b40934f8a70b036e5c3d0a40b7))
- 🐛 SOP-7292 退货装箱.复制单据.复制单据的时候校验需要和新增一致 ([c0bb4be](https://gitlab.hd123.com/vue/soa/commit/c0bb4be2b7ba2c276a5266a31d763901615ceb51))
- 🐛 SOP-7295 退货装箱.新建：有数据变更后，点击返回按钮，没有二次弹窗提示 ([522c0c3](https://gitlab.hd123.com/vue/soa/commit/522c0c30412ae29d8562d6904e6fafa547b5a843))
- 🐛 SOP-7305、SOP-7305 ([cafa698](https://gitlab.hd123.com/vue/soa/commit/cafa698116be6ba5b54fd2924de1c7c525f84a6d))
- 🐛 SOP-7306 退货装箱.新建：扫描同样供应商商品，前端仍然分配了新的箱码 ([76b5c58](https://gitlab.hd123.com/vue/soa/commit/76b5c5811d128f440373afb6b676f23c6cb31c11))
- 🐛 SOP-7308 门店助手：商品查询详情页：默认展示全部仓位，点击仓位切换某一个仓位，再切换为全部仓位，接口报错 ([13f6e12](https://gitlab.hd123.com/vue/soa/commit/13f6e1246815bf15d7d2d490b7d524c51d9c65a9))
- 🐛 SOP-7309、SOP-7295 ([4ec1a25](https://gitlab.hd123.com/vue/soa/commit/4ec1a25a0c8cb75d3bec3fa9f0840c52e6343e34))
- 🐛 SOP-7313 退货装箱.新建：存在未装箱商品进行提交，弹窗提示缺少未装箱商品 code ([c0cda28](https://gitlab.hd123.com/vue/soa/commit/c0cda281db5133dff1def47884091e76fdd7c5eb))
- 🐛 SOP-7315 箱码--全部 tab 下，只显示了未装箱的数据 ([e1b7fbb](https://gitlab.hd123.com/vue/soa/commit/e1b7fbbabca8e091a8fe21bcc9f349b09a962a5b))
- 🐛 SOP-7320 重点商品检查 APP 兼容优化 ([a7b7a89](https://gitlab.hd123.com/vue/soa/commit/a7b7a894abddd3547362fd9e2a2a6d5baadbc015))
- 🐛 SOP-7326 移除任务模块组件引用 hd-loading、hd-toast 和 hd-modal 组件 ([43b8773](https://gitlab.hd123.com/vue/soa/commit/43b87736b43b3a00ecf2a308b7c0ddfb4862bc9d))
- 🐛 SOP-7330 超市门店退货装箱退货申请单字段调整 ([b313ad9](https://gitlab.hd123.com/vue/soa/commit/b313ad9b78b7040d9ca82886a8eb5c531eba1cbd))
- 🐛 SOP-7332 移除完成反馈弹出框组件内的 loading 组件 ([456f166](https://gitlab.hd123.com/vue/soa/commit/456f16686d6e2642ef1ee6a8602e7c5a907962e3))
- 🐛 SOP-7338 编辑页汇总信息显示优化 ([8139aca](https://gitlab.hd123.com/vue/soa/commit/8139aca2efd438f70c8e39a09402224e3d35a71e))
- 🐛 SOP-7338 退货装箱.编辑页汇总信息显示优化 ([2ba2f38](https://gitlab.hd123.com/vue/soa/commit/2ba2f389f2f34731a24780837e2ffe54a5016ff6))
- 🐛 SOP-7340 超市版兼容.账单单据模块.详情页面点击【立即支付】，页面报错问题 ([fa7f577](https://gitlab.hd123.com/vue/soa/commit/fa7f57744e35c2b06f6c779605a9bdcf3e518e2b))
- 🐛 SOP-7349 申请退货-新增退货申请-选择退货原因提示框-名字超过一行时没有省略显示 ([7beb4cf](https://gitlab.hd123.com/vue/soa/commit/7beb4cfca3bee91c3032fd1e59a41fa99cace88b))
- 🐛 SOP-7365 退货装箱模块切换分包，并且模块跳转采用 Route ([deb16f4](https://gitlab.hd123.com/vue/soa/commit/deb16f4255880e5f959352027ee500ba576c1460))
- 🐛 SOP-7526 修复了整改任务没有 tag 的问题 ([a127635](https://gitlab.hd123.com/vue/soa/commit/a1276351e74eeffe3d3e214385cc7caab19d5c29))
- 🐛 SOP-7526 SOP-7519 SOP-7517 SOP-7484 台账问题修复 ([8ab7658](https://gitlab.hd123.com/vue/soa/commit/8ab7658d82bd90a2531e217819b3b0ec0b5e5fdc))
- 🐛 SOP-7534 修复在接入 UNI 时仍检查 APP 更新的问题 ([0e2b4b4](https://gitlab.hd123.com/vue/soa/commit/0e2b4b4cfaaca6fde86ea48d774ccc56b366c698))
- 🐛 SOP-7544 线路调整门店搜索按 name 搜索 ([f1006df](https://gitlab.hd123.com/vue/soa/commit/f1006df72d2b6c94d23df1276af98ac6ea456757))
- 🐛 SOP-7562 修复了模板名称过长时不合格样式展示的问题 ([787e3ce](https://gitlab.hd123.com/vue/soa/commit/787e3ce3ba4756e5279a27d6e141291899c3d5c3))
- 🐛 SOP-7567 APP 端盘点白屏问题处理 ([3ebcd75](https://gitlab.hd123.com/vue/soa/commit/3ebcd75263e10e8fea5ffff3297f849004689916))
- 🐛 SOP-7572 修复了台账任务提交反馈提交评价字数限制未生效的问题 ([d64e2cf](https://gitlab.hd123.com/vue/soa/commit/d64e2cfd5a2ac12c7c6a5f2727193bf088b2f4ce))
- 🐛 SOP-7574 修复了台账任务历史评价无内容的问题 ([d799261](https://gitlab.hd123.com/vue/soa/commit/d799261d8f7ec79c73254efe90f46663cc30d69b))
- 🐛 SOP-7665 退货通知限量标签显示 ([5a081b1](https://gitlab.hd123.com/vue/soa/commit/5a081b18c684f09ba5b5c750c83f04ad32ea1dd1))
- 🐛 SOP-7665 退货通知限量标签显示 ([96797d8](https://gitlab.hd123.com/vue/soa/commit/96797d83e532287892ee8561f952fc26a8028e8b))
- 🐛 SOP-7666 修复了台账反馈和评价流程的问题 ([63e793c](https://gitlab.hd123.com/vue/soa/commit/63e793cebcf43de1ea91c6deaa4298efeb4f248d))
- 🐛 SOP-7666 修复了台账任务完成反馈评价的流程问题 ([acad9ca](https://gitlab.hd123.com/vue/soa/commit/acad9ca60b20f841375bea9fbb6ffa866c35cdce))

## [2.17.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.16.3...v2.17.0) (2022-08-26)

### Features

- ✨ SOP-7278 质量反馈隐藏赔付比例 ([fa828e7](https://gitlab.hd123.com:20022/vue/soa/commit/fa828e796be3b8fdd00ac72cfb96cdbed8f2be3c))
- ✨ SOP-7283 增加门店资金账户相关查看功能 ([d042c8e](https://gitlab.hd123.com:20022/vue/soa/commit/d042c8e67fdf79cac71b415c722e523df73d4d3f))

### Bug Fixes

- 🐛 SOP-7431 账户流水页界面问题 ([8ac9400](https://gitlab.hd123.com:20022/vue/soa/commit/8ac940057f1a7bf48b94550bc9f34bef10d21a7f))
- 🐛 SOP-7434 账户流水：最低金额输入 0 后进行筛选，筛选弹窗中对应值为空 ([2bf8081](https://gitlab.hd123.com:20022/vue/soa/commit/2bf808136ee69a1134bfe21f83581b9306fd7c73))
- 🐛 SOP-7437 账户流水：流水明细页界面问题 ([c93d013](https://gitlab.hd123.com:20022/vue/soa/commit/c93d013fbdc5f5ba60aca65b833120892d1baf03))

### [2.16.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.16.2...v2.16.3) (2022-08-17)

### Bug Fixes

- 🐛 SOP-7393 价格带模块位置调整 ([b7eeccf](https://gitlab.hd123.com:20022/vue/soa/commit/b7eeccfef5ec8a9b523cd1c050962d8691079774))

### [2.16.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.15.2...v2.16.2) (2022-08-15)

## [2.16.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.15.1...v2.16.0) (2022-08-12)

### Features

- ✨ SOP-7282 线路调整.督导管理门店范围权限通过数据授权门店关系实现 ([8ae33e9](https://gitlab.hd123.com:20022/vue/soa/commit/8ae33e984546f55fe3510f82cb3d0edae520ea88))
- ✨ SOP-7282 线路调整.督导管理门店范围权限通过数据授权门店关系实现 ([39a9522](https://gitlab.hd123.com:20022/vue/soa/commit/39a952254670fe74beb39b2e540d1a8b51dcfb16))
- ✨ SOP-7312 门店价格带优化 ([df564bb](https://gitlab.hd123.com:20022/vue/soa/commit/df564bbed9d6c7eae08ce642e4133420f6dc1e45))
- ✨ SOP-7312 门店价格带优化 ([d63069a](https://gitlab.hd123.com:20022/vue/soa/commit/d63069adc35d0b5c58bffb37daee584ca5d2132d))
- ✨ SOP-7312 门店价格带优化 ([3291231](https://gitlab.hd123.com:20022/vue/soa/commit/3291231d45210453ed5ca9ba04b7b53cde374af2))
- ✨ SOP-7312 门店价格带优化 自定义价格显示优化 ([554be36](https://gitlab.hd123.com:20022/vue/soa/commit/554be3677acc00988ad18551aa49ce58d398b613))

### Bug Fixes

- 🐛 SOP-7367 价格带详情页，切换门店和区域价格带 tab 后，列表显示数据范围重复 ([c849a78](https://gitlab.hd123.com:20022/vue/soa/commit/c849a783991391d52675b6972ddf196c70f8679d))
- 🐛 SOP-7371 价格方案标题输入框问题，编辑完成后回到搜索页面未刷新 ([bef311d](https://gitlab.hd123.com:20022/vue/soa/commit/bef311db713b170375e0341081e43270b5b18ff7))
- 🐛 SOP-7371 搜索页调整 ([a651fae](https://gitlab.hd123.com:20022/vue/soa/commit/a651faef3fcd520e8fa927404fa51c1ea4773018))
- 🐛 SOP-7372 价格带商品详情页添加删除弹窗显示问题 ([d7882b9](https://gitlab.hd123.com:20022/vue/soa/commit/d7882b95c063193ccfaa75c99e8a7c10b0db0fbe))
- 🐛 SOP-7374 价格带列表需要分开显示区域商品和门店商品数量 ([1e8331b](https://gitlab.hd123.com:20022/vue/soa/commit/1e8331b24ca232ec7ec46771818fec36f7356055))
- 🐛 SOP-7375 价格带列表未加载更多 ([6abb53b](https://gitlab.hd123.com:20022/vue/soa/commit/6abb53b2ec3b582d19b2d2295adb86e3a766e765))

### [2.16.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.16.0...v2.16.1) (2022-08-15)

### Features

- ✨ SOP-7312 门店价格带优化 ([df564bb](https://gitlab.hd123.com:20022/vue/soa/commit/df564bbed9d6c7eae08ce642e4133420f6dc1e45))

### Bug Fixes

- 🐛 SOP-7348 修复了订货提醒任务提交后任务状态没有改变，修复了店长看到督导可评价的任务后也存在评价按钮的问题 ([10eb8a2](https://gitlab.hd123.com:20022/vue/soa/commit/10eb8a2f076e1bcb51551b45384bcbc18c4c41be))
- 🐛 SOP-7367 价格带详情页，切换门店和区域价格带 tab 后，列表显示数据范围重复 ([c849a78](https://gitlab.hd123.com:20022/vue/soa/commit/c849a783991391d52675b6972ddf196c70f8679d))
- 🐛 SOP-7371 价格方案标题输入框问题，编辑完成后回到搜索页面未刷新 ([bef311d](https://gitlab.hd123.com:20022/vue/soa/commit/bef311db713b170375e0341081e43270b5b18ff7))
- 🐛 SOP-7371 搜索页调整 ([a651fae](https://gitlab.hd123.com:20022/vue/soa/commit/a651faef3fcd520e8fa927404fa51c1ea4773018))
- 🐛 SOP-7372 价格带商品详情页添加删除弹窗显示问题 ([d7882b9](https://gitlab.hd123.com:20022/vue/soa/commit/d7882b95c063193ccfaa75c99e8a7c10b0db0fbe))
- 🐛 SOP-7374 价格带列表需要分开显示区域商品和门店商品数量 ([1e8331b](https://gitlab.hd123.com:20022/vue/soa/commit/1e8331b24ca232ec7ec46771818fec36f7356055))
- 🐛 SOP-7375 价格带列表未加载更多 ([6abb53b](https://gitlab.hd123.com:20022/vue/soa/commit/6abb53b2ec3b582d19b2d2295adb86e3a766e765))

## [2.16.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.15.1...v2.16.0) (2022-08-12)

### Features

- ✨ SOP-7282 线路调整.督导管理门店范围权限通过数据授权门店关系实现 ([8ae33e9](https://gitlab.hd123.com:20022/vue/soa/commit/8ae33e984546f55fe3510f82cb3d0edae520ea88))
- ✨ SOP-7282 线路调整.督导管理门店范围权限通过数据授权门店关系实现 ([39a9522](https://gitlab.hd123.com:20022/vue/soa/commit/39a952254670fe74beb39b2e540d1a8b51dcfb16))

### [2.15.2](https://gitlab.hd123.com/vue/soa/compare/v2.15.1...v2.15.2) (2022-08-12)

### Bug Fixes

- 🐛 SOP-7348 修复了订货提醒任务提交后任务状态没有改变，修复了店长看到督导可评价的任务后也存在评价按钮的问题 ([10eb8a2](https://gitlab.hd123.com/vue/soa/commit/10eb8a2f076e1bcb51551b45384bcbc18c4c41be))

### [2.15.1](https://gitlab.hd123.com/vue/soa/compare/v2.15.0...v2.15.1) (2022-08-11)

### Features

- ✨ SOP-7347 将质量反馈可上传图片上限改为 6 张 ([850e553](https://gitlab.hd123.com/vue/soa/commit/850e5533b1727c6f0da1f32079057039f7dd5716))

## [2.15.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.14.6...v2.15.0) (2022-08-01)

### Features

- ✨ SOP-7098 直送退货支持价格改高 ([6699240](https://gitlab.hd123.com:20022/vue/soa/commit/66992406a98711e42c704b0f901763e58e79bed9))
- ✨ SOP-7100 订货支持下拉滚动切换上一分类 ([4d2daa2](https://gitlab.hd123.com:20022/vue/soa/commit/4d2daa29bf1d9de5640e646503c3614debebec25))
- ✨ SOP-7116 修改了质量反馈中申请原因描述的提示文案 ([c0d7bd7](https://gitlab.hd123.com:20022/vue/soa/commit/c0d7bd7b69ef67727cfd7626cfd7965ff0962ab0))
- ✨ SOP-7124 新增重点商品检查类任务 ([c0ea26a](https://gitlab.hd123.com:20022/vue/soa/commit/c0ea26afdbc133475419a60882d1b6f8e0f31ddb))
- ✨ SOP-7239 质量反馈增加上传图片上限，无上传视频 ([fe3b45b](https://gitlab.hd123.com:20022/vue/soa/commit/fe3b45b5125145dec4c3a7fab68af668faa7a5ca))
- ✨ SOP-7239 质量反馈增加图片上传数量上限 ([126f4b8](https://gitlab.hd123.com:20022/vue/soa/commit/126f4b8d74580f7ace16898c2ae822332967a188))

### Bug Fixes

- 🐛 修复了父任务反馈图片展示问题 ([cb71577](https://gitlab.hd123.com:20022/vue/soa/commit/cb71577a9b76694ef3ae50188ae845fc1b6f5c6c))
- 🐛 修复了上传图片上限后，提示上传没有隐藏的问题 ([d59e8c0](https://gitlab.hd123.com:20022/vue/soa/commit/d59e8c01ff53cbc5fc8c8d07b8803700b01266ff))
- 🐛 修复了重点商品陈列任务整改任务待评价查看父任务反馈缺少完成反馈的问题 ([132c6f6](https://gitlab.hd123.com:20022/vue/soa/commit/132c6f6968cd295287df5cc7c99d567116589692))
- 🐛 SOP-7151 获取 OSS 签名优化 ([92bf8b7](https://gitlab.hd123.com:20022/vue/soa/commit/92bf8b7c05d34449d97bde81a8ddf78695fd5f3a))
- 🐛 SOP-7151 获取 OSS 签名优化 ([4defa32](https://gitlab.hd123.com:20022/vue/soa/commit/4defa32db586f04c597b3581aadc4120e85dfcd2))
- 🐛 SOP-7151 获取 OSS 签名优化使用 uni 默认 toast 组件 ([e78f743](https://gitlab.hd123.com:20022/vue/soa/commit/e78f74327c4244f309ae42ace726cdd1e15ac41f))
- 🐛 SOP-7167 修复了图标透明的样式；SOP-7174 修复了整改任务单的展示;SOP-7191 修复了领取人错误 ([80b6fa3](https://gitlab.hd123.com:20022/vue/soa/commit/80b6fa309e078f84927d5cd9a654cc1c5081272c))
- 🐛 SOP-7187 修复了重点商品查看父任务反馈未展示完成反馈的问题，修复了转交退回后领取人唯一的问题 ([37067a8](https://gitlab.hd123.com:20022/vue/soa/commit/37067a8a640867665882a9ad83048acccd9f623b))
- 🐛 SOP-7248 门店助手增加历史评价 ([f529286](https://gitlab.hd123.com:20022/vue/soa/commit/f5292869b8fcfb01de5cc3f00745c2f80507be87))

### [2.14.6](https://gitlab.hd123.com:20022/vue/soa/compare/v2.14.5...v2.14.6) (2022-07-31)

### Bug Fixes

- 🐛 SOP-7260 加工模块乱码问题 ([7c27e55](https://gitlab.hd123.com:20022/vue/soa/commit/7c27e55a2eee93dd93c75f53ba226d8b277402d7))

### [2.14.5](https://gitlab.hd123.com/vue/soa/compare/v2.14.4...v2.14.5) (2022-07-23)

### Bug Fixes

- 🐛 SOP-7158 首页查询组织变更结果微信平台兼容自定义 tabbar ([26bdf0c](https://gitlab.hd123.com/vue/soa/commit/26bdf0c4e63125d1b470713925d2688e4f5a3875))

### [2.14.4](https://gitlab.hd123.com/vue/soa/compare/v2.14.3...v2.14.4) (2022-07-19)

### [2.14.3](https://gitlab.hd123.com/vue/soa/compare/v2.14.0...v2.14.3) (2022-07-18)

### Features

- ✨ SOP-7104 微信端增加自定义 tabbar 支持将数据模块调整至其他分包 ([2c82ff5](https://gitlab.hd123.com/vue/soa/commit/2c82ff50c15740b5b31c15dbcaab48df36723104))

### Bug Fixes

- 🐛 SOP-7104 Tabbar 组件支持设置徽标 ([575a867](https://gitlab.hd123.com/vue/soa/commit/575a8674d267fbe1d466d8412b1fad26040cf18d))
- 🐛 SOP-7117 Tabbar 组件修复重新进入小程序后无法定位到对应页面的问题 ([93dfdc2](https://gitlab.hd123.com/vue/soa/commit/93dfdc2ba700ef5747385bbeb288b14d5350d92e))

## [2.14.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.13.0...v2.14.0) (2022-07-15)

### Features

- ✨ SOP-6971 收货支持查看商品备注 ([bc61b8e](https://gitlab.hd123.com:20022/vue/soa/commit/bc61b8e3c950a6101089942744e049f39eedbc77))
- ✨ SOP-6973 水印颜色调整为较浅 ([3e2e855](https://gitlab.hd123.com:20022/vue/soa/commit/3e2e855dc986953bdf19043d5ebd9b54cf9dd40a))
- ✨ SOP-6973 支持显示门店信息水印 ([aece7be](https://gitlab.hd123.com:20022/vue/soa/commit/aece7be250f897c4a4c3af6c2ca060d634aeb752))
- ✨ SOP-6973 支持显示门店信息水印 ([131916e](https://gitlab.hd123.com:20022/vue/soa/commit/131916e4f28b0e40e1de43cfa46b84c943fdd9cb))
- ✨ SOP-7001 质量反馈历史反馈拒绝原因与备注显示逻辑调整 ([4fcb1a5](https://gitlab.hd123.com:20022/vue/soa/commit/4fcb1a5dac1b536f4e9e89f1ed9ff68b40a157b7))
- ✨ SOP-7014 巡检整改任务的完成反馈根据点检项配置决定哪些是必填内容 ([854ec81](https://gitlab.hd123.com:20022/vue/soa/commit/854ec81bd05aa306f0ba7fcc00c953a5504c9ecb))
- ✨ SOP-7019 门店费用代扣优化 ([136ed30](https://gitlab.hd123.com:20022/vue/soa/commit/136ed30fd0dfc6ee0c26daa5b5d9d77b1b97c2c8))

### Bug Fixes

- 🐛 sop-6971 出货备注取 note ([8c1ce9c](https://gitlab.hd123.com:20022/vue/soa/commit/8c1ce9ce65e3bab15f2a6f3f7fecf9f79a7cdb36))
- 🐛 SOP-6971 两个备注在界面上下合并显示 ([6d51878](https://gitlab.hd123.com:20022/vue/soa/commit/6d5187886fde2da539ac8df414a50420608372d6))
- 🐛 SOP-7080 独立订货 loading 优化 ([e08f2f5](https://gitlab.hd123.com:20022/vue/soa/commit/e08f2f52047e78f3c92b27bfbbf09fcf942a5a03))

### [2.13.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.13.1...v2.13.2) (2022-07-18)

### Bug Fixes

- 🐛 SOP-7118 账单管理门店传门店代码给鼎付通 ([34d9147](https://gitlab.hd123.com:20022/vue/soa/commit/34d9147a05fd3032f0e01580d110563ab4a29ed2))

### [2.13.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.13.0...v2.13.1) (2022-07-12)

### Bug Fixes

- 🐛 SOP-7080 独立订货 loading 优化 ([e08f2f5](https://gitlab.hd123.com:20022/vue/soa/commit/e08f2f52047e78f3c92b27bfbbf09fcf942a5a03))

## [2.13.0](https://gitlab.hd123.com/vue/soa/compare/v2.12.0...v2.13.0) (2022-07-06)

### Features

- ✨ 首页增加用户组织校验，变动则引导退出登录 ([c1acdfd](https://gitlab.hd123.com/vue/soa/commit/c1acdfdc74d59e7ca3855522986c47e667ba07b1))
- ✨ SOP-6147 新增账单管理模块 ([55970d5](https://gitlab.hd123.com/vue/soa/commit/55970d5773c4f89f045c6ffbef0e76ad833b2f50))
- ✨ SOP-6147 新增账单管理模块 ([c15a63a](https://gitlab.hd123.com/vue/soa/commit/c15a63a911a10fb022177e1fed83fcd93bafcfbc))
- ✨ SOP-6502 增加线路调整模块 ([90752d2](https://gitlab.hd123.com/vue/soa/commit/90752d21f13022e816ea7113d1b44a9355c31da2))
- ✨ SOP-6770 退货业务支持录入商品的生产日期判断是否允许退货 ([78104e6](https://gitlab.hd123.com/vue/soa/commit/78104e6e0e31807e35ea8765263c605240ee5359))
- ✨ SOP-6863 标准订货显示商品规格数 ([af085e3](https://gitlab.hd123.com/vue/soa/commit/af085e30817e356acafca013774d508b6bac78f2))
- ✨ SOP-6874 门店助手新增待评价功能相关改造 ([d631bb8](https://gitlab.hd123.com/vue/soa/commit/d631bb8d9fc88f40357bf87f2a03b6d5e868034f))
- ✨ SOP-6874 门店助手新增待评价任务模块接收人展示详情问题修复 ([fd53536](https://gitlab.hd123.com/vue/soa/commit/fd5353633a982050cbac791a6d7cf39d402eec85))
- ✨ SOP-6874 详情页按钮权限调整 ([c602b1c](https://gitlab.hd123.com/vue/soa/commit/c602b1c2b18301740b291d1021f1492057494cfe))
- ✨ SOP-6889 新增不适用功能中整改任务查看父任务的详情不适用不属实图片修改 ([13d4696](https://gitlab.hd123.com/vue/soa/commit/13d469672da0be6e970e67196f233b99e822d698))
- ✨ SOP-6889 新增点检项任务处理评价的是否使用是否属实功能 ([61ae0d3](https://gitlab.hd123.com/vue/soa/commit/61ae0d3b0f76c92e7e009b816dfc9aed519e9f6a))
- ✨ SOP-6891 门店提交点检项提交评价逻辑优化 ([830d45b](https://gitlab.hd123.com/vue/soa/commit/830d45bb5135d0641c86265f2a7723c7e57576ef))

### Bug Fixes

- 🐛 线路调整门店按组织过滤 ([7cc6efd](https://gitlab.hd123.com/vue/soa/commit/7cc6efd5ebc022c81a3624b4b614d88c5007b151))
- 🐛 修复了点击查看点检项小项选择时层级问题 ([62887ca](https://gitlab.hd123.com/vue/soa/commit/62887cab591478288b74035ca31c54a8504848ee))
- 🐛 SOP-6863 标准订货显示商品规格数前端取整 ([8e54efe](https://gitlab.hd123.com/vue/soa/commit/8e54efe4b8037e65d68338b8e660d95b446b8286))
- 🐛 SOP-6874 修复了门店助手详情页退回按钮展示的条件还有列表的气泡问题 ([f2bd112](https://gitlab.hd123.com/vue/soa/commit/f2bd1124a4533d978ede63863838e1b0111868d5))
- 🐛 SOP-6874 修复了派发岗位引入的门店助手不同任务类型待评价详情页提交等问题 ([2ff5a48](https://gitlab.hd123.com/vue/soa/commit/2ff5a48befae3d8d4277f0fb99fe4a42d2938577))
- 🐛 SOP-6874 修复了退回领取按钮的样式不生效问题 ([e4df387](https://gitlab.hd123.com/vue/soa/commit/e4df387739f0652a498d52db6cda4f7a4e1a130e))
- 🐛 SOP-6874 修复了转交人在待领取和待处理不同情况的过滤 ([e2702a5](https://gitlab.hd123.com/vue/soa/commit/e2702a510023dc4c3439827b2343f78e40e50f59))
- 🐛 SOP-6874 转交人选择中选择岗位后再次选择岗位，默认选中上次选择的岗位 ([d06fdaf](https://gitlab.hd123.com/vue/soa/commit/d06fdaf183423990d96f86e8cd678ff20f0bdd73))
- 🐛 SOP-6891 修复了提交提示出来是 toast 仍然弹出的问题 ([f3f73e7](https://gitlab.hd123.com/vue/soa/commit/f3f73e72aca3e4bd29fe6d804194d0665a118c41))
- 🐛 SOP-6896 退仓模块增加运费信息的计算、展示 ([d2faaa4](https://gitlab.hd123.com/vue/soa/commit/d2faaa418ee2a1eaebf2961c7af2217e2db61c7b))
- 🐛 SOP-6899 标准订货切换下一类别优化 ([f598e4e](https://gitlab.hd123.com/vue/soa/commit/f598e4eb6b6e1e870301ffa3f67f93525f0e3b07))
- 🐛 SOP-6901 退货业务支持录入商品的生产日期判断是否允许退货.返回商品页面，生产日期/到效日期需要保留 ([df375fa](https://gitlab.hd123.com/vue/soa/commit/df375fa204d59f7b2898c4818d1bef276a2b9bec))
- 🐛 SOP-6908 退货业务支持录入商品的生产日期判断是否允许退货.编辑退货申请.返回商品列表页面，退货方式会显示 ([29f2c71](https://gitlab.hd123.com/vue/soa/commit/29f2c710f914ee6ac9be906a6d232fd48c80caaa))
- 🐛 SOP-6949 线路调整-移动门店提示框和添加门店修改下路提示框样式与 UI 不符 ([3eb6bd2](https://gitlab.hd123.com/vue/soa/commit/3eb6bd2ce581964f4b9e0ea3d22c080a60ef0b55))
- 🐛 SOP-6950 线路调整-门店只有一条线路，只展示【移动】按钮，实际展示了移动和删除 ([fa92370](https://gitlab.hd123.com/vue/soa/commit/fa92370149203c58f17d54361f922b8f086d6fb9))
- 🐛 SOP-6958、SOP-6960 修复了转交后接收人展示问题和非店长看不到转交按钮的问题 ([5bcec0e](https://gitlab.hd123.com/vue/soa/commit/5bcec0e366ce78b89537740cf7641e65378ea034))
- 🐛 SOP-6975 修复了待评价执行人不正确的问题 ([7d12c2f](https://gitlab.hd123.com/vue/soa/commit/7d12c2f2b2b11a8810ce3d82e3782d900d39b67f))
- 🐛 SOP-6997 修复了巡检整改单待处理中仍有是否适用选项的问题 ([a259727](https://gitlab.hd123.com/vue/soa/commit/a259727fe4589fca9e3807560c7c9dc6956499c4))

## [2.12.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.11.2...v2.12.0) (2022-06-17)

### Features

- ✨ SOP-6250 订货在线支付增加运费信息的计算、展示 ([2cc3335](https://gitlab.hd123.com:20022/vue/soa/commit/2cc3335395935100d05760471bef13b23a4b1c04))
- ✨ SOP-6772 收货与收货明细界面展示效期 ([82f8e03](https://gitlab.hd123.com:20022/vue/soa/commit/82f8e03c271004f225192d8471be6e7bf5d9bf10))
- ✨ SOP-6774 针对捷强客户增加隐藏部分功能的逻辑 ([c8afb64](https://gitlab.hd123.com:20022/vue/soa/commit/c8afb64db9f65314b0d5c65cd2f364a5abedf084))

### Bug Fixes

- 🐛 订货运费取值错误 ([819a3ed](https://gitlab.hd123.com:20022/vue/soa/commit/819a3ed05eb70807b274a011f3e87c9d8594cf7f))
- 🐛 SOP-6834 直送收货和直送退货模块.配置"useGift": "false"时，页面优化 ([9d6baf0](https://gitlab.hd123.com:20022/vue/soa/commit/9d6baf0368a302a75317acff5e6e5c6c70101c17))
- 🐛 SOP-6849 退仓模块.详情页面.运费展示问题 ([7ff2a0b](https://gitlab.hd123.com:20022/vue/soa/commit/7ff2a0b2a38e5197f1c308f6907e1aa5fb821c6a))
- 🐛 SOP-6860 修复了任务中心领取任务前清空列表，领取任务失败后没有重新获取列表的问题 ([6047222](https://gitlab.hd123.com:20022/vue/soa/commit/60472223461bb1d8ad5f8b9f3c0c1ec115f8592c))

### [2.11.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.11.1...v2.11.2) (2022-06-15)

### [2.11.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.11.0...v2.11.1) (2022-06-10)

### Features

- ✨ SOP-6177 门店标准订货走草稿模式 ([5b22f4d](https://gitlab.hd123.com:20022/vue/soa/commit/5b22f4d7a80cde50ec898827efd6486aa3b13e92))
- ✨ SOP-6251 订货界面展示优化 ([482ce17](https://gitlab.hd123.com:20022/vue/soa/commit/482ce17d2497d2448ae09741ac4e0f2fa3ecbcae))
- ✨ SOP-6424 门店叫货加盟订货优化 ([091fe8b](https://gitlab.hd123.com:20022/vue/soa/commit/091fe8b85f3901d7ac1a9278131ae6f48c1ab46e))
- ✨ SOP-6424 门店叫货加盟订货走草稿模式 ([d922781](https://gitlab.hd123.com:20022/vue/soa/commit/d9227815f551e485b141abf52a69f66f62e6c363))
- ✨ SOP-6680 门店效期订货走草稿模式 ([8f117fc](https://gitlab.hd123.com:20022/vue/soa/commit/8f117fcfae8a392f6f73bc1604b3f2316aa3eff2))

### Bug Fixes

- 🐛 订货点击步进器时切换了下一分类 ([69f3bb4](https://gitlab.hd123.com:20022/vue/soa/commit/69f3bb4eb6a47527bc7a17831193673da8a39a85))
- 🐛 订货再来一单草稿 id 错误 ([871f898](https://gitlab.hd123.com:20022/vue/soa/commit/871f898a46d246c1f0b3b1aedd06e4757b40f637))
- 🐛 独立订货库存不足时页面信息更新 ([c9b9ad2](https://gitlab.hd123.com:20022/vue/soa/commit/c9b9ad27ca03dfa38a2e6dac2dafcf63e850b061))
- 🐛 SOP-6251 订货无商品时也支持切换分类 ([647e8e6](https://gitlab.hd123.com:20022/vue/soa/commit/647e8e6353836f1260283388ef3597abc9ecdf4b))
- 🐛 SOP-6451 门店叫货加盟订货优化.请选择页数弹框，默认定位显示不正确 ([65e003e](https://gitlab.hd123.com:20022/vue/soa/commit/65e003e46e607c9a727465d687ac42fbba303dbd))
- 🐛 SOP-6456 加盟订货优化.修改商品数量后，合计的商品件数显示不正确 ([9ab690b](https://gitlab.hd123.com:20022/vue/soa/commit/9ab690ba21ec1aaee00a0e61191da5ab902b6387))
- 🐛 SOP-6473 购物车页面，没有分类的商品未显示 ([fb513fb](https://gitlab.hd123.com:20022/vue/soa/commit/fb513fbf3c48dbbcb4281ef18ef9935703d80fa2))
- 🐛 SOP-6539 门店叫货加盟订货优化.商品搜索列表返回分类列表，分页器显示错误 ([a78844a](https://gitlab.hd123.com:20022/vue/soa/commit/a78844a1d09ea9a1756e4ba70b72f2dd9fc00e94))
- 🐛 SOP-6543 门店叫货加盟订货优化.商品搜索列表，点击【取消】按钮实现错误 ([afa7585](https://gitlab.hd123.com:20022/vue/soa/commit/afa75854b9b3241292ca88092f246acc456de716))
- 🐛 SOP-6546 加盟订货优化.购物车页面，删除商品列表显示优化 ([cb7dbcd](https://gitlab.hd123.com:20022/vue/soa/commit/cb7dbcdb880adc58ac56f80273299cd2385f2a57))
- 🐛 SOP-6556 门店叫货加盟订货优化.购物车页面，分页器显示问题汇总 ([0939c44](https://gitlab.hd123.com:20022/vue/soa/commit/0939c4426bcbb6553aabe1649dce2cfccd0e8e39))
- 🐛 SOP-6561 商品删除 remove 接口调用问题汇总 ([5bce522](https://gitlab.hd123.com:20022/vue/soa/commit/5bce522b6a5a0a3c2bfcfab4d6b196ebfd2157d6))
- 🐛 SOP-6564 门店叫货加盟订货优化.点击提交按钮，接口调用问题汇总 ([30b4633](https://gitlab.hd123.com:20022/vue/soa/commit/30b4633e37f6e1f567f638bc06aa8438908fb0b8))
- 🐛 SOP-6565 库存不足弹框中，商品全部删除成功，应该不能提交成功 ([cdd5754](https://gitlab.hd123.com:20022/vue/soa/commit/cdd57549f68f42387e67d195681e0178f40fdcbb))
- 🐛 SOP-6566 购物车保存单据，保存接口传参错误 ([60f21e9](https://gitlab.hd123.com:20022/vue/soa/commit/60f21e9c7f9f427379a002125963db8144d83178))
- 🐛 SOP-6576 购物车页面，删除商品后获取列表接口传参问题汇总 ([6385f59](https://gitlab.hd123.com:20022/vue/soa/commit/6385f5931fcae89cfe0bb1ffdcb457de6c632913))
- 🐛 SOP-6576 购物车页面，删除商品后获取列表接口传参问题汇总 ([e6a5842](https://gitlab.hd123.com:20022/vue/soa/commit/e6a5842b02b55c3d76558ea26033c1500fd2834f))
- 🐛 SOP-6578 商品搜索页面，点击取消按钮，调用的 save 接口传参错误 ([21716f9](https://gitlab.hd123.com:20022/vue/soa/commit/21716f90f672b5d71c293490e6971a5a3fe5944e))
- 🐛 SOP-6582 订货购物车逻辑调整 ([fda0471](https://gitlab.hd123.com:20022/vue/soa/commit/fda047159ae65f52f756ac859559f189c4afcf9d))
- 🐛 SOP-6590 门店叫货.草稿模式兼容再来一单 ([f752ea3](https://gitlab.hd123.com:20022/vue/soa/commit/f752ea3dfaa71b41abcfa204efb3a8ff9803d112))
- 🐛 SOP-6592 订货优化.初始进入页面显示 loading 问题汇总 ([b7fee9e](https://gitlab.hd123.com:20022/vue/soa/commit/b7fee9e5c8f1aeab29cafa94a0d331c1e99d20f8))
- 🐛 SOP-6592 门店叫货加盟订货优化.初始进入页面显示 loading 问题汇总 ([6b8315c](https://gitlab.hd123.com:20022/vue/soa/commit/6b8315c2b8f3f3f81e2e7f8fd74393fab7010b67))
- 🐛 SOP-6606 建议叫货，页面中保存按钮在某种情况下显示不正确 ([c86e67e](https://gitlab.hd123.com:20022/vue/soa/commit/c86e67e2d7cd8e1e32c914e1f0cac84353abfeaa))
- 🐛 SOP-6608 建议叫货，分类商品列表，勾选商品行，save 接口传参错误 ([4922622](https://gitlab.hd123.com:20022/vue/soa/commit/4922622d884ca1c6b4fac39596c2e2dcbee90296))
- 🐛 SOP-6609 添加商品页面返回商品列表页面，商品列表页面未刷新 ([3064d03](https://gitlab.hd123.com:20022/vue/soa/commit/3064d031bc8e77dcff7d36a67b9c077b588e28db))
- 🐛 SOP-6618 门店叫货加盟订货优化.商品列表查询加载实现优化 ([e7c9abf](https://gitlab.hd123.com:20022/vue/soa/commit/e7c9abf63611c23f55a5c373bd7c4a99a188f847))
- 🐛 SOP-6631 门店叫货优化.提交订货，提交接口报错（传参错 ([3e1a63e](https://gitlab.hd123.com:20022/vue/soa/commit/3e1a63e8319d1c4b5346455663ab0a9e0d4f5557))
- 🐛 SOP-6641 加盟订货优化.分类商品列表，未存在商品数量编辑，切换分类存在问题 ([66a5c66](https://gitlab.hd123.com:20022/vue/soa/commit/66a5c66d0093970041e1b9dc9e4bc94cd3ae5492))
- 🐛 SOP-6645 门店叫货优化.提交订货，接口调用和页面显示问题 ([1b44404](https://gitlab.hd123.com:20022/vue/soa/commit/1b4440439f6dcb70b055479487658796884b9f7d))
- 🐛 SOP-6646 门店叫货优化.非订货时间内，点击弹框中【保存并返回】按钮，接口调用和页面显示问题 ([e94d05b](https://gitlab.hd123.com:20022/vue/soa/commit/e94d05b0bf02e4d71856e9d5dbbd1c17ea52ba56))
- 🐛 SOP-6649 门店叫货加盟订货优化.编辑单据页面，保存提示优化 ([2d062e4](https://gitlab.hd123.com:20022/vue/soa/commit/2d062e4d8c51811f3b7065246d83c1a424cc1515))
- 🐛 SOP-6651 建议叫货，商品列表删除商品，问题汇总 ([aa068a3](https://gitlab.hd123.com:20022/vue/soa/commit/aa068a3d498526a1fb8213063a7a263ed044fbdf))
- 🐛 SOP-6652 建议叫货，商品列表中非自主添加的商品，不允许删除 ([84ebf05](https://gitlab.hd123.com:20022/vue/soa/commit/84ebf0516c349114638c51b29f03a8ccc1c66752))
- 🐛 SOP-6652、SOP-6653 建议叫货 bug 修复 ([b37edf8](https://gitlab.hd123.com:20022/vue/soa/commit/b37edf869c34664e09d38bb3950005d9b0f28511))
- 🐛 SOP-6655 建议叫货，商品列表中非自主添加的商品，商品数量为 0 后 save 接口传参错误 ([73beff1](https://gitlab.hd123.com:20022/vue/soa/commit/73beff1131ff1912449257a01cc7e22d8366a8b8))
- 🐛 SOP-6656 建议叫货，点击保存按钮，页面显示问题汇总 ([8efa729](https://gitlab.hd123.com:20022/vue/soa/commit/8efa7297d0be4fe4f999cb1fc851213d51982d08))
- 🐛 SOP-6660 订货优化.页面点击返回按钮，弹框问题显示汇总 ([7c68bc4](https://gitlab.hd123.com:20022/vue/soa/commit/7c68bc4c7cd07723a4778b43eae8d0e5b2d178ed))
- 🐛 SOP-6663 建议叫货，点击备注弹框中提交按钮，问题汇总 ([5258210](https://gitlab.hd123.com:20022/vue/soa/commit/52582103bd84b1537d8b9313c9cf4736585082de))
- 🐛 SOP-6666 门店叫货加盟订货优化.新增正常订货，页面报“未指定草稿标识” ([43b3963](https://gitlab.hd123.com:20022/vue/soa/commit/43b39634909f6c355707f481ef8e3783210d0949))
- 🐛 SOP-6667 门店叫货加盟订货优化.商品列表查询加载问题汇总 ([e4cf573](https://gitlab.hd123.com:20022/vue/soa/commit/e4cf573a3201ab9893428d3ce134134ec6f1de9d))
- 🐛 SOP-6681 建议叫货，点击页面返回按钮，问题汇总 ([418deea](https://gitlab.hd123.com:20022/vue/soa/commit/418deeaa033ce859836844593b0673acd6cbe16c))
- 🐛 SOP-6686 门店叫货优化.购物车页面，调用 save 接口时页面显示问题 ([f9c83ef](https://gitlab.hd123.com:20022/vue/soa/commit/f9c83ef22f8f6c611bd71b6ba8db9e3d22f36738))
- 🐛 SOP-6690 门店叫货优化.提交可订数不足时接口调用，页面显示存在问题 ([ecaf787](https://gitlab.hd123.com:20022/vue/soa/commit/ecaf787cb5a2bf42621e106c996eb6cc8a3b2c44))
- 🐛 SOP-6691 门店叫货优化.点击可订数不足弹框的关闭按钮，返回页面，商品数据显示不正确 ([ecb5aac](https://gitlab.hd123.com:20022/vue/soa/commit/ecb5aacd51efd0f59d328e6d4dbaf804b31a7a43))
- 🐛 SOP-6694 门店叫货加盟订货优化.购物车页面，向上加载商品列表时，期望页面显示 loading ([f5a4d6b](https://gitlab.hd123.com:20022/vue/soa/commit/f5a4d6b12080ed44739af824c98996d924171a97))
- 🐛 SOP-6697 门店叫货优化.点击可订数不足弹框的关闭按钮，返回页面合计金额显示不正确 ([2f9e933](https://gitlab.hd123.com:20022/vue/soa/commit/2f9e933a20c6eda23d5f0529f142e5555f63c7f2))
- 🐛 SOP-6698 门店叫货优化.存在商品可订数变为 0，页面问题汇总 ([bd88498](https://gitlab.hd123.com:20022/vue/soa/commit/bd88498a02880f699866ec3f866db01ee945600a))
- 🐛 SOP-6701 ([f6939dc](https://gitlab.hd123.com:20022/vue/soa/commit/f6939dc2273036a6cd1f7967e5b934c5f07a4a40))
- 🐛 SOP-6705 门店叫货优化.商品搜索页面点击提交按钮，存在商品下架，商品搜索列表未更新成功 ([3f1eaaf](https://gitlab.hd123.com:20022/vue/soa/commit/3f1eaaf9364d051022ed255efc4e44c70e0cf923))
- 🐛 SOP-6708 购物车页面，商品步进器按钮问题 ([7d338c5](https://gitlab.hd123.com:20022/vue/soa/commit/7d338c53b38ebfb1cd3ff3ad0416b051baa1f1a8))
- 🐛 SOP-6711 购物车页面，新增的时候点击【保存/提交】按钮，调用的接口不正确 ([5c891aa](https://gitlab.hd123.com:20022/vue/soa/commit/5c891aab9dd9725581b4cab9bd0cf8cabe4e7a4c))
- 🐛 SOP-6715 门店叫货优化.商品搜索页面，存在下架商品，弹出的弹框中订货品项数和订货金额不正确 ([c9d675f](https://gitlab.hd123.com:20022/vue/soa/commit/c9d675fffc91da350a02df94e144e412336dc4f9))
- 🐛 SOP-6725 效期订货.商品搜索页面.商品效期行步进器+/-按钮问题 ([a85e8b6](https://gitlab.hd123.com:20022/vue/soa/commit/a85e8b680919d5b0a67eb16737fb52d8b68132c6))
- 🐛 SOP-6728 门店叫货优化.购物车页面，余额不足时返回购物车页面，页面中一直显示 loading ([7ef5219](https://gitlab.hd123.com:20022/vue/soa/commit/7ef52192bec5bea4ca1f9eba5898631c9a2676ab))
- 🐛 SOP-6731 门店叫货优化.购物车页面，弹出的可订数不足弹框显示不正确 ([347e60f](https://gitlab.hd123.com:20022/vue/soa/commit/347e60f1972f9eab96db5db97966b6f65cb78c25))
- 🐛 SOP-6736 商品效期数据刷新 ([6e5329f](https://gitlab.hd123.com:20022/vue/soa/commit/6e5329f08b716d5d33e348c52877f6de64509363))
- 🐛 SOP-6736 效期订货.商品效期库存不足点击【提交】按钮，页面没有弹出效期库存不足提示弹窗 ([5d76057](https://gitlab.hd123.com:20022/vue/soa/commit/5d76057f4919a2eaccf26b9812ce7913a114bcb4))
- 🐛 SOP-6744 门店叫货优化.商品列表，存在商品价格变化，问题汇总 ([2c0c922](https://gitlab.hd123.com:20022/vue/soa/commit/2c0c92282423c6f29d017472aa364454e1b05092))
- 🐛 SOP-6744 门店叫货优化.商品列表，存在商品价格变化，问题汇总 ([0032851](https://gitlab.hd123.com:20022/vue/soa/commit/00328518a9102d15f06d0455c9f1009b7d192ec9))
- 🐛 SOP-6751 门店效期订货.效期商品调用/draft/line/remove 接口成功后，页面步进器显示问题 ([68e99c0](https://gitlab.hd123.com:20022/vue/soa/commit/68e99c088fcd2c6951dd11ebe684c7a93eaddffe))
- 🐛 SOP-6753 门店效期订货.订货页面底部加购商品金额合计问题 ([f1be3ac](https://gitlab.hd123.com:20022/vue/soa/commit/f1be3ac334cc77e0311c6127ab55b659b9ea22fc))
- 🐛 SOP-6754 门店叫货优化.购物车页面，分类过多页面显示不正确 ([3019fc3](https://gitlab.hd123.com:20022/vue/soa/commit/3019fc3c73bd988b28882f7ee46b7283788d4489))
- 🐛 SOP-6764 效期订货.库存不足弹窗，修改商品加购数量，/order/draft/save 接口传参问题 ([59652a9](https://gitlab.hd123.com:20022/vue/soa/commit/59652a9d3f3c9384e01556bb98468ac52807e188))
- 🐛 SOP-6767 门店叫货优化.购物车页面，商品数量编辑为 0，问题汇总 ([754c9e0](https://gitlab.hd123.com:20022/vue/soa/commit/754c9e09eae9c63deb0314a40f29caa62fe6006b))
- 🐛 SOP-6779 效期订货.效期库存提示弹窗问题汇总 ([063b9ed](https://gitlab.hd123.com:20022/vue/soa/commit/063b9ed94f29f10848ac45bfafb9bc92ee0f3618))
- 🐛 SOP-6780 小程序.门店助手.编辑页面.分类列表页面加购商品数据展示问题 ([330ae8d](https://gitlab.hd123.com:20022/vue/soa/commit/330ae8d3977253c29ad1b21fab53e9d9abe24957))
- 🐛 SOP-6783 门店叫货优化.分类商品列表页面，分类获取接口返回为空/失败，页面显示不正确 ([023da16](https://gitlab.hd123.com:20022/vue/soa/commit/023da16e9c62f18d2703dd16180086f97cf5c193))
- 🐛 SOP-6784 分类点击显示问题 ([c637c37](https://gitlab.hd123.com:20022/vue/soa/commit/c637c3737731d9a15f575594470e78df0b2fbfe2))
- 🐛 SOP-6784 门店叫货优化.分类商品列表页面，切换分类过程中，不允许再点击其他分类 ([43c6a33](https://gitlab.hd123.com:20022/vue/soa/commit/43c6a33b8e34248b7d0b89b2f975cfa8584a0836))
- 🐛 SOP-6786 效期订货.编辑页面进入购物车；购物车页面相关问题 ([26f1840](https://gitlab.hd123.com:20022/vue/soa/commit/26f184079aa4ee959f13290b69fa1af607f0f418))
- 🐛 SOP-6786 效期订货.编辑页面进入购物车；购物车页面相关问题 ([046318b](https://gitlab.hd123.com:20022/vue/soa/commit/046318b68540dd9feafe0893f742f27ad751e462))
- 🐛 SOP-6786 效期订货.编辑页面进入购物车；购物车页面相关问题 ([2d6f715](https://gitlab.hd123.com:20022/vue/soa/commit/2d6f715034016e4f625db358fdf27562f2301157))
- 🐛 SOP-6787 门店叫货优化.分类商品列表页面，存在商品编辑下切换分类，调用 save 接口时页面没有显示 load ([fabda56](https://gitlab.hd123.com:20022/vue/soa/commit/fabda56e0a158bef3edf29c72c114143f64bdfda))
- 🐛 SOP-6788 门店叫货优化.分类商品列表页面，分页切换查看列表问题汇总 ([d7cb31c](https://gitlab.hd123.com:20022/vue/soa/commit/d7cb31c906dd78507928942521d544835d199b36))
- 🐛 SOP-6790 效期订货.再来一单.单据中存在的已加购商品.数量相关问题 ([2032493](https://gitlab.hd123.com:20022/vue/soa/commit/20324935e7c61b01e927f07021f7dfc6c4b2f6c0))
- 🐛 SOP-6798 门店叫货优化.商品列表返回分类列表，需要调用接口更新分类角标 ([9964b81](https://gitlab.hd123.com:20022/vue/soa/commit/9964b816bc39e0a53b4d905ba9602d04f0d7cdba))
- 🐛 SOP-6798 门店叫货优化.商品列表返回分类列表，需要调用接口更新分类角标 ([0fc4d83](https://gitlab.hd123.com:20022/vue/soa/commit/0fc4d8382d9cd824b69ce62216dd9cc3a1a78d67))

## [2.11.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.10.0...v2.11.0) (2022-06-02)

### Features

- ✨ 修改了新增标价签申请界面扫码进入搜索商品时的传参获取、搜索框文案修改 ([cf899e9](https://gitlab.hd123.com:20022/vue/soa/commit/cf899e9977d6194d0e41105adaffb3c7a4cc523f))
- ✨ SOP-6500 公司间退货单和配货出货退货单显示退款标识 ([2762465](https://gitlab.hd123.com:20022/vue/soa/commit/27624651a3deb0bf1e3dada1aca36247ecae5ee3))
- ✨ SOP-6525 标价签申请 ([3aa28e6](https://gitlab.hd123.com:20022/vue/soa/commit/3aa28e66840d7bb940958dd8d3e1b1417a512d99))
- ✨ SOP-6525 标价签申请编辑界面刷新时机修改 ([6891f0e](https://gitlab.hd123.com:20022/vue/soa/commit/6891f0ec6aefac0e7f15f5e6d5395e13a1c323bc))
- ✨ SOP-6525 标价签申请方法备注 ([be6a402](https://gitlab.hd123.com:20022/vue/soa/commit/be6a4024300ed95cf58c93a1616de2eff7c242da))
- ✨ SOP-6525 标价签申请改动 ([e78de01](https://gitlab.hd123.com:20022/vue/soa/commit/e78de017f8482ad67990577c4193cbd2b6efbb71))
- ✨ SOP-6525 标价签申请列表增加分页器 ([18fe816](https://gitlab.hd123.com:20022/vue/soa/commit/18fe816e159842c8b9af24aeeef4b3d7b369f5a3))
- ✨ SOP-6525 标价签申请商品数量展示调整 ([0fcba4f](https://gitlab.hd123.com:20022/vue/soa/commit/0fcba4f8fe4b0efc1071cdaa90fcb42b6db9fdf2))
- ✨ SOP-6525 标价签申请添加权限定义 ([ee77338](https://gitlab.hd123.com:20022/vue/soa/commit/ee77338f893f43b7012d38ff77979d0a82808e57))
- ✨ SOP-6525 标价签申请新增等权限添加 ([56cdc98](https://gitlab.hd123.com:20022/vue/soa/commit/56cdc98fc5772f482f3413c30bcb5f4d2a0fa96b))
- ✨ SOP-6525 标价签申请新增商品搜索页面 ([104e25f](https://gitlab.hd123.com:20022/vue/soa/commit/104e25f221cc643cf4585b65605d2f7d342ae233))
- ✨ SOP-6525 标价签申请样式修改，部分方法名备注 ([f508545](https://gitlab.hd123.com:20022/vue/soa/commit/f50854537bada655175d191f6f38a5099c311a56))
- ✨ SOP-6525 价签申请按钮权限添加 ([ef6c870](https://gitlab.hd123.com:20022/vue/soa/commit/ef6c87050f0da645fccba50efe81ceb2cb992dff))
- ✨ SOP-6525 价签申请按钮权限修改 ([7a41906](https://gitlab.hd123.com:20022/vue/soa/commit/7a419069c992930e729d4441da319fe3eb204633))
- ✨ SOP-6525 新增标价签叫货模块 ([9e65da0](https://gitlab.hd123.com:20022/vue/soa/commit/9e65da0cfe01582da9aedfc1995415648b2988d8))
- ✨ SOP-6525 新增标价签叫货模块 ([ecc6d99](https://gitlab.hd123.com:20022/vue/soa/commit/ecc6d9970f8d3d42621d9bfb417a77159e4e883a))
- ✨ SOP-6525 新增标价签申请模块修改 ([046c563](https://gitlab.hd123.com:20022/vue/soa/commit/046c56336a7e52f439a02d211d750065e38ce968))
- ✨ SOP-6689 订货模块.可订货金额点击热区扩大优化 ([0fe4452](https://gitlab.hd123.com:20022/vue/soa/commit/0fe445212db4fd0d3cbab7802b4dd244f692b25f))

### Bug Fixes

- 🐛 当从编辑或新建的单据提交后的详情页返回，需要多返回一级 ([e184276](https://gitlab.hd123.com:20022/vue/soa/commit/e184276597fa4166798e98d9281d7af4186b4273))
- 🐛 二次确认是否删除商品行时，操作后需要重新获取草稿内容 ([8bf3821](https://gitlab.hd123.com:20022/vue/soa/commit/8bf38210b88e917e365a2e5877e96c3ee3063e2f))
- 🐛 添加标价签申请编辑权限 ([7688595](https://gitlab.hd123.com:20022/vue/soa/commit/7688595c60baa0fc09fdbf4f52147c8d4de35d8d))
- 🐛 SOP-6756 商品选择的标价签类型不在该商品类别中提交不能成功，添加提示 ([7c0c01a](https://gitlab.hd123.com:20022/vue/soa/commit/7c0c01afaf3cc226ca262d18ae25c3433681847e))

## [2.10.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.9.2...v2.10.0) (2022-05-20)

### Features

- ✨ SOP-6397 质量反馈支持审批功能 ([8b6c36d](https://gitlab.hd123.com:20022/vue/soa/commit/8b6c36dffc2409d1b972810cbffc8794b972c20d))
- ✨ SOP-6476 叫货界面金额旁边显示本单总件数 ([71d7f57](https://gitlab.hd123.com:20022/vue/soa/commit/71d7f57504a6f49b89a269ed284e718e3c1eeaa4))

### Bug Fixes

- 🐛 SOP-6594 质量反馈.时间选择器在某种情况下，会报错 ([691da13](https://gitlab.hd123.com:20022/vue/soa/commit/691da1387b7bd07a64c59bc6d73bf94f242092b9))

### [2.9.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.9.1...v2.9.2) (2022-05-12)

### Features

- ✨ SOP-6245 食惠邦门店订货流程优化 ([3c9815b](https://gitlab.hd123.com:20022/vue/soa/commit/3c9815b06ce843e33317b5f143aab6110765e695))

### Bug Fixes

- 🐛 食惠邦订货传参调整 ([64bda14](https://gitlab.hd123.com:20022/vue/soa/commit/64bda141170b5a276b1d4214e61672add4bd42da))
- 🐛 SOP-6483 新增订货页面.存在效期库存的商品，添加问题 ([9107bed](https://gitlab.hd123.com:20022/vue/soa/commit/9107bed72c1197451ae5f88f5fbce4a680b8bbe4))
- 🐛 SOP-6485 食惠邦订货.商品效期内容展示问题 ([9716eb8](https://gitlab.hd123.com:20022/vue/soa/commit/9716eb8b455596ea3bdb81683d66182491f603b5))
- 🐛 SOP-6489、SOP-6490 ([1bbb2fa](https://gitlab.hd123.com:20022/vue/soa/commit/1bbb2fa5e412d43f92f8c630e68c8a2ffd136528))
- 🐛 SOP-6506 食惠邦订货界面库存不足优化 ([182570f](https://gitlab.hd123.com:20022/vue/soa/commit/182570f3a1b168a170f84a2b0fb2dc3ad74d66ce))
- 🐛 SOP-6510 商品启用效期，单所有效期均没有库存时，效期内容显示优化 ([f2cac86](https://gitlab.hd123.com:20022/vue/soa/commit/f2cac8698650a9ec6eff80c4402f1bcb1d56893c))

### [2.9.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.9.0...v2.9.1) (2022-05-06)

### Features

- ✨ SOP-6396 质量反馈功能优化 ([a91f0e2](https://gitlab.hd123.com:20022/vue/soa/commit/a91f0e2b965f920880b7598f43ad36a5e0ad1edf))
- ✨ SOP-6429 盘点优化 ([f019eab](https://gitlab.hd123.com:20022/vue/soa/commit/f019eabee55c30ba9cb91c7610f7e7313d388bb1))

### Bug Fixes

- 🐛 SOP-6431 营销玩法促销价玩法修复滑动删除商品下标错误 ([61ea2f9](https://gitlab.hd123.com:20022/vue/soa/commit/61ea2f9e1307220e1439e780b633aa08118f01e6))
- 🐛 SOP-6434 选项卡切换时屏蔽上个 tab 的请求数据 ([175ec66](https://gitlab.hd123.com:20022/vue/soa/commit/175ec6618bc9a109e3af6703c69586781f0aa203))
- 🐛 SOP-6448 阶梯满减没满减商品无法滑动删除修复 ([746d2a0](https://gitlab.hd123.com:20022/vue/soa/commit/746d2a0682b87cca4a68b77788fbc65d8ac13c9d))
- 🐛 SOP-6449 退公司选项卡切换时屏蔽上个 tab 的请求数据 ([4f3ffbd](https://gitlab.hd123.com:20022/vue/soa/commit/4f3ffbd29956b4e0a43ea48f0bcb7fca49a90cbd))
- 🐛 SOP-6454 质量反馈.历史反馈记录搜索页面，商品质量反馈信息展示问题 ([3ecdc94](https://gitlab.hd123.com:20022/vue/soa/commit/3ecdc942e092045245983e1471aa86aa49bc232e))

## [2.9.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.8.0...v2.9.0) (2022-04-29)

### Bug Fixes

- 🐛 标准订货可订数全部为 0 时回显错误 ([e448ced](https://gitlab.hd123.com:20022/vue/soa/commit/e448ced04df68adb9e223a7b9d90cc2134ede981))
- 🐛 修复标准订货可订数为 0 时影响提交 ([21c52a9](https://gitlab.hd123.com:20022/vue/soa/commit/21c52a9bcddbb68e551126d242900564b3865826))
- 🐛 SOP-6310 任务中心.搜索页面【领取】任务报错问题 ([7969c56](https://gitlab.hd123.com:20022/vue/soa/commit/7969c56335698f95268259b73283acdb284d1ddb))

### [2.4.6](https://gitlab.hd123.com:20022/vue/soa/compare/v2.7.0...v2.4.6) (2022-04-14)

### Bug Fixes

- 🐛 SOP-6312 全局配置 sysConfig 获取时序调整 ([739ebdc](https://gitlab.hd123.com:20022/vue/soa/commit/739ebdc15f60ad7171f0ed351adeb0b3394f9a8a))

## [2.8.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.7.0...v2.8.0) (2022-04-22)

### Features

- ✨ SOP-6244 报损报溢添加图片 ([2ddb117](https://gitlab.hd123.com:20022/vue/soa/commit/2ddb1171df33faae12a1444dc2abe93cb39706c6))
- ✨ SOP-6247 订货叫货，展示加盟资金余额 ([34bcc0b](https://gitlab.hd123.com:20022/vue/soa/commit/34bcc0b47bccb9fd868e5c8f7a8a733c4f89f10a))

### Bug Fixes

- 🐛 SOP-6129 修复订货模块提交按钮是否可以点击判断错误的问题 ([0e1ff48](https://gitlab.hd123.com:20022/vue/soa/commit/0e1ff4863dcb2a2a552c6f911005e10d61914e3e))
- 🐛 SOP-6312 全局配置 sysConfig 获取时序调整 ([6845e2d](https://gitlab.hd123.com:20022/vue/soa/commit/6845e2d23e7ef9c48247320dc1f40118f479a54e))
- 🐛 SOP-6314 全局页面跳转携带中文时保持不乱码 ([70eda85](https://gitlab.hd123.com:20022/vue/soa/commit/70eda85c4f603acc25cd93be982da2ce1131b354))
- 🐛 SOP-6314 全局页面跳转携带中文时保持不乱码 ([ec4f298](https://gitlab.hd123.com:20022/vue/soa/commit/ec4f2984a2d2b2b5ee9d5a19ad4725d45d57e110))
- 🐛 SOP-6314、SOP-6337 直送收退编辑页面进货金额合计金额与 apos 不一致 ([05d9187](https://gitlab.hd123.com:20022/vue/soa/commit/05d9187f938191bdfbc79393e5940fef92f2a409))
- 🐛 SOP-6333、SOP-6334 报损报溢添加图片问题 ([7978712](https://gitlab.hd123.com:20022/vue/soa/commit/7978712b89cf7c2eb64b753afa7303beb62b7a6d))
- 🐛 SOP-6338 报损报溢添加图片.查看视频优化 ([2165cd8](https://gitlab.hd123.com:20022/vue/soa/commit/2165cd804396af9ea0011c762152b258586a351a))
- 🐛 SOP-6340 报损报溢传参问题 ([81c9037](https://gitlab.hd123.com:20022/vue/soa/commit/81c9037b1ae36e73d9768dfe2be8bc4fdc18ddc3))
- 🐛 SOP-6346 报损报溢添加图片.单据详情页面，图片实现不正确 ([f4ae376](https://gitlab.hd123.com:20022/vue/soa/commit/f4ae376819aa46423fc0055a081f1daa76efe494))
- 🐛 SOP-6360 叫货单展示商品件数和商品规格数.单据列表合计需要修改 ([b72fd5d](https://gitlab.hd123.com:20022/vue/soa/commit/b72fd5da4bec2506c60357b5cb2cf7aa0be7f194))
- 🐛 SOP-6363 订货展示加盟资金余额.新增/编辑订货页面，页面展示的可订货金额与 UI 不符 ([c2c184c](https://gitlab.hd123.com:20022/vue/soa/commit/c2c184ca2ba14fa1428785535c8e0cea9028dbd2))
- 🐛 SOP-6370、SOP-6371 ([5f0c2ea](https://gitlab.hd123.com:20022/vue/soa/commit/5f0c2ea57e790db667954af5d86e371d90f40b56))

## [2.7.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.6.1...v2.7.0) (2022-04-13)

### Features

- ✨ SOP-6038 门店叫货单保存时控制叫货包装数量为整数 ([4e96444](https://gitlab.hd123.com:20022/vue/soa/commit/4e96444b9691b2c64fc30de006b5227f04b725c7))
- ✨ SOP-6060 新增调查类任务 ([ff6683b](https://gitlab.hd123.com:20022/vue/soa/commit/ff6683b1222de749976b4e72b60f54c96ea80776))
- ✨ SOP-6060 新增调查类任务 ([607618f](https://gitlab.hd123.com:20022/vue/soa/commit/607618f9d09ed4ae858764a954a95a656be110c9))
- ✨ SOP-6060 新增调查类任务 ([1be050c](https://gitlab.hd123.com:20022/vue/soa/commit/1be050cda42bd98b1ed02a509cde18b5b458aa87))
- ✨ SOP-6060 新增调查类任务 ([83b7abd](https://gitlab.hd123.com:20022/vue/soa/commit/83b7abdcb14b2bbbf618d7f00caf4e4bad5926a6))
- ✨ SOP-6148 新增订单查询模块 ([7dd5ec8](https://gitlab.hd123.com:20022/vue/soa/commit/7dd5ec89712864b8b1721a1dffa2816156557a93))
- ✨ SOP-6226 调查类任务详情页面相关问题 ([f17728e](https://gitlab.hd123.com:20022/vue/soa/commit/f17728e3124cc37b27d4d80c1290380bcd97e0dd))

### Bug Fixes

- 🐛 调整任务【待处理】状态到【待评价】状态，反馈信息消失问题 ([cecdb26](https://gitlab.hd123.com:20022/vue/soa/commit/cecdb26e0ab8249e209d219858c153d5c280a7e3))
- 🐛 SOP-6193 标准订货模块.商品数量框输入数据时.页面提示问题 ([7850d90](https://gitlab.hd123.com:20022/vue/soa/commit/7850d900527256b5c41e24c4d3434120cb43cf0e))
- 🐛 SOP-6204 订单查询页面基础样式问题汇总 ([ccc6db0](https://gitlab.hd123.com:20022/vue/soa/commit/ccc6db067c72fe71b595c9e466a9514f959929c6))
- 🐛 SOP-6205 订单查询模块.订单状态选择框实现优化 ([71420db](https://gitlab.hd123.com:20022/vue/soa/commit/71420dbc5ffbdbba465b329f3f123f8bfef10da8))
- 🐛 SOP-6209 订单查询模块.订单列表问题优化 ([1961734](https://gitlab.hd123.com:20022/vue/soa/commit/1961734d5cd7dd0fe0b8521257db81f8d3f7b41e))
- 🐛 SOP-6218 订单查询模块.商品清单页面.商品查询传参优化 ([4bd6ccd](https://gitlab.hd123.com:20022/vue/soa/commit/4bd6ccda0650f0fd21919f8923a4b23241833ffd))
- 🐛 SOP-6240 调查任务.任务列表按钮显示问题 ([a6a35f4](https://gitlab.hd123.com:20022/vue/soa/commit/a6a35f45a37abb6ce83cfadbfe661a6883373687))
- 🐛 SOP-6240 调查任务.任务列表按钮显示问题 ([46b914f](https://gitlab.hd123.com:20022/vue/soa/commit/46b914f1b0e6d3c0c7c2200b40a6f88fd2f72a38))
- 🐛 SOP-6240 调查任务.任务列表按钮显示问题 ([6643975](https://gitlab.hd123.com:20022/vue/soa/commit/66439758b1b22c6730e040a2fe204ee67b691a57))
- 🐛 SOP-6264 门店助手图片上传优化 ([1b856a2](https://gitlab.hd123.com:20022/vue/soa/commit/1b856a2a7653cf8bb3350cd242c9e7182933e6b4))
- 🐛 SOP-6266 调查类任务.【提交】按钮逻辑问题 ([fc368d6](https://gitlab.hd123.com:20022/vue/soa/commit/fc368d6d0b618df59103536422af463758865e1e))
- 🐛 SOP-6268 调查类任务.【待复评】状态的任务详情页面展示问题 ([1f1e198](https://gitlab.hd123.com:20022/vue/soa/commit/1f1e19842d25e3b81c5eda6fce1ca4468bede161))

### [2.6.1](https://gitlab.hd123.com/vue/soa/compare/v2.6.0...v2.6.1) (2022-03-30)

### Bug Fixes

- 🐛 修复 ShopSkuEdit 页面 CSS 语法错误导致微信编译报错 ([6d28506](https://gitlab.hd123.com/vue/soa/commit/6d28506b0b3fcedb6e8fa9d5a1b12924110ad4ca))

## [2.6.0](https://gitlab.hd123.com/vue/soa/compare/v2.5.2...v2.6.0) (2022-03-28)

### Features

- ✨ SOP-5910 后端适配后，输入框展示权限控制还原 ([622e0d3](https://gitlab.hd123.com/vue/soa/commit/622e0d38813986cb80164fa0f0c5933119dcfd46))
- ✨ SOP-6035 自定义价格带和促销标签编辑页和详情页 ([eae1dee](https://gitlab.hd123.com/vue/soa/commit/eae1dee3e302996ac4bc7bd1ad14ffc9a288e677))
- ✨ SOP-6035 自定义价格带和促销标签初始页和搜索页 ([fecee94](https://gitlab.hd123.com/vue/soa/commit/fecee949721de8c63c7d64687216f8863730acbf))
- ✨ SOP-6056 任务列表增加父子任务关联展示 ([f0d7ef2](https://gitlab.hd123.com/vue/soa/commit/f0d7ef2f8059a967a1cc15a1d7e106ff8e084f11))
- ✨ SOP-6061 畅销品缺货订货适配任务，尚未适配 ERP 交货单 ([c660ad5](https://gitlab.hd123.com/vue/soa/commit/c660ad59b3e8d677bc2b28c0eaca109285248c04))
- ✨ SOP-6061 接通 ERP 叫货后前端调整订货模块跳转 ([f2ed10e](https://gitlab.hd123.com/vue/soa/commit/f2ed10eb6574675fed0c83e9dfa19f3443f402ab))
- ✨ SOP-6118 订货权限控制优化 ([06ef20b](https://gitlab.hd123.com/vue/soa/commit/06ef20b652ab17bde19d0bd3c3440afafe79f8d5))
- ✨ SOP-6118 订货权限控制优化 ([d10b13e](https://gitlab.hd123.com/vue/soa/commit/d10b13ebf503c95da0d744b5fdfd5cbfc2bedf7e))
- ✨ SOP-6118 订货权限控制优化 ([3f36d3f](https://gitlab.hd123.com/vue/soa/commit/3f36d3fa40ff10df32ac69f6e180de1adb05d514))
- ✨ SOP-6118 订货权限控制优化 ([7eccb84](https://gitlab.hd123.com/vue/soa/commit/7eccb84358ff99238d2d397d527aa51fda238d64))
- ✨ SOP-6139 畅销品适配执行人接受人改动后转交按钮展示逻辑优化 ([be6aa51](https://gitlab.hd123.com/vue/soa/commit/be6aa5126f3094c7d91bf5b0c0683f4c9edd17ba))
- ✨ SOP-6141 多店长需求 ([516b979](https://gitlab.hd123.com/vue/soa/commit/516b9794f7d14771a2b0b7078b9c82dfd58b8e79))

### Bug Fixes

- 🐛 价格带获取标签接口修改 ([0aaffcd](https://gitlab.hd123.com/vue/soa/commit/0aaffcdd75623222f00a882827a2373db4c2c83a))
- 🐛 SOP-6127 列表关联任务，点击未跳转至详情页 ([21d3a55](https://gitlab.hd123.com/vue/soa/commit/21d3a555f58b408e67d9686cc44f790811f84f99))
- 🐛 SOP-6156 多店长需求.转交后页面按钮显示不正确 ([f5b5b5f](https://gitlab.hd123.com/vue/soa/commit/f5b5b5f2059c5a6f7df8597b826b5c6332fb18a1))

### [2.4.5](https://gitlab.hd123.com/vue/soa/compare/v2.5.0...v2.4.5) (2022-03-17)

### Bug Fixes

- 🐛 修复标准订货备注有默认值 ([e6255a8](https://gitlab.hd123.com/vue/soa/commit/e6255a8a425d637ec25b6318fe94b7e10fe20334))

## [2.5.0](https://gitlab.hd123.com/vue/soa/compare/v2.4.4...v2.5.0) (2022-03-11)

### Features

- ✨ SOP-5823 营销玩法.选择商品部分逻辑优化 ([e8962e4](https://gitlab.hd123.com/vue/soa/commit/e8962e40fda4a8e0d017829022760f79b53bc45a))

### Bug Fixes

- 🐛 营销玩法促销换购价格换购折扣保存 ([ebd77a4](https://gitlab.hd123.com/vue/soa/commit/ebd77a41f171762b5f1e5de25e32602a466aba29))

### [2.5.2](https://gitlab.hd123.com/vue/soa/compare/v2.4.4...v2.5.2) (2022-03-18)

### Features

- ✨ 待办功能支持店务模块 ([b47513a](https://gitlab.hd123.com/vue/soa/commit/b47513ac32ec18f8041ff6f518a62f7b05f9cade))

### Bug Fixes

- 🐛 SOP-6082 店务待办任务 tag 展示问题修复 ([3afc889](https://gitlab.hd123.com/vue/soa/commit/3afc889564060c1f307539b8627676992e8c7ae5))
- 🐛 SOP-6082 店务待办界面问题修复 ([dd058a7](https://gitlab.hd123.com/vue/soa/commit/dd058a722c1555aa2516818c871cbaa2d72d0995))
- 🐛 SOP-6082 界面展示问题修复 ([3c2cb9c](https://gitlab.hd123.com/vue/soa/commit/3c2cb9c10cf63fba144134329fb6186b4b4b8dcb))

### [2.5.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.5.0...v2.5.1) (2022-03-17)

### Bug Fixes

- 🐛 标准订货备注显示问题 ([046f7e1](https://gitlab.hd123.com:20022/vue/soa/commit/046f7e1fcd44d9f403ce4af6faf4b3d2fc5612df))

## [2.5.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.4...v2.5.0) (2022-03-11)

### Features

- ✨ SOP-5867 价签任务接口联调修改 ([7d65011](https://gitlab.hd123.com:20022/vue/soa/commit/7d65011b9f793c7dc906525dd782fa1f1c909af8))
- ✨ SOP-5603 价签调整接口联调 ([8d6d55b](https://gitlab.hd123.com:20022/vue/soa/commit/8d6d55b079886a84b33727d78c809489d2e4e0ef))
- ✨ SOP-5822、SOP-5884、SOP-5887 盘点模块优化 ([aba4e5a](https://gitlab.hd123.com:20022/vue/soa/commit/aba4e5acfba2c8117638ca0bb7112beed65c7e3f))
- ✨ SOP-5823 营销玩法.选择商品部分逻辑优化 ([e8962e4](https://gitlab.hd123.com:20022/vue/soa/commit/e8962e40fda4a8e0d017829022760f79b53bc45a))
- ✨ SOP-5867 标价签接口联调修改 ([84f0b54](https://gitlab.hd123.com:20022/vue/soa/commit/84f0b5439a25e21fa5bb1bec22b7b4d301ff4cb0))
- ✨ SOP-5867 价签调整任务真机调试接口问题 ([2ae2402](https://gitlab.hd123.com:20022/vue/soa/commit/2ae2402a4666e44d577e07dc903338df1abfe52e))
- ✨ SOP-5867 价签任务接口联调 ([51ad4a7](https://gitlab.hd123.com:20022/vue/soa/commit/51ad4a79ac07307d15430ee9723cde8c70b5a1ed))
- ✨ SOP-5867 价签任务接口联调 ([f454e60](https://gitlab.hd123.com:20022/vue/soa/commit/f454e60a5e5f9c96e822c27dd843267ee447c3bb))
- ✨ SOP-5867 价签任务接口联调修改 ([ffca6e3](https://gitlab.hd123.com:20022/vue/soa/commit/ffca6e35bdc6ac589908701cd561229d83aa0c01))
- ✨ SOP-5883 直送收货和直送退货选择供应商增加筛选条件 ([6378b2f](https://gitlab.hd123.com:20022/vue/soa/commit/6378b2f0f270a39094db11c96f21428429325056))
- ✨ SOP-5888 盘点数量输入框数字键盘弹出数字键盘 ([bc0ad0e](https://gitlab.hd123.com:20022/vue/soa/commit/bc0ad0e0714ab4fb728f9dfc1e9529fced16d7ff))
- ✨ SOP-5910 文字必填与非必填样式修改 ([dadeedf](https://gitlab.hd123.com:20022/vue/soa/commit/dadeedf4408ac800110cbd8675060e12277a3353))
- ✨ SOP-5911 任务支持多次转交 ([1ce70d5](https://gitlab.hd123.com:20022/vue/soa/commit/1ce70d5683a671bbf68f1b9366263ac90a91dc83))
- ✨ SOP-5912 支持任务级别控制是否可以从相册中选取视频/图片 ([9ef9649](https://gitlab.hd123.com:20022/vue/soa/commit/9ef9649d3aa27a59eb49f83ef9e2264c8215d9ba))
- ✨ SOP-5977 支持单独配置直配收货不允许多收 ([ea28f46](https://gitlab.hd123.com:20022/vue/soa/commit/ea28f46cd943ed77b4fab9b2995075096d173b9f))
- ✨ SOP-5977 支持单独配置直配收货不允许多增加收容差弹窗限制 ([652aac0](https://gitlab.hd123.com:20022/vue/soa/commit/652aac049168ac9073a19efb982299e1fa45cf7b))
- ✨ SOP-5978 门店日结增加提醒字样 ([ce37c01](https://gitlab.hd123.com:20022/vue/soa/commit/ce37c01a2056f098c38059a3af5740cac8faa9ea))
- ✨ SOP-5985 不重要请求 loading 延时 200 毫秒优化体验 ([039b455](https://gitlab.hd123.com:20022/vue/soa/commit/039b455fb0674e2ad5f30fd4880d5269ee807bed))
- ✨ SOP-5985 Toast 和 Loading 组件替换为 fant-mini 组件 ([f3af0c4](https://gitlab.hd123.com:20022/vue/soa/commit/f3af0c47a4f2b20e5afb18e1fa0870033bba4b07))

### Bug Fixes

- 🐛 点检项编辑页切换增加防抖 ([23162a7](https://gitlab.hd123.com:20022/vue/soa/commit/23162a7cebdbdaf3e29f03fb05ebc515ecc9ae01))
- 🐛 营销玩法促销换购价格换购折扣保存 ([ebd77a4](https://gitlab.hd123.com:20022/vue/soa/commit/ebd77a41f171762b5f1e5de25e32602a466aba29))
- 🐛 增加获取用户岗位信息 ([8128019](https://gitlab.hd123.com:20022/vue/soa/commit/8128019d798c44d5d29bf63f698745956658cc26))
- 🐛 SOP-5887 不存在“开始准备”权限的时候，仍然可以进行此操作 ([b4a4dcf](https://gitlab.hd123.com:20022/vue/soa/commit/b4a4dcfb28c96f8d68f8d07b9f2f04c6ca39d342))
- 🐛 SOP-5911 支持多次转交问题修改 ([fb65400](https://gitlab.hd123.com:20022/vue/soa/commit/fb65400ef58793e3020e48936602e842c4535115))
- 🐛 SOP-5913 上传图片、视频时过程中切换上一项，会导致上传失败,增加 loading 组件 ([50681c9](https://gitlab.hd123.com:20022/vue/soa/commit/50681c9a63ab521a43e6c619d782b80040d9d0a5))
- 🐛 SOP-5946 直送收货&直送退货.供应商列表显示不正确 ([26d169d](https://gitlab.hd123.com:20022/vue/soa/commit/26d169d9231a1106e61da3b77b4b57539087b124))
- 🐛 SOP-5947 、SOP-5948、SOP-5949 供应商直送/直推 bug 修复 ([8ad4a7e](https://gitlab.hd123.com:20022/vue/soa/commit/8ad4a7ef930592e25395a8689163d39d46db807e))
- 🐛 SOP-5948 直送收货&直送退货.重新点击供应商弹框数据需要清空 ([32c986d](https://gitlab.hd123.com:20022/vue/soa/commit/32c986d27c48951b41d51fe30bd6db150062885c))
- 🐛 SOP-5956 复盘-抽盘-搜索结果页面商品操作错误 ([942ae6b](https://gitlab.hd123.com:20022/vue/soa/commit/942ae6bb890be90ec2051577f8122953a17d248a))
- 🐛 SOP-5980 文案字数限制问题修改 ([9708ee1](https://gitlab.hd123.com:20022/vue/soa/commit/9708ee188e53d80aba1587ab5e63b3523235a219))
- 🐛 SOP-5980 文字超出限制 ([cdde99f](https://gitlab.hd123.com:20022/vue/soa/commit/cdde99f7447cfb0637a080fa7d8932c1cf4f056c))
- 🐛 SOP-5986 盘点数量编辑框问题汇总 ([174d403](https://gitlab.hd123.com:20022/vue/soa/commit/174d4035baca519541443995b2ac0bfeb052b815))
- 🐛 SOP-5997 价签调整任务显示转交问题修改 ([d86cf23](https://gitlab.hd123.com:20022/vue/soa/commit/d86cf232306f2a1ef346c002adfb8e6a29bc570a))
- 🐛 SOP-6012 门店日结增加提醒字样.当日日结任务完成，切换 tab 时实现错误 ([30301c9](https://gitlab.hd123.com:20022/vue/soa/commit/30301c92ff9af180e64fed8bc51cdcca98d81586))
- 🐛 SOP-6013 直配收货显示提示语和按钮，在商品搜索下显示不正确 ([773fbc1](https://gitlab.hd123.com:20022/vue/soa/commit/773fbc1ace4f2df8ac640d30e9dcd587770885db))
- 🐛 SOP-6015 Modal 组件兼容多次弹出不出现的问题 ([83e50f4](https://gitlab.hd123.com:20022/vue/soa/commit/83e50f41234abf27a30d993fda1d3b5456e1b38d))

### [2.4.4](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.3...v2.4.4) (2022-03-02)

### Bug Fixes

- 🐛 盘点去掉保存/删除草稿弹窗 ([7490315](https://gitlab.hd123.com:20022/vue/soa/commit/7490315b045fb8285ca172e3a675eed9a0d48cd6))
- 🐛 去掉自采保存提示、修复商品搜索名称不显示 ([be99209](https://gitlab.hd123.com:20022/vue/soa/commit/be992099c11cda09133650ae6bdf1d047f7d172b))

### [2.4.4](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.3...v2.4.4) (2022-03-02)

### Bug Fixes

- 🐛 去掉自采保存提示、修复商品搜索名称不显示 ([be99209](https://gitlab.hd123.com:20022/vue/soa/commit/be992099c11cda09133650ae6bdf1d047f7d172b))

### [2.4.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.2...v2.4.3) (2022-03-02)

### Bug Fixes

- 🐛 SOP-5935 首页-大数据接口门店 id 传参错误，目前为固定传参 ([41bf3f1](https://gitlab.hd123.com:20022/vue/soa/commit/41bf3f184e784a7757ad84dee2fb54af5b526200))

### [2.4.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.1...v2.4.2) (2022-03-02)

### Bug Fixes

- 🐛 标准订货 loading 不隐藏问题 ([aa0e337](https://gitlab.hd123.com:20022/vue/soa/commit/aa0e3373396e90fabbe774d89e323f5318c3d6be))

### [2.4.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.4.0...v2.4.1) (2022-03-02)

### Features

- ✨ SOP-5931 门店助手分包 ([c631525](https://gitlab.hd123.com:20022/vue/soa/commit/c63152506b8a02f8160833c88f770db1107ffda6))

## [2.4.0](https://gitlab.hd123.com:20022/vue/soa/compare/v2.3.4...v2.4.0) (2022-02-25)

### Features

- ✨ SOP-5604 订货活动报名优化 ([56bf005](https://gitlab.hd123.com:20022/vue/soa/commit/56bf00503d53a3a9cbfd7fd23a8fa9e2c42e1e3f))
- ✨ SOP-5619 质量反馈支持直接添加商品反馈按店务配置 ([0ccd7dc](https://gitlab.hd123.com:20022/vue/soa/commit/0ccd7dc50a8bc9950ee61fba7bac4a65ac4aafe0))
- ✨ SOP-5619 质量反馈支持直接添加添加商品反馈 ([dd28dbc](https://gitlab.hd123.com:20022/vue/soa/commit/dd28dbc1afeabd41157034c5870721e33bbcab0d))
- ✨ SOP-5715 自采模块商品检索优化 ([a5184bc](https://gitlab.hd123.com:20022/vue/soa/commit/a5184bc107e87e220278893ceca9b1c59ae10a83))
- ✨ SOP-5726 自采模块增加商品查询和选择页面 ([d46a006](https://gitlab.hd123.com:20022/vue/soa/commit/d46a0066e7dba05131fad8c08dbb8a909f2ce9cd))
- ✨ SOP-5757 营销玩法排除促销品发起促销时生效 ([49d8f4d](https://gitlab.hd123.com:20022/vue/soa/commit/49d8f4d639bcc6757bd6bc160822fcb05f3e5359))
- ✨ SOP-5758 任务模块无权限的处理 ([628d3a6](https://gitlab.hd123.com:20022/vue/soa/commit/628d3a6b34fa9a63934af5ca9dacd6718055389d))
- ✨ SOP-5800 去掉登录岗位判断 ([f5bc63f](https://gitlab.hd123.com:20022/vue/soa/commit/f5bc63f89219a2dab37e6ee60b17d95994f9d66a))
- ✨ SOP-5826 排除商品选择其他商品.其他商品列表展示优化 ([72c8522](https://gitlab.hd123.com:20022/vue/soa/commit/72c85223766c21a6ed602099719f6f9d82efad7e))

### Bug Fixes

- 🐛 SOP-5796 自采页面扫描添加商品返回问题 ([abb408f](https://gitlab.hd123.com:20022/vue/soa/commit/abb408fef72f71899289dcfcb1024e54173d1849))
- 🐛 SOP-5797 门店自采.选择商品页面实现问题 ([3072f5b](https://gitlab.hd123.com:20022/vue/soa/commit/3072f5b80f9c8cf4aa9db87fc6165afa96de2f29))
- 🐛 SOP-5813 质量反馈配置传参问题 ([d20c315](https://gitlab.hd123.com:20022/vue/soa/commit/d20c3151c214230abb54a9f6a56853866b608a21))
- 🐛 SOP-5825 普通折扣.点击【保存并生效】按钮传参问题 ([16443f9](https://gitlab.hd123.com:20022/vue/soa/commit/16443f9ecf029b8df9f992d6a29269fa8ffa20c3))
- 🐛 SOP-5833 营销玩法.新增页面排除商品选项显示问题 ([55db8ee](https://gitlab.hd123.com:20022/vue/soa/commit/55db8eefa476a30a96ed8279f42f0fdd11c187e3))
- 🐛 SOP-5835 普通折扣无配置时默认显示优化 ([07bc82e](https://gitlab.hd123.com:20022/vue/soa/commit/07bc82ec0f06372382ef1b66f01a53b965d6ce53))
- 🐛 SOP-5835 营销玩法.普通折扣显示优化 ([f444e1c](https://gitlab.hd123.com:20022/vue/soa/commit/f444e1c94b576ebbda0f2aae66fe3fdd642803d6))
- 🐛 SOP-5835 营销玩法.中台关闭 supportExcludePrm 和 supportExcludeSku 配置,此 ([ea160cf](https://gitlab.hd123.com:20022/vue/soa/commit/ea160cff97302a9b27bc1ffbd865850fff58c890))
- 🐛 SOP-5861 营销玩法合并到 soa ([d47633b](https://gitlab.hd123.com:20022/vue/soa/commit/d47633b2cb045785c420f508ca4e7b50c1c42b16))

### [2.3.4](https://gitlab.hd123.com/vue/soa/compare/v2.3.3...v2.3.4) (2022-02-21)

### Bug Fixes

- 🐛 SOP-5811 分包优化 ([c308e79](https://gitlab.hd123.com/vue/soa/commit/c308e7965a5891893cd9108c49a503a3de08742e))

### [2.3.3](https://gitlab.hd123.com:20022/vue/soa/compare/v2.3.2...v2.3.3) (2022-02-21)

### Bug Fixes

- 🐛 SOP-5811 退公司分包 ([a0a79f0](https://gitlab.hd123.com:20022/vue/soa/commit/a0a79f0d8fe1ef6fe7e1b9ece149ce734201a85a))

### [2.3.2](https://gitlab.hd123.com:20022/vue/soa/compare/v2.3.0...v2.3.2) (2022-02-18)

### Bug Fixes

- 🐛 订货图片显示传参问题 ([d307702](https://gitlab.hd123.com:20022/vue/soa/commit/d30770288a9dd312895e953f96198d2e5e38420a))

### [2.3.1](https://gitlab.hd123.com:20022/vue/soa/compare/v2.3.0...v2.3.1) (2022-02-18)

### Bug Fixes

- 🐛 订货图片显示传参问题 ([d307702](https://gitlab.hd123.com:20022/vue/soa/commit/d30770288a9dd312895e953f96198d2e5e38420a))

## [2.3.0](https://gitlab.hd123.com/vue/soa/compare/v2.2.1...v2.3.0) (2022-02-14)

### Features

- ✨ 退仓/退公司样式优化 ([0e8e90c](https://gitlab.hd123.com/vue/soa/commit/0e8e90c912f6f497a66bc6e88b642c5f3f5d964e))
- ✨ SOP-5446 新增退公司模块 ([2cf2a8b](https://gitlab.hd123.com/vue/soa/commit/2cf2a8bc45366670d3b643a7bf04bbf1493cae9c))
- ✨ SOP-5479 ([baad65c](https://gitlab.hd123.com/vue/soa/commit/baad65c6e5e984e65586f99485f32a6fc8e161b6))
- ✨ SOP-5479 ([a78c924](https://gitlab.hd123.com/vue/soa/commit/a78c92480fb42335b7d5493233502a6b1a91d0ee))
- ✨ SOP-5479 ([d885279](https://gitlab.hd123.com/vue/soa/commit/d885279675bebb489ea6027497ead44e78c41b3a))
- ✨ SOP-5479 ([d0ba1fe](https://gitlab.hd123.com/vue/soa/commit/d0ba1fef3b01bfcbc85bec95be080bcf4f4985ae))
- ✨ SOP-5486 退仓模块增加代出标记 ([40f6c14](https://gitlab.hd123.com/vue/soa/commit/40f6c14765a24b678ff9a6a9d125c9a99d451939))
- ✨ SOP-5515 消息中心逻辑调整，Loading 优化 ([3fbca95](https://gitlab.hd123.com/vue/soa/commit/3fbca9526238d6bf3edbbb379be99dd16628474d))
- ✨ SOP-5529 任务转交选择弹窗展示姓名手机号 ([8da5b95](https://gitlab.hd123.com/vue/soa/commit/8da5b9557d95117bf3e1ad183b5c04f6913bab04))
- ✨ SOP-5535 门店生成的整改任务，可以看到父任务对应点检项的反馈与评价 ([89085a5](https://gitlab.hd123.com/vue/soa/commit/89085a5d07a5f5b2ab0ab246fb7b7eb1b8bb09dd))
- ✨ SOP-5578 标准订货模块提交校验优化 ([f1d7626](https://gitlab.hd123.com/vue/soa/commit/f1d7626ff9d3394b5b4d5261e1b42140008281d2))

### Bug Fixes

- 🐛 父反馈详情图片显示错乱 ([2888752](https://gitlab.hd123.com/vue/soa/commit/288875226d706ed5f5012972f035abe984b54bdc))
- 🐛 任务.父任务反馈图片排版问题 ([b533e05](https://gitlab.hd123.com/vue/soa/commit/b533e058cb2933f938a90a4c77cd0fa62bfc613c))
- 🐛 任务详情 swiper，合格、不合格图片显示位置问题 ([6b12af4](https://gitlab.hd123.com/vue/soa/commit/6b12af40fe878b9a6c2d935d5f8b27a87e7455c3))
- 🐛 任务详情 swiper，合格、不合格图片显示位置问题 ([a5c835f](https://gitlab.hd123.com/vue/soa/commit/a5c835febee67d0d7cd53838cf454ffc14eb1800))
- 🐛 SOP-5529 点击选中转交人取消选中状态 ([356cdc7](https://gitlab.hd123.com/vue/soa/commit/356cdc76c39710034c1d104e441ed3fd44ad65c4))
- 🐛 SOP-5545 首页销售数据显示优化 ([bb441ec](https://gitlab.hd123.com/vue/soa/commit/bb441eca7d7831546ee2051c3b4799774bde244e))
- 🐛 SOP-5546 退公司模块搜索页面的单据进入详情错误 ([9a261ff](https://gitlab.hd123.com/vue/soa/commit/9a261ff5c30998275b25a998f5a30bab6b9d9c11))
- 🐛 SOP-5549 退公司模块.单据详情页面问题汇总 ([482df4f](https://gitlab.hd123.com/vue/soa/commit/482df4f9efa172e8becd58861fc0bab77cf24fcc))
- 🐛 SOP-5611 标准订货-新建订货提交时商品下架，提交失败未保存 ([556a6e2](https://gitlab.hd123.com/vue/soa/commit/556a6e2fd22797e53ae7b8cc33aec803fb19174a))

### [2.2.1](https://gitlab.hd123.com/vue/soa/compare/v2.1.4...v2.2.1) (2022-01-25)

### Bug Fixes

- 🐛 SOP-5545 首页销售数据显示优化 ([b68e29a](https://gitlab.hd123.com/vue/soa/commit/b68e29a33baf7b373a16e55abdde642c45127cfc))
- 🐛 SOP-5559 销售数据显示优化兼容微信 ([ec19800](https://gitlab.hd123.com/vue/soa/commit/ec19800d82d1b7a91dfd5fcb155746d60a004e65))
- 🐛 SOP-5625 删除测试环境写死数据并合并 mixins ([f6408f9](https://gitlab.hd123.com/vue/soa/commit/f6408f9c94294c4ba9c527ba60ff03fa71eda34f))

## [2.2.0](https://gitlab.hd123.com/vue/soa/compare/v2.1.3...v2.2.0) (2022-01-17)

### Features

- ✨ SOP-5244 支持要货退货 ([e2bae84](https://gitlab.hd123.com/vue/soa/commit/e2bae84f15c13e7cf907c1dc3eec027c113cdf02))
- ✨ SOP-5421 收货模块增加失效状态单据显示 ([998460d](https://gitlab.hd123.com/vue/soa/commit/998460df8b565c8a1cacfff0be3631fa17c8d070))

### Bug Fixes

- 🐛 SOP-5414 订货列表-订货单号较长时页面显示不友好 ([d1bfab3](https://gitlab.hd123.com/vue/soa/commit/d1bfab3627d1f66003e438ad265c6565fa182a30))
- 🐛 SOP-5447 当前时间非正常要货时间，strictControl 为 true 时，新增页面未显示不可叫货弹框 ([a764b1c](https://gitlab.hd123.com/vue/soa/commit/a764b1c34559f425b79736d6b03865abc8d50222))
- 🐛 SOP-5451 商品添加备注点击提交，备注未保存，再次点击为空 ([1101212](https://gitlab.hd123.com/vue/soa/commit/1101212eb826d420d66d62299818d1786a98235a))
- 🐛 SOP-5451 搜索页面-商品添加备注失败 ([6e84621](https://gitlab.hd123.com/vue/soa/commit/6e84621e546f5708ee764bbb0ccf555c1a121c92))
- 🐛 SOP-5460 可订数不足弹框-点击继续提交按钮无反应 ([a02db0a](https://gitlab.hd123.com/vue/soa/commit/a02db0a865c161e7d8dbaf261ca8a68fbc4215bb))

### [2.1.4](https://gitlab.hd123.com/vue/soa/compare/v2.1.3...v2.1.4) (2022-01-25)

### Bug Fixes

- 🐛 SOP-5625 删除测试环境写死数据并合并 mixins ([f6408f9](https://gitlab.hd123.com/vue/soa/commit/f6408f9c94294c4ba9c527ba60ff03fa71eda34f))

### [2.1.3](https://gitlab.hd123.com/vue/soa/compare/v2.0.1...v2.1.3) (2022-01-11)

### Bug Fixes

- 🐛 SOP-5456 正常订货调整库存金限制配置 ([af00723](https://gitlab.hd123.com/vue/soa/commit/af007231894a30930d3ce1bee684e44a28b043de))

### [2.1.2](https://gitlab.hd123.com/vue/soa/compare/v2.1.1...v2.1.2) (2022-01-07)

### Features

- ✨ SOP-5368 商品查询权限 ([3d16900](https://gitlab.hd123.com/vue/soa/commit/3d169005c3ffac8df2da7277a3e4f07789199ab7))

### [2.1.1](https://gitlab.hd123.com/vue/soa/compare/v2.1.0...v2.1.1) (2022-01-06)

### Features

- 修复树形组件在微信无法滚动的问题 ([170d0de](https://gitlab.hd123.com/vue/soa/commit/170d0dee36262b66ab1313c1004df2cade8f498f))

### Bug Fixes

- 🐛 SOP-5177 门店首页样式修复 ([5049ac3](https://gitlab.hd123.com/vue/soa/commit/5049ac357cb8f6dafd7911aa99594a320b84cae8))
- 🐛 SOP-5390、SOP-5389 修复分类查询接口传参为 Null，修复回到原界面保留选择记录 ([99d9660](https://gitlab.hd123.com/vue/soa/commit/99d96603aa7bd1ecb8ee4bbf80d982e6e552a4ea))

## [2.1.0](https://gitlab.hd123.com/vue/soa/compare/v2.0.0...v2.1.0) (2022-01-04)

### Features

- ✨ hd-textarea 组件优化 ([1a1cfc3](https://gitlab.hd123.com/vue/soa/commit/1a1cfc3b7ed81c5746f8b46497371da8249a5ba9))
- ✨ hd-textarea 组件优化 ([1acd4a1](https://gitlab.hd123.com/vue/soa/commit/1acd4a12e563bcbbecb7abff0fce9d850abbb364))
- ✨ Merge branch 'feature-0.2 版本优化' into feature-1231 ([25e625d](https://gitlab.hd123.com/vue/soa/commit/25e625d1028ed3f6bfbe1534e4ba31242d3260aa))
- ✨ SOP-5128 ([d17854e](https://gitlab.hd123.com/vue/soa/commit/d17854eb4bcb081ac1567f1b68ee2a61bc3c6f2b))
- ✨ SOP-5152 营销玩法详情促销原因全部展示 ([e3f07f0](https://gitlab.hd123.com/vue/soa/commit/e3f07f0815baf1430f2ead1fc74fe43d8a7ad4af))
- ✨ SOP-5303 ([3294a82](https://gitlab.hd123.com/vue/soa/commit/3294a820617a58939c7c8c5b98d8d32899082e33))
- ✨ SOP-5304 ([00f3812](https://gitlab.hd123.com/vue/soa/commit/00f38121cfaafddbbf83778f75604faefb3c120c))
- ✨ SOP-5304 ([54ee2b7](https://gitlab.hd123.com/vue/soa/commit/54ee2b71ad8f8aa7dfd5a849a5c3b945e0a97591))
- ✨ SOP-5304 ([0e1fc62](https://gitlab.hd123.com/vue/soa/commit/0e1fc625eda5f146b0a7b80e5d3115876c98f131))
- ✨ SOP-5356 ([98b5418](https://gitlab.hd123.com/vue/soa/commit/98b5418f73d3f2555117558b3368c58b1ea94a19))
- ✨ SOP-5378 新增操作流程动作 DISPOSE_FINISH、 SOP-5369 商品查询列表等完善 ([8b64886](https://gitlab.hd123.com/vue/soa/commit/8b64886ce72a72bc1e3a1d2094e0c9b623a8a338))
- ✨ SOP-5387 代码合并后质量反馈流程验证 ([29e3acd](https://gitlab.hd123.com/vue/soa/commit/29e3acdc4497f6ab6a7ca6c00a6c58de14d0e01f))
- ✨ 促销原因弹窗标题修改 ([3459f1b](https://gitlab.hd123.com/vue/soa/commit/3459f1bd04d5b7de73ec47516896795588cb51c3))
- ✨ 修改图片使用 ([6dcce9d](https://gitlab.hd123.com/vue/soa/commit/6dcce9d3f64a3aa6626e41b9ab0556f1d39976ec))
- ✨ 商品查询模块功能完善 ([ace977e](https://gitlab.hd123.com/vue/soa/commit/ace977e0105e923db60f16f86881464fc92d4e15))
- ✨ 扫码流程修改 ([ddff18f](https://gitlab.hd123.com/vue/soa/commit/ddff18f4a1ef652185a3f7c980ea80547a575878))
- ✨ 新增商品查询页面 ([ce92305](https://gitlab.hd123.com/vue/soa/commit/ce923051e10ec7453ebf3d272acf5398a10bd60f))
- SOP-5308 订货活动输入框提交时自动失焦 ([c15ac5e](https://gitlab.hd123.com/vue/soa/commit/c15ac5e3fbcf07755fa9a5c881f356fd9381df1d))
- 增加提交后剩余可订数不足弹窗 ([9cbe2f4](https://gitlab.hd123.com/vue/soa/commit/9cbe2f4473e4f6f08ae44f096311fe05c911afcd))

### Bug Fixes

- SOP-5365 账单取消支付优化 ([5934235](https://gitlab.hd123.com/vue/soa/commit/593423593def5ecfc8d7228e0da9eac25ce9d867))
- 🐛 SOP-5156 营销玩法详情按钮样式优化 ([397e3cd](https://gitlab.hd123.com/vue/soa/commit/397e3cdff562faae44a3584d4ec2b9fb45970d9d))
- 🐛 SOP-5168 ([e53d9c8](https://gitlab.hd123.com/vue/soa/commit/e53d9c8bf90fdee8d615f52ceafd842604090eb3))
- 🐛 SOP-5168 ([33c634a](https://gitlab.hd123.com/vue/soa/commit/33c634a4a3fb5b4359d12f634adbe54998590cb5))
- 🐛 SOP-5168 ([f66b0a9](https://gitlab.hd123.com/vue/soa/commit/f66b0a9ee4b4cd9b1f9d874c198ec53746ff4b9d))
- 🐛 SOP-5168 ([29a4811](https://gitlab.hd123.com/vue/soa/commit/29a481154d367aa572920d1f25b6788103daa760))
- 🐛 SOP-5168 ([2b80654](https://gitlab.hd123.com/vue/soa/commit/2b80654c8a224e193b4785570c5dc5fcbe376bee))
- 🐛 SOP-5168 ([dc8a382](https://gitlab.hd123.com/vue/soa/commit/dc8a382b8a08d470c50af9ac1f44c616808e77fd))
- 🐛 SOP-5169 ([8b5228f](https://gitlab.hd123.com/vue/soa/commit/8b5228ffbb936e7a72bcda505d0e9d46b335d7d0))
- 🐛 SOP-5176 ([fc1c949](https://gitlab.hd123.com/vue/soa/commit/fc1c949ecb14e6fa366c668bb9b0d242ac5d5c5c))
- 🐛 SOP-5300 金额校验错误，目前与叫货金额进行判断 ([7dfa44f](https://gitlab.hd123.com/vue/soa/commit/7dfa44f879318f3d64c5bb9dc5ffb2af766b09b2))
- 🐛 SOP-5307 库存金额兼容 IOS 低版本 ([b7329e6](https://gitlab.hd123.com/vue/soa/commit/b7329e643c3666e683bf701b9d9f9538cd31b10c))
- 🐛 sop-5307 库存金额 ([a7e08d3](https://gitlab.hd123.com/vue/soa/commit/a7e08d384b9e50fcce5afd4b13a0550756d02ede))
- 🐛 SOP-5310 商品之间等级互不干扰 ([9430143](https://gitlab.hd123.com/vue/soa/commit/9430143305fe4d150b51ea277d2ccfac6ebab417))
- 🐛 SOP-5354 ([a07f2d0](https://gitlab.hd123.com/vue/soa/commit/a07f2d032bd4a9f2c018cea17ec590d7f23dd090))
- 🐛 SOP-5354 质量反馈新增等级.等级内容为空时页面调整 ([05acb5a](https://gitlab.hd123.com/vue/soa/commit/05acb5a5c38c2f583260f0440210747fc7df6e8f))
- 🐛 SOP-5359 质量反馈创建反馈时增加组织信息 ([545303e](https://gitlab.hd123.com/vue/soa/commit/545303ebc5b381fa454bec85be052392f093abe4))
- 🐛 SOP-5388 修复搜索商品无法下滑问题，添加商品查看权限定义 ([7013301](https://gitlab.hd123.com/vue/soa/commit/70133019580514f83c15865b46d407ce44077233))
- 🐛 SOP-5391 店务模块结构调整 ([aedd4d0](https://gitlab.hd123.com/vue/soa/commit/aedd4d0db4e3940f88f5ffc38723d03b0359f20c))
- 🐛 SOP-5393 质量反馈增加组织信息 ([4472dcc](https://gitlab.hd123.com/vue/soa/commit/4472dcc826cb566de6001aac00fa4c33976b3274))
- 🐛 任务详情.转交标签颜色修复 ([2940e87](https://gitlab.hd123.com/vue/soa/commit/2940e879f6dbeacea759667062cc099b2d7e82b5))
- 🐛 应用删除和增加响应两次 ([10a7353](https://gitlab.hd123.com/vue/soa/commit/10a735375dbfe451e38df325a3782ba982ec3d67))
- 🐛 恢复质量反馈时间类型提示 ([27d629b](https://gitlab.hd123.com/vue/soa/commit/27d629ba04cb09814d20776250041260b7192d9a))
- 🐛 整改单适配陈列任务 ([78d78c8](https://gitlab.hd123.com/vue/soa/commit/78d78c8efffdb531cb8ddc77749684464fbcb256))
- 🐛 更换上传图片视频组件按钮图片 ([53c5ecd](https://gitlab.hd123.com/vue/soa/commit/53c5ecd3b17b16c45f1148dbf8e6728807dcd885))
- 🐛 查看点检项详情时，无父组名称，固定显示 '点检项' ([7c56487](https://gitlab.hd123.com/vue/soa/commit/7c56487d42a8c64055d3cd8a2551bfea74ef6aa0))
- 🐛 标准订货 sort 与 goods 同级 ([2c5b735](https://gitlab.hd123.com/vue/soa/commit/2c5b7353a610ebb64a18a05ca9234e3fae269afd))
- 🐛 点检项编辑 textarea 增加圆角 ([1a0921b](https://gitlab.hd123.com/vue/soa/commit/1a0921bc7a99eefda9ebc5eddc0768d014c2accf))
- 🐛 点检项编辑背景色 ([b2fe9d2](https://gitlab.hd123.com/vue/soa/commit/b2fe9d2bba2159c26575b1d2e7e6bf3bc1e46e2a))
- 🐛 点检项评价，无描述时隐藏，不显示 -- ([625fdb9](https://gitlab.hd123.com/vue/soa/commit/625fdb9e9867a604cfcd3a72ccb42ce8fd6bc030))
- 🐛 陈列任务反馈 接口调用时序调整 ([fbf1bed](https://gitlab.hd123.com/vue/soa/commit/fbf1bed2168724baf9593d850b776edb8fd3ab13))
- 🐛 陈列任务详情 应显示陈列项 ([e348feb](https://gitlab.hd123.com/vue/soa/commit/e348febb4d96a7512bbcd116b99f22d00a02f683))
- 🐛 陈列项反馈页面文案兼容 ([adf154f](https://gitlab.hd123.com/vue/soa/commit/adf154f08db9d2a6b0ed15c62199dc91d0baf42b))
- 🐛 陈列项页面标题置空 ([672202d](https://gitlab.hd123.com/vue/soa/commit/672202dd5943ed034ca2885be87ce146be836f88))
- SOP-5235、SOP-5247 ([a0f42ff](https://gitlab.hd123.com/vue/soa/commit/a0f42ffd11461e9bdedce85c1322ee9ffeb4cfc1))
- 处理指定分类微信兼容问题 ([e925252](https://gitlab.hd123.com/vue/soa/commit/e925252701d34a4d2a4799d2efa2c975fd59d5c0))
- 提交信息显示优化 ([6830982](https://gitlab.hd123.com/vue/soa/commit/683098224a58d1aab4d22df1b29a18df3bc75a5d))
- 爆品活动去叫货逻辑 ([3dd5d9a](https://gitlab.hd123.com/vue/soa/commit/3dd5d9af1e17c1638d45e295d1374362e08e296f))

### [2.1.2](https://gitlab.hd123.com/vue/soa/compare/v2.1.1...v2.1.2) (2022-01-07)

### Features

- ✨ SOP-5368 商品查询权限 ([3d16900](https://gitlab.hd123.com/vue/soa/commit/3d169005c3ffac8df2da7277a3e4f07789199ab7))

### Bug Fixes

- 🐛 SOP-5177 门店首页样式修复 ([5049ac3](https://gitlab.hd123.com/vue/soa/commit/5049ac357cb8f6dafd7911aa99594a320b84cae8))

### [2.1.1](https://gitlab.hd123.com/vue/soa/compare/v2.1.0...v2.1.1) (2022-01-06)

### Features

- ✨ hd-textarea 组件优化 ([1a1cfc3](https://gitlab.hd123.com/vue/soa/commit/1a1cfc3b7ed81c5746f8b46497371da8249a5ba9))
- ✨ hd-textarea 组件优化 ([1acd4a1](https://gitlab.hd123.com/vue/soa/commit/1acd4a12e563bcbbecb7abff0fce9d850abbb364))
- ✨ Merge branch 'feature-0.2 版本优化' into feature-1231 ([25e625d](https://gitlab.hd123.com/vue/soa/commit/25e625d1028ed3f6bfbe1534e4ba31242d3260aa))
- ✨ SOP-5303 ([3294a82](https://gitlab.hd123.com/vue/soa/commit/3294a820617a58939c7c8c5b98d8d32899082e33))
- ✨ SOP-5304 ([00f3812](https://gitlab.hd123.com/vue/soa/commit/00f38121cfaafddbbf83778f75604faefb3c120c))
- ✨ SOP-5304 ([54ee2b7](https://gitlab.hd123.com/vue/soa/commit/54ee2b71ad8f8aa7dfd5a849a5c3b945e0a97591))
- ✨ SOP-5304 ([0e1fc62](https://gitlab.hd123.com/vue/soa/commit/0e1fc625eda5f146b0a7b80e5d3115876c98f131))
- ✨ SOP-5356 ([98b5418](https://gitlab.hd123.com/vue/soa/commit/98b5418f73d3f2555117558b3368c58b1ea94a19))
- ✨ SOP-5378 新增操作流程动作 DISPOSE_FINISH、 SOP-5369 商品查询列表等完善 ([8b64886](https://gitlab.hd123.com/vue/soa/commit/8b64886ce72a72bc1e3a1d2094e0c9b623a8a338))
- ✨ 修改图片使用 ([6dcce9d](https://gitlab.hd123.com/vue/soa/commit/6dcce9d3f64a3aa6626e41b9ab0556f1d39976ec))
- ✨ 商品查询模块功能完善 ([ace977e](https://gitlab.hd123.com/vue/soa/commit/ace977e0105e923db60f16f86881464fc92d4e15))
- ✨ 扫码流程修改 ([ddff18f](https://gitlab.hd123.com/vue/soa/commit/ddff18f4a1ef652185a3f7c980ea80547a575878))
- ✨ 新增商品查询页面 ([ce92305](https://gitlab.hd123.com/vue/soa/commit/ce923051e10ec7453ebf3d272acf5398a10bd60f))
- 修复树形组件在微信无法滚动的问题 ([170d0de](https://gitlab.hd123.com/vue/soa/commit/170d0dee36262b66ab1313c1004df2cade8f498f))

### Bug Fixes

- 🐛 SOP-5168 ([e53d9c8](https://gitlab.hd123.com/vue/soa/commit/e53d9c8bf90fdee8d615f52ceafd842604090eb3))
- 🐛 SOP-5168 ([33c634a](https://gitlab.hd123.com/vue/soa/commit/33c634a4a3fb5b4359d12f634adbe54998590cb5))
- 🐛 SOP-5168 ([f66b0a9](https://gitlab.hd123.com/vue/soa/commit/f66b0a9ee4b4cd9b1f9d874c198ec53746ff4b9d))
- 🐛 SOP-5168 ([29a4811](https://gitlab.hd123.com/vue/soa/commit/29a481154d367aa572920d1f25b6788103daa760))
- 🐛 SOP-5168 ([2b80654](https://gitlab.hd123.com/vue/soa/commit/2b80654c8a224e193b4785570c5dc5fcbe376bee))
- 🐛 SOP-5168 ([dc8a382](https://gitlab.hd123.com/vue/soa/commit/dc8a382b8a08d470c50af9ac1f44c616808e77fd))
- 🐛 SOP-5169 ([8b5228f](https://gitlab.hd123.com/vue/soa/commit/8b5228ffbb936e7a72bcda505d0e9d46b335d7d0))
- 🐛 SOP-5176 ([fc1c949](https://gitlab.hd123.com/vue/soa/commit/fc1c949ecb14e6fa366c668bb9b0d242ac5d5c5c))
- 🐛 SOP-5388 修复搜索商品无法下滑问题，添加商品查看权限定义 ([7013301](https://gitlab.hd123.com/vue/soa/commit/70133019580514f83c15865b46d407ce44077233))
- 🐛 SOP-5390、SOP-5389 修复分类查询接口传参为 Null，修复回到原界面保留选择记录 ([99d9660](https://gitlab.hd123.com/vue/soa/commit/99d96603aa7bd1ecb8ee4bbf80d982e6e552a4ea))
- 🐛 任务详情.转交标签颜色修复 ([2940e87](https://gitlab.hd123.com/vue/soa/commit/2940e879f6dbeacea759667062cc099b2d7e82b5))
- 🐛 应用删除和增加响应两次 ([10a7353](https://gitlab.hd123.com/vue/soa/commit/10a735375dbfe451e38df325a3782ba982ec3d67))
- 🐛 恢复质量反馈时间类型提示 ([27d629b](https://gitlab.hd123.com/vue/soa/commit/27d629ba04cb09814d20776250041260b7192d9a))
- 🐛 整改单适配陈列任务 ([78d78c8](https://gitlab.hd123.com/vue/soa/commit/78d78c8efffdb531cb8ddc77749684464fbcb256))
- 🐛 更换上传图片视频组件按钮图片 ([53c5ecd](https://gitlab.hd123.com/vue/soa/commit/53c5ecd3b17b16c45f1148dbf8e6728807dcd885))
- 🐛 查看点检项详情时，无父组名称，固定显示 '点检项' ([7c56487](https://gitlab.hd123.com/vue/soa/commit/7c56487d42a8c64055d3cd8a2551bfea74ef6aa0))
- 🐛 点检项编辑 textarea 增加圆角 ([1a0921b](https://gitlab.hd123.com/vue/soa/commit/1a0921bc7a99eefda9ebc5eddc0768d014c2accf))
- 🐛 点检项编辑背景色 ([b2fe9d2](https://gitlab.hd123.com/vue/soa/commit/b2fe9d2bba2159c26575b1d2e7e6bf3bc1e46e2a))
- 🐛 点检项评价，无描述时隐藏，不显示 -- ([625fdb9](https://gitlab.hd123.com/vue/soa/commit/625fdb9e9867a604cfcd3a72ccb42ce8fd6bc030))
- 🐛 陈列任务反馈 接口调用时序调整 ([fbf1bed](https://gitlab.hd123.com/vue/soa/commit/fbf1bed2168724baf9593d850b776edb8fd3ab13))
- 🐛 陈列任务详情 应显示陈列项 ([e348feb](https://gitlab.hd123.com/vue/soa/commit/e348febb4d96a7512bbcd116b99f22d00a02f683))
- 🐛 陈列项反馈页面文案兼容 ([adf154f](https://gitlab.hd123.com/vue/soa/commit/adf154f08db9d2a6b0ed15c62199dc91d0baf42b))
- 🐛 陈列项页面标题置空 ([672202d](https://gitlab.hd123.com/vue/soa/commit/672202dd5943ed034ca2885be87ce146be836f88))
- 处理指定分类微信兼容问题 ([e925252](https://gitlab.hd123.com/vue/soa/commit/e925252701d34a4d2a4799d2efa2c975fd59d5c0))

## [2.1.0](https://gitlab.hd123.com/vue/soa/compare/v1.22.2...v2.1.0) (2022-01-04)

### Features

- ✨ SOP-5049 ([5b72f44](https://gitlab.hd123.com/vue/soa/commit/5b72f443ed4b931c2e59541b68e37322b1db7f13))
- ✨ SOP-5049 ([c3c3ab4](https://gitlab.hd123.com/vue/soa/commit/c3c3ab4beb5e51c11994f58350435a06dcab298e))
- ✨ SOP-5117 ([0e17124](https://gitlab.hd123.com/vue/soa/commit/0e17124b8561a4d130a0749647ef92f3658d1429))
- ✨ SOP-5117 ([b87db98](https://gitlab.hd123.com/vue/soa/commit/b87db98f8b49e9c397fa92d7c0d05ac2995d980f))
- ✨ SOP-5117 ([f59eca6](https://gitlab.hd123.com/vue/soa/commit/f59eca6f942a54690cb0d78444e1cae754ca6d21))
- ✨ SOP-5117 ([9079908](https://gitlab.hd123.com/vue/soa/commit/907990860c5c6c3e5a74a0bbcfd201c4da4da1a5))
- ✨ SOP-5128 ([d17854e](https://gitlab.hd123.com/vue/soa/commit/d17854eb4bcb081ac1567f1b68ee2a61bc3c6f2b))
- ✨ SOP-5152 营销玩法详情促销原因全部展示 ([e3f07f0](https://gitlab.hd123.com/vue/soa/commit/e3f07f0815baf1430f2ead1fc74fe43d8a7ad4af))
- ✨ SOP-5387 代码合并后质量反馈流程验证 ([29e3acd](https://gitlab.hd123.com/vue/soa/commit/29e3acdc4497f6ab6a7ca6c00a6c58de14d0e01f))
- ✨ ucharts 统一 ([ed3922a](https://gitlab.hd123.com/vue/soa/commit/ed3922ae975b39ad54e9482d0336dd486a16c8db))
- ✨ ucharts 适配 ([cb496f0](https://gitlab.hd123.com/vue/soa/commit/cb496f086347c651e521b3f28289462e855cf33e))
- ✨ web 端服务未上，写死内容，1231 版本记得恢复 ([c4644ca](https://gitlab.hd123.com/vue/soa/commit/c4644cae91ce92ff07899c3211cc866624a63f83))
- ✨ 促销原因弹窗标题修改 ([3459f1b](https://gitlab.hd123.com/vue/soa/commit/3459f1bd04d5b7de73ec47516896795588cb51c3))
- ✨ 调整 charts 源码，首页、订货销量图表适配 ([7df5334](https://gitlab.hd123.com/vue/soa/commit/7df53341f53f143a710411f06a67733c90c5c217))
- SOP-5308 订货活动输入框提交时自动失焦 ([c15ac5e](https://gitlab.hd123.com/vue/soa/commit/c15ac5e3fbcf07755fa9a5c881f356fd9381df1d))
- 增加提交后剩余可订数不足弹窗 ([9cbe2f4](https://gitlab.hd123.com/vue/soa/commit/9cbe2f4473e4f6f08ae44f096311fe05c911afcd))

### Bug Fixes

- SOP-5365 账单取消支付优化 ([5934235](https://gitlab.hd123.com/vue/soa/commit/593423593def5ecfc8d7228e0da9eac25ce9d867))
- 🐛 0.2 一轮测试优化部分 UI、企业微信兼容状态弹窗问题修改 ([27e106b](https://gitlab.hd123.com/vue/soa/commit/27e106b6e71cefaffc6f408f3183d11fdf32d7eb))
- 🐛 ios 任务详情页 按钮边框 1rpx 问题 ([d0493d4](https://gitlab.hd123.com/vue/soa/commit/d0493d410a8d01e5a2bada4d530ddd585d64390c))
- 🐛 revert 上次提交（调整 charts 源码，首页、订货销量图标适配） ([cd11109](https://gitlab.hd123.com/vue/soa/commit/cd11109aa8ca0fa917489c306c0184a540e9b5c5))
- 🐛 SOP-5088 ([dcef328](https://gitlab.hd123.com/vue/soa/commit/dcef3280df6810eb0bf2d479adde0d7122caf9a5))
- 🐛 SOP-5091 ([8c3f982](https://gitlab.hd123.com/vue/soa/commit/8c3f982b53346699c891a696dd7954fc25220c56))
- 🐛 SOP-5091 ([404c3ba](https://gitlab.hd123.com/vue/soa/commit/404c3ba05063239243e55d94771471adc0c805e2))
- 🐛 SOP-5095 ([5de2d5b](https://gitlab.hd123.com/vue/soa/commit/5de2d5b078a161295d4cc4af613e941875286d7a))
- 🐛 SOP-5156 营销玩法详情按钮样式优化 ([397e3cd](https://gitlab.hd123.com/vue/soa/commit/397e3cdff562faae44a3584d4ec2b9fb45970d9d))
- 🐛 SOP-5180 ([df259de](https://gitlab.hd123.com/vue/soa/commit/df259dec25b2a5170307884ff017304c8b556790))
- 🐛 SOP-5180 ([19c8ab7](https://gitlab.hd123.com/vue/soa/commit/19c8ab71bd98343144af91d0b31c09943e3a4303))
- 🐛 SOP-5181 ([aad5abf](https://gitlab.hd123.com/vue/soa/commit/aad5abf82dab4e9bc28adca83fa135114fe9df1f))
- 🐛 SOP-5181 ([0fda8af](https://gitlab.hd123.com/vue/soa/commit/0fda8aff6d24c73c100fd0ff69b5facc15cf2137))
- 🐛 SOP-5182 ([1ef5740](https://gitlab.hd123.com/vue/soa/commit/1ef5740d646aba2b9e36a3fbea79a117479fd7dd))
- 🐛 SOP-5300 金额校验错误，目前与叫货金额进行判断 ([7dfa44f](https://gitlab.hd123.com/vue/soa/commit/7dfa44f879318f3d64c5bb9dc5ffb2af766b09b2))
- 🐛 SOP-5307 库存金额兼容 IOS 低版本 ([b7329e6](https://gitlab.hd123.com/vue/soa/commit/b7329e643c3666e683bf701b9d9f9538cd31b10c))
- 🐛 sop-5307 库存金额 ([a7e08d3](https://gitlab.hd123.com/vue/soa/commit/a7e08d384b9e50fcce5afd4b13a0550756d02ede))
- 🐛 SOP-5310 商品之间等级互不干扰 ([9430143](https://gitlab.hd123.com/vue/soa/commit/9430143305fe4d150b51ea277d2ccfac6ebab417))
- 🐛 SOP-5354 ([a07f2d0](https://gitlab.hd123.com/vue/soa/commit/a07f2d032bd4a9f2c018cea17ec590d7f23dd090))
- 🐛 SOP-5354 质量反馈新增等级.等级内容为空时页面调整 ([05acb5a](https://gitlab.hd123.com/vue/soa/commit/05acb5a5c38c2f583260f0440210747fc7df6e8f))
- 🐛 SOP-5359 质量反馈创建反馈时增加组织信息 ([545303e](https://gitlab.hd123.com/vue/soa/commit/545303ebc5b381fa454bec85be052392f093abe4))
- 🐛 SOP-5391 店务模块结构调整 ([aedd4d0](https://gitlab.hd123.com/vue/soa/commit/aedd4d0db4e3940f88f5ffc38723d03b0359f20c))
- 🐛 SOP-5393 质量反馈增加组织信息 ([4472dcc](https://gitlab.hd123.com/vue/soa/commit/4472dcc826cb566de6001aac00fa4c33976b3274))
- 🐛 上传视频组件，上传视频 30s，偶发上传异常 ([2257dff](https://gitlab.hd123.com/vue/soa/commit/2257dff72aa52bf1eba105595c22e6515d44eb29))
- 🐛 任务.点检项编辑增加条件编译 ([0d26509](https://gitlab.hd123.com/vue/soa/commit/0d265094f2385c06d599b53a9faaaeaa6c9127ee))
- 🐛 任务中心列表状态选择弹窗问题 ([a41fd08](https://gitlab.hd123.com/vue/soa/commit/a41fd08b43b63722c7170377edc1488cf7360487))
- 🐛 任务列表选择状态弹窗不显示问题修改 ([9597e41](https://gitlab.hd123.com/vue/soa/commit/9597e410a2c3b299adb158198c2c553683a691fd))
- 🐛 任务详情操作列表.iphoneXR 手机滚动问题.滚动不到最底部 ([72af422](https://gitlab.hd123.com/vue/soa/commit/72af4224b80bf34077f63983090a8cff0e74677f))
- 🐛 微信安卓机器兼容测试 ([ede3e89](https://gitlab.hd123.com/vue/soa/commit/ede3e89169b85ded261b6b39672f96c491d52de2))
- 🐛 收货差异 card 显示异常问题 ([2de6791](https://gitlab.hd123.com/vue/soa/commit/2de6791440e82ab3a07e1f128f325e1e06e374d8))
- 🐛 暂时隐藏 数据 tabbar ([01770a9](https://gitlab.hd123.com/vue/soa/commit/01770a992b70fbef02f4c779507d081938dee290))
- 🐛 标准订货 sort 与 goods 同级 ([2c5b735](https://gitlab.hd123.com/vue/soa/commit/2c5b7353a610ebb64a18a05ca9234e3fae269afd))
- 🐛 点击选择状态弹窗增加防抖机制 ([189d709](https://gitlab.hd123.com/vue/soa/commit/189d709e250b2e7c905b4060556cfca210719da5))
- 🐛 点检项编辑背景色 ([40e346c](https://gitlab.hd123.com/vue/soa/commit/40e346c6a78e2b4bf84ecfa2d866c30db60fe8e3))
- 🐛 点检项编辑背景色 ([f93743c](https://gitlab.hd123.com/vue/soa/commit/f93743cec57a5d58a8f8c6964c81ffb39591d431))
- 🐛 质量反馈页面 created 调整为 onLoad ([19e832c](https://gitlab.hd123.com/vue/soa/commit/19e832c7ce95f03574ccc9471908385a9942add3))
- SOP-5235、SOP-5247 ([a0f42ff](https://gitlab.hd123.com/vue/soa/commit/a0f42ffd11461e9bdedce85c1322ee9ffeb4cfc1))
- 提交信息显示优化 ([6830982](https://gitlab.hd123.com/vue/soa/commit/683098224a58d1aab4d22df1b29a18df3bc75a5d))
- 爆品活动去叫货逻辑 ([3dd5d9a](https://gitlab.hd123.com/vue/soa/commit/3dd5d9af1e17c1638d45e295d1374362e08e296f))

### [2.0.2](https://gitlab.hd123.com/vue/soa/compare/v2.0.1...v2.0.2) (2022-01-21)

### Bug Fixes

- 🐛 SOP-5581 ([d716d0c](https://gitlab.hd123.com/vue/soa/commit/d716d0c47ebdf66dbc55740150d6f22c01349a90))

### [2.0.1](https://gitlab.hd123.com/vue/soa/compare/v2.0.0...v2.0.1) (2022-01-10)

### Features

- ✨ SOP-5444 增加岗位为空的情况下可以访问门店助手的逻辑 ([3f9a598](https://gitlab.hd123.com/vue/soa/commit/3f9a598f9ec6f7e208f568d7eb57cf998f5dd28c))

## [2.0.0](https://gitlab.hd123.com/vue/soa/compare/v1.22.2...v2.0.0) (2021-12-17)

### Features

- ✨ 调整 charts 源码，首页、订货销量图表适配 ([7df5334](https://gitlab.hd123.com/vue/soa/commit/7df53341f53f143a710411f06a67733c90c5c217))
- ✨ SOP-5049 ([5b72f44](https://gitlab.hd123.com/vue/soa/commit/5b72f443ed4b931c2e59541b68e37322b1db7f13))
- ✨ SOP-5049 ([c3c3ab4](https://gitlab.hd123.com/vue/soa/commit/c3c3ab4beb5e51c11994f58350435a06dcab298e))
- ✨ SOP-5117 ([0e17124](https://gitlab.hd123.com/vue/soa/commit/0e17124b8561a4d130a0749647ef92f3658d1429))
- ✨ SOP-5117 ([b87db98](https://gitlab.hd123.com/vue/soa/commit/b87db98f8b49e9c397fa92d7c0d05ac2995d980f))
- ✨ SOP-5117 ([f59eca6](https://gitlab.hd123.com/vue/soa/commit/f59eca6f942a54690cb0d78444e1cae754ca6d21))
- ✨ SOP-5117 ([9079908](https://gitlab.hd123.com/vue/soa/commit/907990860c5c6c3e5a74a0bbcfd201c4da4da1a5))
- ✨ ucharts 适配 ([cb496f0](https://gitlab.hd123.com/vue/soa/commit/cb496f086347c651e521b3f28289462e855cf33e))
- ✨ ucharts 统一 ([ed3922a](https://gitlab.hd123.com/vue/soa/commit/ed3922ae975b39ad54e9482d0336dd486a16c8db))
- ✨ web 端服务未上，写死内容，1231 版本记得恢复 ([c4644ca](https://gitlab.hd123.com/vue/soa/commit/c4644cae91ce92ff07899c3211cc866624a63f83))

### Bug Fixes

- 🐛 0.2 一轮测试优化部分 UI、企业微信兼容状态弹窗问题修改 ([27e106b](https://gitlab.hd123.com/vue/soa/commit/27e106b6e71cefaffc6f408f3183d11fdf32d7eb))
- 🐛 点击选择状态弹窗增加防抖机制 ([189d709](https://gitlab.hd123.com/vue/soa/commit/189d709e250b2e7c905b4060556cfca210719da5))
- 🐛 点检项编辑背景色 ([40e346c](https://gitlab.hd123.com/vue/soa/commit/40e346c6a78e2b4bf84ecfa2d866c30db60fe8e3))
- 🐛 点检项编辑背景色 ([f93743c](https://gitlab.hd123.com/vue/soa/commit/f93743cec57a5d58a8f8c6964c81ffb39591d431))
- 🐛 任务.点检项编辑增加条件编译 ([0d26509](https://gitlab.hd123.com/vue/soa/commit/0d265094f2385c06d599b53a9faaaeaa6c9127ee))
- 🐛 任务列表选择状态弹窗不显示问题修改 ([9597e41](https://gitlab.hd123.com/vue/soa/commit/9597e410a2c3b299adb158198c2c553683a691fd))
- 🐛 任务详情操作列表.iphoneXR 手机滚动问题.滚动不到最底部 ([72af422](https://gitlab.hd123.com/vue/soa/commit/72af4224b80bf34077f63983090a8cff0e74677f))
- 🐛 任务中心列表状态选择弹窗问题 ([a41fd08](https://gitlab.hd123.com/vue/soa/commit/a41fd08b43b63722c7170377edc1488cf7360487))
- 🐛 上传视频组件，上传视频 30s，偶发上传异常 ([2257dff](https://gitlab.hd123.com/vue/soa/commit/2257dff72aa52bf1eba105595c22e6515d44eb29))
- 🐛 收货差异 card 显示异常问题 ([2de6791](https://gitlab.hd123.com/vue/soa/commit/2de6791440e82ab3a07e1f128f325e1e06e374d8))
- 🐛 微信安卓机器兼容测试 ([ede3e89](https://gitlab.hd123.com/vue/soa/commit/ede3e89169b85ded261b6b39672f96c491d52de2))
- 🐛 暂时隐藏 数据 tabbar ([01770a9](https://gitlab.hd123.com/vue/soa/commit/01770a992b70fbef02f4c779507d081938dee290))
- 🐛 质量反馈页面 created 调整为 onLoad ([19e832c](https://gitlab.hd123.com/vue/soa/commit/19e832c7ce95f03574ccc9471908385a9942add3))
- 🐛 ios 任务详情页 按钮边框 1rpx 问题 ([d0493d4](https://gitlab.hd123.com/vue/soa/commit/d0493d410a8d01e5a2bada4d530ddd585d64390c))
- 🐛 revert 上次提交（调整 charts 源码，首页、订货销量图标适配） ([cd11109](https://gitlab.hd123.com/vue/soa/commit/cd11109aa8ca0fa917489c306c0184a540e9b5c5))
- 🐛 SOP-5088 ([dcef328](https://gitlab.hd123.com/vue/soa/commit/dcef3280df6810eb0bf2d479adde0d7122caf9a5))
- 🐛 SOP-5091 ([8c3f982](https://gitlab.hd123.com/vue/soa/commit/8c3f982b53346699c891a696dd7954fc25220c56))
- 🐛 SOP-5091 ([404c3ba](https://gitlab.hd123.com/vue/soa/commit/404c3ba05063239243e55d94771471adc0c805e2))
- 🐛 SOP-5095 ([5de2d5b](https://gitlab.hd123.com/vue/soa/commit/5de2d5b078a161295d4cc4af613e941875286d7a))
- 🐛 SOP-5180 ([df259de](https://gitlab.hd123.com/vue/soa/commit/df259dec25b2a5170307884ff017304c8b556790))
- 🐛 SOP-5180 ([19c8ab7](https://gitlab.hd123.com/vue/soa/commit/19c8ab71bd98343144af91d0b31c09943e3a4303))
- 🐛 SOP-5181 ([aad5abf](https://gitlab.hd123.com/vue/soa/commit/aad5abf82dab4e9bc28adca83fa135114fe9df1f))
- 🐛 SOP-5181 ([0fda8af](https://gitlab.hd123.com/vue/soa/commit/0fda8aff6d24c73c100fd0ff69b5facc15cf2137))
- 🐛 SOP-5182 ([1ef5740](https://gitlab.hd123.com/vue/soa/commit/1ef5740d646aba2b9e36a3fbea79a117479fd7dd))

### 1.21.0

_2021-11-19_

#### 优化

- 门店钉钉的复盘商品前增加序号、并支持序号筛选（SOP-4709）

### 1.20.2

_2021-11-19_

#### Bug 修复

- 复盘时修改商品数量再次搜索后商品数量重置

### 1.20.1

_2021-11-18_

#### Bug 修复

- 加盟订货时因使用缓存的单据导致单据重复

### 1.20.0

_2021-11-09_

#### 优化

- 标准订货模块增加库存查看权限，支持配置隐藏库存（SOP-4575）
- 标准订货和退货申请模块支持精确查找商品（SOP-4575）
- 店务相关模块增加系统单据提醒功能（SOP-3613），新增支持：门店叫货单、配货出货单、配货差异单、直配出货单、门店商品退货通知单、配货出货退货单、直配出货退货单、调拨单、报损单、报溢单、盘点计划单据提醒
- 盘点模块优化（SOP-3631）
  - 支持分页在线编辑，优化性能
- 店务相关模块单据状态变更后同步至门店助手（SOP-4194）
  - 涉及模块： 收货、收货差异、退货申请、退仓、退供应商、加工、调拨、报损、报溢，增加显示已作废的状态标识

### 1.19.0

_2021-10-27_

#### 优化

- 调拨模块调入支持编辑商品数量（SOP-4193）
  - 调入：可编辑调入单收货数量，只能少收不能多收。默认数量为收货数量
- 明康汇订货编辑页面增加 tag 筛选（SOP-4195）
  - 多选的标签用 and 逻辑进行筛选，筛选全部标签都符合的商品

### 1.18.0

_2021-10-8_

#### 新特性

- 增加不停业盘点功能（SOP-4041）
  - 增加不停业盘点功能及入口

#### 优化

- 质量反馈电商渠道优化（明康汇客户 SOP-4002）
  - 进入质量反馈商品添加订单来源标志
  - 无论目前处于什么时间反馈提示：“线下订单：19:45 之前为收货质量反馈，之后为异常质量反馈；电商订单：19:45 之前为收货质量反馈，之后为异常质量反馈”
  - 历史反馈商品添加订单来源标志
  - 添加商品界面添加提示显示：“线下订单：自确认收货日起 x 天内可反馈；电商订单：自确认收货日起 x 天内可反馈”

### 1.17.1

_2021-9-30_

#### Bug 修复

- 日志接口报错导致埋点数据无法清除
  - 调整埋点清除逻辑，超过埋点条数上限即清理埋点数据。调整 vuex 同步到 storage 的逻辑，从所有 vuex 数据对应一个 storage 改为每个 vuex 属性对应一个 storage，避免超过单个 storage 的上限 1M

### 1.17.0

_2021-9-17_

#### Bug 修复

- hd-note 因 text-area 不支持绑定值为 null 导致系统崩溃
  - 调整相关逻辑，传入的值为 null 时修改为空字符串

#### 新特性

- 增加前端请求日志（SOP-4033）
  - 存储每个请求的参数，用于问题排查，日志接口地址https://track.qianfan123.com:9999/track，更新此特性需要客户新增安全域名配置track.qianfan123.com

#### 优化

- 明康汇订货交互优化（SOP-4012）

  - 明康汇订货增加计算到店价促销逻辑后商品价格及金额为后端返回，因此对于接口交互时序要严格控制，故增加修改商品后 loading 的逻辑。后由于客户反映体感有卡顿，故对该交互进行优化，每次修改商品后进入处理队列，小程序顺序处理，不阻塞用户操作

- 钉钉门店助手切换门店时，展示门店代码并支持名称、代码模糊查找(SOP-3968)
- 川鼎汇订货模式图片支持点击放大，放大后支持多张图片切换查看(SOP-3964)
- 直送收货送货价可调高(SOP-4018)

### 1.16.0

_2021-9-6_

#### Bug 修复

- http 请求状态码为 0 时自动退出
  - 调整相关逻辑，仅状态码为 401 时自动退出

#### 新特性

- 督导角色功能调整
  - 首页增加“我的关注”面板（SOP-3857）
  - “我的待办”样式优化（SOP-3921）
- 川鼎汇订货增加在线支付叫货功能（SOP-3715）
  - 加盟门店在线支付叫货需求（SOP-3614）
  - 独立叫货模式支持自动配货（SOP-3615）
  - 独立叫货模式购物车优化（SOP-3660）
- 督导银行
  - 明康汇订货增加到店价促销计算逻辑

#### 优化

- 搜索框样式优化（SOP-3858）
  - 搜索框按照 UI 调整为圆角，涉及到的模块为：订货（明康汇订货、川鼎汇订货、正常订货）、门店自采、直送退货、直送收货、收货、收货差异、退货申请、退仓、退供应商、报损、报溢、加工、调拨申请、调拨。
- 直送收、直送退模块优化（SOP-3859）
  - 直送入库或者退货时，保存时需要看到每个商品的送货价，送货金额。
  - 直送入库或者退货时，需要看到整单合计数量、合计金额。
