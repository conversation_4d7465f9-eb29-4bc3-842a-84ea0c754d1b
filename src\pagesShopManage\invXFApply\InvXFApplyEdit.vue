<template>
  <view class="content inv-xf-apply-edit" :style="rootStyle">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <hd-date-picker></hd-date-picker>

    <view class="inv-xf-apply-header">
      <hd-cell
        :title="counterType + '门店'"
        :placeholder="'请选择' + counterType + '门店'"
        :value="data.counterShopName"
        :isLink="true"
        direction="right"
        @onClick="doSelectShop"
      ></hd-cell>
      <hd-cell title="备注" placeholder="请填写备注信息" :value="data.note" :isLink="true" direction="right" @onClick="doNoteShow"></hd-cell>
      <hd-cell
        v-if="infDeliveryTypeShow"
        title="配送模式"
        placeholder="请选择配送模式"
        :value="deliveryType"
        :isLink="true"
        direction="right"
        @onClick="doDeliveryTypeShow"
      ></hd-cell>
    </view>
    <view class="inv-xf-apply-operator">
      <hd-operator-cell
        title="商品明细"
        :clickable="data.counterShopId ? true : false"
        :showSearch="tabType === 'good'"
        :show-title-two="showPack === '1'"
        @scan="doScan"
        @add="doSelectGoods"
        @search="doSearchGoods"
      >
        <view slot="title-one" class="inv-xf-goods" @click="getTab('good')">
          <text :class="[tabType === 'good' ? 'inv-xf-goods-txt' : 'inv-xf-goods-txt-sub']">商品明细</text>
          <view v-if="tabType === 'good'" class="inv-xf-goods-btn"></view>
        </view>
        <view slot="title-two" class="inv-xf-goods" @click="getTab('package')">
          <text :class="[tabType === 'package' ? 'inv-xf-goods-txt' : 'inv-xf-goods-txt-sub']">包材明细</text>
          <view v-if="tabType === 'package'" class="inv-xf-goods-btn"></view>
        </view>
      </hd-operator-cell>
    </view>
    <!--商品明细列表-->
    <block v-if="tabType === 'good'">
      <view v-if="data.lines.length" class="inv-xf-apply-body">
        <hd-swipe-action
          v-for="(item, i) in showList"
          :key="item.goods.uuid"
          @onDelete="doDelete(item)"
          :index="i"
          :moveIndex="moveIndex"
          @updateIndex="onUpdateIndex"
        >
          <edit-card
            :item="item"
            :showPrice="showPrice"
            :showRtlPrice="showRtlPrice"
            :enableValidDate="enableValidDate"
            :isShowModify="isShowModify"
            @numberChange="doNumberChange"
            @removeBatch="(batch) => handleRemoveBatch(i, batch)"
            @editBatchDate="(batch) => handleEditBatchDate(i, batch)"
            @editBatchQty="(batch) => handleEditBatchQty(i, batch)"
            @addBatch="handleAddBatch(i)"
            @modifyTransferPrice="(batch) => modifyTransferPrice(i, batch)"
          ></edit-card>
        </hd-swipe-action>
        <view v-if="!finished" class="loading">加载中···</view>
      </view>
      <view v-else class="inv-xf-apply-empty">
        <image class="inv-xf-apply-empty-icon" :src="'/static/img/img_empty_goods.png' | oss"></image>
        <text class="inv-xf-apply-empty-txt" v-if="data.counterShopId">暂无{{ data.type === InvXFApplyType.increase ? '调入' : '调出' }}商品</text>
        <text class="inv-xf-apply-empty-txt" v-else>请先选择{{ counterType }}门店</text>
      </view>
    </block>
    <!--包材明细列表-->
    <block v-if="tabType === 'package'">
      <view v-if="data.packLines.length" class="inv-xf-apply-body">
        <hd-swipe-action
          v-for="(item, i) in data.packLines"
          :key="item.pack.uuid"
          @onDelete="doDeletePka(item)"
          :index="i"
          :moveIndex="moveIndex"
          @updateIndex="onUpdateIndex"
        >
          <package-card :item="item" @numberChange="doPkaNumberChange" @editBatchQty="(batch) => handlePkaEditBatchQty(i, batch)"></package-card>
        </hd-swipe-action>
        <view v-if="!finished" class="loading">加载中···</view>
      </view>
      <view v-else class="inv-xf-apply-empty">
        <image class="inv-xf-apply-empty-icon" :src="'/static/img/img_empty_goods.png' | oss"></image>
        <text class="inv-xf-apply-empty-txt" v-if="data.counterShopId">暂无{{ data.type === InvXFApplyType.increase ? '调入' : '调出' }}包材</text>
        <text class="inv-xf-apply-empty-txt" v-else>请先选择{{ counterType }}门店</text>
      </view>
    </block>
    <view class="inv-xf-apply-footer">
      <view
        class="footer-total-no-pack"
        :class="{ flexC: showRtlPrice && !showPrice, flexCE: !showRtlPrice && showPrice }"
        v-if="(showRtlPrice || showPrice) && !isHasPack"
      >
        <view v-if="showRtlPrice">
          售价总额：
          <text class="total-price">{{ rtnTotal }}</text>
        </view>
        <view v-if="showPrice">
          调拨金额：
          <text class="total-price">{{ total }}</text>
        </view>
      </view>

      <view class="footer-total-bar" v-if="isHasPack">
        <view class="footer-total-bar-top">
          调拨金额：
          <text class="total-price dp-price">{{ aggTotal }}</text>
        </view>
        <view class="footer-total-bar-btn">
          <view class="footer-total-bar-top">
            货品调拨金额：
            <text class="total-price">{{ total }}</text>
          </view>
          <view class="footer-total-bar-top">
            包材调拨金额：
            <text class="total-price">{{ packTotal }}</text>
          </view>
        </view>
      </view>
      <view class="footer-btn">
        <hd-button v-if="permission.save" :type="permission.submit ? 'white' : 'primary'" :disabled="!validate" @click="doSave">保存</hd-button>
        <hd-button type="primary" v-if="permission.submit" :disabled="!validate" @click="doSubmit">提交</hd-button>
      </view>
    </view>

    <uni-popup ref="note" type="bottom">
      <hd-note :value="data.note" @confirm="doNoteConfirm" @close="doNoteClose"></hd-note>
    </uni-popup>
    <uni-popup ref="reason" type="bottom">
      <hd-reason title="请选择配送模式" @confirm="doReasonConfirm" :reasons="reasons" :reason="reason" @close="doReasonClose"></hd-reason>
    </uni-popup>

    <edit-qty-dialog ref="edit" @confirm="handleEditBatchConfirm"></edit-qty-dialog>

    <edit-pka-qty-dialog ref="pkaEdit" @confirm="handlePkaEditBatchConfirm"></edit-pka-qty-dialog>

    <hd-drag-button
      v-if="data.counterShopId"
      custom-style="height: 100vh;position: fixed;top:0;left:0;z-index:1;"
      :enablePointerEvents="false"
      :x="12"
      :y="400"
      :img="'/static/icon/move_scan_2x.png' | oss"
      @click="doScan"
    ></hd-drag-button>
    <!-- 筛选的弹窗 -->
    <uni-popup type="center" ref="modifyPricePop" :maskClick="false">
      <modify-transfer-dialog
        :modifyTranferLine="modifyTranferLine"
        :show="showModify"
        @modifyCancel="modifyCancel"
        @modifyConfirm="modifyConfirm"
      ></modify-transfer-dialog>
    </uni-popup>
  </view>
</template>
<script lang="ts" src="./InvXFApplyEdit.ts"></script>

<style lang="scss" scoped>
.inv-xf-apply-edit {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #f6f6f6;
  overflow-x: hidden;

  .inv-xf-apply-header {
    width: 100%;
    margin-bottom: 16rpx;
    flex: 0 0 auto;
    position: fixed;
    top: 0;
    z-index: 1;
  }

  .inv-xf-apply-operator {
    flex: 0 0 auto;
    border-bottom: 1px solid #e3e4e8;

    .inv-xf-goods {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      margin-right: 16rpx;

      .inv-xf-goods-txt {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32rpx;
        color: #1a1a1a;
        text-align: center;
        font-style: normal;
        margin-top: 22rpx;
      }

      .inv-xf-goods-txt-sub {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        text-align: center;
        font-style: normal;
        margin-top: 24rpx;
      }

      .inv-xf-goods-btn {
        width: 64rpx;
        height: 7rpx;
        background: #1c64fd;
        border-radius: 4rpx;
      }
    }
  }

  .inv-xf-apply-body {
    background: #ffffff;

    .loading {
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: rgb(148, 150, 154);
    }
  }

  .inv-xf-apply-empty {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    flex-direction: column;

    .inv-xf-apply-empty-icon {
      margin-top: 118rpx;
      width: 300rpx;
      height: 300rpx;
    }

    .inv-xf-apply-empty-txt {
      margin-top: 10rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(148, 150, 154, 1);
      line-height: 40rpx;
    }
  }

  .inv-xf-apply-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    flex: 0 0 auto;
    padding-bottom: 0;
    padding-bottom: constant(safe-area-inset-bottom) !important;
    padding-bottom: env(safe-area-inset-bottom) !important;
    .footer-total-bar {
      width: 750rpx;
      height: 88rpx;
      background: #fff7e0;
      font-size: 24rpx;
      color: #999;
      .footer-total-bar-top {
        display: flex;
        justify-content: flex-end;
        padding-right: 24rpx;
        align-items: center;
      }

      .footer-total-bar-btn {
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
      .total-price {
        font-size: 24rpx;
        font-family: HelveticaNeue, HelveticaNeue;
        font-weight: 600;
        color: #333;
        &::before {
          content: '￥';
          font-size: 24rpx;
        }
      }
      .dp-price {
        font-weight: bold;
        font-size: 32rpx;
      }
    }
    .footer-total-no-pack {
      width: 750rpx;
      height: 88rpx;
      padding: 0 24rpx;
      background: #fff7e0;
      font-size: 24rpx;
      color: #999;
      @include flex(row, space-between, center);
      box-sizing: border-box;
      .total-price {
        font-size: 32rpx;
        font-family: HelveticaNeue, HelveticaNeue;
        font-weight: bold;
        color: #333;
        &::before {
          content: '￥';
          font-size: 24rpx;
        }
      }
    }
    .flexC {
      display: flex;
      align-items: center;
    }
    .flexCE {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
    .footer-btn {
      display: flex;
    }
  }
}
</style>
