/*
 * @Author: yuzhipi
 * @Date: 2025-04-09 17:17:31
 * @LastEditTime: 2025-04-17 18:15:16
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: /soa/src/pagesSD/labelPrinting/LabelPrintingSkuImport.ts
 * 记得注释
 */
import { Vue, Component } from 'vue-property-decorator'
import DateUtil from '@/utils/DateUtil'
import { mixins } from 'vue-class-component'
import config from '@/config'
import { debounce } from 'ts-debounce'
import SkuImportCard from './cmp/SkuImportCard.vue'
import QueryRequest from '@/model/base/QueryRequest'
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js'
import QueryCondition from 'model/default/QueryCondition'
import AppPrintLabelApi from '@/network/AppPrintLabelApi/AppPrintLabelApi'
import AppPrintLabelRprcAdjDTO from 'model/AppPrintLabel/AppPrintLabelRprcAdjDTO'
import AppPrintLabelPromBillDTO from 'model/AppPrintLabel/AppPrintLabelPromBillDTO'
import AppPrintLabelPromBillResultDTO from 'model/AppPrintLabel/AppPrintLabelPromBillResultDTO'
import AppPrintLabelRprcAdjResultDTO from 'model/AppPrintLabel/AppPrintLabelRprcAdjResultDTO'
import AppPrintLabelGoodsExpandDTO from 'model/AppPrintLabel/AppPrintLabelGoodsExpandDTO'
import AppPrintLabelGoodsDTO from 'model/AppPrintLabel/AppPrintLabelGoodsDTO'

type DataListItem = (AppPrintLabelPromBillResultDTO | AppPrintLabelRprcAdjResultDTO) & {
  uuid?: string
  isCheck?: boolean
}

@Component({
  components: { SkuImportCard }
})
export default class LabelPrintingSkuImport extends mixins(MescrollMixin) {
  mode: string = '' // 导入模式
  currentDate: string = DateUtil.format(new Date(), 'yyyy-MM-dd') // 变价时间
  checkAll: boolean = false // 是否全选
  dataList: DataListItem[] = [] // 通用数据列表
  selectedList: Record<string, string[]> = {} // 选中数据列表，以日期为键名存储
  conditionList: QueryCondition[] = [] // 带过来的单据筛选条件
  result: AppPrintLabelGoodsExpandDTO[] = [] // 商品列表
  page: number = 0
  resultPage: number = 0
  isLoading: boolean = false
  doConfirm = debounce(this.onConfirm, 300, { isImmediate: true })
  downOption = {
    use: false,
    auto: false
  }

  upOption = {
    auto: false,
    page: {
      num: 0,
      size: 10
    },
    textNoMore: '-- 我是有底线的 --',
    noMoreSize: 5,
    empty: {
      use: true,
      icon: config.sourceUrl + 'img/img_empty_content.png',
      tip: '暂无数据',
      fixed: true,
      top: '100rpx'
    }
  }

  // 选中数据的长度
  get confirmLength() {
    return this.selectedList[this.currentDate]?.length || 0
  }

  onLoad(option) {
    this.mode = option.mode
    uni.setNavigationBarTitle({
      title: this.mode !== '1' ? '导入促销商品' : '导入售价改价商品'
    })
    if (uni.getStorageSync('labelPrintingCondition')) {
      this.conditionList = uni.getStorageSync('labelPrintingCondition')
      if (this.conditionList.length > 0) {
        const dateIndex = this.conditionList.findIndex((item) => item.operation === 'effectDate:=')
        this.currentDate = dateIndex > -1 ? this.conditionList[dateIndex].parameters[0] : DateUtil.format(new Date(), 'yyyy-MM-dd')
        if (this.mode !== '1') {
          const uuids = this.conditionList.filter((item) => item.operation === 'promBillUuids:in')
          this.$set(this.selectedList, this.currentDate, uuids.length ? uuids[0].parameters : [])
        } else {
          const nums = this.conditionList.filter((item) => item.operation === 'rprcAdjNums:in')
          this.$set(this.selectedList, this.currentDate, nums.length ? nums[0].parameters : [])
        }
      }
      uni.removeStorage({ key: 'labelPrintingCondition' })
    }
    this.doRefresh()
  }

  onShow() {
    this.$nextTick(() => {
      this.doRefresh()
    })
  }

  mescrollInit(mescroll) {
    this.mescroll = mescroll
  }

  async doRefresh() {
    this.page = 0
    if (this.mescroll) {
      this.mescroll.resetUpScroll()
    }
  }

  async doLoad() {
    await this.getResult()
  }

  doCheckAll() {
    this.checkAll = !this.checkAll
  }

  // 切换日期时保留数据
  selectDate() {
    this.$showPicker({
      currentDate: this.currentDate,
      success: (res) => {
        if (res.date) {
          // 保存当前日期的选中数据
          if (!this.selectedList[this.currentDate]) {
            this.$set(this.selectedList, this.currentDate, [])
          }
          // 切换到新日期
          this.currentDate = res.date
          // 初始化新日期的数据
          if (!this.selectedList[this.currentDate]) {
            this.$set(this.selectedList, this.currentDate, [])
          }
          this.doRefresh()
        }
      }
    })
  }

  // 加载单据
  async getResult(showLoading: boolean = false) {
    if (showLoading) {
      this.page = 0
      this.$showLoading({ delayTime: 200 })
      this.dataList = []
    }

    if (this.isLoading) return

    const params = this.mode !== '1' ? new AppPrintLabelPromBillDTO() : new AppPrintLabelRprcAdjDTO()
    params.page = this.page
    params.pageSize = this.upOption.page.size
    params.effectDate = this.currentDate

    this.isLoading = true
    try {
      const resp = await (this.mode !== '1' ? AppPrintLabelApi.listEffectPromBill(params) : AppPrintLabelApi.listEffectRprcAdj(params))

      this.$hideLoading()
      this.isLoading = false

      const listData = ((resp.data || []) as DataListItem[]).map((item: DataListItem) => {
        return {
          ...item,
          isCheck: this.selectedList[this.currentDate]
            ? this.mode !== '1'
              ? this.selectedList[this.currentDate].includes(item.uuid!)
              : this.selectedList[this.currentDate].includes(item.num)
            : false
        }
      })
      if (params.page === 0) {
        this.dataList = listData
      } else {
        this.dataList.push(...listData)
      }
      this.page++
      this.mescroll.endSuccess(listData.length, !!resp.more)
    } catch (e) {
      this.$hideLoading()
      this.isLoading = false
      this.mescroll.endErr()
      this.$showToast({ icon: 'error', title: e.msg || '加载失败' })
    }
  }

  // 改变单据的选中状态
  radioChange(value: DataListItem) {
    let index: number = 0
    if (this.mode !== '1') {
      index = this.dataList.findIndex((item) => item.uuid === value.uuid)
    } else {
      index = this.dataList.findIndex((item) => item.num === value.num)
    }
    if (index === -1) return
    this.$set(this.dataList[index], 'isCheck', !this.dataList[index].isCheck)
    if (this.dataList[index].isCheck) {
      if (!this.selectedList[this.currentDate]) {
        this.$set(this.selectedList, this.currentDate, [])
      }
      if (this.mode !== '1') {
        this.selectedList[this.currentDate].push(this.dataList[index].uuid!)
      } else {
        this.selectedList[this.currentDate].push(this.dataList[index].num)
      }
    } else {
      if (this.mode !== '1') {
        this.selectedList[this.currentDate].splice(this.selectedList[this.currentDate].indexOf(this.dataList[index].uuid!), 1)
      } else {
        this.selectedList[this.currentDate].splice(this.selectedList[this.currentDate].indexOf(this.dataList[index].num), 1)
      }
    }
  }

  // 确定事件
  onConfirm() {
    const conditionList: QueryCondition[] = [
      {
        operation: 'effectDate:=',
        parameters: [this.currentDate]
      },
      {
        operation: this.mode !== '1' ? 'promBillUuids:in' : 'rprcAdjNums:in',
        parameters: this.selectedList[this.currentDate] || []
      }
    ]
    this.getGoods(conditionList)
  }

  // 获取单据下的商品
  async getGoods(conditionList: QueryCondition[] = []) {
    const params = new QueryRequest()
    params.page = this.resultPage
    params.pageSize = 20
    params.conditions.push(...conditionList)
    this.$showLoading({ delayTime: 200 })
    AppPrintLabelApi.queryGoods(params)
      .then((resp) => {
        this.$hideLoading()
        this.resultPage++
        if (resp.data) {
          const listData: AppPrintLabelGoodsExpandDTO[] = resp.data.map((item: AppPrintLabelGoodsDTO) => {
            return {
              ...item,
              qty: 1,
              checked: false,
              mfgDate: null,
              expDate: null,
              templateName: '',
              gid: ''
            }
          })
          if (params.page === 0) {
            this.result = listData
          } else {
            this.result.push(...listData)
          }
          if (resp.more) {
            this.getGoods(conditionList)
          } else {
            uni.$emit('labelPrintingSkuNumChange', { conditionList, result: this.result })
            this.selectedList = {}
            uni.navigateBack({})
          }
        }
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg || '加载失败' })
      })
  }
}
