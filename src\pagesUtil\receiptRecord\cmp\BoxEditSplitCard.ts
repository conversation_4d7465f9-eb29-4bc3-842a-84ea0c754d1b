/*
 * @Author: 刘湘
 * @Date: 2021-05-06 16:22:21
 * @LastEditTime: 2025-05-07 13:58:34
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /soa/src/pagesUtil/receiptRecord/cmp/BoxEditSplitCard.ts
 * 记得注释
 */
import { Vue, Component, Prop, Emit, Watch, Inject } from 'vue-property-decorator'
import { Getter, State } from 'vuex-class'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import CommonUtil from '@/utils/CommonUtil'
import AppReceiptRecordBoxGoodsDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxGoodsDTO'
import config from '@/config'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'

// eslint-disable-next-line @typescript-eslint/ban-ts-ignore
// @ts-ignore
@Component({
  components: {},
  options: {
    virtualHost: true
  }
})
export default class BoxEditSplitCard extends Vue {
  @Inject('queueObj') queueObj: any
  @Getter('qtyScale') qtyScale: number
  @State('optionList') optionList: ModuleOption[]
  @Prop({ type: Object, default: () => new AppReceiptRecordBoxGoodsDTO() }) sku: AppReceiptRecordBoxGoodsDTO // 商品信息
  @Prop({ type: String, default: '' }) openUserName: string // 收货人
  @Prop({ type: Boolean, default: false }) confirmed: boolean
  @Prop({ type: Number, default: 1 }) index: number // 商品下标顺序
  @Prop({ type: Boolean, default: false }) readonly: boolean

  // 非散称
  wholeQty: number = 0 // 整件数
  splitQty: number = 0 // 单品数

  // 散称
  dispWholeQty: number = 0 // 件数
  dispSplitQty: number = 0 // 重量

  showGift: boolean = false // 是否显示赠品
  exhibitValue: string = '' // 陈列位置输入框的值
  _uid: any

  /**
   * 是否展示陈列位置按钮
   */
  get isShowExhibitBtn() {
    return PermissionMgr.hasPermission(Permission.receiptDisplayLocationBind)
  }

  //获取字段展示权限
  get showMaster() {
    const masterPermission = {
      showDisplayLocation: false // 陈列位置
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDISPLAYLOCATION)) {
      masterPermission.showDisplayLocation = true
    }
    return masterPermission
  }

  // 是否启用差异原因功能
  get diffReasonsReportedByStore() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.diffReasonsReportedByStore == '1'
    }
    return false
  }

  // 散称双计量是否需要录入件数（包装数）
  get doubleMeasureGoodsEnterQpcQty() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.doubleMeasureGoodsEnterQpcQty == '1'
    }
    return false
  }

  /**
   * 陈列位置长度
   */
  get slotSegmentCount() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.slotSegmentCount) {
      return Number(moduleConfig[0].options.slotSegmentCount)
    }
    return 4
  }

  /**
   * 整件
   */
  get wholeMax() {
    if (this.sku) {
      return Math.floor((this.sku.qty - this.splitQty) / this.sku.goods.qpc)
    } else {
      return 9999999
    }
  }

  /**
   * 单品
   */
  get splitMax() {
    if (this.sku) {
      return (this.sku.qty - this.wholeQty * this.sku.goods.qpc).scale(this.qtyScale)
    } else {
      return 9999999
    }
  }

  /**
   * 差异
   */
  get diff() {
    if (this.sku) {
      return this.isDisp
        ? (this.sku.qty - Number(this.sku.receiptQty)).scale(this.qtyScale)
        : (this.sku.qty - this.wholeQty * this.sku.goods.qpc - this.splitQty).scale(this.qtyScale)
    } else {
      return 0
    }
  }

  /**
   * 是否有多个陈列位置
   */
  get hasMutiple() {
    return this.sku && this.sku.displayLocation && this.sku.displayLocation.split(',').length > 1
  }

  /**
   * 是否允许一品多货位
   */
  get allowOneGoodsMultipleSlot() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.allowOneGoodsMultipleSlot) {
      return moduleConfig[0].options.allowOneGoodsMultipleSlot === 'true'
    }
    return false
  }

  /**
   * 数据来源：0，H6，不显示陈列位置相关；1，鼎力云，显示
   */
  get slotSource() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.slotSource) {
      return moduleConfig[0].options.slotSource === '1'
    }
    return false
  }

  // 商品图片
  get img() {
    const sku = this.sku
    return sku && sku.goods.images && sku.goods.images.length && CommonUtil.isImageUrl(sku.goods.images[0])
      ? `${sku.goods.images[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
      : `${config.sourceUrl}icon/pic_goods.png`
  }

  get imageList() {
    const sku = this.sku
    return sku && sku.goods.images && sku.goods.images.filter((item) => CommonUtil.isImageUrl(item)).length
      ? sku.goods.images.filter((item) => CommonUtil.isImageUrl(item))
      : [`${config.sourceUrl}icon/pic_goods.png`]
  }

  @Watch('sku', { deep: true, immediate: true })
  onSkuChaneg(newVal) {
    if (newVal && !this.isDisp) {
      const qtyArr = (newVal.receiptQpcQty || '').split('+')
      if (qtyArr && qtyArr.length > 1) {
        this.wholeQty = Number(qtyArr[0])
        this.splitQty = Number(qtyArr[1])
      } else {
        this.wholeQty = Math.floor(Number(newVal.receiptQty) / newVal.goods.qpc)
        this.splitQty = (Number(newVal.receiptQty) - this.wholeQty * newVal.goods.qpc).scale(this.qtyScale)
      }
    } else if (this.isDisp) {
      this.dispWholeQty = Number(newVal.receiptQpcQty)
      this.dispSplitQty = Number(newVal.receiptQty)
    }
    this.exhibitValue = ''
  }

  get max() {
    if (!this.sku) {
      return 9999999
    } else {
      return Math.floor(this.sku.qty / this.sku.goods.qpc)
    }
  }

  /**
   * 商品名
   */
  get name() {
    if (this.sku && this.sku.goods && this.sku.goods.name) {
      return this.sku.goods.name
    } else {
      return ''
    }
  }

  /**
   * 商品inputCode
   */
  get inputCode() {
    if (this.sku && this.sku.goods && this.sku.goods.inputCode) {
      return this.sku.goods.inputCode
    } else {
      return ''
    }
  }

  /**
   * 商品qpcStr
   */
  get qpcStr() {
    if (this.sku && this.sku.goods && this.sku.goods.qpcStr) {
      return this.sku.goods.qpcStr
    } else {
      return ''
    }
  }

  /**
   * 商品price
   */
  get price() {
    if (this.sku && this.sku.goods && this.sku.goods.price) {
      return `￥${this.sku.goods.price}/${this.sku.goods.munit}`
    } else {
      return ''
    }
  }

  /**
   * 商品可售库存
   */
  get invQty() {
    if (this.sku && this.sku.invQty) {
      return `${this.sku.invQty}${this.sku.goods.minMunit}`
    } else {
      return ''
    }
  }

  /**
   * 商品应收数量
   */
  get isDisp() {
    if (this.sku) {
      return this.sku.goods.isDisp
    } else {
      return false
    }
  }

  mounted() {
    if (this.queueObj && this.queueObj.pushToQueue) {
      this.queueObj.pushToQueue({ close: this.closeGift, uid: this._uid })
    }
  }

  closeGift() {
    this.showGift = false
  }

  openGift() {
    this.showGift = true
    if (this.queueObj && this.queueObj.closeOther) {
      this.queueObj.closeOther({ close: this.closeGift, uid: this._uid })
    }
  }

  /**
   * 散称商品规格数量变化，同步重量
   */
  handleWholeChange() {
    const receiptQty = Number(this.dispWholeQty).multiply(this.sku.goods.qpc).scale(this.qtyScale)
    const receiptQpcQty = `${this.dispWholeQty}`
    const receiptTotal = receiptQty.multiply(this.sku.goods.price).divide(this.sku.goods.qpc).scale(2)
    const sku: AppReceiptRecordBoxGoodsDTO = CommonUtil.deepClone(this.sku)
    sku.receiptQpcQty = receiptQpcQty
    sku.receiptQty = receiptQty
    sku.receiptTotal = receiptTotal
    this.$emit('change', sku)
  }

  /**
   * 散称商品重量变化，不同步规格数量
   * 散称商品如果是1*1规格，则将重量同步给规格数量
   */
  handleSplitChange() {
    const receiptQty = Number(this.dispSplitQty).scale(this.qtyScale)
    // 散称商品如果是1*1规格，则将重量同步给规格数量
    const receiptQpcQty = this.sku.goods.qpcStr === '1*1' ? `${receiptQty}` : `${this.dispWholeQty}`
    const receiptTotal = receiptQty.multiply(this.sku.goods.price).divide(this.sku.goods.qpc).scale(2)
    const sku: AppReceiptRecordBoxGoodsDTO = CommonUtil.deepClone(this.sku)
    sku.receiptQpcQty = receiptQpcQty
    sku.receiptQty = Math.min(receiptQty, this.sku.qty)
    sku.receiptTotal = receiptTotal
    this.$emit('change', sku)
  }

  // 单品步进器点击事件
  doSingleChange(qty: number) {
    this.splitQty = qty.scale(this.qtyScale)
    const receiptQty = (this.wholeQty * this.sku.goods.qpc + this.splitQty).scale(this.qtyScale)
    const receiptQpcQty = `${this.wholeQty}+${this.splitQty}`
    const receiptTotal = (this.wholeQty * this.sku.goods.price + (this.splitQty * this.sku.goods.price) / this.sku.goods.qpc).scale(2)
    const sku: AppReceiptRecordBoxGoodsDTO = JSON.parse(JSON.stringify(this.sku))
    sku.receiptQpcQty = receiptQpcQty
    sku.receiptQty = receiptQty
    sku.receiptTotal = receiptTotal
    this.$emit('change', sku)
  }

  // 整件商品步进器点击事件
  doQpcChange(qty: number) {
    this.wholeQty = qty
    const receiptQty = (this.wholeQty * this.sku.goods.qpc + this.splitQty).scale(this.qtyScale)
    const receiptQpcQty = `${this.wholeQty}+${this.splitQty}`
    const receiptTotal = (this.wholeQty * this.sku.goods.price + (this.splitQty * this.sku.goods.price) / this.sku.goods.qpc).scale(2)
    const sku: AppReceiptRecordBoxGoodsDTO = JSON.parse(JSON.stringify(this.sku))
    sku.receiptQpcQty = receiptQpcQty
    sku.receiptQty = receiptQty
    sku.receiptTotal = receiptTotal
    this.$emit('change', sku)
  }

  /**
   * 编辑原因
   */
  handleEditReason() {
    this.$emit('edit-reason', this.sku)
  }

  /**
   * 预览图片
   */
  handlePreviewImg() {
    uni.previewImage({
      current: String(0),
      urls: this.imageList
    })
  }

  /**
   * 获取该商品信息
   */
  getGoodsInfo(e) {
    const value = e.detail.value
    if (!value && this.allowOneGoodsMultipleSlot) return
    if (!value && !this.allowOneGoodsMultipleSlot) {
      this.$showModal({
        title: '',
        content: '是否确定当前陈列位置置为空？',
        showCancel: true,
        confirmText: '确定',
        success: (action) => {
          if (action.confirm) {
            this.inputComfirm(value)
          }
        }
      })
      return
    }
    if (value) {
      this.inputComfirm(value)
    }
  }

  /**
   * 陈列位置确认
   */
  inputComfirm(value) {
    const regex = /^[A-Z0-9]+(-[A-Z0-9]+)*$/
    if (!regex.test(value) && value) {
      this.exhibitValue = ''
      this.$showToast({ title: '需要按照XX-XX-XX-XX，且只能为大写字母和数字的形式填写', icon: 'none' })
      return
    }
    if (regex.test(value) && value) {
      const list = value.split('-')
      if (list.length > this.slotSegmentCount) {
        this.exhibitValue = ''
        this.$showToast({ title: `陈列位置最大长度为${this.slotSegmentCount}级`, icon: 'none' })
        return
      } else {
        const idx = list.findIndex((item) => item.length > 4)
        if (idx > -1) {
          this.exhibitValue = ''
          this.$showToast({ title: `陈列位置每级最大长度为4`, icon: 'none' })
          return
        }
      }
    }
    this.sku.exhibitValue = value
    this.$emit('getRequestBody', this.sku)
  }

  /**
   * 扫码陈列位置
   */
  doScanExhibit(info) {
    uni.scanCode({
      success: (res) => {
        this.inputComfirm(res.result || '')
      }
    })
  }

  /**
   * 绑定陈列位置
   */
  bindExhibit() {
    this.$emit('bindExhibit', this.sku)
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit() {
    this.$emit('viewExhibit', this.sku)
  }

  /**
   * 陈列位置调整
   */
  resetExhibit() {
    this.$emit('resetExhibit', this.sku)
  }

  /**
   * 查看收货明细
   */
  handleViewDetail() {
    this.$emit('detail', this.sku)
  }

  /**
   * 标记已收
   */
  handleMarkConfirmed() {
    const sku: AppReceiptRecordBoxGoodsDTO = CommonUtil.deepClone(this.sku)
    sku.confirmed = true
    this.$emit('mark-confirmed', sku)
  }
}
