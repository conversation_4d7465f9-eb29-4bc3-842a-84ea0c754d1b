<template>
  <view class="spec-gd-regist-detail safe-area-inset-bottom" v-if="billDetail">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>

    <view class="top-header">
      <view class="state-line" @click="doShowProcess">
        <image :src="stateImgMap[billDetail.state || 'initial']" class="header-image"></image>
        <view class="state-text">{{ stateTextMap[billDetail.state || 'initial'] | empty }}</view>
        <image class="title-arrow" :src="'/static/icon/ic_noticebar_right_grey.png' | oss" mode="aspectFill"></image>
      </view>
      <view class="num-text">{{ billDetail.num | empty }}</view>
    </view>

    <!-- 商品明细 -->
    <spec-gd-regist-goods-list
      v-if="billLines && billLines.length"
      :lines="billLines.slice(0, 4)"
      :showMore="showMore"
      @total="doViewMore"
    ></spec-gd-regist-goods-list>

    <!-- 底部信息 -->
    <view class="bottom-info">
      <view class="field">
        <view class="label">库存分类</view>
        <view class="data">{{ billDetail.invCatName }}</view>
      </view>

      <view class="field">
        <view class="label">操作人</view>
        <view class="data">{{ billDetail.lastModifierName | empty }}</view>
      </view>
      <view class="field">
        <view class="label">操作时间</view>
        <view class="data">{{ billDetail.lastModified | fullDate | empty }}</view>
      </view>
    </view>

    <!-- 操作日志弹窗 -->
    <uni-popup type="bottom" ref="flowDialog">
      <flow-dialog :title="billDetail.type === 0 ? '特殊品登记流程' : '特殊品移除流程'"></flow-dialog>
    </uni-popup>
  </view>
</template>

<script lang="ts" src="./SpecGdRegistDetail.ts"></script>

<style lang="scss">
.spec-gd-regist-detail {
  min-height: 100vh;
  width: 100vw;
  background: $list-bg-color-lx;
  overflow-x: hidden;
  box-sizing: border-box;

  .top-header {
    width: 750rpx;
    box-sizing: border-box;
    padding: 36rpx $base-padding;

    .state-line {
      @include flex(row, flex-start, center);
      line-height: 48rpx;
      .header-image {
        margin-right: 12rpx;
        width: 48rpx;
        height: 48rpx;
      }
      .title-arrow {
        width: 32rpx;
        height: 32rpx;
      }

      .state-text {
        font-size: 36rpx;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: $color-text-secondary;
      }
      .ec-image {
        width: 28rpx;
        height: 28rpx;

        vertical-align: middle;
      }
      .ec-tag {
        width: 96rpx;
        height: 40rpx;
        line-height: 40rpx;

        background: #1857fc;
        border-radius: 20rpx;
        margin-left: 12rpx;

        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        text-align: center;
      }
    }
    .num-text {
      margin-left: 50rpx;
      margin-top: 8rpx;
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $color-text-thirdly;
      line-height: 40rpx;
    }
  }

  .bottom-info {
    border-radius: 12rpx;
    overflow: hidden;
    background-color: #ffffff;
    margin: 16rpx 0;
    padding: 24rpx $base-padding;
    box-sizing: border-box;

    .field {
      width: 100%;
      display: flex;
      font-size: 28rpx;

      .label {
        flex: 0 0 auto;
        min-width: 140rpx;
        color: $font-color-darklight;
        height: 40rpx;
      }

      .data {
        word-break: break-all;
        color: $color-text-primary;
        margin-left: 15rpx;
        flex: 1 1 auto;
        flex-wrap: wrap;
        min-height: 40rpx;
        text-align: right;
      }

      & + .field {
        margin-top: 24rpx;
      }
    }
  }
}
</style>
