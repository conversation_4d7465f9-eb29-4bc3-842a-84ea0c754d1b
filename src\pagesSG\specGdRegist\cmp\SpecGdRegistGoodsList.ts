/*
 * @Author: weish<PERSON>
 * @Date: 2025-04-25 10:00:00
 * @LastEditTime: 2025-04-24 10:19:01
 * @LastEditors: weisheng
 * @Description: 特殊品登记商品列表组件
 * @FilePath: /soa/src/pagesSG/specGdRegist/cmp/SpecGdRegistGoodsList.ts
 * 记得注释
 */
import { Vue, Component, Prop } from 'vue-property-decorator'
import AppSpecGdRegistLineDTO from '@/model/AppSpecGdRegist/AppSpecGdRegistLineDTO'
import config from '@/config'
import CommonUtil from '@/utils/CommonUtil'

@Component({})
export default class SpecGdRegistGoodsList extends Vue {
  @Prop({ default: () => [] }) lines!: AppSpecGdRegistLineDTO[] // 商品列表
  @Prop({ default: false }) showMore!: boolean // 是否显示更多按钮
  @Prop({ default: false }) isSticky!: boolean // 是否固定标题

  // 商品图片
  skuImg(line: AppSpecGdRegistLineDTO): string {
    if (line.images && line.images.length && CommonUtil.isImageUrl(line.images[0])) {
      return `${line.images[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
    }
    return `${config.sourceUrl}icon/pic_goods.png`
  }

  // 获取图片列表
  imageList(line: AppSpecGdRegistLineDTO): string[] {
    if (line.images && line.images.filter((item: string) => CommonUtil.isImageUrl(item)).length) {
      return line.images.filter((item: string) => CommonUtil.isImageUrl(item))
    }
    return [`${config.sourceUrl}icon/pic_goods.png`]
  }

  // 预览图片
  handlePreviewImg(line: AppSpecGdRegistLineDTO) {
    const images = this.imageList(line)
    if (images.length > 0) {
      uni.previewImage({
        urls: images,
        current: images[0]
      })
    }
  }

  // 查看更多
  viewMore() {
    this.$emit('total')
  }
}
