<!--
 * @Author: 庞昭昭
 * @Date: 2021-08-03 09:55:51
 * @LastEditTime: 2025-05-15 11:40:36
 * @LastEditors: hanwei
 * @Description: 商品明细（购物车）
 * @FilePath: \soa\src\pagesSD\gdQuery\GdQuerySkuDetail.vue
 * 记得注释
-->
<template>
  <view class="gd-query-sku-detail" @click="doCloseOutside">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <view class="swiper" v-if="swiperList.length">
      <hd-swiper :list="swiperList" borderRadius="0" :height="536" mode="dot" @click="bannerClick" :autoplay="false"></hd-swiper>
    </view>
    <view class="info card">
      <view class="card-header">商品信息</view>
      <view class="card-body">
        <view class="card-body__name">
          <text class="new" v-if="isNewList">新</text>
          <text :class="[skuInfo.goods.busGateCode === 'S00001' ? 'green' : 'yellow']" v-if="skuInfo.goods.busGateCode">
            {{ skuInfo.goods.busGateName | trimString }}
          </text>
          <text>{{ friendlyStr | empty }}</text>
        </view>
        <view class="sku-tags">
          <view class="sku-tags-content" v-if="skuInfo.goods && skuInfo.goods.tags.length">
            <view class="sku-tags-item" v-for="(item, index) in skuInfo.goods.tags" :key="index">{{ item.name }}</view>
          </view>
        </view>
        <view class="sku-sorts" v-if="showMaster.showSkuCategory">
          <text>品类：</text>
          <text class="sku-sorts-value">{{ category }}</text>
        </view>
        <view class="card-body__item">
          <text class="label">商品条码：</text>
          <text class="value">{{ skuInfo.goods.inputCode | empty }}</text>
        </view>
        <view class="card-body__item">
          <text class="label">商品代码：</text>
          <text class="value">{{ skuInfo.goods.code | empty }}</text>
        </view>
        <view class="card-body__item" v-if="showMaster.showDetailSkuSpec">
          <text class="label">箱规：</text>
          <text class="value">{{ skuInfo.goods.bulkPackDesc | empty }}</text>
        </view>

        <view class="card-body__item">
          <text class="label">保质期：</text>
          <text class="value">{{ skuInfo.goods.validPeriod | empty }}天</text>
        </view>

        <view class="card-body__item">
          <text class="label">零售价：</text>
          <text class="value">￥{{ skuInfo.goods.rtlPrc | empty }}/{{ skuInfo.goods.minMunit | empty }}</text>
        </view>

        <view class="card-body__item" v-if="!hidePrice && showMaster.showOrderPrice">
          <text class="label">订货价：</text>
          <text class="value">￥{{ skuInfo.goods.price | empty }}/{{ skuInfo.goods.munit | empty }}</text>
        </view>

        <view class="card-body__item" v-if="!hidePrice && showMaster.showInvPrice">
          <text class="label">库存成本价：</text>
          <text class="value">￥{{ skuInfo.goods.invPrc | empty }}/{{ skuInfo.goods.minMunit | empty }}</text>
        </view>

        <view class="card-body__item" v-if="!hideRate && skuInfo.goods.grossProfitTitle">
          <hd-popover>
            <view style="display: inline-flex; align-items: center">
              <text class="label">{{ skuInfo.goods.grossProfitTitle }}</text>
              <image class="img" :src="'/static/icon/<EMAIL>' | oss"></image>
              <text class="label">：</text>
            </view>
            <text class="value">{{ skuInfo.goods.grossProfit | empty }}</text>
            <template #bubble>
              <view style="width: 232rpx">不包含今日</view>
            </template>
          </hd-popover>
        </view>

        <view
          class="card-body__item"
          v-if="showMaster.showDetailSkuQualification && skuInfo.goods.quaImages && skuInfo.goods.quaImages.length"
          @click.stop="handlePreviewQuaImg(skuInfo.goods.quaImages)"
        >
          <text class="label">商品资质：</text>
          <text class="value">查看</text>
          <image class="img" :src="'/static/icon/ic_right_grey.png' | oss"></image>
        </view>
        <view class="card-body__description" v-if="showDetailSkuExplain && skuInfo.goods.description">
          <text>说明：{{ skuInfo.goods.description }}</text>
        </view>
      </view>
    </view>
    <view class="stock card">
      <view class="card-header">销售/库存信息</view>
      <view class="card-body">
        <view class="card-body__item" v-if="showMaster.showSellableInvQty">
          <hd-popover>
            <view style="display: inline-flex; align-items: center">
              <text class="label">可售库存</text>
              <image class="img" :src="'/static/icon/<EMAIL>' | oss"></image>
              <text class="label">：</text>
            </view>
            <text class="value" v-if="isDisp || invQtyDisplayStrategy">{{ skuInfo.goods.invQty | empty }}{{ skuInfo.goods.minMunit | empty }}</text>
            <text class="value" v-else>{{ skuInfo.goods.invQpcQtyTitle | empty }}</text>
            <template #bubble>
              <view style="width: 232rpx">指门店的实物库存</view>
            </template>
          </hd-popover>
        </view>
        <view class="card-body__item">
          <hd-popover>
            <view style="display: inline-flex; align-items: center">
              <text class="label">在途库存</text>
              <image class="img" :src="'/static/icon/<EMAIL>' | oss"></image>
              <text class="label">：</text>
            </view>
            <text class="value" v-if="isDisp || invQtyDisplayStrategy">
              {{ skuInfo.goods.inTransitInvQty | empty }}{{ skuInfo.goods.minMunit | empty }}
            </text>

            <text class="value" v-else>{{ skuInfo.goods.inTransitInvQpcQty | empty }}</text>
            <template #bubble>
              <view style="width: 232rpx">指发货方已发出但暂未收货的库存</view>
            </template>
          </hd-popover>
        </view>
        <view class="card-body__item">
          <hd-popover>
            <view style="display: inline-flex; align-items: center">
              <text class="label">未发货库存</text>
              <image class="img" :src="'/static/icon/<EMAIL>' | oss"></image>
              <text class="label">：</text>
            </view>
            <text class="value" v-if="isDisp || invQtyDisplayStrategy">
              {{ skuInfo.goods.inOrderInvQty | empty }}{{ skuInfo.goods.minMunit | empty }}
            </text>

            <text class="value" v-else>{{ skuInfo.goods.inOrderInvQpcQty | empty }}</text>
            <template #bubble>
              <view style="width: 232rpx">指订单已确认但尚未发货的库存</view>
            </template>
          </hd-popover>
        </view>
        <view class="card-body__item" v-if="showMaster.showHighInv">
          <text class="label">库存上限：</text>
          <text class="value">{{ skuInfo.goods.highInv | empty }}{{ skuInfo.goods.minMunit | empty }}</text>
        </view>
        <view class="card-body__item" v-if="showMaster.showLowInv">
          <text class="label">库存下限：</text>
          <text class="value">{{ skuInfo.goods.lowInv | empty }}{{ skuInfo.goods.minMunit | empty }}</text>
        </view>
        <view class="card-body__item" v-if="showMaster.showDetailSkuMonthSale">
          <text class="label">月销量：</text>
          <text class="value">{{ skuInfo.goods.monthSaleQty | empty }}{{ skuInfo.goods.minMunit | empty }}</text>
        </view>
        <view class="card-body__item" v-if="showMaster.showDetailSkuWeekSale">
          <text class="label">周销量：</text>
          <text class="value">{{ skuInfo.goods.weekSaleQty | empty }}{{ skuInfo.goods.minMunit | empty }}</text>
        </view>
        <view class="main-half" v-if="showMaster.showDisplayLocation">
          <view :class="[hasMutiple(skuInfo.goods.displayLocation) ? 'goods-one' : '']">
            陈列位置：
            <text style="color: #333333">{{ skuInfo.goods.displayLocation | empty }}</text>
          </view>
          <image
            class="good-img"
            :src="'/static/icon/ic_right_grey.png' | oss"
            v-if="hasMutiple(skuInfo.goods.displayLocation)"
            @click="viewExhibit"
          />
        </view>
      </view>
    </view>
    <view class="sale card" v-if="showAvg">
      <view class="card-header">平均日销</view>
      <view class="card-body">
        <avg-sale-dialog :enableDatasource="true" :datasource="skuInfo" :uuid="uuid" :inputCode="inputCode"></avg-sale-dialog>
      </view>
    </view>

    <!-- 查看多个陈列位置 -->
    <uni-popup ref="viewExhibit" type="bottom">
      <view-exhibit :name="skuInfo.goods.friendlyStr" :displayLocation="skuInfo.goods.displayLocation" @doClose="closeViewExhibit"></view-exhibit>
    </uni-popup>
  </view>
</template>
<script lang="ts" src="./GdQuerySkuDetail.ts"></script>
<style lang="scss" scoped>
.gd-query-sku-detail {
  position: relative;
  width: 750rpx;
  overflow-x: hidden;
  min-height: 100vh;
  box-sizing: border-box;
  background: #f6f6f6;
  padding-bottom: 0 !important;
  padding-bottom: constant(safe-area-inset-bottom) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
  .swiper {
    margin-bottom: 20rpx;
  }

  .card {
    width: 100%;
    background: #ffffff;
    &:not(:last-child) {
      margin-bottom: 20rpx;
    }

    &-header {
      position: relative;
      height: 88rpx;
      width: 100%;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      padding: 0 24rpx;

      font-size: 32rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 550;
      color: #1a1a1a;
      &::after {
        content: ' ';
        position: absolute;
        z-index: 1;
        width: 100%;
        height: 2rpx;
        background: #e1e1e1;
        left: 0;
        bottom: 0;
        transform: scaleY(0.5);
      }
    }

    &-body {
      display: flex;
      flex-wrap: wrap;
      box-sizing: border-box;
      padding: 24rpx;

      &__name {
        width: 100%;
        box-sizing: border-box;
        font-family: $font-medium;
        font-weight: 550;
        font-size: 30rpx;
        color: #282c34;
        text-align: left;
        font-style: normal;
        line-height: 40rpx;
        @include ellipsis(2);
        margin-bottom: 10rpx;

        .new {
          background: linear-gradient(180deg, #fd4f6e 0%, #fa273b 100%);
          border-radius: 4rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #ffffff;
          line-height: 32rpx;
          padding: 0 4rpx;
          text-align: left;
          font-style: normal;
          margin-right: 8rpx;
        }

        .green {
          padding: 0 4rpx;
          font-weight: 550;
          font-size: 24rpx;
          color: #08b07c;
          background: #e9fff1;
          border-radius: 4rpx;
          border: 1rpx solid rgba(8, 176, 124, 0.65);
          margin-right: 8rpx;
          @include ellipsis();
        }

        .yellow {
          background: #fffbf6;
          border-radius: 4rpx;
          border: 1rpx solid #fd9b1c;
          font-weight: 550;
          padding: 0 4rpx;
          font-size: 24rpx;
          color: #fd9b1c;
          line-height: 32rpx;
          margin-right: 8rpx;
          @include ellipsis();
        }
      }
      &__item {
        flex: 0 0 auto;
        width: 50%;
        word-break: break-all;
        display: flex;
        align-items: center;
        min-height: 42rpx;

        .label {
          font-size: 24rpx;
          color: #94969a;
          line-height: 32rpx;
        }
        .value {
          font-size: 24rpx;
          color: #585a5e;
          line-height: 32rpx;
        }
        .exhibit {
          max-width: 180rpx;
          @include ellipsis();
        }
        .good-img {
          width: 32rpx;
          height: 32rpx;
        }
        .ex_value {
          max-width: 180rpx;
          @include ellipsis();
        }
        .img {
          width: 32rpx;
          height: 32rpx;
        }
      }

      &__description {
        margin-top: 16rpx;
        width: 100%;
        background: #f5f6f7;
        border-radius: 8rpx;
        box-sizing: border-box;
        padding: 12rpx;
        font-size: 24rpx;
        color: #94969a;
        line-height: 32rpx;
      }
      .main-half {
        width: 100%;
        flex: 0 0 auto;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #94969a;
        display: flex;
        align-items: center;
        margin-top: 12rpx;

        .good-img {
          width: 32rpx;
          height: 32rpx;
          display: inline-table;
        }

        .goods-one {
          flex: 1;
          @include ellipsis();
        }
      }

      .sku-sorts {
        width: 100%;
        min-height: 42rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        line-height: 42rpx;
        text-align: left;
        display: flex;

        &-value {
          flex: 1;
          @include ellipsis();
          color: #585a5e;
        }
      }

      .sku-tags {
        width: 100%;
        margin-bottom: 8rpx;
        display: flex;
        justify-content: space-between;

        &-content {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          gap: 8rpx;
        }

        &-item {
          border-radius: 4rpx;
          border: 1rpx solid #cccccc;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 20rpx;
          color: #666666;
          line-height: 32rpx;
          padding: 0 8rpx;
          box-sizing: border-box;
          max-width: 326rpx;
          @include ellipsis();
        }

        &-img {
          width: 36rpx;
          height: 36rpx;
        }

        &-expand {
          transform: rotate(180deg);
        }
      }
    }
  }

  .expiration-tip {
    width: 100%;
    height: 72rpx;
    box-sizing: border-box;
    padding: 16rpx 24rpx;
    background: #e9f0ff;
    font-size: 26rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 40rpx;
    display: flex;
    align-items: center;
  }

  .sale {
    .card-body {
      padding: 0 !important;
    }
  }
}
</style>
