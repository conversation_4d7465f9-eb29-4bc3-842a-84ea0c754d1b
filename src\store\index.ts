/*
 * @Author: 徐庆凯
 * @Date: 2020-11-04 13:34:38
 * @LastEditTime: 2025-05-24 10:16:47
 * @LastEditors: hanwei
 * @Description:vuex
 * @FilePath: /soa/src/store/index.ts
 * @symbol_custom_string_obkoro1: 记得注释
 */
import Vue from 'vue'
import Vuex from 'vuex'
import { Commit } from 'vuex'
import UserInfo from '@/model/user/UserInfo'
import StoragePlugin from './StoragePlugin'
import Store from '@/model/store/Store'
import { State } from 'vuex-class'
import config from '@/config'

import SafeConfigItem from '@/model/user/SafeConfigItem'
import UserApi from '@/network/user/UserApi'
import SosStore from '@/model/default/Store'
import ModuleOption from '@/model/default/ModuleOption'
import SysConfigItem from '@/model/sysConfig/SysConfigItem'
import PmsPromotionConfig from '@/model/pms/config/PmsPromotionConfig'
import Position from '@/model/employee/Position'
import ConfigResult from '@/model/jyzsData/ConfigResult'
import TabbarInfo from '@/model/tabbar/TabbarInfo'
import { HdSetTabBarBadgeOptions } from '@/typings/custom'
import MpInfo from '@/model/microApp/MpInfo'
import LogMgr from '@/Log/LogMgr'
import { PositionType } from '@/model/position/PositionType'
import RequireApplyStoreInfo from '@/model/requireApply/RequireApplyStoreInfo'
import { ModuleId } from '@/model/common/OptionListModuleId'
import ModuleFieldRights from '@/model/default/ModuleFieldRights'
import ShopScoreSetting from '@/model/shopScore/ShopScoreSetting'
import TokenInfo from '@/model/portalModel/cache/TokenInfo'
import CUserLogonInfo from '@/model/portalModel/login/CUserLogonInfo'
import TenantVo from '@/model/portalModel/tenant/TenantVo'
import PrintModuleSettingVo from '@/model/portalModel/print/PrintModuleSettingVo'
import StationModal from '@/model/station/Station'
import OrderShip from '@/model/orderPerformance/OrderShip'
import Vendor from '@/model/boothInfo/Vendor'
import IMMessageGroup from '@/model/IMMessage/IMMessageGroup'
import CMessageTopicGroupVO from '@/model/portalModel/basic/CMessageTopicGroupVO'
import CodeName from '@/model/base/CodeName'
Vue.use(Vuex)

export interface State {
  tenantInfo: TenantVo // portal中的租户信息
  orgInfo: OrgQueryRes // portal中的所选的组织信息
  userInfo: Nullable<UserInfo> // 用户信息
  employeeInfo: CUserLogonInfo // portal中的员工信息
  tokenInfo: TokenInfo // portal中的登录Token信息
  appSearchHistory: Nullable<string[]> // 应用搜索历史
  goodsList: any[]
  templateInfo: Nullable<CodeName> // 单品打折模板信息
  supplierDirectGoodsList: any[] //供应商商品列表
  store: Nullable<Store> // 选择的门店信息
  storeTotal: Nullable<number> // 门店总数
  showTip: Nullable<boolean> // 门店日结底部btn按钮是否显示上方的提示
  permission: string[] // 权限列表（单单权限信息）
  permissionLoaded: boolean // 权限是否已加载完成
  orderHistory: Nullable<string[]> // 订货单搜索历史
  messageMustRead: Nullable<boolean> // 公告是否强制必读
  staticPath: string // 静态文件路径
  isGoods: Nullable<boolean> // 是否为商品
  config: Nullable<SafeConfigItem> // 安全策略的配置
  faqHistory: Nullable<string[]> // 订货单搜索历史
  isLoginDirect: boolean // 是否免密登录（用于控制是否调用单点登录校验接口）
  optionList: ModuleOption[] // 获取模块选项配置
  moduleFieldRightsList: ModuleFieldRights[] // 获取模块字段权限
  cloudFund: Nullable<{ appId: string; mid: string }> //云资金配置
  imgUrlList: Record<string, string> // 图片地址map
  marketPlayConfig: PmsPromotionConfig
  positionList: Position[] // 用户角色集合
  openCheckQueryList: Record<string, string> //不停业盘点新增数据恢复
  openCheckChangeList: Record<string, boolean> //不停业盘点编辑数据恢复
  appList: Nullable<any[]> //常用应用列表
  bigDataTenant: Nullable<string> //对应大数据租户
  jyzsconfigResult: ConfigResult //经营助手配置
  checkQueryList: Record<string, string> //停业盘点新增数据恢复
  checkChangeList: Record<string, boolean> //停业盘点编辑数据恢复
  showMineTask: Nullable<boolean> // 任务中心是否显示仅查看我的任务
  sysConfig: Nullable<SysConfigItem> // 全局配置
  fieldsConfig: string[] // 权限配置
  signInfo: any //OSS签名信息
  active: boolean // 门店助手是否设备激活（仅APP）
  isOrderHasNotSave: boolean // 是否提示数据未保存
  tabbarInfo: TabbarInfo // 自定义tabbar,
  currentTenant: string // 当前租户id
  bindTerminalId: string // 绑定的终端号
  bindStoreId: string // 绑定的门店号
  microInfo: Nullable<MpInfo> // 在uni-portal环境中的登录信息
  enableCheckLicense: boolean // 是否可以开始校验license（用于完成登录等操作时触发定时校验）
  isVmdTaskSuper: boolean //是否为超市陈列任务
  requireApplyStoreInfo: RequireApplyStoreInfo // 要货申请模块配置
  selfPickUp: Nullable<number> //是否自提
  receiptName: string //操作人姓名
  shopScoreSettingList: Nullable<ShopScoreSetting[]> // 门店分种列表
  storeInfo: Nullable<SosStore> //门店店务信息
  currentDevice: Nullable<BluetoothDevice>
  appHasUpdate: {
    time: number // 检查时间
    status: boolean // 是否需要更新
  } | null // app是否需要更新
  isHdPortal: boolean // 是否是portal登录模式
  myDeviceList: PrintModuleSettingVo[] // 打印设备列表
  // 履约助手
  station: Nullable<StationModal> // 摊位
  lyzsTenant: string // 履约助手租户ID
  jyzsTenant: string // 经营助手租户ID
  performanceDetail: Nullable<OrderShip> // 履约详情模型
  vendor: Nullable<Vendor> // 供应商
  msgInfo: Nullable<IMMessageGroup> // 客服消息会话
  portalMsgGroups: CMessageTopicGroupVO[] //  portal消息分组列表
  messageGetPoll: Nullable<number> // 获取消息定时器id
  messagePrintPoll: Nullable<number> // 打印定时器id
}

// #ifndef APP-PLUS
// 定时器用来查询登录状态是否有变
let timer: any = null

/**
 * 查询用户登录状态是否有变
 */
function doChange() {
  UserApi.changed()
  timer = setInterval(() => {
    UserApi.changed()
  }, 120000)
}
// #endif

const store = new Vuex.Store({
  plugins: [StoragePlugin],
  state: {
    tenantInfo: new TenantVo(), // portal中的租户信息
    userInfo: null,
    tokenInfo: new TokenInfo(), // 登录Token信息
    employeeInfo: new CUserLogonInfo(), // 员工信息
    orgInfo: {} as OrgQueryRes, // portal中的所选的组织信息
    imgUrlList: {}, // 图片地址map
    appSearchHistory: null, // 应用搜索历史
    templateInfo: null, // 单品打折模板信息
    supplierDirectGoodsList: [], //供应商商品列表
    goodsList: [], // 报损/报溢商品列表
    store: null, // 选择的门店信息
    storeTotal: null, // 门店总数
    showTip: null, // 日结页面底部btn提示语是否显示
    permission: [], // 权限
    permissionLoaded: false, // 权限是否已加载完成
    orderHistory: null, // 订货单据搜索历史
    messageMustRead: false, // 公告是否强制必读
    staticPath: config.source, // 静态文件路径
    isGoods: false, // 加工模块是否为选择商品，用来控制原料成品联动，默认为否
    config: null, // 安全策略的配置
    faqHistory: null, // faq搜索历史
    isLoginDirect: false, // 是否免密登录（用于控制是否调用单点登录校验接口）
    optionList: [], // 获取模块选项配置
    moduleFieldRightsList: [], // 获取模块字段权限
    cloudFund: null,
    checkQueryList: {},
    checkChangeList: {},
    openCheckQueryList: {}, //不停业盘点未保存的盘点计划及对应草稿标识号
    openCheckChangeList: {}, //不停业盘点有修改记录的盘点计划及对应草稿标识号
    marketPlayConfig: new PmsPromotionConfig(),
    positionList: [], // 用户角色集合
    appList: null, //常用应用列表
    bigDataTenant: null, //对应大数据租户
    jyzsconfigResult: new ConfigResult(), //经营助手配置
    showMineTask: false,
    sysConfig: new SysConfigItem(), // 全局设置
    fieldsConfig: [], // 配置权限
    signInfo: {}, //OSS签名信息
    active: false, // 门店助手是否设备激活（仅APP）
    isOrderHasNotSave: false, // 是否提示数据未保存
    tabbarInfo: new TabbarInfo(), // 自定义tabbar
    currentTenant: '', // 当前租户id（授权码或者输入获取到的租户id）
    bindTerminalId: '', // 绑定的终端号
    bindStoreId: '', // 绑定的门店号
    microInfo: null, // 在uni-portal环境中的登录信息
    enableCheckLicense: false, // 是否可以开始校验license（用于完成登录等操作时触发定时校验）
    isVmdTaskSuper: false,
    requireApplyStoreInfo: new RequireApplyStoreInfo(),
    selfPickUp: null,
    receiptName: '',
    shopScoreSettingList: [], // 门店分种列表
    storeInfo: null,
    currentDevice: null, // 当前蓝牙设备
    appHasUpdate: null, // app是否需要更新
    isHdPortal: false, // 是否是portal登录模式
    myDeviceList: [], // 打印设备列表
    // 履约助手
    station: null, // 摊位信息
    lyzsTenant: '', // 履约助手租户ID
    jyzsTenant: '', // 经营助手租户ID
    performanceDetail: null,
    vendor: null, // 供应商信息
    msgInfo: null, // 客服消息会话
    portalMsgGroups: [], // portal消息分组列表
    messageGetPoll: null, // 获取消息定时器id
    messagePrintPoll: null // 打印定时器id
  },
  mutations: {
    tenantInfo(state: State, tenantInfo: TenantVo) {
      state.tenantInfo = tenantInfo
    },
    userInfo(state: State, userInfo: UserInfo) {
      state.userInfo = userInfo
      LogMgr.init()
    },
    tokenInfo(state: State, tokenInfo: TokenInfo) {
      state.tokenInfo = tokenInfo
    },
    employeeInfo(state: State, employeeInfo: CUserLogonInfo) {
      state.employeeInfo = employeeInfo
    },
    orgInfo(state: State, orgInfo: OrgQueryRes) {
      state.orgInfo = orgInfo
    },
    appSearchHistory(state: State, history: string[]) {
      state.appSearchHistory = history
    },
    mutationTemplateInfo(state: State, templateInfo: CodeName) {
      state.templateInfo = templateInfo
    },
    goodsList(state: State, goodsList: any[]) {
      state.goodsList = goodsList
    },
    supplierDirectGoodsList(state: State, supplierDirectGoodsList: any[]) {
      state.supplierDirectGoodsList = supplierDirectGoodsList
    },

    store(state: State, store: Store) {
      state.store = store
    },
    storeTotal(state: State, storeTotal: number) {
      state.storeTotal = storeTotal
    },
    showTip(state: State, showTip: boolean) {
      state.showTip = showTip
    },
    permission(state: State, permission: string[]) {
      state.permission = permission
      state.permissionLoaded = true
    },
    orderHistory(state: State, history: string[]) {
      state.orderHistory = history
    },

    messageMustRead(state: State, messageMustRead: boolean) {
      state.messageMustRead = messageMustRead
    },
    config(state: State, config: SafeConfigItem) {
      state.config = config
      // #ifndef APP-PLUS
      if (config.enableOnlyOneAccess && !state.isLoginDirect) {
        clearInterval(timer)
        doChange()
      } else {
        clearInterval(timer)
      }
      // #endif
    },
    isGoods(state: State, isGoods: boolean) {
      state.isGoods = isGoods
    },
    clear(state: State) {
      state.userInfo = null
      state.tokenInfo = new TokenInfo()
      state.employeeInfo = new CUserLogonInfo()
      // state.tenantInfo = new TenantVo()
      state.store = null
      state.storeTotal = null
      state.showTip = null
      state.positionList = []
      state.permission = []
      state.fieldsConfig = []
      state.enableCheckLicense = false
      state.receiptName = ''
      state.permissionLoaded = false
      state.vendor = null
      if (state.messageGetPoll) {
        clearInterval(state.messageGetPoll)
        state.messageGetPoll = null
      }
      if (state.messagePrintPoll) {
        clearInterval(state.messagePrintPoll)
        state.messagePrintPoll = null
      }
    },
    faqHistory(state: State, faqHistory: string[]) {
      state.faqHistory = faqHistory
    },
    isLoginDirect(state: State, isLoginDirect: boolean) {
      state.isLoginDirect = isLoginDirect
    },
    optionList(state: State, optionList: ModuleOption[]) {
      state.optionList = optionList
    },
    moduleFieldRightsList(state: State, moduleFieldRightsList: ModuleFieldRights[]) {
      state.moduleFieldRightsList = moduleFieldRightsList
    },
    cloudFund(state: State, cloudFund: any) {
      state.cloudFund = cloudFund
    },
    imgUrlList(state: State, imgUrlList: Record<string, string>) {
      state.imgUrlList = imgUrlList
    },
    openCheckQueryList(state: State, openCheckQueryList: Record<string, string>) {
      state.openCheckQueryList = openCheckQueryList
    },
    openCheckChangeList(state: State, openCheckChangeList: Record<string, boolean>) {
      state.openCheckChangeList = openCheckChangeList
    },
    positionList(state: State, positionList: Position[]) {
      state.positionList = positionList
    },
    appList(state: State, appList: Nullable<any[]>) {
      state.appList = appList
    },
    bigDataTenant(state: State, bigDataTenant: Nullable<string>) {
      state.bigDataTenant = bigDataTenant
    },
    jyzsconfigResult(state: State, jyzsconfigResult: ConfigResult) {
      state.jyzsconfigResult = jyzsconfigResult
    },
    checkQueryList(state: State, checkQueryList: Record<string, string>) {
      state.checkQueryList = checkQueryList
    },
    checkChangeList(state: State, checkChangeList: Record<string, boolean>) {
      state.checkChangeList = checkChangeList
    },
    showMineTask(state: State, showMineTask: boolean) {
      state.showMineTask = showMineTask
    },
    marketPlayConfig(state: State, marketPlayConfig: PmsPromotionConfig) {
      state.marketPlayConfig = marketPlayConfig
    },
    sysConfig(state: State, sysConfig: SysConfigItem) {
      state.sysConfig = sysConfig
    },
    fieldsConfig(state: State, fieldsConfig: string[]) {
      state.fieldsConfig = fieldsConfig
    },
    signInfo(state: State, signInfo: any) {
      state.signInfo = signInfo
    },
    active(state: State, active: boolean) {
      state.active = active
    },
    isOrderHasNotSave(state: State, isOrderHasNotSave: any) {
      state.isOrderHasNotSave = isOrderHasNotSave
    },
    tabbarInfo(state: State, tabbarInfo: TabbarInfo) {
      state.tabbarInfo = tabbarInfo
    },
    setTabBarBadge(state: State, option: HdSetTabBarBadgeOptions) {
      if (state.tabbarInfo && state.tabbarInfo.list && state.tabbarInfo.list[option.index]) {
        state.tabbarInfo.list[option.index].count = option.text
      }
    },
    currentTenant(state: State, currentTenant: string) {
      state.currentTenant = currentTenant
    },
    bindTerminalId(state: State, bindTerminalId: string) {
      state.bindTerminalId = bindTerminalId
    },
    bindStoreId(state: State, bindStoreId: string) {
      state.bindStoreId = bindStoreId
    },
    microInfo(state: State, microInfo: MpInfo) {
      state.microInfo = microInfo
    },
    enableCheckLicense(state: State, enableCheckLicense: boolean) {
      state.enableCheckLicense = enableCheckLicense
    },
    isVmdTaskSuper(state: State, isVmdTaskSuper: boolean) {
      state.isVmdTaskSuper = isVmdTaskSuper
    },
    requireApplyStoreInfo(state: State, requireApplyStoreInfo: RequireApplyStoreInfo) {
      state.requireApplyStoreInfo = requireApplyStoreInfo
    },
    selfPickUp(state: State, selfPickUp: number) {
      state.selfPickUp = selfPickUp
    },
    receiptName(state: State, receiptName: string) {
      state.receiptName = receiptName
    },
    shopScoreSettingList(state: State, shopScoreSettingList: any[]) {
      state.shopScoreSettingList = shopScoreSettingList
    },
    storeInfo(state: State, storeInfo: SosStore) {
      state.storeInfo = storeInfo
    },
    currentDevice(state: State, currentDevice: Nullable<BluetoothDevice>) {
      state.currentDevice = currentDevice
    },
    appHasUpdate(
      state: State,
      appHasUpdate: {
        time: number // 检查时间
        status: boolean // 是否需要更新
      } | null
    ) {
      state.appHasUpdate = appHasUpdate
    },
    setIsHdPortal(state: State, isHdPortal: boolean) {
      state.isHdPortal = isHdPortal
    },
    setMyDeviceList(state: State, myDeviceList: PrintModuleSettingVo[]) {
      state.myDeviceList = myDeviceList
    },
    // 履约助手
    setLyzsTenant(state: State, lyzsTenant: string) {
      state.lyzsTenant = lyzsTenant
    },
    //jyzs租户
    setJyzsTenant(state: State, jyzsTenant: string) {
      state.jyzsTenant = jyzsTenant
    },
    performanceDetail(state: State, performanceDetail: any) {
      state.performanceDetail = performanceDetail
    },
    vendor(state: State, vendor: Nullable<Vendor>) {
      state.vendor = vendor
    },
    msgInfo(state: State, msgInfo: Nullable<IMMessageGroup>) {
      state.msgInfo = msgInfo
    },
    portalMsgGroups(state: State, portalMsgGroups: CMessageTopicGroupVO[]) {
      state.portalMsgGroups = portalMsgGroups
    },
    setMessageGetPoll(state: State, messageGetPoll: number) {
      state.messageGetPoll = messageGetPoll
    },
    setMessagePrintPoll(state: State, messagePrintPoll: number) {
      state.messagePrintPoll = messagePrintPoll
    }
  },
  getters: {
    tenantInfo: (state: State) => {
      return state.tenantInfo
    },
    /**
     * 用户信息
     * @param state
     * @returns
     */
    userInfo: (state: State) => {
      return state.userInfo
    },
    tokenInfo: (state: State) => {
      return state.tokenInfo
    },
    employeeInfo: (state: State) => {
      return state.employeeInfo
    },
    orgInfo: (state: State) => {
      return state.orgInfo
    },
    /**
     * POS信息
     * @param state
     * @returns
     */
    active: (state: State) => {
      return state.active
    },
    /**
     * 是否是店长
     */
    hasShopManager: (state: State) => {
      if (state.positionList) {
        return (
          state.positionList.filter((position: Position) => {
            return position.name == PositionType.SHOP_MANAGER
          }).length > 0
        )
      }
      return false
    },
    /**
     * 所属岗位是否有访问权限
     */
    hasPositionPermission: (state: State) => {
      if (state.positionList) {
        return (
          state.positionList.filter((position: Position) => {
            return position.name == PositionType.SHOP_MANAGER || position.name == PositionType.CLERK
          }).length > 0
        )
      } else {
        return true
      }
    },
    /**
     * 是否在UNI-PORTAL中
     */
    isMicro: (state: State) => {
      if (state.microInfo) {
        return true
      } else {
        return false
      }
    },
    /**
     * 质量反馈模式(商品模式)
     * 0-收货质单反馈(def)，1-商品质单反馈:"1”
     */
    aftermarketMode: (state: State) => {
      if (state.optionList) {
        const arr =
          state.optionList.filter((option: ModuleOption) => {
            return option.moduleId == ModuleId.sosGdfeedback
          }) || []

        if (arr.length > 0 && arr[0].options) {
          return arr[0].options.feedbackMode == '1'
        }
        return false
      }
      return false
    },
    /**
     * 账单管理模式是否走店务支付
     * 开启配货额度控制且且支付方式为0-资金账户，使用店务支付接口,单据状态由后端处理
     */

    payBySos: (state: State) => {
      if (state.storeInfo) {
        if (state.storeInfo.useAlcCtrl === 1 && state.storeInfo.payMode === 0) {
          return true
        }
        return false
      }
      return false
    },
    /**
     * 录入数量的小数位精度
     */
    qtyScale(state: State) {
      let scale: number = 4
      const moduleConfig = state.optionList
        ? state.optionList.filter((option: ModuleOption) => {
            return option.moduleId == ModuleId.sosGlobal
          })
        : []
      if (moduleConfig.length > 0 && moduleConfig[0].options && typeof moduleConfig[0].options.qtyScale == 'string') {
        scale = isNaN(Number(moduleConfig[0].options.qtyScale)) ? 4 : Number(moduleConfig[0].options.qtyScale)
      }
      return scale
    },
    /**
     * 是否开启 隐藏无商品的分类
     * 0-否；1-是
     */
    hideEmptyCategory: (state: State) => {
      const moduleConfig = state.optionList
        ? state.optionList.filter((option: ModuleOption) => {
            return option.moduleId == ModuleId.sosGlobal
          })
        : []
      if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.hideEmptyCategory == '1') {
        return true
      }
      return false
    },
    myDeviceList: (state: State) => {
      return state.myDeviceList
    },
    portalMsgGroups: (state: State) => {
      return state.portalMsgGroups
    }
  },
  actions: {
    setTenantInfo(context: { commit: Commit }, tenantInfo: TenantVo) {
      context.commit('tenantInfo', tenantInfo)
    },
    userInfo(context: { commit: Commit }, userInfo: UserInfo) {
      context.commit('userInfo', userInfo)
    },
    tokenInfo(context: { commit: Commit }, tokenInfo: TokenInfo) {
      context.commit('tokenInfo', tokenInfo)
    },
    setEmployeeInfo(context: { commit: Commit }, employeeInfo: CUserLogonInfo) {
      context.commit('employeeInfo', employeeInfo)
      const originUserInfo = new UserInfo()
      originUserInfo.uuid = employeeInfo.employeeId
      originUserInfo.user.code = employeeInfo.employeeCode
      originUserInfo.user.name = employeeInfo.employeeName
      originUserInfo.loginId = employeeInfo.employeeCode
      originUserInfo.nickName = employeeInfo.employeeName
      originUserInfo.mobile = employeeInfo.mobile
      originUserInfo.linkMan = employeeInfo.mobile
      originUserInfo.orgId = employeeInfo.worKOrgId as string
      originUserInfo.token = this.state.tokenInfo.accessToken
      context.commit('userInfo', originUserInfo)
    },
    setOrgInfo(context: { commit: Commit }, orgInfo: OrgQueryRes) {
      context.commit('orgInfo', orgInfo)
      const storeInfo: Store = new Store()
      storeInfo.orgId = orgInfo.parentOrgId
      storeInfo.id = orgInfo.id
      storeInfo.name = orgInfo.name
      storeInfo.code = orgInfo.code
      context.commit('store', storeInfo)
      // this.actionStore(storeInfo)
    },
    appSearchHistory(context: { commit: Commit }, history: Nullable<string[]>) {
      context.commit('appSearchHistory', history)
    },
    goodsList(context: { commit: Commit }, goodsList: any[]) {
      context.commit('goodsList', goodsList)
    },
    supplierDirectGoodsList(context: { commit: Commit }, supplierDirectGoodsList: any[]) {
      context.commit('supplierDirectGoodsList', supplierDirectGoodsList)
    },

    store(context: { commit: Commit }, store: Store) {
      context.commit('store', store)
    },
    storeTotal(context: { commit: Commit }, storeTotal: number) {
      context.commit('storeTotal', storeTotal)
    },
    showTip(context: { commit: Commit }, showTip: boolean) {
      context.commit('showTip', showTip)
    },
    permission(context: { commit: Commit }, permission: string[]) {
      context.commit('permission', permission)
    },
    orderHistory(context: { commit: Commit }, history: Nullable<string[]>) {
      context.commit('orderHistory', history)
    },

    messageMustRead(context: { commit: Commit }, messageMustRead: boolean) {
      context.commit('messageMustRead', messageMustRead)
    },
    supervisorConfig(context: { commit: Commit }, supervisorConfig: SysConfigItem) {
      context.commit('supervisorConfig', supervisorConfig)
    },
    config(context: { commit: Commit }, config: SafeConfigItem) {
      context.commit('config', config)
    },
    isGoods(context: { commit: Commit }, isGoods: boolean) {
      context.commit('isGoods', isGoods)
    },
    clear(context: { commit: Commit }) {
      context.commit('clear')
    },
    faqHistory(context: { commit: Commit }, faqHistory: Nullable<string[]>) {
      context.commit('faqHistory', faqHistory)
    },
    isLoginDirect(context: { commit: Commit }, isLoginDirect: boolean) {
      context.commit('isLoginDirect', isLoginDirect)
    },
    optionList(context: { commit: Commit }, optionList: ModuleOption[]) {
      context.commit('optionList', optionList)
    },
    moduleFieldRightsList(context: { commit: Commit }, moduleFieldRightsList: ModuleFieldRights[]) {
      context.commit('moduleFieldRightsList', moduleFieldRightsList)
    },
    cloudFund(context: { commit: Commit }, cloudFund: any) {
      context.commit('cloudFund', cloudFund)
    },
    imgUrlList(context: { commit: Commit }, imgUrlList: Record<string, string>) {
      context.commit('imgUrlList', imgUrlList)
    },
    openCheckQueryList(context: { commit: Commit }, openCheckQueryList: Record<string, string>) {
      context.commit('openCheckQueryList', openCheckQueryList)
    },
    openCheckChangeList(context: { commit: Commit }, openCheckChangeList: Record<string, boolean>) {
      context.commit('openCheckChangeList', openCheckChangeList)
    },
    positionList(context: { commit: Commit }, positionList: Position[]) {
      context.commit('positionList', positionList)
    },
    appList(context: { commit: Commit }, appList: Nullable<any[]>) {
      context.commit('appList', appList)
    },
    bigDataTenant(context: { commit: Commit }, bigDataTenant: Nullable<string>) {
      context.commit('bigDataTenant', bigDataTenant)
    },
    jyzsconfigResult(context: { commit: Commit }, jyzsconfigResult: ConfigResult) {
      context.commit('jyzsconfigResult', jyzsconfigResult)
    },
    checkQueryList(context: { commit: Commit }, checkQueryList: Record<string, string>) {
      context.commit('checkQueryList', checkQueryList)
    },
    checkChangeList(context: { commit: Commit }, checkChangeList: Record<string, string>) {
      context.commit('checkChangeList', checkChangeList)
    },
    marketPlayConfig(context: { commit: Commit }, marketPlayConfig: PmsPromotionConfig) {
      context.commit('marketPlayConfig', marketPlayConfig)
    },
    showMineTask(context: { commit: Commit }, showMineTask: boolean) {
      context.commit('showMineTask', showMineTask)
    },
    sysConfig(context: { commit: Commit }, sysConfig: SysConfigItem) {
      context.commit('sysConfig', sysConfig)
    },
    fieldsConfig(context: { commit: Commit }, fieldsConfig: string[]) {
      context.commit('fieldsConfig', fieldsConfig)
    },
    signInfo(context: { commit: Commit }, signInfo: any) {
      context.commit('signInfo', signInfo)
    },
    active(context: { commit: Commit }, active: boolean) {
      context.commit('active', active)
    },
    isOrderHasNotSave(context: { commit: Commit }, isOrderHasNotSave: any) {
      context.commit('isOrderHasNotSave', isOrderHasNotSave)
    },
    tabbarInfo(context: { commit: Commit }, tabbarInfo: TabbarInfo) {
      context.commit('tabbarInfo', tabbarInfo)
    },
    setTabBarBadge(context: { commit: Commit }, option: HdSetTabBarBadgeOptions) {
      context.commit('setTabBarBadge', option)
    },
    currentTenant(context: { commit: Commit }, currentTenant: string) {
      context.commit('currentTenant', currentTenant)
    },
    bindTerminalId(context: { commit: Commit }, bindTerminalId: string) {
      context.commit('bindTerminalId', bindTerminalId)
    },
    bindStoreId(context: { commit: Commit }, bindStoreId: string) {
      context.commit('bindStoreId', bindStoreId)
    },
    microInfo(context: { commit: Commit }, microInfo: MpInfo) {
      context.commit('microInfo', microInfo)
    },
    enableCheckLicense(context: { commit: Commit }, enableCheckLicense: boolean) {
      context.commit('enableCheckLicense', enableCheckLicense)
    },
    isVmdTaskSuper(context: { commit: Commit }, isVmdTaskSuper: boolean) {
      context.commit('isVmdTaskSuper', isVmdTaskSuper)
    },
    requireApplyStoreInfo(context: { commit: Commit }, requireApplyStoreInfo: RequireApplyStoreInfo) {
      context.commit('requireApplyStoreInfo', requireApplyStoreInfo)
    },
    selfPickUp(context: { commit: Commit }, selfPickUp: number) {
      context.commit('selfPickUp', selfPickUp)
    },
    receiptName(context: { commit: Commit }, receiptName: string) {
      context.commit('receiptName', receiptName)
    },
    shopScoreSettingList(context: { commit: Commit }, shopScoreSettingList: any[]) {
      context.commit('shopScoreSettingList', shopScoreSettingList)
    },
    storeInfo(context: { commit: Commit }, storeInfo: SosStore) {
      context.commit('storeInfo', storeInfo)
    },
    currentDevice(context: { commit: Commit }, currentDevice: Nullable<BluetoothDevice>) {
      context.commit('currentDevice', currentDevice)
    },
    appHasUpdate(
      context: { commit: Commit },
      appHasUpdate: {
        time: number // 检查时间
        status: boolean // 是否需要更新
      } | null
    ) {
      context.commit('appHasUpdate', appHasUpdate)
    },
    setIsHdPortal(context: { commit: Commit }, isHdPortal: boolean) {
      context.commit('setIsHdPortal', isHdPortal)
    },
    actionMyDeviceList(context: { commit: Commit }, myDeviceList: PrintModuleSettingVo[]) {
      context.commit('setMyDeviceList', myDeviceList)
    },
    // 履约助手
    setLyzsTenant(context: { commit: Commit }, lyzsTenant: string) {
      context.commit('setLyzsTenant', lyzsTenant)
    },
    //jyzs租户
    actionJyzsTenant(context: { commit: Commit }, jyzsTenant: string) {
      context.commit('setJyzsTenant', jyzsTenant)
    },
    performanceDetail(context: { commit: Commit }, performanceDetail: any) {
      context.commit('performanceDetail', performanceDetail)
    },
    vendor(context: { commit: Commit }, vendor: Vendor) {
      context.commit('vendor', vendor)
    },
    msgInfo(context: { commit: Commit }, msgInfo: Nullable<IMMessageGroup>) {
      context.commit('msgInfo', msgInfo)
    },
    setPortalMsgGroups(context: { commit: Commit }, portalMsgGroups: CMessageTopicGroupVO[]) {
      context.commit('portalMsgGroups', portalMsgGroups)
    }
  }
})

export default store
