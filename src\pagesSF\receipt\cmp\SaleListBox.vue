<template>
  <view class="sale-list-contain" :style="{ paddingTop: isSticky ? top : 0 }">
    <view class="list-title" :class="{ 'title-sticky': isSticky }" :style="{ top: top }">
      <view class="name">
        品名/代码/条码
        <text v-if="showPrice">/单价</text>
      </view>
      <view v-if="!showPrice" style="width: 120rpx"></view>
      <view class="qty">应收</view>
      <view class="qty-real">实收</view>
      <view class="amount" v-if="showPrice">金额(元)</view>
    </view>
    <view class="divide"></view>
    <view
      class="list"
      v-for="(line, index) in lines"
      :key="line.goods.inputCode"
      :class="{ 'list-none-border': !showMore && index == lines.length - 1 }"
    >
      <view class="info">
        <view class="info__img">
          <image lazy-load class="info__img-sku" :src="img(line)" @click.stop="handlePreviewImg(line)" />
          <view class="info__scale">
            <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
          </view>
        </view>
        <view class="info__value">
          <view class="sku-name">
            <text class="sku-gift" v-if="line.aggGiftQty">含赠</text>
            {{ line.goods.name | empty }}
          </view>
          <view class="sku-code">
            <view class="goods-tag-primary" v-if="line.goods.isDisp">散称</view>

            <view class="info-tag">{{ line.goods.code | cds(13) | empty }}</view>

            <view class="info-tag">{{ line.goods.inputCode | cds(13) | empty }}</view>

            <view class="info-tag" v-if="showPrice">￥{{ line.goods.singlePrice | fmt }}/{{ line.goods.minMunit | empty }}</view>
          </view>
          <view class="main-half" v-if="showMaster.showDisplayLocation">
            <view :class="[hasMutiple(line.goods.displayLocation) ? 'goods-one' : '']">
              陈列位置：
              <text class="goods-text">{{ line.goods.displayLocation | empty }}</text>
            </view>
            <image
              class="good-img"
              :src="'/static/icon/ic_right_grey.png' | oss"
              v-if="hasMutiple(line.goods.displayLocation)"
              @click="viewExhibit(line)"
            />
          </view>
        </view>
      </view>

      <view class="sku-info" v-if="line.goods.isDisp && showWeightSkuReceivedQty">
        <view class="label">
          <block>件数</block>
        </view>
        <view class="qty-real">
          <text class="num">{{ line.qpcQty | qpcQty(line.goods.munit, line.goods.minMunit) }}</text>
        </view>
        <view class="qty">
          <text class="num">{{ line.receiptQpcQty | qpcQty(line.goods.munit, line.goods.minMunit) }}</text>
        </view>
        <view class="amount total" v-if="showPrice">
          <block v-if="!line.goods.isDisp">{{ line.receiptTotal | fmt }}</block>
        </view>
      </view>
      <view class="sku-info">
        <view class="label">
          <block v-if="line.goods.isDisp">重量</block>
        </view>
        <view class="qty-real">
          <text class="num">{{ line.qty }}{{ line.goods.minMunit }}</text>
        </view>
        <view class="qty">
          <text class="num">{{ line.receiptQty }}{{ line.goods.minMunit }}</text>
        </view>
        <view class="amount total" v-if="showPrice">{{ line.receiptTotal | fmt }}</view>
      </view>
      <view class="detail" v-if="enableMultipleReceipt">
        <view class="detail-operation" @click="handleViewDetail(line)">收货明细</view>
      </view>
    </view>
    <view class="view-more" v-if="showMore">
      <text class="click-btn" @click="viewMore">查看更多</text>
      <text class="arrow"></text>
    </view>
  </view>
</template>
<script lang="ts" src="./SaleListBox.ts"></script>
<style lang="scss" scoped>
.sale-list-contain {
  width: 702rpx;
  margin: 0 auto;
  padding: 0 $base-padding;
  background: #ffffff;
  box-sizing: border-box;
  border-radius: 12rpx;
  position: relative;
  .list-title {
    position: relative;
    width: 100%;
    height: 76rpx;
    display: flex;
    box-sizing: border-box;
    align-items: center;
    justify-content: space-between;
    font-size: 26rpx;
    color: $font-color-darklight;
    background: #ffffff;
    .name {
      width: 254rpx;
      flex: 0 0 auto;
    }
    .qty {
      width: 96rpx;
      flex: 0 0 auto;
      text-align: right;
    }
    .qty-real {
      margin-left: 12rpx;
      width: 130rpx;
      flex: 0 0 auto;
      text-align: right;
    }
    .amount {
      margin-left: 12rpx;
      width: 166rpx;
      flex: 0 0 auto;
      text-align: right;
    }
  }
  .divide {
    left: 0;
    width: 702rpx;
    position: absolute;
    top: 76rpx;
    border-bottom: $list-border-bottom;
  }

  .title-sticky {
    position: fixed;
    width: 100%;
    box-sizing: border-box;
    z-index: 2;
    top: 0;
    left: 0;
    padding: 0 24rpx;
  }

  .list {
    padding: $base-padding 0;
    border-bottom: $list-border-bottom;
    .sku-name {
      line-height: 44rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: $color-text-primary;
      font-family: PingFangSC-Medium, PingFang SC;
    }
    .sku-gift {
      flex: 0 0 auto;
      display: inline-flex;
      align-items: center;
      padding: 0 10rpx;
      box-sizing: border-box;
      margin-right: 8rpx;
      width: 68rpx;
      height: 40rpx;
      background: linear-gradient(313deg, #ff9a00 0%, #ffd05b 100%);
      border-radius: 8rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #ffffff;
    }
    .sku-code {
      margin-top: 10rpx;

      .tag {
        width: 56rpx;
        height: 28rpx;
        margin-right: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #e9f0ff;
        color: #1c64fd;
        border-radius: 4rpx;
        font-size: 20rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
      .code {
        color: rgba(148, 150, 154, 1);
        font-size: 26rpx;
        margin-right: 10rpx;
      }
      .price {
        font-size: 26rpx;
        color: rgba(88, 90, 94, 1);
      }
    }
    .batch-lines {
      margin: 16rpx 0rpx 20rpx;
      width: 654rpx;

      background: #f9f9f9;
      position: relative;
      padding: 12rpx 32rpx;
      box-sizing: border-box;
      .batch-item {
        @include flex(row, flex-start, center);
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: $color-text-fourth;
        line-height: 36rpx;
        width: 590rpx;
        .date {
          width: 300rpx;
        }
        .qty {
          margin-left: 108rpx;
          .qty-number {
            color: $color-text-thirdly;
          }
        }
      }
      .batch-item:not(:first-child) {
        margin-top: 4rpx;
      }
    }
    .batch-lines::before {
      content: '';
      position: absolute;
      width: 0;
      height: 0rpx;
      border-top: solid 12rpx #f9f9f9;
      border-right: solid 12rpx transparent;
      border-left: solid 12rpx transparent;
      bottom: -12rpx;
      left: 260rpx;
    }

    .valid-lines {
      margin: 16rpx auto 20rpx auto;
      width: 654rpx;
      padding: 16rpx 20rpx;
      background: #f9f9f9;
      border-radius: 8rpx;
      position: relative;
      box-sizing: border-box;
      .batch-item {
        @include flex(row, flex-start, center);
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: $color-text-thirdly;
        line-height: 36rpx;

        .vb-code {
          width: 420rpx;
        }

        .vb-qpc {
          margin: 0 auto;
        }

        .num {
          color: $color-text-secondary;
        }

        .valid-open-line {
          @include flex(row, flex-start, center);
          .valid-open-icon {
            width: 32rpx;
            height: 32rpx;
            transform: rotate(90deg);
          }

          .valid-close-icon {
            width: 32rpx;
            height: 32rpx;
            transform: rotate(270deg);
          }
        }
      }
      .table-header {
        color: $color-text-fourth;
      }
      .batch-item:not(:first-child) {
        margin-top: 8rpx;
      }
    }
    .sku-info {
      height: 40rpx;
      line-height: 40rpx;
      display: flex;
      align-items: center;
      margin-top: 10rpx;
      justify-content: flex-end;
      color: #585a5e;
      font-size: 26rpx;
      .label {
        flex: 1 1 auto;
        display: flex;
        align-items: center;
        width: 48rpx;
        height: 32rpx;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #94969a;
      }
      .qty-real {
        flex: 0 0 auto;
        width: 200rpx;
        text-align: right;
      }
      .qty {
        flex: 0 0 auto;
        box-sizing: border-box;
        width: 150rpx;
        text-align: right;
      }

      .amount {
        flex: 0 0 auto;
        width: 170rpx;
        text-align: right;
      }
      .num,
      .amount {
        font-size: 32rpx;
        font-weight: bold;
      }
      .total {
        color: #ff8800;
      }
    }

    .sku_location {
      margin-top: 10rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
    }

    .main-half {
      width: 528rpx;
      background: #f5f5f5;
      border-radius: 8rpx;
      padding: 8rpx 16rpx;
      box-sizing: border-box;
      flex: 0 0 auto;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .good-img {
        width: 32rpx;
        height: 32rpx;
        display: inline-table;
      }

      .goods-text {
        color: #333333;
        font-size: 26rpx;
        font-weight: 500;
      }

      .goods-one {
        flex: 1;
        @include ellipsis();
      }
    }

    .wrhNote {
      margin: 12rpx 0rpx 0rpx;
      width: 702rpx;

      background: #f3f7ff;
      padding: 16rpx 20rpx 20rpx;
      box-sizing: border-box;
      border-radius: 8rpx;

      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $color-text-thirdly;
      @include flex(column, space-between, flex-start);

      .wrhNote-line {
        line-height: 36rpx;
        @include flex(row, flex-start);
      }

      .wrhNote-line:nth-child(2) {
        margin-top: 12rpx;
      }

      .data {
        flex: 0 0 auto;
        width: 516rpx;
        @include ellipsis();
        color: $color-text-secondary;
      }
    }
  }

  .info {
    width: 100%;
    display: flex;
    margin-bottom: 16rpx;
    &__img {
      position: relative;
      width: 120rpx;
      height: 120rpx;
      margin-right: 24rpx;
      flex: 0 0 auto;
      &-sku {
        width: 120rpx;
        height: 120rpx;
      }
    }

    &__scale {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 24rpx;
      height: 24rpx;
      background: rgba(0, 0, 0, 0.4);
      border-radius: 8rpx 0px 8rpx 0rpx;
      text-align: center;
      @include flex();
      &-img {
        width: 16rpx;
        height: 16rpx;
      }
    }

    &__value {
      position: relative;
      flex: 1 1 auto;
      &-half {
        width: 50%;
        flex: 0 0 auto;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        display: inline-flex;

        // 如果不是最后两个元素，则有向下16rpx的margin
        &:not(:nth-last-child(-n + 2)) {
          margin-bottom: 16rpx;
        }

        .good-img {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }

  .list-none-border {
    border: none;
  }

  .view-more {
    width: 100%;
    height: 88rpx;
    font-size: 32rpx;
    font-weight: 400;
    color: rgba(88, 90, 94, 1);
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    .arrow {
      width: 14rpx;
      height: 14rpx;
      display: inline-block;
      border-top: 1px solid #94969a;
      border-right: 1px solid #94969a;
      transform: rotate(45deg);
      margin-left: 20rpx;
    }
  }

  .detail {
    display: flex;
    justify-content: flex-end;
    margin-top: 16rpx;
    &-operation {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 48rpx;
      background: #ffffff;
      border: 1rpx solid #cccccc;
      border-radius: 32rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      padding: 0 12rpx;
      margin-left: 12rpx;
      min-width: 112rpx;
    }
  }
}
</style>
