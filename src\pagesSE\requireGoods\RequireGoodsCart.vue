<template>
  <view class="cart" @click="doCloseOutside()">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>

    <!-- 顶部提示信息 -->
    <view class="header" v-if="(forecastArrivalDate || expireDate) && !isPre">
      <image class="header-img" :src="'/static/icon/ic_noticebar_inform.png' | oss"></image>
      <text class="header-txt" v-if="expireDate">期望到货时间：{{ expireDate | date('yyyy/MM/dd') }}</text>
      <text class="header-txt" v-if="forecastArrivalDate">预计到货时间：{{ forecastArrivalDate | date('yyyy/MM/dd') }}</text>
    </view>
    <view class="red-text flex-start" v-if="hasHotGoods">
      <image :src="'/static/icon/ic_yiwen_2x.png' | oss" mode="aspectFill" class="warn-icon"></image>
      超出爆品可订数的商品将按正常价格计算
    </view>
    <!-- 顶部提示信息  结束-->
    <view id="cartHeader">
      <view id="categoryList" class="category-list" :class="{ 'category-list--more': !collapseStatus }">
        <view
          class="category-list-item"
          v-for="(item, index) in goodsCategoryList"
          :key="item.uuid"
          :class="{ 'category-list-item--active': activeIndex === index }"
          @click="doChange(item, index)"
        >
          <text class="category-name">{{ item.name }}</text>
          <text>{{ `(${item.recCnt})` }}</text>
        </view>
      </view>
      <view class="show-more" @click="doShowMore" v-if="showCollapse">
        <text class="show-more-txt">{{ collapseStatus ? '展开全部' : '收起全部' }}</text>
        <image class="show-more-img" :src="collapseIcon"></image>
      </view>
    </view>
    <scroll-view
      class="cart-list"
      :style="{ height: cartHeight, paddingBottom: enableAutoSave ? '264rpx' : '242rpx' }"
      @scrolltolower="loadMoreGoods"
      scroll-y
    >
      <template v-for="(goodLine, index) in showGoodsList">
        <hd-swipe-action
          style="margin-bottom: 16rpx"
          :key="goodLine.goods.uuid"
          @onDelete="doDeleteLine(goodLine)"
          :index="index"
          :moveIndex="moveIndex"
          @updateIndex="onUpdateIndex"
        >
          <goods-list-item
            :gtyLowLimitlevel="gtyLowLimitlevel"
            :gtyHighLimitlevel="gtyHighLimitlevel"
            :hideInvQty="hideInvQty"
            :goodsLine="goodLine"
            :accessoryGoodsCategoryNamePrefix="accessoryGoodsCategoryNamePrefix"
            @change="doNumberChange"
            :index="index"
            :key="goodLine.goods.uuid"
            :showAvgArrow="false"
            :showGdImage="showGdImage"
            :showNote="false"
            :validateLevel4CaseQty="validateLevel4CaseQty"
            :isShopCart="true"
            :accessoryManage="accessoryManage"
            @click.native="doCloseOutside"
          ></goods-list-item>
        </hd-swipe-action>
      </template>
      <view v-if="!finished && hasMore && showGoodsList.length > 0" class="loading">{{ loadText }}</view>
      <pagination :eachPage="eachPage" :curPage="curpage" :totalNum="total" :hasMore="hasMore" @pageChange="doShow" @hide="doHide"></pagination>
    </scroll-view>
    <template v-if="showBottom">
      <view id="goodsAction">
        <goods-action
          :num="goodsNum"
          :price="goodsPrice"
          :totalQpcQty="totalQpcQty"
          :groupPurchase="groupPurchase"
          @groupPurchase="doGroupPurchase"
          @submit="doSubmitAfter"
          @doNoteShow="doNoteShow"
          @save="doSaveAfter"
          @clear="doClear"
          :btnPermission="btnPermission"
          :clickAble="clickAble"
          :hideInvQty="hideInvQty"
          :showBalance="showBalance"
          :balance="balance"
          :dpsAccountInfo="dpsAccountInfo"
          :isPre="isPre"
          :isCart="true"
          :showSelfPickUp="showSelfPickUp"
          @doSelfPickUp="doSelfPickUp"
          :selfPickUp="selfPickUp"
        ></goods-action>
      </view>
    </template>
    <uni-popup ref="note" type="bottom">
      <hd-note :value="note" @confirm="doNoteConfirm" @close="doNoteClose"></hd-note>
    </uni-popup>
    <!-- 库存不足时弹窗 -->

    <uni-popup ref="Inventory" type="bottom">
      <inventory-diff
        @close="doInventoryClose"
        @confirm="doInventoryConfirm"
        title="以下商品信息发生变化"
        showbutton
        btntext="继续提交"
        :disable="!goodsNum"
      >
        <view slot="content">
          <view class="list-title">
            <view class="name">品名/条码/代码/单位</view>
            <view class="price">单价</view>
            <view class="qty">可订数</view>
            <view class="total">小计</view>
          </view>
          <view class="divide"></view>
          <scroll-view scroll-y style="height: 490rpx">
            <view class="list" v-for="line in showList" :key="line.goods.uuid">
              <view :class="[line.validCode == '1' && 'disableBck']">
                <view class="sku-name">
                  {{ line.goods.name | empty }}
                </view>
                <view class="sku-info">
                  <view class="code-price">
                    <view class="goods-tag-primary" v-if="line.goods.isDisp">散称</view>
                    <view class="info-tag">{{ line.goods.inputCode | empty }}</view>
                    <view class="info-tag">{{ line.goods.code | empty }}</view>
                    <view class="info-tag">{{ line.goods.munit | empty }}</view>
                  </view>
                  <view style="width: 100%; height: 10rpx"></view>
                  <view class="name"></view>
                  <view class="price">
                    <text class="num">{{ line.goods.tempPrice | fmt }}</text>
                    <text class="qpc-src" v-if="line.goods.srcPrice != line.goods.tempPrice">{{ line.goods.srcPrice | fmt }}</text>
                  </view>
                  <!--1/kg-->
                  <view class="qty">
                    <text class="num">{{ line.qpcQty | empty }}</text>
                  </view>
                  <view class="total">
                    <text class="amount">{{ line.total | fmt }}</text>
                  </view>
                </view>
              </view>
              <image :src="'/static/icon/icon_xiajia.png' | oss" class="deleteImg" mode="aspectFit" v-if="line.validCode == '1'"></image>
            </view>
          </scroll-view>
          <view class="red-text">
            <view>
              <image :src="'/static/icon/ic_yiwen_inverted_2x.png' | oss" mode="aspectFill" class="warn-icon"></image>
              若继续提交系统将自动修改价格或订货数
            </view>
            <view class="flex-row">
              <view>
                订货品项数：
                <text class="qty">{{ goodsNum }}</text>
                <text class="qpc-src" v-if="oldGoodsNum != goodsNum">{{ oldGoodsNum }}</text>
              </view>
              <view>
                订货金额：
                <text class="qty">{{ goodsPrice | fmt }}</text>

                <text class="qpc-src" v-if="oldGoodsPrice != goodsPrice">{{ oldGoodsPrice | fmt }}</text>
              </view>
            </view>
          </view>
        </view>
      </inventory-diff>
    </uni-popup>

    <!-- 准备支付 -->
    <uni-popup ref="perparepay" type="bottom">
      <prepare-pay @close="doPerparePayClose" @confirm="doPerparePayConfirm" :paymentSrcContext="paymentSrcContext"></prepare-pay>
    </uni-popup>

    <!-- #ifdef MP-WEIXIN -->
    <hd-privacy-popup id="privacy-popup"></hd-privacy-popup>
    <!-- #endif -->
  </view>
</template>

<script lang="ts" src="./RequireGoodsCart.ts"></script>

<style lang="scss" scoped>
.cart {
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  border-radius: 20rpx 20rpx 0rpx 0rpx;
  background: rgba(255, 255, 255, 1);

  .header {
    position: relative;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 $base-padding;
    width: 100%;
    height: 72rpx;
    background: $color-background;

    .header-img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }

    .header-txt {
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;

      &:not(:last-child) {
        margin-right: 32rpx;
      }
    }

    .header-txt--after {
      text-align: left;
      margin-left: 12rpx;
      font-size: 28rpx;
      font-family: $font-medium;
      font-weight: 500;
      color: $color-primary;
    }
  }
  .cart-title {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    height: 112rpx;
    font-size: 32rpx;
    font-family: $font-medium;
    font-weight: 500;
    color: rgba(40, 44, 52, 1);
  }
  .category-list {
    width: 750rpx;
    max-height: 200rpx;
    overflow: hidden;
    padding: 16rpx 24rpx;
    box-sizing: border-box;
    @include flex(row, flex-start, center);
    flex-wrap: wrap;
    .category-list-item {
      width: 164rpx;
      height: 48rpx;
      margin-right: 15rpx;

      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: rgba(88, 90, 94, 1);
      background-color: #f7f6f9;
      .category-name {
        max-width: 120rpx;
        @include ellipsis();
      }
    }
    .category-list-item:nth-child(n + 5) {
      margin-top: 16rpx;
    }
    .category-list-item:nth-child(4n) {
      margin-right: 0;
    }
    .category-list-item--active {
      color: $color-primary;
      background-color: $color-background;
    }
  }
  .category-list--more {
    max-height: 258rpx;
    overflow-y: scroll;
  }
  .cart-list {
    // flex: 1 1 auto;
    // overflow-y: auto;
    flex: 1;
    box-sizing: border-box;
    padding: 16rpx;
    padding-bottom: 252rpx;
    background-color: rgba(246, 246, 246, 1);
  }
  .show-more {
    height: 48rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgba(148, 150, 154, 1);
    .show-more-txt {
      font-size: 24rpx;
      color: rgba(148, 150, 154, 1);
    }
    .show-more-img {
      height: 32rpx;
      width: 32rpx;
    }
  }
  .delete {
    position: absolute;
    top: 32rpx;
    left: 24rpx;
    height: 48rpx;
    width: 92rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: rgba(148, 150, 154, 1);
    .delete-img {
      height: 32rpx;
      width: 32rpx;
    }
  }
  .close {
    position: absolute;
    top: 32rpx;
    right: 24rpx;
    width: 48rpx;
    height: 48rpx;
  }
  .loading {
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    background-color: #ffffff;
    color: rgb(148, 150, 154);
  }

  .red-text {
    width: 750rpx;
    line-height: 48rpx;
    overflow: hidden;
    background: rgba(253, 155, 28, 0.1);
    padding: 16rpx 24rpx;
    text-align: left;
    box-sizing: border-box;

    font-size: 24rpx;
    color: #ff9100;

    .warn-icon {
      width: 32rpx;
      height: 32rpx;
      vertical-align: middle;
      margin-right: 12rpx;
    }
    .qpc-src {
      margin-left: 4rpx;
      text-decoration: line-through;
      font-size: 22rpx;
      font-family: PingFangSC;
      color: #94969a;
    }
    .flex-row {
      margin-top: 12rpx;
      @include flex(row, space-between, center);
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $color-text-thirdly;
      .qty {
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: $color-text-primary;
      }
    }
  }
  .list-title {
    position: relative;
    width: 100%;
    height: 76rpx;
    padding: 0 24rpx;
    display: flex;
    box-sizing: border-box;
    align-items: center;
    justify-content: space-between;
    font-size: 26rpx;
    color: $font-color-darklight;
    background: #ffffff;
    .name {
      flex: 1.5;
    }
    .price {
      flex: 1;
      text-align: center;
    }
    .qty {
      flex: 0.5;
      text-align: center;
    }
    .total {
      flex: 1;
      text-align: right;
    }
  }
  .divide {
    width: 750rpx;

    border-bottom: $list-border-bottom;
  }
  .list {
    padding: $base-padding 24rpx;
    border-bottom: $list-border-bottom;
    position: relative;
    .sku-name {
      /*height: 44rpx;*/
      line-height: 44rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: $color-text-primary;
      font-family: PingFangSC-Medium, PingFang SC;
      overflow: hidden;
      text-overflow: ellipsis;
      /*white-space: nowrap;*/
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      .code {
        color: #282c34;
        font-size: 26rpx;
        margin-right: 10rpx;
      }
    }
    .sku-info {
      line-height: 40rpx;
      display: flex;
      align-items: center;
      margin-top: 8rpx;
      justify-content: space-between;
      flex-wrap: wrap;
      color: #585a5e;
      font-size: 26rpx;
      .code-price {
        display: flex;
        align-items: center;

        justify-content: flex-start;
      }
      .qpc-src {
        margin-left: 4rpx;
        text-decoration: line-through;
        font-size: 22rpx;
        font-family: PingFangSC;
        color: #94969a;
      }

      .name {
        // flex: 1.5;
        width: 37.5%;
      }
      .price {
        flex: 1;
        text-align: center;
      }
      .qty {
        flex: 0.5;
        text-align: center;
      }
      .total {
        flex: 1;
        text-align: right;
      }

      .total {
        flex: 1;
        text-align: right;
      }
      .num {
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: $color-text-thirdly;
      }
      .amount {
        font-size: 28rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: $color-text-primary;
      }
    }
  }
  .flex-start {
    display: flex;
    align-items: center;
  }
  .deleteImg {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 128rpx;
    height: 128rpx;
    z-index: 2;
  }
  .disableBck {
    opacity: 0.2;
  }
}
</style>
