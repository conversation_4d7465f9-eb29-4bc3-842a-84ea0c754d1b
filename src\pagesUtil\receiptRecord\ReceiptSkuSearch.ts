import { Component } from 'vue-property-decorator'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import { mixins } from 'vue-class-component'
import { Getter, State } from 'vuex-class'
import ModuleOption from '@/model/default/ModuleOption'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import QueryRequest from '@/model/base/QueryRequest'
import UserInfo from '@/model/user/UserInfo'
import AppReceiptRecordApi from '@/network/AppReceiptRecordApi/AppReceiptRecordApi'
import AppReceiptRecordLineDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordLineDTO'
import AppReceiptRecordLineCheckDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordLineCheckDTO'
import ReceiptEditCard from './cmp/ReceiptEditCard.vue'
import { ModuleId } from '@/model/common/OptionListModuleId'
import AppReceiptMultipleRcvRecordQueryer from '@/model/receipt/AppReceiptMultipleRcvRecordQueryer'
import AppReceiptMultipleRcvGdRecordDTO from '@/model/receipt/AppReceiptMultipleRcvGdRecordDTO'
import ReceiptApi from '@/network/receipt/ReceiptApi'
import EditCardDialog from './cmp/EditCardDialog.vue'
import SkuRecordDialog from '@/pages/cmp/SkuRecordDialog.vue'
import Slot from '@/model/data/Slot'
import GoodsSlotBinder from '@/model/data/GoodsSlotBinder'
import SelectExhibit from '@/components/select-exhibit/SelectExhibit.vue'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import ResetExhibit from '@/components/select-exhibit/ResetExhibit.vue'
import AppReceiptRecordDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordDTO'

@Component({
  components: { SkuRecordDialog, EditCardDialog, ReceiptEditCard, SelectExhibit, ViewExhibit, ResetExhibit }
})
export default class ReceiptSkuSearch extends mixins(BroadCast) {
  @State('optionList') optionList: ModuleOption[]
  @State('userInfo') userInfo: Nullable<UserInfo> // 用户信息
  @Getter('qtyScale') qtyScale: number

  $refs: any
  keyWord: string = '' // 搜索关键字
  skuList: AppReceiptRecordLineDTO[] = [] // 商品列表
  billId: string = '' // 单据id
  receiptBillId: string = '' // 收货单单号

  // 分页相关
  pageSize: number = 10 // 每页大小
  pageNum: number = 0 // 页码
  finished: boolean = false // 是否加载完成
  isLoading: boolean = false // 是否在加载
  showEmpty: boolean = false // 是否展示空状态

  hasSelelcExhibit: Slot = new Slot() // 已选中的陈列位置
  viewExhibitInfo: AppReceiptRecordLineDTO = new AppReceiptRecordLineDTO() // 陈列位置弹窗数据
  exhibitIndex: number = 0 // 陈列位置下标

  // 是否信任模式
  get trust() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.receiptMode == 'trust'
    }
    return false
  }

  get showPrice() {
    if (PermissionMgr.hasPermission(Permission.receiptShowprice)) {
      return true
    }
    return false
  }

  async onLoad(option) {
    this.receiptBillId = option.receiptBillId

    if (option && option.id) {
      this.billId = option.id
    }
    if (option && option.keyWord) {
      try {
        this.keyWord = decodeURIComponent(option.keyWord)
      } catch (error) {
        this.keyWord = option.keyWord
      }
      this.handleSearch()
    }

    if (option && option.searchword) {
      try {
        this.keyWord = decodeURIComponent(option.searchword)
      } catch (error) {
        this.keyWord = option.searchword
      }
      this.handleSearch(false)
    }

    uni.$off('refreshExhibit')
    uni.$on('refreshExhibit', () => {
      this.$refs.exhibit.refresh()
      this.$refs.resetExhibit.refresh()
    })
  }

  onUnload() {
    // 卸载
    uni.$off('refreshExhibit')
  }

  /**
   * 查看商品行收货记录
   * @param sku
   */
  async handleViewDetail(sku: AppReceiptRecordLineDTO) {
    try {
      this.$showLoading()
      const recordList = await this.listMultipleRcvRecord({
        billId: this.receiptBillId,
        gdInputCode: sku.goods.inputCode
      })
      this.$hideLoading()

      if (recordList.length === 0) {
        this.$showToast({
          title: '暂无收货记录'
        })
      } else {
        this.$refs.recordDialog.open(recordList)
      }
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 确认收货前置
   * @param sku
   */
  handleBeforeConfirm(sku: AppReceiptRecordLineDTO) {
    this.handleConfirm(sku)
  }

  /**
   * 商品行结束收货
   * @param sku
   */
  handelOver(sku: AppReceiptRecordLineDTO) {
    this.$showModal({
      title: '结束收货确认',
      content: '确认后该商品将按本次收货数量确认到【已确认】，后续无法对该商品继续收货',
      success: async (action) => {
        if (action.confirm) {
          this.handleSkuOver(sku)
        }
      }
    })
  }

  /**
   * 结束收货
   * @param sku
   */
  async handleSkuOver(sku: AppReceiptRecordLineDTO, confirm: boolean = true, finished: boolean = true) {
    try {
      this.$showLoading()
      const body: AppReceiptRecordLineCheckDTO = {
        // 单据标识
        billId: sku.billId,
        // 商品行标识
        lineUuid: sku.uuid!,
        // 商品GID
        gdUuid: sku.goods.uuid,
        // 收货数量
        receiptQty: sku.receiptQty,
        // 收货金额
        receiptTotal: Number(sku.receiptQty).multiply(sku.goods.price).divide(sku.goods.qpc).scale(2),
        // 收货规格数
        receiptQpcQty: sku.receiptQpcQty,
        // 实际收货数量
        realReceiptQty: sku.receiptQty,
        // 生产日期
        mfgDate: sku.mfgDate,
        // 到效期
        expDate: sku.expDate,
        // 是否已清点
        confirmed: confirm,
        // 是否已结束
        finished: finished
      }
      const summary = await this.modifySku(body)
      this.$hideLoading()
      this.doResetPage()
      this.loadskuList()
      uni.$emit('receipt-edit-query')
      // 更新待收数量
      uni.$emit('update-un-confirm-sku', { unConfirmLineCnt: summary.unConfirmLineCnt, confirmedLineCnt: summary.confirmedLineCnt })
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 待收货的箱子确认收货
   * @param sku
   */
  async handleConfirm(sku: AppReceiptRecordLineDTO) {
    try {
      this.$showLoading()
      const body: AppReceiptRecordLineCheckDTO = {
        // 单据标识
        billId: sku.billId,
        // 商品行标识
        lineUuid: sku.uuid!,
        // 商品GID
        gdUuid: sku.goods.uuid,
        // 收货数量
        receiptQty: sku.receiptQty,
        // 收货金额
        receiptTotal: sku.receiptQty.multiply(sku.goods.price).scale(2),
        // 收货规格数
        receiptQpcQty: sku.receiptQpcQty,
        // 实际收货数量
        realReceiptQty: sku.receiptQty,
        // 生产日期
        mfgDate: sku.mfgDate,
        // 到效期
        expDate: sku.expDate,
        // 是否已清点
        confirmed: true,
        // 是否结束收货
        finished: sku.finished
      }
      const summary = await this.modifySku(body)
      this.$hideLoading()
      this.doResetPage()
      this.loadskuList()
      uni.$emit('receipt-edit-query')
      // 更新待收数量
      uni.$emit('update-un-confirm-sku', { unConfirmLineCnt: summary.unConfirmLineCnt, confirmedLineCnt: summary.confirmedLineCnt })
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 编辑箱
   * @param sku
   */
  handleEdit(sku: AppReceiptRecordLineDTO) {
    this.$refs.edit.open(sku)
  }

  handleEditReason(sku: AppReceiptRecordLineDTO) {
    this.$refs.reason.open(sku)
  }

  async loadskuList(isScan: boolean = false) {
    try {
      this.$showLoading()
      await this.queryLine(isScan)
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '查询失败',
        icon: 'error'
      })
    }
  }

  // 显示更多商品
  onReachBottom() {
    if (this.finished || this.isLoading) {
      return
    }
    this.loadskuList(false)
  }

  /**
   * 查询箱码列表
   * @param body
   * @returns
   */
  queryLine(isScan: boolean) {
    this.isLoading = true
    const body = new QueryRequest()
    body.page = this.pageNum
    body.pageSize = this.pageSize
    body.sorts = [
      { asc: false, field: 'type' },
      { asc: true, field: 'lineNo' }
    ]
    body.conditions = [{ operation: 'billId:=', parameters: [this.billId] }]
    if (this.keyWord) {
      body.conditions.push({ operation: 'keyword:%=%', parameters: [this.keyWord] })
    }
    if (isScan) {
      body.conditions.push({ operation: 'isScan:=', parameters: ['true'] })
    } else {
      body.conditions.push({ operation: 'isScan:=', parameters: ['false'] })
    }

    return new Promise<AppReceiptRecordLineDTO[]>((resolve, reject) => {
      AppReceiptRecordApi.queryLine(body)
        .then((resp) => {
          if (!this.showEmpty) {
            this.showEmpty = true
          }
          this.pageNum++
          if (!resp.more) {
            this.finished = true
          }
          if (body.page === 0) {
            this.skuList = resp.data
          } else {
            this.skuList.push(...resp.data)
          }
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
        .finally(() => {
          this.isLoading = false
        })
    })
  }

  /**
   * 重置分页参数
   */
  doResetPage() {
    this.pageNum = 0
    this.isLoading = false
    this.finished = false
  }

  // 搜索框清空事件
  doClear() {
    this.keyWord = ''
    this.doResetPage()
    this.skuList = []
  }

  doCancel() {
    this.$Router.back(1)
  }

  /**
   * 商品行收货
   * @param body
   * @returns
   */
  modifySku(body: AppReceiptRecordLineCheckDTO) {
    return new Promise<AppReceiptRecordDTO>((resolve, reject) => {
      AppReceiptRecordApi.checkLine(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查询商品行多次收货记录
   * @param body
   * @returns
   */
  listMultipleRcvRecord(body: AppReceiptMultipleRcvRecordQueryer) {
    return new Promise<AppReceiptMultipleRcvGdRecordDTO[]>((resolve, reject) => {
      ReceiptApi.listMultipleRcvRecord(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  // 搜索框搜索事件
  async doSearch() {
    if (this.keyWord.trim() === '') {
      this.$nextTick(() => {
        uni.pageScrollTo({
          scrollTop: 0
        })
      })
    } else {
      this.handleSearch(false)
    }
  }

  /**
   * PDA扫码回调事件
   * @param scanWord 扫码文字
   * @returns
   */
  async doScanAfter(scanWord: string) {
    if (this.isLoading) {
      this.$showToast({ title: '正在加载，请稍后重试~' })
      return
    }
    this.keyWord = scanWord
    this.handleSearch()
  }

  async handleSearch(isScan: boolean = true) {
    this.doResetPage()
    await this.loadskuList(isScan)
    this.$nextTick(() => {
      uni.pageScrollTo({
        scrollTop: 0
      })
    })
  }

  /**
   * 绑定陈列位置
   */
  bindExhibit(info: AppReceiptRecordLineDTO, index: number) {
    this.exhibitIndex = index
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.open(this.hasSelelcExhibit, slotGoods, 'bindExhibit')
  }

  /**
   * 陈列位置调整
   */
  resetExhibit(info: AppReceiptRecordLineDTO, index: number) {
    this.hasSelelcExhibit = new Slot()
    this.exhibitIndex = index
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.$refs.resetExhibit.open(slotGoods)
  }

  /**
   * 获取该商品信息
   */
  getRequestBody(info: AppReceiptRecordLineDTO) {
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 目标货位
    slotGoods.targetSlotCode = info.exhibitValue
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.success(this.hasSelelcExhibit, slotGoods)
  }

  /**
   * 输入框的值组装
   */
  inputExhibit(code) {
    this.hasSelelcExhibit.code = code
    this.hasSelelcExhibit.name = code
    this.hasSelelcExhibit.uuid = ''
  }

  /**
   * 陈列位置绑定成功
   */
  confirmExhibit() {
    this.doResetPage()
    this.loadskuList()
    this.hasSelelcExhibit = new Slot()
  }

  /**
   * 添加陈列位置
   */
  addExhibit() {
    this.$Router.push({
      name: 'addExhibitLocation'
    })
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info: AppReceiptRecordLineDTO) {
    this.viewExhibitInfo = { ...info }
    this.$refs.viewExhibit.open()
  }
}
