<template>
  <view class="box-edit-split-card" @click="closeGift">
    <view class="header">
      <text class="header-tag" v-if="isDisp">散称</text>
      <text class="header-tag header-tag--confirmed" v-if="sku.confirmed">已收</text>
      <text class="header-name">{{ name }}</text>
    </view>

    <view class="info">
      <view class="info__img">
        <image lazy-load class="info__img-sku" :src="img" @click.stop="handlePreviewImg" />
        <view class="info__scale">
          <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
        </view>
      </view>
      <view class="info__value">
        <view class="info__value-half">条码：{{ inputCode }}</view>
        <view class="info__value-half">规格：{{ qpcStr }}</view>
        <view class="info__value-half">价格：{{ price }}</view>
        <view class="info__value-half">可售库存：{{ invQty }}</view>
      </view>
    </view>

    <view class="detail">
      <view class="detail-item">
        <text class="detail-item-label">应收数：</text>
        <text class="detail-item-qty" v-if="sku.isDisp">{{ sku.shipQty }}{{ sku.goods.minMunit | empty }}</text>
        <text class="detail-item-qty" v-else>{{ sku.shipQpcQty | qpcQty(sku.goods.munit, sku.goods.minMunit) }}</text>
      </view>

      <view class="detail-item">
        <text class="detail-item-label">应收金额：</text>
        <text class="detail-item-total">￥{{ sku.shipTotal }}</text>
      </view>
      <view class="detail-item">
        <text class="detail-item-label">待收数：</text>
        <text class="detail-item-qty" v-if="sku.isDisp">{{ sku.qty }}{{ sku.goods.minMunit | empty }}</text>
        <text class="detail-item-qty" v-else>{{ sku.qpcQty | qpcQty(sku.goods.munit, sku.goods.minMunit) }}</text>
      </view>
    </view>
    <view class="main-exhibit" v-if="showMaster.showDisplayLocation">
      <view :class="[hasMutiple ? 'goods-one' : '']">
        陈列位置：
        <text class="goods-text">{{ sku.displayLocation | empty }}</text>
      </view>
      <image class="good-img" :src="'/static/icon/ic_right_grey.png' | oss" v-if="hasMutiple" @click="viewExhibit" />
    </view>
    <!-- 绑定陈列位置 -->
    <view class="bind-exhibit" v-if="slotSource && !readonly && isShowExhibitBtn">
      <view class="bind-exhibit-select">
        <view class="bind-exhibit-block-left">
          <text class="left-text">绑定陈列位置</text>
        </view>
        <view>
          <text @click="resetExhibit" v-if="allowOneGoodsMultipleSlot">陈列位置调整</text>
          <!-- <text @click="bindExhibit" style="margin-left: 32rpx">手动选择陈列位置</text> -->
        </view>
      </view>
      <view class="bind-exhibit-block">
        <view class="bind-exhibit-block-right">
          <input class="right-input" v-model="exhibitValue" placeholder="输入商品陈列位置编号 例：A-1-1-1" @blur="getGoodsInfo" />
        </view>
        <view class="right-scan" @click="doScanExhibit">
          <image class="right-img" :src="'/static/icon/ic_scan_blue.png' | oss" />
          <text class="right-text">扫码识别</text>
        </view>
      </view>
    </view>
    <template v-if="sku.goods.isDisp">
      <view class="qty" v-if="doubleMeasureGoodsEnterQpcQty">
        <view class="qty-txt">件数</view>
        <view class="qty-number">
          <view class="qty-operator" v-if="sku.goods.qpc">{{ sku.goods.qpc | empty }} ×</view>
          <template v-if="readonly">{{ dispWholeQty }}</template>
          <hd-number-box-test
            v-else
            @change="handleWholeChange"
            v-model="dispWholeQty"
            :max="Number(sku.qpcQty)"
            :scale="qtyScale"
            :min="0"
          ></hd-number-box-test>
        </view>
      </view>
      <view class="qty">
        <view class="qty-txt">重量{{ sku.goods.minMunit ? `(${sku.goods.minMunit})` : '' }}</view>
        <view class="qty-number">
          <template v-if="readonly">
            {{ dispSplitQty }}
          </template>
          <hd-number-box-test
            v-else
            v-model="dispSplitQty"
            :max="sku.qty"
            :scale="qtyScale"
            :min="0"
            @change="handleSplitChange"
          ></hd-number-box-test>
        </view>
      </view>
    </template>
    <template v-else>
      <template v-if="readonly">
        <view class="qty">
          <view class="qty-txt">整件数（即规格的整倍数）</view>
          <view class="qty-number">
            <view class="qty-operator" v-if="sku.goods.qpc">{{ sku.goods.qpc | empty }} ×</view>
            {{ wholeQty }}
          </view>
        </view>
        <view class="qty">
          <view class="qty-txt">单品</view>
          <view class="qty-number">
            {{ splitQty }}
          </view>
        </view>
      </template>
      <template v-else>
        <view class="qty">
          <view class="qty-txt">整件数（即规格的整倍数）</view>
          <view class="qty-number">
            <view class="qty-operator" v-if="sku.goods.qpc">{{ sku.goods.qpc | empty }} ×</view>
            <hd-number-box-test v-model="wholeQty" :max="wholeMax" :scale="0" :min="0" @change="doQpcChange"></hd-number-box-test>
          </view>
        </view>
        <view class="qty">
          <view class="qty-txt">单品</view>
          <view class="qty-number">
            <hd-number-box-test v-model="splitQty" :max="splitMax" :scale="qtyScale" :min="0" @change="doSingleChange"></hd-number-box-test>
          </view>
        </view>
      </template>
    </template>
    <!-- TODO 产品要求去掉差异的显示 害怕他们又改回来，先放在这 -->
    <!-- <view class="diff" v-if="diff && !readonly">
      差异：
      <text style="color: #fd3431">{{ diff }}{{ sku.goods.minMunit || '' }}</text>
    </view> -->

    <view class="operation">
      <view class="operation-left">
        <template v-if="confirmed">
          <img :src="'/static/icon/ic_user.png' | oss" class="operation-left-icon" />
          {{ openUserName | empty }}
        </template>
      </view>
      <view class="operation-detail" @click="handleViewDetail">收货明细</view>
      <view v-if="!sku.confirmed" class="operation-detail" @click="handleMarkConfirmed">标记已确认</view>
    </view>
  </view>
</template>

<script lang="ts" src="./BoxEditSplitCard.ts"></script>

<style lang="scss" scoped>
.box-edit-split-card {
  position: relative;
  width: 100%;
  background: #ffffff;
  box-sizing: border-box;
  padding: 24rpx;
  border-radius: 16rpx;

  .header {
    width: 100%;
    line-height: 40rpx;
    margin-bottom: 12rpx;

    &-name {
      font-weight: 550;
      color: #333333;
      font-size: 28rpx;
    }

    &-tag {
      display: inline-flex;
      align-items: center;
      padding: 0 10rpx;
      box-sizing: border-box;
      margin-right: 8rpx;
      width: 68rpx;
      height: 40rpx;
      background: #e9f0ff;
      border-radius: 8rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #1c64fd;
    }
    &-tag--confirmed {
      background: #f3fbf9;
      color: #12b886;
    }
  }

  .info {
    width: 100%;
    display: flex;
    margin-bottom: 16rpx;
    &__img {
      position: relative;
      width: 120rpx;
      height: 120rpx;
      margin-right: 24rpx;
      flex: 0 0 auto;
      &-sku {
        width: 120rpx;
        height: 120rpx;
      }
    }

    &__scale {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 24rpx;
      height: 24rpx;
      background: rgba(0, 0, 0, 0.4);
      border-radius: 8rpx 0px 8rpx 0rpx;
      text-align: center;
      @include flex();
      &-img {
        width: 16rpx;
        height: 16rpx;
      }
    }

    &__value {
      position: relative;
      flex: 1 1 auto;
      &-half {
        width: 50%;
        flex: 0 0 auto;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        display: inline-flex;

        // 如果不是最后两个元素，则有向下16rpx的margin
        &:not(:nth-last-child(-n + 2)) {
          margin-bottom: 16rpx;
        }

        .good-img {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }

  .detail {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 16rpx;
    &-item {
      min-height: 40rpx;
      font-size: 26rpx;

      &:not(:last-child) {
        margin-right: 32rpx;
      }

      &-label {
        color: #999999;
      }
      &-qty {
        color: #333333;
        font-weight: 550;
      }
      &-total {
        color: #999999;
      }
    }

    .text-body-text {
      width: 100%;
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: rgba(148, 150, 154, 1);

      .good-location {
        font-size: $font-size-xsmall;
        font-weight: 400;
        color: $color-text-secondary;
        max-width: 179rpx;
        height: 32rpx;
        line-height: 32rpx;
        @include ellipsis();
      }

      .good-img {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .main-half {
    width: 100%;
    flex: 0 0 auto;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .good-img {
      width: 32rpx;
      height: 32rpx;
      display: inline-table;
    }

    .goods-one {
      @include ellipsis();
    }
  }

  .main-exhibit {
    width: 100%;
    background: #f5f5f5;
    border-radius: 8rpx;
    padding: 8rpx 16rpx;
    box-sizing: border-box;
    flex: 0 0 auto;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .good-img {
      width: 32rpx;
      height: 32rpx;
      display: inline-table;
    }

    .goods-text {
      color: #333333;
      font-size: 26rpx;
      font-weight: 500;
    }

    .goods-one {
      @include ellipsis();
    }
  }

  .bind-exhibit {
    width: 100%;
    margin-bottom: 16rpx;
    display: flex;
    flex-direction: column;

    .bind-exhibit-block {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-left {
        display: flex;
        align-items: center;

        .left-text {
          height: 32rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #585a5e;
          line-height: 32rpx;
          text-align: left;
          font-style: normal;
        }

        .left-img {
          width: 32rpx;
          height: 32rpx;
        }
      }

      &-right {
        flex: 1;
        height: 72rpx;
        background: #f5f5f5;
        border-radius: 4rpx;
        display: flex;
        align-items: center;
        padding: 0 16rpx;
        box-sizing: border-box;

        .right-input {
          flex: 1;
          height: 40rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
        }
      }

      .right-scan {
        width: 188rpx;
        height: 72rpx;
        background: #e9f0ff;
        border-radius: 8rpx;
        margin-left: 16rpx;
        @include flex(row);

        .right-img {
          width: 36rpx;
          height: 36rpx;
          margin-right: 16rpx;
        }

        .right-text {
          width: 104rpx;
          height: 32rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 26rpx;
          color: #1c64fd;
          line-height: 32rpx;
          text-align: left;
          font-style: normal;
        }
      }
    }

    .bind-exhibit-select {
      width: 100%;
      height: 32rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #1c64fd;
      line-height: 32rpx;
      text-align: right;
      font-style: normal;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
    }
  }

  .qty {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 48rpx;
    box-sizing: border-box;

    &:not(:last-child) {
      margin-bottom: 24rpx;
    }
    .qty-number {
      display: flex;
      justify-content: flex-end;
      .qty-operator {
        height: 48rpx;
        margin-right: 12rpx;
        line-height: 48rpx;
        font-size: 24rpx;
        font-family: HelveticaNeue;
        color: rgba(88, 90, 94, 1);
      }
    }
    .qty-txt {
      height: 48rpx;
      max-width: 386rpx;
      line-height: 48rpx;
      font-size: 26rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      @include ellipsis();
    }
    .qty-arrow {
      height: 32rpx;
      width: 32rpx;
    }
  }
  .diff {
    margin-top: 20rpx;
    display: flex;
    justify-content: flex-end;
    font-size: 26rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
  }

  .operation {
    margin-top: 16rpx;
    width: 100%;
    flex: 0 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24rpx;
    color: #333333;

    &-left {
      flex: 1 1 auto;
      @include ellipsis();
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      display: flex;
      align-items: center;
      &-icon {
        width: 40rpx;
        height: 40rpx;
      }
    }

    &-detail {
      flex: 0 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 48rpx;
      background: #ffffff;
      border: 1rpx solid #cccccc;
      border-radius: 32rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      padding: 0 12rpx;
      margin-left: 12rpx;
      min-width: 112rpx;
    }
  }

  &:not(:last-child) {
    margin-bottom: 16rpx;
  }
}
</style>
