<!--
 * @Author: weisheng
 * @Date: 2023-07-10 15:00:44
 * @LastEditTime: 2025-05-26 15:55:38
 * @LastEditors: yuzhipi
 * @Description: 
 * @FilePath: /soa/src/pagesUtil/receiptRecord/ReceiptBoxEdit.vue
 * 记得注释
-->
<template>
  <view class="receipt-box-edit">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <hd-video-preview id="hd-video-preview"></hd-video-preview>
    <!-- #ifdef MP-WEIXIN -->
    <hd-privacy-popup id="privacy-popup"></hd-privacy-popup>
    <!-- #endif -->

    <box-edit-card-dialog
      ref="edit"
      @confirm="handleConfirm"
      @cancel="doStopScan"
      @getRequestBody="getRequestBody"
      @bindExhibit="bindExhibit"
      @resetExhibit="resetExhibit"
      @viewExhibit="viewExhibit"
    ></box-edit-card-dialog>
    <record-diff-dialog ref="diff" @finish="handleDiffFinish" @close="handleDiffCancel" @viewExhibit="viewExhibit"></record-diff-dialog>
    <sku-record-dialog ref="recordDialog"></sku-record-dialog>

    <view class="header">
      <view class="header-num">
        <image :src="'/static/icon/ic_odd_numbers.png' | oss" class="header-num-img"></image>
        <text class="header-num-txt">单号：{{ num }}</text>
      </view>
      <view class="header-count">待收共 {{ receipt.packQty }}箱，其中周转箱有{{ receipt.splitedBoxCnt }}箱</view>

      <view class="header-search">
        <view class="header-search-body" @click="doSearch">
          <image class="header-search-img" :src="'/static/icon/ic_search.png' | oss"></image>
          <text class="header-search-txt">箱码/商品名称/条码/代码</text>
        </view>
        <view class="header-search-scan" @click.stop="doScan">
          <image class="header-search-img" :src="'/static/icon/ic_saoma_green.png' | oss"></image>
          <text class="header-search-scan-txt">扫一扫</text>
        </view>
      </view>
    </view>

    <view class="tool">
      <view class="tool-switch">
        <view @click="handleShowUnconfirmed" :class="['tool-switch-option', !showConfirmed ? 'tool-switch-option--active' : '']">
          待收{{ unConfirmPack || '' }}
        </view>
        <view @click="handleShowConfirmed" :class="['tool-switch-option', showConfirmed ? 'tool-switch-option--active' : '']">
          已确认{{ confirmedPack || '' }}
        </view>
        <view class="tool-switch-cachet" :style="!showConfirmed ? 'transform: translateX(0)' : 'transform: translateX(182rpx)'"></view>
      </view>
      <view class="tool-filter" v-if="showConfirmed">
        <view class="tool-filter-sort" @click="handleViewMine">
          <hd-radio :checked="viewMine" :preventClick="true" :clearable="false"></hd-radio>
          <text class="tool-filter-sort-txt">仅看自己</text>
        </view>
      </view>
    </view>
    <view class="list" v-if="boxList.length > 0">
      <templat v-if="showConfirmed">
        <hd-swipe-action
          v-for="(box, index) in boxList"
          :key="index"
          @onDelete="handleCancel(box)"
          :index="index"
          :swipe-able="box.confirmed"
          :moveIndex="moveIndex"
          @updateIndex="onUpdateIndex"
          operatorText="取消收货"
        >
          <box-edit-card
            @confirm="handleBeforeConfirm"
            @click="handleCardClick"
            @edit="(info) => handleEdit(info, index)"
            @edit-reason="handleEditReason"
            :box="box"
            :showConfirmed="showConfirmed"
            @detail="handleViewDetail"
            @getRequestBody="getRequestBody"
            @bindExhibit="(info) => bindExhibit(info, index)"
            @resetExhibit="(info) => resetExhibit(info, index)"
            @viewExhibit="viewExhibit"
          ></box-edit-card>
        </hd-swipe-action>
      </templat>
      <template v-else>
        <box-edit-card
          v-for="(box, index) in boxList"
          :key="index"
          @confirm="handleBeforeConfirm"
          @click="handleCardClick"
          @edit="(info) => handleEdit(info, index)"
          @edit-reason="handleEditReason"
          :box="box"
          :showConfirmed="showConfirmed"
          @detail="handleViewDetail"
          @getRequestBody="getRequestBody"
          @bindExhibit="(info) => bindExhibit(info, index)"
          @resetExhibit="(info) => resetExhibit(info, index)"
          @viewExhibit="viewExhibit"
        ></box-edit-card>
      </template>
      <view class="loading" v-if="!finished && isLoading">
        <view class="load-more"></view>
        <text>拼命加载中~~~</text>
      </view>
    </view>
    <hd-empty v-else :img="'/static/img/img_empty_goods.png' | oss" title="暂无相关商品"></hd-empty>
    <check-pagination
      v-if="showPagination"
      ref="checkpick"
      :cur-page="currentPage"
      :each-page="eachPage"
      @page-change="handlePageChange"
      :totalNum="total"
    ></check-pagination>

    <view class="footer">
      <view class="footer-operation">
        <template v-if="!showConfirmed">
          <hd-button type="white" @click="handlePreFullCheck" v-if="btnPermission.save">一键确认</hd-button>
          <hd-button type="primary" @click="handlePreFinish">结束收货</hd-button>
        </template>
        <template v-else>
          <hd-button @click="handleSave" type="white">保存</hd-button>

          <hd-button type="primary" @click="handlePreSubmit" v-if="btnPermission.submit">提交</hd-button>
        </template>
      </view>
    </view>

    <!-- 选择陈列位置 -->
    <select-exhibit ref="exhibit" @addExhibit="addExhibit" @success="confirmExhibit"></select-exhibit>

    <!-- 陈列位置调整 -->
    <reset-exhibit ref="resetExhibit" @addExhibit="addExhibit" @success="confirmExhibit"></reset-exhibit>

    <!-- 页码选择 -->
    <page-picker ref="pagepick" :totalPage="totalPage" :page="currentPage" @confirm="handlePagePickerConfirm"></page-picker>

    <!-- 查看多个陈列位置 -->
    <uni-popup ref="viewExhibit" type="bottom">
      <view-exhibit
        :name="viewExhibitInfo.goods.name"
        :displayLocation="viewExhibitInfo.displayLocation || viewExhibitInfo.goods.displayLocation"
        @doClose="closeViewExhibit"
      ></view-exhibit>
    </uni-popup>
  </view>
</template>

<script lang="ts" src="./ReceiptBoxEdit.ts"></script>

<style lang="scss" scoped>
.receipt-box-edit {
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  box-sizing: border-box;
  background: #f5f6f7;
  padding-bottom: 100rpx !important;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 100rpx) !important;
  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx) !important;

  .header {
    width: 100%;
    box-sizing: border-box;
    background: #ffffff;
    padding: 24rpx 24rpx 0 24rpx;
    &-num {
      display: flex;
      align-items: center;
      width: 100%;
      height: 44rpx;
      align-items: center;
      font-size: 32rpx;
      color: $color-text-secondary;
      margin-bottom: 24rpx;

      &-img {
        width: 44rpx;
        height: 44rpx;
      }

      &-txt {
        margin-left: 4rpx;
      }
    }

    &-count {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 32rpx;
    }

    &-search {
      width: 100%;
      height: 120rpx;
      background: #ffffff;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-body {
        flex: 0 0 auto;
        width: 510rpx;
        height: 72rpx;
        background: #ffffff;
        border-radius: 36rpx;
        border: 2rpx solid #e9eefb;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding: 0 20rpx;
      }
      &-scan {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        min-width: 172rpx;
        height: 72rpx;
        border-radius: 46rpx;
        border: 1rpx solid #1c64fd;
        box-sizing: border-box;
        padding: 0 20rpx;
        &-txt {
          font-size: 28rpx;
          color: #1c64fd;
        }
      }
      &-img {
        width: 40rpx;
        height: 40rpx;
        margin-right: 8rpx;
      }

      &-txt {
        font-size: 26rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #cccccc;
      }
    }
    ::v-deep .hd-search-bar {
      padding-bottom: 0 0 24rpx 0;
    }
  }

  .tool {
    position: sticky;
    z-index: 10;
    top: -1rpx;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 18rpx 24rpx;
    background: #f5f6f7;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-switch {
      position: relative;
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 384rpx;
      height: 60rpx;
      background: #ffffff;
      border-radius: 8rpx;

      &-option {
        position: relative;
        height: 60rpx;
        width: 50%;
        box-sizing: border-box;
        z-index: 2;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        color: $color-text-thirdly;
        display: flex;
        align-items: center;
        justify-content: center;
        &--active {
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: $color-white;
        }
      }

      &-cachet {
        transition-timing-function: ease;
        transition-duration: 300ms;
        position: absolute;
        z-index: 1;
        left: 8rpx;
        top: 8rpx;
        width: 186rpx;
        height: 48rpx;
        background: #1c64fd;
        border-radius: 8rpx;
      }
    }

    &-filter {
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 24rpx;
      box-sizing: border-box;

      &-sort {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        font-family: PingFangSC;
        color: $color-text-thirdly;
        &-txt {
          margin-left: 8rpx;
        }
      }
    }
  }

  .list {
    width: 100%;
    box-sizing: border-box;
    padding: 0 24rpx 16rpx 24rpx;
    .loading {
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: rgb(148, 150, 154);
    }
  }

  .footer {
    position: fixed;
    z-index: 10;
    display: flex;
    flex-direction: column;
    width: 100%;
    bottom: 0;
    left: 0;
    padding-bottom: 0 !important;
    padding-bottom: constant(safe-area-inset-bottom) !important;
    padding-bottom: env(safe-area-inset-bottom) !important;
    background: $color-bg-primary;

    &-diff {
      width: 100%;
      height: 80rpx;
      background: #fff7e0;
      display: flex;
      align-items: center;
      justify-content: center;

      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;

      &-num {
        font-family: PingFangSC, PingFang SC;
        font-weight: 550;
        font-size: 30rpx;
        color: #333333;
      }
    }

    &-operation {
      display: flex;
    }
  }
}
</style>
