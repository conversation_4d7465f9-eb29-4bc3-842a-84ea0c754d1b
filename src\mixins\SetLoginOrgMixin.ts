import { Vue, Component } from 'vue-property-decorator'
import { State, Action } from 'vuex-class'
import OrgApi from '@/network/portal/OrgApi'
import LogonUser<PERSON><PERSON> from '@/network/portal/LogonUserApi'
import OrgQueryVo from '@/model/portalModel/org/OrgQueryVo'
import { NextStepEnum } from '@/model/portalModel/login/LoginEnum'
import TenantApi from '@/network/portal/TenantApi'
import UserInfo from '@/model/portalModel/cache/UserInfo'
import Store from '@/model/store/Store'
import CLastLoginVo from '@/model/portalModel/login/ClastLoginVo'

@Component
export default class SetLoginOrgMixin extends Vue {
  @Action('store') actionStore
  @State('store') store: Store
  @State('orgInfo') orgInfo!: OrgQueryRes
  @Action('storeTotal') actionStoreTotal
  @Action('setOrgInfo') actionOrgInfo
  // 根据条件获取当前用户的可登录组织
  async getOrgList(): Promise<OrgQueryRes | boolean> {
    return new Promise((resolve, reject) => {
      try {
        const orgQueryFilter: OrgQueryVo = new OrgQueryVo()
        orgQueryFilter.page = 0
        orgQueryFilter.pageSize = 10
        OrgApi.orgQuery(orgQueryFilter)
          .then((res) => {
            this.actionStoreTotal(res.total)
            if (res.data && res.data.length === 1) {
              resolve(res.data[0])
            } else {
              resolve(false)
            }
          })
          .catch((e) => {
            reject(new Error(`Failed to fetch organization list: ${e.message}`))
          })
      } catch (e) {
        reject(new Error(`Failed to fetch organization list: ${(e as Error).message}`))
      }
    })
  }
  // 登录租户。
  selectOrgAction(org: OrgQueryRes): Promise<boolean> {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
      this.$showLoading()
      try {
        const res = await OrgApi.setLoginOrg(org.id || '')
        if (res.success) {
          this.actionOrgInfo(org)
          // this.$store.commit('setLoginAndPickOrg', true)
          await this.getLogonInfo()
          // await this.setUserEmployeeStationState()
          // 设置组织成功进入首页
          // ShortcutMgr.switchTenantSuccess()
          resolve(true)
        } else {
          // 如果设置组织失败进入选择组织页面
          // uni.navigateTo({ url: '/pages/auth/OrgSelect?nextStep=' + NextStepEnum.ENTER_HOME_PAGE + '&from=login' })
        }
      } catch (e) {
        // 如果设置组织失败进入选择组织页面
        // uni.navigateTo({ url: '/pages/auth/OrgSelect?nextStep=' + NextStepEnum.ENTER_HOME_PAGE + '&from=login' })
        reject(e)
      } finally {
        this.$hideLoading()
      }
    })
  }

  // 获取当前用户最近一次登录的组织
  async getLastLoginOrg() {
    return new Promise((resolve, reject) => {
      OrgApi.getLastLoginOrg()
        .then((res) => {
          if (res.data) {
            resolve(res.data)
          } else {
            resolve(false)
          }
        })
        .catch((e) => {
          reject(new Error(`Failed to fetch organization list: ${e.message}`))
        })
    })
  }
  // 判断是否需要进行默认设置组织的操作；1. 如果当前用户的组织只有一个，默认进入；该条件的优先级更高；2.如果当前用户查询到最近一次的登录组织，默认进入
  async setDefaultOrg() {
    try {
      // 1. 如果当前用户的组织只有一个，默认进入
      const hasOneOrg = await this.getOrgList()
      console.log('hasOneOrg', hasOneOrg && this.isOrgQueryRes(hasOneOrg))
      if (hasOneOrg && this.isOrgQueryRes(hasOneOrg)) {
        await this.selectOrgAction(hasOneOrg)
        return
      }
      // 2.如果当前用户查询到最近一次的登录组织，默认进入
      const res = await OrgApi.getLastLoginOrg()
      console.log('getLastLoginOrg', res)
      if (res.success && res.data) {
        const hasSet = await this.selectOrgAction(res.data)
        return
      }
      // 3.如果当前用户没有最近一次的登录组织，则进入组织列表选择
      this.$store.commit('setLoginAndPickOrg', false)
      uni.navigateTo({ url: '/pages/auth/OrgSelect?nextStep=' + NextStepEnum.ENTER_HOME_PAGE + '&from=login' })
    } catch (e) {
      // TODO handle the exception
    } finally {
      this.$hideLoading()
    }
  }

  setDefaultOrgNew() {
    return new Promise((resolve, reject) => {
      this.$showLoading()
      OrgApi.getLastAndLogin()
        .then((res) => {
          const result = res.data as CLastLoginVo
          // 存储可选组织数量
          this.actionStoreTotal(result.loginableOrgTotal)
          if (result.loginableOrgTotal > 0) {
            // 判断是否有默认登录组织
            if (result.lastLogin?.id) {
              // 存储登录组织
              this.actionOrgInfo(result.lastLogin)
              // 存储登录信息
              if (result.userLogonInfo) {
                this.$store.dispatch('setEmployeeInfo', result.userLogonInfo)
              }
            } else {
              // 若上一次登录信息为空，进入组织列表页
              // 但是home页面做了判断（未选择门店跳转到选择门店页面），在home页面跳转
            }
            resolve(true)
          } else {
            // 如果没有可选组织数量，报错
            this.$showToast({ title: '当前用户没有可登录的组织', icon: 'error' })
            resolve(false)
          }
        })
        .catch((e) => {
          const error = e as any
          this.$showToast({ title: error.msg, icon: 'error' })
          resolve(false)
        })
        .finally(() => {
          this.$hideLoading()
        })
    })
  }
  isOrgQueryRes(obj: any): obj is OrgQueryRes {
    return obj && typeof obj === 'object' && 'id' in obj
  }
  // 获取登录信息
  async getLogonInfo() {
    try {
      const logonInfo = await LogonUserApi.getLogonInfo()
      if (logonInfo && logonInfo.data) {
        this.$store.dispatch('setEmployeeInfo', logonInfo.data)
      }
    } catch (e) {
      const error = e as any
      this.$showToast({ title: error.msg, icon: 'error' })
      // TODO handle the exception
    }
  }

  // 获取登录的租户信息
  async getTenant() {
    this.$showLoading({ delayTime: 200 })
    try {
      const resp = await TenantApi.getLogonTenant()
      if (resp && resp.data) {
        // this.actionTenantInfo(resp.data)
        // ShortcutMgr.switchTenantSuccess()
      }
    } catch (e) {
      const error = e as any
      this.$showToast({ title: error.msg, icon: 'error' })
    } finally {
      this.$hideLoading()
    }
    // TenantApi.getLogonTenant()
    //   .then((resp) => {
    //     this.$hideLoading()
    //     if (resp && resp.data) {
    //       this.actionTenantInfo(resp.data)
    //       ShortcutMgr.switchTenantSuccess()
    //       // 去选组织
    //       uni.navigateTo({ url: '/pages/auth/OrgSelect?nextStep=' + NextStepEnum.ENTER_HOME_PAGE + '&from=login' })
    //     }
    //   })
    //   .catch((e) => {
    //     this.$hideLoading()
    //     this.$showToast({ title: e.msg, icon: 'error' })
    //   })
  }
  // 登录成功后设置用户的在岗状态
  async setUserEmployeeStationState() {
    try {
      const employeeId = this.$store.state.employeeId
      const res = await LogonUserApi.changeEmployeeStationState(employeeId, 'onDuty')
    } catch (e) {
      const error = e as any
      this.$showToast({ title: error.msg, icon: 'error' })
    }
  }
}
