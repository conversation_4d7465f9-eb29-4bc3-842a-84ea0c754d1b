import { Component } from 'vue-property-decorator'
import TipsModal from './cmp/TipsModal.vue'
import { mixins } from 'vue-class-component'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import ReceiptApi from '@/network/receipt/ReceiptApi'
import ReceiptApiV2 from '@/network/receipt/ReceiptApiV2'
import AppReceiptBoxDiffGoodsDTO from 'model/receipt/dto/AppReceiptBoxDiffGoodsDTO'
import Receipt from '@/model/receipt/Receipt'
import QueryRequest from '@/model/receipt/default/QueryRequest'
import AppReceiptBoxDTO from '@/model/receipt/AppReceiptBoxDTO'
import BoxEditCard from './cmp/BoxEditCard.vue'
import AppReceiptBoxModificationDTO from '@/model/receipt/AppReceiptBoxModificationDTO'
import AppReceiptBoxDiffSumDTO from '@/model/receipt/AppReceiptBoxDiffSumDTO'
import AppReceiptBoxGoodsModificationDTO from '@/model/receipt/AppReceiptBoxGoodsModificationDTO'
import BoxEditCardDialog from './cmp/BoxEditCardDialog.vue'
import AppReceiptModificationDTO from '@/model/receipt/AppReceiptModificationDTO'
import ID from '@/model/receipt/default/ID'
import { ReceiptCheckLockState } from '@/model/receipt/default/ReceiptCheckLockState'
import UserInfo from '@/model/user/UserInfo'
import { Getter, State } from 'vuex-class'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import BoxEditReasonDialog from './cmp/BoxEditReasonDialog.vue'
import UseQueues from '@/components/hd-popover/UseQueues'
import Slot from '@/model/data/Slot'
import GoodsSlotBinder from '@/model/data/GoodsSlotBinder'
import AppReceiptBoxGoodsDTO from '@/model/receipt/AppReceiptBoxGoodsDTO'
import SelectExhibit from '@/components/select-exhibit/SelectExhibit.vue'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import ResetExhibit from '@/components/select-exhibit/ResetExhibit.vue'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import { runJob } from '@/common/Job'
import ReceiptQueryRequest from '@/model/base/QueryRequest'
import AppReceiptDiffCheckStartDTO from 'model/receipt/dto/AppReceiptDiffCheckStartDTO'

@Component({
  components: {
    BoxEditCard,
    BoxEditCardDialog,
    BoxEditReasonDialog,
    SelectExhibit,
    ViewExhibit,
    ResetExhibit,
    TipsModal
  }
})
export default class ReceiptBoxEdit extends mixins(BroadCast, UseQueues) {
  @State('userInfo') userInfo: UserInfo // 当前用户信息
  @Getter('qtyScale') qtyScale: number
  @State('optionList') optionList: ModuleOption[]

  $refs: any
  diffGoodsList: AppReceiptBoxDiffGoodsDTO[] = [] // 单据差异商品列表
  receipt: Receipt = new Receipt() // 单据信息
  boxList: AppReceiptBoxDTO[] = [] // 箱子列表
  // 分页相关
  pageSize: number = 10 // 每页大小
  pageNum: number = 0 // 页码
  finished: boolean = false // 是否加载完成
  isLoading: boolean = false // 是否在加载
  summary: AppReceiptBoxDiffSumDTO = new AppReceiptBoxDiffSumDTO() // 箱子汇总信息
  unConfirmPack: Nullable<number> = null // 未确认箱数
  showConfirmed: boolean = false // 是否展示已收
  viewMine: boolean = false // 仅看自己
  scaning: boolean = false // 是否连扫中

  hasSelelcExhibit: Slot = new Slot() // 已选中的陈列位置
  viewExhibitInfo: AppReceiptBoxGoodsDTO = new AppReceiptBoxGoodsDTO() // 陈列位置弹窗数据
  exhibitIndex: number = 0 // 陈列位置下标

  /**
   * 提交加工方式：0-异步；1-同步
   */
  get submitProcessMode() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []

    if (moduleConfig.length > 0) {
      return moduleConfig.findIndex((item) => item.options && item.options.submitProcessMode === '1') > -1
    }
    return false
  }

  // 是否启用差异原因功能
  get diffReasonsReportedByStore() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.diffReasonsReportedByStore == '1'
    }
    return false
  }

  // 获取按钮权限
  get btnPermission() {
    const btnPermission = {
      save: false,
      submit: false,
      boxDataAccess: false
    }
    if (PermissionMgr.hasPermission(Permission.receiptEdit)) {
      btnPermission.save = true
    }
    if (PermissionMgr.hasPermission(Permission.receiptSubmit)) {
      btnPermission.submit = true
    }

    if (PermissionMgr.hasPermission(Permission.receiptBoxDataAccess)) {
      btnPermission.boxDataAccess = true
    }
    return btnPermission
  }

  /**
   * 单据是否锁定
   */
  get isLocked() {
    if (this.receipt && this.receipt.checkLockState === ReceiptCheckLockState.locked) {
      return true
    } else {
      return false
    }
  }

  onLoad(option) {
    if (option && option.id) {
      this.$nextTick(async () => {
        await this.handleInit(option.id)
        if (option.keyword) {
          let keyword: string = ''
          try {
            keyword = decodeURIComponent(option.keyword)
          } catch (error) {
            keyword = option.keyWord
          }
          this.doScanAfter(keyword)
        }
        if (option.searchword) {
          let searchword: string = ''
          try {
            searchword = decodeURIComponent(option.searchword)
          } catch (error) {
            searchword = option.searchword
          }
          const timer = setTimeout(() => {
            this.$Router.push({
              name: 'receiptBoxSearch',
              params: {
                id: this.receipt.billId,
                searchword: searchword
              }
            })
            clearTimeout(timer)
          }, 200)
        }
      })
    }

    // 卸载
    uni.$off('boxQuery')
    // 重新监听
    uni.$on('boxQuery', () => {
      this.doResetPage()
      this.loadBoxList()
    })

    // 卸载
    uni.$off('update-un-confirm')
    // 重新监听
    uni.$on('update-un-confirm', (res) => {
      this.unConfirmPack = res
    })
    uni.$off('refreshExhibit')
    uni.$on('refreshExhibit', () => {
      this.$refs.exhibit.refresh()
      this.$refs.resetExhibit.refresh()
    })
  }

  onUnload() {
    // 卸载
    uni.$off('boxQuery')
    uni.$off('update-un-confirm')
    uni.$off('refreshExhibit')
  }

  // 显示更多商品
  async onReachBottom() {
    if (this.finished || this.isLoading) {
      return
    }
    try {
      this.$showLoading()
      await this.queryBox()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '查询失败',
        icon: 'error'
      })
    }
  }

  /**
   * 关闭所有气泡
   */
  doCloseOutside() {
    if (this.queueObj && this.queueObj.closeOutside) {
      this.queueObj.closeOutside()
    }
  }

  async handleInit(billId: string) {
    try {
      this.$showLoading()
      this.receipt = await this.getReceipt(billId)
      this.unConfirmPack = this.receipt.unConfirmPack
      if (this.receipt.state !== 'initial') {
        this.$hideLoading()
        this.$showModal({
          title: '提示',
          content: '该单据已被处理',
          showCancel: false,
          confirmText: '我知道了',
          success: (action) => {
            if (action.confirm) {
              this.$Router.replace({
                name: 'receiptDetail',
                params: { id: billId }
              })
            }
          }
        })
        return
      }
      if (this.isLocked) {
        this.$hideLoading()
        const timer = setTimeout(() => {
          this.$Router.replace({
            name: 'receiptBoxDiffCheck',
            params: { id: billId }
          })
          clearTimeout(timer)
        }, 200)
        return
      }
      await this.queryBox()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: error.msg })
    }
  }

  handleViewMine() {
    this.viewMine = !this.viewMine
    this.doResetPage()
    this.loadBoxList(true)
  }

  doSearch() {
    this.$Router.push({
      name: 'receiptBoxSearch',
      params: {
        id: this.receipt.billId
      }
    })
  }

  // 扫码按钮点击事件
  doScan() {
    uni.scanCode({
      success: (res) => {
        const scanWord = res.result || ''
        this.doScanAfter(scanWord)
      },
      fail: () => {
        this.scaning = false
      }
    })
  }

  /**
   * 中断连续扫码
   */
  doStopScan() {
    this.scaning = false
  }

  /**
   * PDA扫码回调事件
   * @param scanWord 扫码文字
   * @param isScan 是否扫码
   * @returns
   */
  async doScanAfter(scanWord: string, isScan: boolean = true) {
    if (this.isLoading) {
      this.$showToast({ title: '正在加载，请稍后重试~' })
      return
    }

    try {
      this.$showLoading()
      this.scaning = true
      const boxList = await this.queryBox(isScan, scanWord)
      this.$hideLoading()
      if (!boxList || !boxList.length) {
        // 无搜索结果跳转至搜索页面
        this.$Router.push({
          name: 'receiptBoxSearch',
          params: {
            id: this.receipt.billId,
            keyWord: scanWord
          }
        })
      } else if (boxList.length === 1) {
        // 当未锁单时  判断单子是否收货和收货人是不是当前用户
        // 当存在操作人且不是自己，直接提示
        if (boxList[0].openUserId && boxList[0].openUserId !== this.userInfo.loginId) {
          this.$showToast({ title: `此商品已被${boxList[0].openUserName}收货，无法进行修改` })
        } else {
          if (boxList[0].type === 'split') {
            this.handleCardClick(boxList[0])
          } else {
            this.handleEdit(boxList[0], 0)
          }
        }
      } else {
        this.$Router.push({
          name: 'receiptBoxSearch',
          params: {
            id: this.receipt.billId,
            keyWord: scanWord
          }
        })
      }
    } catch (error) {
      this.scaning = false
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 重置分页参数
   */
  doResetPage() {
    this.pageNum = 0
    this.isLoading = false
    this.finished = false
  }

  /**
   * 拆零箱点击
   * @param box
   */
  handleCardClick(box: AppReceiptBoxDTO) {
    if (box.type === 'split') {
      this.$Router.push({
        name: 'receiptBoxLineEdit',
        params: {
          boxNo: box.boxNo,
          id: this.receipt.billId
        }
      })
    }
  }

  /**
   * 确认收货前置
   * @param box
   */
  handleBeforeConfirm(box: AppReceiptBoxDTO) {
    this.$showModal({
      title: '确定已完成该商品收货？',
      success: async (action) => {
        if (action.confirm) {
          this.handleConfirm(box)
        }
      }
    })
  }

  /**
   * 待收货的箱子确认收货
   * @param box
   */
  async handleConfirm(box: AppReceiptBoxDTO) {
    try {
      this.$showLoading()
      const boxGoodss: AppReceiptBoxGoodsModificationDTO[] = box.boxGoodss.map((item) => {
        return {
          uuid: item.uuid,
          // 商品数据标识
          gdUuid: item.goods.uuid,
          // 商品输入码
          gdInputCode: item.goods.inputCode,
          note: item.note,
          receiptQty: item.receiptQty,
          receiptQpcQty: item.receiptQpcQty,
          receiptTotal: item.goods.isDisp
            ? Number(item.receiptQty).multiply(item.goods.price).divide(item.goods.qpc).scale(2)
            : box.receiptQty.multiply(item.goods.price).scale(2),
          reasons: box.boxGoodss[0].reasons
        }
      })
      const body: AppReceiptBoxModificationDTO = { ...box, confirmed: true, billId: this.receipt.billId, diffChecking: 0, boxGoodss: boxGoodss }
      this.summary = await this.modifyBox(body)
      this.unConfirmPack = this.summary.unConfirmPack
      this.$hideLoading()
      this.doResetPage()
      this.loadBoxList(true)

      if (this.scaning) {
        this.doScan()
      }
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 编辑箱
   * @param box
   */
  handleEdit(box: AppReceiptBoxDTO, index) {
    this.viewExhibitInfo = new AppReceiptBoxGoodsDTO()
    this.exhibitIndex = index
    this.$refs.edit.open(box)
  }

  handleEditReason(box: AppReceiptBoxDTO) {
    this.$refs.reason.open(box)
  }

  /**
   * 查看未确认
   */
  handleShowUnconfirmed() {
    if (this.showConfirmed) {
      this.showConfirmed = false
      this.doResetPage()
      this.loadBoxList()
    }
  }

  /**
   * 查看已确认
   */
  async handleShowConfirmed() {
    if (!this.showConfirmed) {
      this.showConfirmed = true
      this.doResetPage()
      this.loadBoxList()
    }
  }

  async loadBoxList(disableUpdateDiff?: boolean) {
    try {
      this.$showLoading()
      await this.queryBox()
      if (this.showConfirmed && this.pageNum === 1 && !disableUpdateDiff) {
        this.summary = await this.queryBoxDiff()
      }

      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '查询失败',
        icon: 'error'
      })
    }
  }

  async handleSave() {
    try {
      this.$showLoading()
      const body = new AppReceiptModificationDTO()
      body.billId = this.receipt.billId
      body.diffChecking = 0
      await this.modifyByBox(body)
      this.$hideLoading()
      this.$showToast({ icon: 'success', title: '保存成功' })
    } catch (error) {
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: error.msg })
    }
  }

  handlePreSubmit() {
    this.$showModal({
      title: '确定提交当前收货单？',
      success: async (action) => {
        if (action.confirm) {
          this.handleSubmit()
        }
      }
    })
  }

  async handleSubmit() {
    try {
      this.$showLoading()
      if (this.submitProcessMode) {
        this.submitAndProcess() // 进行加工处理
      } else {
        const body = new AppReceiptModificationDTO()
        body.billId = this.receipt.billId
        body.diffChecking = 0
        await this.modifyAndSubmitByBox(body)
        this.$hideLoading()
        this.$showToast({ icon: 'success', title: '提交成功' })
        setTimeout(() => {
          this.$Router.replace({
            name: 'receiptDetail',
            params: {
              id: this.receipt.billId
            }
          })
        }, 1500)
      }
    } catch (error) {
      this.$hideLoading()
      this.$showModal({
        title: '提交失败',
        content: `失败原因:${error.msg}`,
        showCancel: true,
        confirmText: '重试',
        success: (action) => {
          if (action.confirm) {
            this.handleSubmit()
          }
        }
      })
    }
  }

  // 异步加工接口
  submitAndProcess() {
    const body = new ID()
    body.id = this.receipt.billId
    ReceiptApi.submitAndProcess(body)
      .then((res) => {
        if (res.data.state === 'executing') {
          runJob(
            this,
            res.data.id,
            () => {
              this.$hideLoading()
              this.$showToast({ icon: 'success', title: '提交成功' })
              setTimeout(() => {
                this.$Router.replace({
                  name: 'receiptDetail',
                  params: {
                    id: this.receipt.billId
                  }
                })
              }, 1500)
            },
            (fail) => {
              this.$showToast({ icon: 'error', title: fail.message || '提交失败' })
            }
          )
        } else if (res.data.state === 'completed') {
          this.$hideLoading()
          this.$showToast({ icon: 'success', title: '提交成功' })
          setTimeout(() => {
            this.$Router.replace({
              name: 'receiptDetail',
              params: {
                id: this.receipt.billId
              }
            })
          }, 1500)
        } else {
          this.$hideLoading()
          this.$showToast({ icon: 'error', title: res.data.message || '提交失败' })
        }
      })
      .catch((err) => {
        this.$showToast({ icon: 'error', title: err.msg || '提交失败' })
      })
      .finally(() => {
        this.$hideLoading()
      })
  }

  /**
   * 开始核对差异
   */
  async handleStartCheck() {
    if (this.diffReasonsReportedByStore) {
      this.$showLoading()
      const body = new ReceiptQueryRequest()
      body.pageSize = -1
      body.conditions = [{ operation: 'billId:=', parameters: [this.receipt.billId] }]
      ReceiptApi.queryDiffGoods(body)
        .then((res) => {
          if (res.data.diffGoodsList.length > 0) {
            // 获取有不同方向的差异商品列表
            this.diffGoodsList = res.data.diffGoodsList.reduce((acc: AppReceiptBoxDiffGoodsDTO[], cur: AppReceiptBoxDiffGoodsDTO) => {
              if (cur.receiptMoreRecCnt > 0 && cur.receiptLessRecCnt > 0) {
                acc.push(cur)
              }
              return acc
            }, [])
            if (this.diffGoodsList.length > 0) {
              this.$refs.tips.open()
            } else {
              this.realStartDiffCheck(false)
            }
          } else {
            this.realStartDiffCheck(false)
          }
        })
        .catch((err) => {
          this.$showToast({ icon: 'error', title: err.msg || '提交失败' })
        })
        .finally(() => {
          this.$hideLoading()
        })
    } else {
      this.realStartDiffCheck(false)
    }
  }

  // 提示弹窗点击事件
  tipsBtnClick(type: string) {
    this.$refs.tips.close()
    if (type === 'continue') {
      this.realStartDiffCheck(true)
    }
  }

  // 真正去调用核对差异接口(isClearReceiptErrorGoodsReason： 是否清除差异原因)
  async realStartDiffCheck(isClearReceiptErrorGoodsReason: boolean) {
    try {
      this.$showLoading()
      const body = new AppReceiptDiffCheckStartDTO()
      body.id = this.receipt.billId
      if (isClearReceiptErrorGoodsReason) body.clearReceiptErrorGoodsReason = 1
      await this.startDiffCheck(body)
      this.$hideLoading()
      this.$Router.replace({
        name: 'receiptBoxDiffCheck',
        params: { id: this.receipt.billId }
      })
    } catch (error) {
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: error.msg })
    }
  }

  // 获取单据详情
  getReceipt(billId: string) {
    return new Promise<Receipt>((resolve, reject) => {
      ReceiptApiV2.get(billId, 'details,referenceBills')
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查询箱码列表
   * @param body
   * @returns
   */
  queryBox(isScan: boolean = false, keyWord?: string) {
    this.isLoading = true
    const body = new QueryRequest()
    body.page = this.pageNum
    body.pageSize = this.pageSize
    body.sorts = [{ asc: false, field: 'type' }]
    body.conditions = [{ operation: 'billId:=', parameters: [this.receipt.billId] }]

    if (this.showConfirmed) {
      body.sorts.push({ field: 'goodsDiffQty', asc: false })
      body.conditions.push({ operation: 'confirmed:=', parameters: ['true'] })
      if (this.viewMine) {
        body.conditions.push({ operation: 'preOpenUserId:=', parameters: [this.userInfo.loginId || ''] })
      }
    } else {
      body.conditions.push({ operation: 'confirmed:=', parameters: ['false'] })
    }

    if (keyWord) {
      body.conditions.push({ operation: 'keyword:%=%', parameters: [keyWord] })
    }
    if (isScan) {
      body.page = 0
      body.pageSize = 2
      body.conditions.push({ operation: 'isScan:=', parameters: ['true'] })
    }

    return new Promise<AppReceiptBoxDTO[]>((resolve, reject) => {
      ReceiptApi.queryBox(body)
        .then((resp) => {
          if (!isScan) {
            this.pageNum++
            if (!resp.more) {
              this.finished = true
            }
            if (body.page === 0) {
              this.boxList = resp.data
            } else {
              this.boxList.push(...resp.data)
            }
          }
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
        .finally(() => {
          this.isLoading = false
        })
    })
  }

  /**
   * 提交
   * @param body
   * @returns
   */
  modifyAndSubmitByBox(body: AppReceiptModificationDTO) {
    return new Promise<void>((resolve, reject) => {
      ReceiptApi.modifyAndSubmitByBox(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 编辑箱码
   * @param body
   * @returns
   */
  modifyBox(body: AppReceiptBoxModificationDTO) {
    return new Promise<AppReceiptBoxDiffSumDTO>((resolve, reject) => {
      ReceiptApi.modifyBox(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查询箱码差异
   * @returns
   */
  queryBoxDiff() {
    return new Promise<AppReceiptBoxDiffSumDTO>((resolve, reject) => {
      const body: QueryRequest = new QueryRequest()
      body.page = 0
      body.pageSize = 1
      body.conditions = [{ operation: 'billId:=', parameters: [this.receipt.billId] }]
      ReceiptApi.queryBoxDiff(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 保存
   * @param body
   * @returns
   */
  modifyByBox(body: AppReceiptModificationDTO) {
    return new Promise<void>((resolve, reject) => {
      ReceiptApi.modifyByBox(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 开始核对差异
   * @param body
   * @returns
   */
  startDiffCheck(body: AppReceiptDiffCheckStartDTO) {
    return new Promise<void>((resolve, reject) => {
      ReceiptApi.startDiffCheck(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 绑定陈列位置
   */
  bindExhibit(info: AppReceiptBoxGoodsDTO, index: number) {
    this.exhibitIndex = index ? index : this.exhibitIndex
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.goods.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.open(this.hasSelelcExhibit, slotGoods, 'bindExhibit')
  }

  /**
   * 陈列位置调整
   */
  resetExhibit(info: AppReceiptBoxGoodsDTO, index: number) {
    this.hasSelelcExhibit = new Slot()
    this.exhibitIndex = index ? index : this.exhibitIndex
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.goods.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.$refs.resetExhibit.open(slotGoods)
  }

  /**
   * 获取该商品信息
   */
  getRequestBody(info: AppReceiptBoxGoodsDTO) {
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.goods.displayLocation
    // 目标货位
    slotGoods.targetSlotCode = info.exhibitValue
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.success(this.hasSelelcExhibit, slotGoods)
  }

  /**
   * 输入框的值组装
   */
  inputExhibit(code) {
    this.hasSelelcExhibit.code = code
    this.hasSelelcExhibit.name = code
    this.hasSelelcExhibit.uuid = ''
  }

  /**
   * 陈列位置绑定成功
   */
  async confirmExhibit() {
    this.doResetPage()
    await this.loadBoxList()
    this.$refs.edit.open(this.boxList[this.exhibitIndex] || [], false)
    this.hasSelelcExhibit = new Slot()
  }

  /**
   * 添加陈列位置
   */
  addExhibit() {
    this.$Router.push({
      name: 'addExhibitLocation'
    })
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info: AppReceiptBoxGoodsDTO) {
    this.viewExhibitInfo = { ...info }
    this.$refs.viewExhibit.open()
  }
}
