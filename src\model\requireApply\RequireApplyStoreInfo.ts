/*
 * @Author: 刘湘
 * @Date: 2021-12-23 13:35:14
 * @LastEditTime: 2024-04-18 17:49:04
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: \soa\src\model\requireApply\RequireApplyStoreInfo.ts
 * 记得注释
 */
import RequireApplyStoreOrderModeRule from './RequireApplyStoreOrderModeRule'
import StoreDpsAccountInfo from './StoreDpsAccountInfo'
import StoreInvTotalLmtConfig from './StoreInvTotalLmtConfig'

export default class RequireApplyStoreInfo {
  // 门店库存金限制配置
  invTotalLmtConfig: StoreInvTotalLmtConfig = new StoreInvTotalLmtConfig()
  // 资金账户信息
  dpsAccountInfo: Nullable<StoreDpsAccountInfo> = null
  // 配送天数
  alcDay: Nullable<number> = null
  // 允许的叫货模式:-1(def)=全部，0=正常叫货，1=紧急叫货，2=加单叫货，3=独立叫货，4=提前叫货
  alwOrderMode: Nullable<string> = null
  // 门店辅料管理:0-按需下单,1-自动下单,2-辅助下单,
  accessoryManage: Nullable<number> = null
  // 是否显示门余额
  showBalance: Nullable<boolean> = null
  // 要货数量下限限制级别: 0-不限制(def)，1-只提醒，2-强控
  gtyLowLimitlevel: Nullable<number> = null
  // 要货数量上限限制级别: -不限制(def)，1-只提醒，2-强控
  gtyHighLimitlevel: Nullable<number> = null
  // 叫货模式规则列表
  orderModeRules: RequireApplyStoreOrderModeRule[] = []
}
