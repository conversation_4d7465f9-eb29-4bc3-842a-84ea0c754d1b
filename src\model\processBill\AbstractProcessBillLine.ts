import Goods from '@/model/data/Goods'

export default class AbstractProcessBillLine {
  // 商品
  goods: Goods = new Goods()
  // 商品图片
  goodsImages: string[] = []
  // 面板分类大类Uuid
  categoryUuid: Nullable<string> = null
  // 面板分类大类代码
  categoryCode: Nullable<string> = null
  // 面板分类大类名称
  categoryName: Nullable<string> = null
  // 数量
  qty: number = 0
  // 规格数量
  qpcQty: number = 0
  // 金额
  total: number = 0
  // 备注
  note: Nullable<string> = null
}
