/*
 * @Author: 徐庆凯
 * @Date: 2022-06-02 10:44:20
 * @LastEditTime: 2025-05-16 16:54:19
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /soa/src/pagesSD/gdQuery/GdQueryPanelSearch.ts
 * 记得注释
 */
import { Vue, Component } from 'vue-property-decorator'
import QueryRequest from '@/model/base/QueryRequest'
import GoodsQueryInfo from '@/model/gdQuery/GoodsQueryInfo'
import AppGdQueryApi from '@/network/gdQuery/AppGdQueryApi'
import SkuCard from './cmp/SkuCard.vue'
import { mixins } from 'vue-class-component'
import UseQueues from '@/components/hd-popover/UseQueues'
import Slot from '@/model/data/Slot'
import GoodsSlotBinder from '@/model/data/GoodsSlotBinder'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import SelectExhibit from '@/components/select-exhibit/SelectExhibit.vue'
import { State } from 'vuex-class'
import ModuleOption from '@/model/default/ModuleOption'
import ResetExhibit from '@/components/select-exhibit/ResetExhibit.vue'
import OperateInvQtyDialog from './cmp/OperateInvQtyDialog.vue'
import BroadCast from '@/common/ScanMixin/broadCastMixin'

@Component({
  components: { SkuCard, ViewExhibit, ResetExhibit, SelectExhibit, OperateInvQtyDialog }
})
export default class GdQueryPanelSearch extends mixins(UseQueues, BroadCast) {
  @State('optionList') optionList: ModuleOption[] //店务配置列表
  pageSize: number = 6 // 每页大小
  pageNum: number = 0 // 页码
  finished: boolean = false // 是否加载完成
  isLoading: boolean = false // 是否在加载
  skuList: GoodsQueryInfo[] = [] // 商品列表
  inited: boolean = false // 是否初始化过
  keyWord: string = '' // 搜索关键词

  viewExhibitInfo: GoodsQueryInfo = new GoodsQueryInfo() // 商品信息
  hasSelelcExhibit: Slot = new Slot() // 已选中的陈列位置
  exhibitIndex: number = -1 // 商品下标
  $refs: any

  onLoad(option) {
    if (option.value) {
      this.keyWord = option.value
      this.doSearch()
    }
    uni.$off('refreshExhibit')
    uni.$on('refreshExhibit', () => {
      this.$refs.exhibit.refresh()
      this.$refs.resetExhibit.refresh()
    })
  }

  onUnload() {
    uni.$off('refreshExhibit')
  }

  //PDA扫码回调事件
  doScanAfter(scanWord) {
    this.keyWord = scanWord
    this.doSearch()
  }

  onReachBottom() {
    this.handleLoadmore()
  }

  // 搜索事件
  async doSearch() {
    this.$showLoading()
    this.pageNum = 0 // 页码
    this.finished = false // 是否加载完成
    this.isLoading = false // 是否在加载
    const query = new QueryRequest()
    query.page = this.pageNum
    query.pageSize = this.pageSize
    query.conditions = [{ operation: 'keyword:%=%', parameters: [this.keyWord] }]
    await this.doQueryGoods(query)
    this.$hideLoading()
  }

  handleShowDesc(desc: string) {
    this.$showModal({
      title: '查看说明',
      content: desc,
      showCancel: false,
      confirmText: '关闭',
      success: (action) => {}
    })
  }

  handleLoadmore() {
    if (this.finished || this.isLoading) {
      return
    }

    this.isLoading = true
    const queryRequest = new QueryRequest()
    queryRequest.page = this.pageNum
    queryRequest.pageSize = this.pageSize
    queryRequest.conditions = [{ operation: 'keyword:%=%', parameters: [this.keyWord] }]
    this.doQueryGoods(queryRequest)
  }

  /**
   * 查询商品列表
   * @param body
   * @returns
   */
  doQueryGoods(body: QueryRequest) {
    return new Promise<void>((resolve, reject) => {
      body.fetchParts = ['image', 'sort', 'tag']
      AppGdQueryApi.queryGoods(body)
        .then((resp) => {
          if (body.page === 0) {
            this.skuList = resp.data || []
          } else {
            this.skuList.push(...(resp.data || []))
          }
          this.pageNum++
          this.isLoading = false
          this.finished = !resp.more
          this.inited = true
          resolve()
        })
        .catch((error) => {
          this.inited = true
          this.$hideLoading()
          this.$showToast({ icon: 'error', title: error.msg })
          reject()
        })
    })
  }

  /**
   * 跳转商品详情
   * @param sku 商品
   */
  handleRedirctDetail(sku: GoodsQueryInfo) {
    this.$Router.push({
      name: 'gdQuerySkuDetail',
      params: {
        uuid: sku.uuid,
        inputCode: sku.inputCode,
        goodsType: sku.goodsType
      }
    })
  }

  //  取消按钮点击事件
  doCancel() {
    this.skuList = []
    uni.navigateBack({})
  }

  // 清空按钮事件
  doClear() {
    this.keyWord = ''
  }

  /**
   * 陈列位置调整
   */
  resetExhibit(info: GoodsQueryInfo, index: number) {
    this.hasSelelcExhibit = new Slot()
    this.exhibitIndex = index
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.inputCode
    this.$refs.resetExhibit.open(slotGoods)
  }

  /**
   * 输入框的值组装
   */
  inputExhibit(code) {
    this.hasSelelcExhibit.code = code
    this.hasSelelcExhibit.name = code
    this.hasSelelcExhibit.uuid = ''
  }

  /**
   * 陈列位置绑定成功
   */
  confirmExhibit() {
    this.doSearch()
    this.hasSelelcExhibit = new Slot()
  }

  /**
   * 添加陈列位置
   */
  addExhibit() {
    this.$Router.push({
      name: 'addExhibitLocation'
    })
  }

  /**
   * 删除陈列位置
   */
  deleteExhibit() {
    this.$Router.push({
      name: 'deleteExhibitLocation'
    })
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info: GoodsQueryInfo) {
    this.viewExhibitInfo = { ...info }
    this.$refs.viewExhibit.open()
  }

  /**
   * 打开库存调整弹窗
   */
  operateInvQty(sku: GoodsQueryInfo) {
    this.$refs.operateInvQty.open(sku)
  }
}
