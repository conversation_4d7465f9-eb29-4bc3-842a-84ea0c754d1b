<template>
  <view class="sale-list-contain">
    <view class="list-title" :class="{ 'title-sticky': isSticky }">
      <view class="name">品名/代码/条码/规格</view>
      <view class="qty">拒收数量/包装数</view>
      <view class="amount" v-if="showPrice">拒收金额(元)</view>
    </view>
    <view class="divide"></view>
    <view class="list" v-for="(line, index) in lines" :key="index" :class="{ 'list-none-border': !showMore && index == lines.length - 1 }">
      <view class="sku-apply">
        <view class="sku-left">
          <image lazy-load class="sku-img" :src="img(line)" @click.stop="handlePreviewImg(line)" />
          <view class="info__scale">
            <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
          </view>
        </view>
        <view>
          <view class="sku-name">
            {{ line.goods.name | empty }}
          </view>
          <view class="sku-code">
            <view class="info-tag sku-tag">{{ line.goods.code | empty }}</view>
            <view class="info-tag sku-tag">{{ (line.goods.code2 || line.goods.inputCode) | empty }}</view>
            <view class="info-tag sku-tag">{{ line.goods.qpcStr | empty }}</view>
          </view>
        </view>
      </view>
      <view class="sku-info" v-for="(reason, j) in line.reasons" :key="j">
        <view class="reason-title">退货原因：{{ reason.reasonName | empty }}</view>
        <block v-for="(reject, rejectIndex) in reason.rejects" :key="rejectIndex">
          <view class="reason-title">拒收原因：{{ reject.reasonName | empty }}</view>
          <view class="reason-header">
            <view class="code-name"></view>
            <view class="qty">
              <text class="num">{{ reject.qty | empty }}</text>
              {{ line.goods.minMunit | empty }}/
              <text class="num">{{ reject.qpcQty | qpcQty(line.goods.munit, line.goods.minMunit) }}</text>
            </view>
            <view class="amount total" v-if="showPrice">{{ reject.total | fmt | empty }}</view>
          </view>
        </block>
        <view class="upload" v-if="reason.attaches && reason.attaches.length > 0">
          <hd-up-img-video
            :disabled="true"
            size="small"
            :videos="
              reason.attaches
                .filter((img) => {
                  return isVideoUrl(img.fileUrl)
                })
                .map((img) => {
                  return img.fileUrl
                })
            "
            :images="
              reason.attaches
                .filter((img) => {
                  return isImageUrl(img.fileUrl)
                })
                .map((img) => {
                  return img.fileUrl
                })
            "
          ></hd-up-img-video>
        </view>
      </view>
    </view>
    <view class="view-more" v-if="showMore">
      <text class="click-btn" @click="viewMore">查看更多</text>
      <text class="arrow"></text>
    </view>
  </view>
</template>
<script lang="ts" src="./RejectList.ts"></script>
<style lang="scss">
.sale-list-contain {
  width: 750rpx;
  padding: 0 $base-padding;
  background: #ffffff;
  box-sizing: border-box;
  border-radius: 12rpx;
  position: relative;
  .list-title {
    position: relative;
    width: 100%;
    height: 76rpx;
    display: flex;
    box-sizing: border-box;
    align-items: center;
    justify-content: space-between;
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: $color-text-fourth;
    background: #ffffff;
    .name {
      flex: 2;
    }
    .qty {
      flex: 1.5;
      text-align: right;
    }
    .amount {
      flex: 1;
      text-align: right;
    }
  }
  .divide {
    left: 0;
    width: 750rpx;
    position: absolute;
    top: 76rpx;
    border-bottom: $list-border-bottom;
  }
  .title-sticky {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    left: 0;
  }

  .list {
    padding: $base-padding 0;
    border-bottom: $list-border-bottom;
    .sku-apply {
      display: flex;
    }
    .sku-left {
      position: relative;
      width: 120rpx;
      height: 120rpx;
      flex: 0 0 auto;
      margin-right: 16rpx;

      .sku-img {
        width: 120rpx;
        height: 120rpx;
      }
      .info__scale {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 24rpx;
        height: 24rpx;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 8rpx 0px 8rpx 0rpx;
        text-align: center;
        @include flex();
        &-img {
          width: 16rpx;
          height: 16rpx;
        }
      }
    }
    .sku-name {
      /*height: 44rpx;*/
      line-height: 44rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: $color-text-primary;
      font-family: PingFangSC-Medium, PingFang SC;
      overflow: hidden;
      text-overflow: ellipsis;
      /*white-space: nowrap;*/
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .sku-code {
      line-height: 40rpx;
      font-size: 30rpx;
      display: flex;
      flex-wrap: wrap;

      .code {
        color: rgba(148, 150, 154, 1);
        font-size: 26rpx;
        margin-right: 10rpx;
      }
      .price {
        font-size: 26rpx;
        color: rgba(88, 90, 94, 1);
      }
      .sku-tag {
        margin-top: 8rpx;
      }
    }
    .sku-info {
      line-height: 40rpx;
      display: flex;
      flex-direction: column;
      margin-top: 10rpx;
      justify-content: space-between;
      color: #585a5e;
      font-size: 26rpx;
      .reason-title {
        width: 100%;
        @include ellipsis();
      }
      .reason-header {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .upload {
        margin-top: 20rpx;
      }
      .code-name {
        flex: 2;
        max-width: 319rpx;
        @include ellipsis();
        .reasonName {
          color: #585a5e;
        }
      }
      .qty {
        box-sizing: border-box;
        padding-right: 24rpx;
        flex: 1.5;
        text-align: right;
      }
      .amount {
        flex: 1;
        text-align: right;
      }
      .num,
      .amount {
        font-size: 32rpx;
        font-weight: bold;
      }
      .total {
        color: #ff8800;
      }
    }
  }
  .list-none-border {
    border: none;
  }
  .view-more {
    width: 100%;
    height: 88rpx;
    font-size: 32rpx;
    font-weight: 400;
    color: rgba(88, 90, 94, 1);
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    .arrow {
      width: 14rpx;
      height: 14rpx;
      display: inline-block;
      border-top: 1px solid #94969a;
      border-right: 1px solid #94969a;
      transform: rotate(45deg);
      margin-left: 20rpx;
    }
  }
}
</style>
