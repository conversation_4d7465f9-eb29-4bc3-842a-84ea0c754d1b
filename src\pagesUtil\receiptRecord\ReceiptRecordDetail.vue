<template>
  <view class="receipt-record-detail">
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>

    <view class="header">
      <image :src="'/static/icon/ic_approval_2x.png' | oss" mode="scaleToFill" class="header-img" />
      <text class="header-title">提交成功</text>
      <text class="header-bill">{{ detail.num }}</text>
    </view>

    <view class="top-summary-expand">
      <image :src="'/static/img/pay_detail_2x.png' | oss" class="back-img" mode="aspectFill"></image>

      <view class="order-amount">
        <view class="label">总金额（元）</view>
        <view class="data">
          {{ detail.receiptTotal | fmt('#,##0.00') | empty }}
        </view>
      </view>

      <view class="content">
        <view class="order-num">
          <view class="label">商品类别（种）</view>
          <view class="data">
            {{ detail.recCnt | empty }}
          </view>
        </view>
        <view class="order-num">
          <view class="label">商品（件）</view>
          <view class="data">
            {{ detail.receiptQty | empty }}
          </view>
        </view>
      </view>
    </view>

    <view class="list" v-if="skuList.length || boxList.length">
      <view class="list-header">
        <text>品名/条码/代码/规格</text>
        <text>实收</text>
      </view>
      <template v-if="detail.receiptWay === 1">
        <record-box-detail-card
          v-for="box in boxList"
          :key="box.uuid"
          :box="box"
          @click="handleNavSplitBox"
          @viewExhibit="viewExhibit"
        ></record-box-detail-card>
      </template>
      <template v-else>
        <record-sku-detail-card v-for="sku in skuList" :key="sku.uuid" :sku="sku" @viewExhibit="viewSkuExhibit"></record-sku-detail-card>
      </template>
    </view>

    <view class="bottom-info" v-if="detail.srcBillSource === 'express'">
      <hd-cell title="单号" :value="detail.num || '--'" :isLink="false" :hasLine="false"></hd-cell>
      <hd-cell title="快递单号" :value="detail.srcBillSourceId || '--'" :isLink="false" :hasLine="false"></hd-cell>
      <hd-cell title="收货人" :value="detail.submitterName || '--'" :isLink="false" :hasLine="false"></hd-cell>
      <hd-cell title="收货完成时间" :value="detail.submitTime" :isLink="false" :hasLine="false"></hd-cell>
    </view>

    <view class="operate-area safe-area-inset-bottom" v-if="uploadRecordAttach && detail.datasource === 'direct'">
      <hd-button type="primary" @click="viewFile">查看附件</hd-button>
    </view>

    <!-- 查看多个陈列位置 -->
    <uni-popup ref="viewExhibit" type="bottom">
      <view-exhibit :name="exhibitGoodsName" :displayLocation="exhibitLocation" @doClose="closeViewExhibit"></view-exhibit>
    </uni-popup>

    <!-- 查看附件 -->
    <uni-popup type="bottom" ref="feedback" :maskClick="false">
      <adjust-feedback :images="images" :billInfo="detail" :readonly="true" @doClose="doCloseFeedback"></adjust-feedback>
    </uni-popup>
  </view>
</template>
<script lang="ts" src="./ReceiptRecordDetail.ts"></script>
<style lang="scss">
.receipt-record-detail {
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  background: $list-bg-color-lx;
  box-sizing: border-box;
  position: relative;
  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom)) !important;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)) !important;
  .header {
    width: 100%;
    padding-top: 50rpx;
    margin-bottom: 24rpx;
    @include flex(column, center, center);
    &-img {
      width: 120rpx;
      height: 120rpx;
    }

    &-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 36rpx;
      color: #333333;
      margin-top: 20rpx;
    }
    &-bill {
      font-size: 28rpx;
      color: #666666;
      margin-top: 8rpx;
    }
  }

  .top-summary-expand {
    margin: 0rpx 24rpx 24rpx 24rpx;
    width: 702rpx;
    box-sizing: border-box;
    background: #ffffff;
    position: relative;
    height: 292rpx;
    box-sizing: border-box;
    border-radius: 16rpx 16rpx 0 0;
    overflow: hidden;
    .back-img {
      width: 702rpx;
      height: 292rpx;
    }
    .order-amount {
      position: absolute;
      top: 28rpx;
      padding: 0rpx 36rpx;
      .label {
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 40rpx;
      }
      .data {
        font-size: 48rpx;
        font-family: DINAlternate-Bold, DINAlternate;
        font-weight: bold;
        color: #ffffff;
        line-height: 56rpx;
      }
    }
    .content {
      width: 702rpx;
      box-sizing: border-box;
      position: absolute;
      bottom: 0;
      @include flex(row, flex-start, center);
      padding: 24rpx 36rpx;
      .order-num {
        flex: 1 1 auto;
        text-align: left;
        .label {
          font-size: 24rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: $color-text-lx-placeholder;
          line-height: 40rpx;
        }
        .data {
          font-size: 48rpx;
          font-family: DINAlternate-Bold, DINAlternate;
          font-weight: bold;
          color: $color-text-primary;
          line-height: 56rpx;
        }
      }
    }
  }

  .list {
    position: relative;
    margin: 0 auto;
    width: 702rpx;
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;

    &-header {
      position: relative;
      padding: 0 24rpx;
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 24rpx;
      color: #999999;
      &::after {
        position: absolute;
        content: ' ';
        width: 100%;
        width: 702rpx;
        height: 1px;
        background: #eeeeee;
        left: 0;
        bottom: 0;
        transform: scaleY(0.5);
      }
    }
  }

  .bottom-info {
    background-color: #ffffff;
    border-radius: 12rpx;
    overflow: hidden;
    margin: 16rpx 24rpx 0 24rpx;
    box-sizing: border-box;
    ::v-deep .cell-title {
      max-width: 180rpx;
    }
    ::v-deep .cell-value {
      text-align: right;
      max-width: 530rpx !important;
    }

    ::v-deep .cell-title-txt {
      color: $font-color-darklight;
      font-size: 28rpx;
    }
    ::v-deep .cell-value-txt {
      color: $color-text-primary;
      font-size: 28rpx;
    }
    ::v-deep .cell--clickable {
      .cell-value-txt {
        color: $color-primary !important;
      }
    }
  }
  .operate-area {
    width: 100%;
    position: fixed;
    background-color: #fff;
    bottom: 0;
    left: 0;
    z-index: 2;
  }
}
</style>
