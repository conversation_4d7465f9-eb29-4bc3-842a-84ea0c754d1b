<template>
  <view class="content dir-alc-bck-edit" :style="{ 'padding-bottom': showBtn ? '188rpx' : '0' }">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <hd-video-preview id="hd-video-preview"></hd-video-preview>
    <!-- #ifdef MP-WEIXIN -->
    <hd-privacy-popup id="privacy-popup"></hd-privacy-popup>
    <!-- #endif -->

    <view class="dir-alc-bck-header">
      <!-- 头部状态 -->
      <view class="top-header">
        <view class="top-left">
          <view class="title" @click="doShowProcess">
            {{ state }}
            <image class="title-arrow" :src="'/static/icon_Flat/ic_noticebar_right_white.png' | oss" mode="aspectFill"></image>
          </view>
          <view class="bill-no">{{ data.num | empty }}</view>
        </view>
      </view>
      <hd-cell title="收货方" :ellipsis="false" placeholder="--" :value="data.receiver.name" :isLink="false" direction="right"></hd-cell>
      <hd-cell title="退货原因" :ellipsis="false" placeholder="--" :value="data.reasonName" :isLink="false" direction="right"></hd-cell>
      <hd-cell title="备注" :ellipsis="false" placeholder="--" :value="data.note" :isLink="false" direction="right"></hd-cell>
    </view>
    <view class="good-list">
      <view class="operator-box">
        <view class="title">
          品名/代码/条码/规格/
          <text v-if="showPrice">价格/</text>
          退货原因
        </view>
        <view>退货数量</view>
      </view>

      <view v-if="data.lines.length > 0" class="dir-alc-bck-body">
        <block v-for="(item, i) in data.lines" :key="item.goods.uuid">
          <view class="dir-alc-bck-edit-card">
            <view class="dir-alc-edit-info">
              <view class="dir-alc-apply-left">
                <image lazy-load class="dir-alc-apply-img" :src="img(item)" @click.stop="handlePreviewImg(item)" />
                <view class="info__scale">
                  <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
                </view>
              </view>
              <view>
                <view class="dir-alc-bck-title">{{ item.goods.name | empty }}</view>
                <view class="dir-alc-bck-code">
                  <view class="info-tag">{{ item.goods.code | empty }}</view>
                  <view class="info-tag">{{ (item.goods.code2 || item.goods.inputCode) | empty }}</view>
                  <view class="info-tag">{{ item.goods.qpcStr | empty }}</view>
                  <view class="info-tag" v-if="showPrice">￥{{ item.goods.singlePrice | fmt }}/{{ item.goods.minMunit | empty }}</view>
                </view>
              </view>
            </view>
            <view class="dir-alc-bck-code" v-if="showMaster.showDisplayLocation">
              <view class="main-exhibit">
                <view :class="[hasMutiple(item.displayLocation) ? 'goods-one' : '']">
                  陈列位置：
                  <text class="goods-text">{{ item.displayLocation | empty }}</text>
                </view>
                <image
                  class="good-img"
                  :src="'/static/icon/ic_right_grey.png' | oss"
                  v-if="hasMutiple(item.displayLocation)"
                  @click="viewExhibit(item)"
                />
              </view>
            </view>
            <view class="dir-alc-bck-reason" v-for="(reason, j) in item.reasons" :key="reason.uuid">
              <view class="reason-header">
                <view class="flex">
                  <view
                    class="dir-alc-bck-reason-txt"
                    :class="{ 'dir-alc-bck-reason-txt--none': !reason.reasonName }"
                    @click="doSelectDetailReason(JSON.stringify(reason), i, j)"
                  >
                    {{ reason.reasonName || '请选择退货原因' }}
                  </view>
                </view>
                <view class="dir-alc-bck-reason-number">
                  <view class="limit-qty dir-alc-bck-reason-txt">
                    审批 ×
                    <text class="limit-qty-number">{{ reason.approvalQty | empty }}</text>
                  </view>
                  <view class="qty-label" v-if="item.useDoubleMeasure">件数（{{ item.goods.munit }}）</view>
                  <block v-if="item.useDoubleMeasure">
                    <hd-number-box
                      class="stk-out-bck-reason-input"
                      :value="reason.rtnQpcQty"
                      :key="reason.uuid"
                      v-if="reason.approvalQpcQty === null || reason.approvalQpcQty > 0 || allowRtnQtyMoreThanApprove"
                      :max="reason.approvalQpcQty !== null && !allowRtnQtyMoreThanApprove ? reason.approvalQpcQty : 99999999"
                      :scale="0"
                      @change="doNumberChange($event, i, j, true)"
                    ></hd-number-box>
                  </block>
                  <block v-else>
                    <hd-number-box
                      class="dir-alc-bck-reason-input"
                      :value="reason.rtnQty"
                      :key="reason.uuid"
                      v-if="reason.approvalQty === null || reason.approvalQty > 0 || allowRtnQtyMoreThanApprove"
                      :max="reason.approvalQty !== null && !allowRtnQtyMoreThanApprove ? reason.approvalQty : 99999999"
                      :scale="4"
                      @change="doNumberChange($event, i, j, false)"
                    ></hd-number-box>
                  </block>
                </view>
              </view>
              <view class="reason-header justify-content-end" v-if="item.useDoubleMeasure">
                <view class="qty-label">
                  {{ !item.isDisp ? `单品（${item.goods.minMunit}）` : '重量（kg）' }}
                </view>
                <hd-number-box
                  class="stk-out-bck-reason-input"
                  :value="reason.rtnQty"
                  :key="reason.uuid"
                  v-if="reason.approvalQty === null || reason.approvalQty > 0 || allowRtnQtyMoreThanApprove"
                  :max="reason.approvalQty !== null && !allowRtnQtyMoreThanApprove ? reason.approvalQty : 99999999"
                  :scale="4"
                  @change="doNumberChange($event, i, j, false)"
                ></hd-number-box>
              </view>
              <view class="upload" v-if="reason.attaches && reason.attaches.length > 0">
                <hd-up-img-video
                  :disabled="true"
                  size="small"
                  :videos="
                    reason.attaches
                      .filter((img) => {
                        return isVideoUrl(img.fileUrl)
                      })
                      .map((img) => {
                        return img.fileUrl
                      })
                  "
                  :images="
                    reason.attaches
                      .filter((img) => {
                        return isImageUrl(img.fileUrl)
                      })
                      .map((img) => {
                        return img.fileUrl
                      })
                  "
                ></hd-up-img-video>
              </view>
            </view>
            <!-- <view class="dir-alc-bck-invQty">当前库存：{{ item.invQty | empty }}{{ item.goods.minMunit | empty }}</view> -->
          </view>
        </block>
      </view>
      <view v-else class="dir-alc-bck-empty">
        <image class="dir-alc-bck-empty-icon" :src="'/static/img/img_empty_goods.png' | oss"></image>
        <text class="dir-alc-bck-empty-txt">暂无退货商品</text>
      </view>
    </view>
    <view class="bottom-info">
      <hd-cell
        v-if="data.applyNum"
        title="申请单号"
        :value="data.applyNum"
        :isLink="returnApplyPermission"
        :hasLine="false"
        @onClick="returnApplyPermission && doNavApply()"
      ></hd-cell>
      <hd-cell title="申请人" :value="data.creatorName | empty" :isLink="false" :hasLine="false"></hd-cell>
      <hd-cell title="申请提交时间" :value="data.created | date('yyyy-MM-dd HH:mm:ss') | empty" :isLink="false" :hasLine="false"></hd-cell>
    </view>
    <view style="position: fixed; bottom: 0; width: 100%" v-if="showBtn">
      <view class="dir-alc-bck-total" v-if="showPrice">
        <text class="count">
          共
          <text class="count-num">{{ count | empty }}</text>
          种
        </text>
        <text class="total">
          合计：
          <text class="total-flag">￥</text>
          <text class="total-num">{{ total | fmt('#,##0.00') }}</text>
        </text>
      </view>
      <view class="dir-alc-bck-footer">
        <hd-button :type="btnPermission.submit ? 'white' : 'primary'" v-if="btnPermission.save" @click="doSave">保存</hd-button>
        <hd-button type="primary" v-if="btnPermission.submit" @click="doSubmit">确认退货</hd-button>
        <!-- <hd-button type="white" @click="doSave">保存</hd-button>
        <hd-button type="primary" @click="doSubmit">确认退货</hd-button> -->
      </view>
    </view>

    <uni-popup type="bottom" ref="flowDialog">
      <flow-dialog title="退货流程"></flow-dialog>
    </uni-popup>
    <!-- 查看多个陈列位置 -->
    <uni-popup ref="viewExhibit" type="bottom">
      <view-exhibit :name="viewExhibitInfo.goods.name" :displayLocation="viewExhibitInfo.displayLocation" @doClose="closeViewExhibit"></view-exhibit>
    </uni-popup>
  </view>
</template>

<script lang="ts" src="./DirAlcBckEdit.ts"></script>

<style lang="scss" scoped>
.dir-alc-bck-edit {
  position: relative;
  display: flex;
  flex-direction: column;
  height: auto;
  padding-bottom: 188rpx;
  background: $list-bg-color-lx;
  .dir-alc-bck-header {
    width: 750rpx;
    box-sizing: border-box;
    overflow-x: hidden;
    margin-bottom: 16rpx;
    .top-header {
      width: 750rpx;
      height: 148rpx;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      justify-content: space-between;
      padding: 20rpx 22rpx 32rpx;
      background: $color-primary;
      .top-left {
        .title {
          display: flex;
          align-items: center;
          font-size: 44rpx;
          height: 48rpx;
          line-height: 48rpx;
          font-weight: 500;
          color: $uni-bg-color;
          .title-arrow {
            width: 32rpx;
            height: 32rpx;
          }
        }
        .bill-no {
          margin-top: 12rpx;
          font-size: 30rpx;
          font-weight: 500;
          color: $uni-bg-color;
          opacity: 0.8;
        }
      }
    }
  }
  .good-list {
    border-radius: 12rpx;
    overflow: hidden;
  }
  .operator-box {
    padding: 20rpx 78rpx 20rpx 24rpx;

    height: 72rpx;
    background: #ffffff;
    border-radius: 16rpx 16rpx 0 0;
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: $color-text-fourth;
    box-sizing: border-box;
    border-bottom: 1rpx solid rgba(227, 228, 232, 1);
    @include flex(row, space-between, center);
  }
  .dir-alc-bck-body {
    background: #ffffff;
    .dir-alc-bck-edit-card {
      position: relative;
      display: flex;
      flex-direction: column;
      min-height: 228rpx;
      width: 100%;
      box-sizing: border-box;
      padding: 24rpx 0;
      background: rgba(255, 255, 255, 1);
      .dir-alc-edit-info {
        display: flex;
        padding: 0 24rpx;
        box-sizing: border-box;
      }
      .dir-alc-apply-left {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        flex: 0 0 auto;

        .dir-alc-apply-img {
          width: 120rpx;
          height: 120rpx;
        }
        .info__scale {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 24rpx;
          height: 24rpx;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 8rpx 0px 8rpx 0rpx;
          text-align: center;
          @include flex();
          &-img {
            width: 16rpx;
            height: 16rpx;
          }
        }
      }
      .dir-alc-bck-title {
        width: 100%;
        box-sizing: border-box;
        padding: 0 24rpx;
        font-size: 30rpx;
        font-family: $font-medium;
        font-weight: 500;
        color: rgba(40, 44, 52, 1);
        line-height: 40rpx;
      }
      .dir-alc-bck-code {
        padding: 0 24rpx;
        max-width: 100%;
        display: flex;
        flex-wrap: wrap;
        margin-top: 12rpx;
      }
      .main-exhibit {
        width: 702rpx;
        background: #f5f5f5;
        border-radius: 8rpx;
        padding: 8rpx 16rpx;
        box-sizing: border-box;
        flex: 0 0 auto;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        display: flex;
        align-items: center;

        .good-img {
          width: 32rpx;
          height: 32rpx;
          display: inline-table;
        }

        .goods-text {
          color: #333333;
          font-size: 26rpx;
          font-weight: 500;
        }

        .goods-one {
          flex: 1;
          @include ellipsis();
        }
      }
      .dir-alc-bck-qpc {
        position: relative;
        width: 100%;
        box-sizing: border-box;
        padding: 0 18rpx;
        margin-top: 14rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .qpc-price {
          font-size: 32rpx;
          font-weight: 600;
          color: rgba(255, 136, 0, 1);
          line-height: 40rpx;
        }
        .qpc-munit {
          font-size: 24rpx;
          font-weight: 400;
          margin-left: 12rpx;
          color: rgba(88, 90, 94, 1);
          line-height: 40rpx;
        }
        .qpc-detail {
          margin-left: 20rpx;
          font-size: 24rpx;
          color: rgba(148, 150, 154, 1);
          line-height: 40rpx;
        }
      }
      .dir-alc-bck-reason {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 100%;
        box-sizing: border-box;
        padding: 0 24rpx;
        margin-top: 20rpx;
        .reason-header {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .justify-content-end {
          margin-top: 10rpx;
          justify-content: flex-end;
        }
        .upload {
          margin-top: 20rpx;
        }
        .qty-label {
          height: 48rpx;
          line-height: 48rpx;
          min-width: 120rpx;
          font-size: 30rpx;
          padding: 0 12rpx;
          font-weight: 400;
          color: rgba(148, 150, 154, 1);
        }
        .dir-alc-bck-reason-number {
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .dir-alc-bck-reason-operator {
            height: 48rpx;
            width: 48rpx;
            margin-left: 24rpx;
          }
          .limit-qty {
            text-align: right;
            padding: 0 12rpx;
            height: 48rpx;
            min-width: 180rpx;
            font-size: 24rpx;
            font-weight: 400;
            color: rgba(148, 150, 154, 1);
          }
          .limit-qty-number {
            font-size: 32rpx;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: rgba(88, 90, 94, 1);
            overflow-y: hidden;
          }
        }
        .dir-alc-bck-reason-txt {
          height: 48rpx;
          max-width: 130rpx;
          line-height: 48rpx;
          font-size: 24rpx;
          color: rgb(127, 132, 143);
          @include ellipsis();
        }
        .dir-alc-bck-reason-txt--none {
          color: #cccccc;
        }
        .dir-alc-bck-reason-arrow {
          height: 32rpx;
          width: 32rpx;
        }
      }
      .dir-alc-bck-invQty {
        padding: 0 24rpx;
        margin-top: 8rpx;
        height: 32rpx;
        line-height: 32rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
      }
    }
    .dir-alc-bck-edit-card:after {
      position: absolute;
      box-sizing: border-box;
      content: ' ';
      pointer-events: none;
      bottom: 0;
      left: 19rpx;
      width: calc(100% - 38rpx);
      border-bottom: 2rpx solid rgba(227, 228, 232, 1);
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
    }
    .loading {
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: rgb(148, 150, 154);
    }
  }
  .dir-alc-bck-empty {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    height: 500rpx;

    .dir-alc-bck-empty-icon {
      width: 300rpx;
      height: 300rpx;
    }
    .dir-alc-bck-empty-txt {
      margin-top: 10rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(148, 150, 154, 1);
      line-height: 40rpx;
    }
  }
  .dir-alc-bck-total {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 88rpx;
    box-sizing: border-box;
    padding: 0 24rpx;
    background-color: #fff7e0;
    font-size: 24rpx;
    color: #94969a;
    text {
      display: inline-flex;
      align-items: center;
      height: 88rpx;
    }
    .count {
      color: #94969a;
      font-size: 24rpx;
      margin-right: 16rpx;
    }
    .count-num {
      color: #585a5e;
      margin: 0 8rpx;
      font-size: 24rpx;
      font-family: HelveticaNeue-Bold, HelveticaNeue;
      font-weight: bold;
    }
    .total-flag {
      color: #585a5e;
      font-size: 24rpx;
      font-family: HelveticaNeue-Bold, HelveticaNeue;
      font-weight: bold;
    }
    .total-num {
      font-size: 32rpx;
      font-family: HelveticaNeue-Bold, HelveticaNeue;
      font-weight: bold;
      color: #585a5e;
    }
  }
  .dir-alc-bck-footer {
    display: flex;
  }
  .bottom-info {
    overflow: hidden;
    border-radius: 12rpx;
    background-color: #ffffff;
    margin: 16rpx 0;
    box-sizing: border-box;
    ::v-deep .cell-title {
      max-width: 180rpx;
    }
    ::v-deep .cell-value {
      text-align: right;
      max-width: 530rpx !important;
    }

    ::v-deep .cell-title-txt {
      color: $font-color-darklight;
      font-size: 28rpx;
    }
    ::v-deep .cell-value-txt {
      color: $color-text-primary;
      font-size: 28rpx;
    }
  }
}
</style>
