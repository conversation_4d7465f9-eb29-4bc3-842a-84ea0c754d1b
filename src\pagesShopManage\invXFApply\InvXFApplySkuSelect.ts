import { Component, Vue } from 'vue-property-decorator'
import { State, Mutation, Getter } from 'vuex-class'
import CollapseBar from '@/pages/cmp/CollapseBar.vue'
import SideBar from '@/pages/cmp/SideBar.vue'
import HdNumberBoxTest from '@/components/hd-number-box-test/hd-number-box-test.vue'
import HdButton from '@/components/hd-button/hd-button.vue'
import DataApi from '@/network/data/DataApi'
import GoodsCategory from '@/model/data/GoodsCategory'
import QueryRequest from 'model/base/QueryRequest'
import CommonUtil from '@/utils/CommonUtil'
import Category from '@/model/data/Category'
import InvXFApplyApi from 'network/invXFApply/InvXFApplyApi'
import InvXFApplyGoods from 'model/invXFApply/InvXFApplyGoods'
import InvXFApplyLine from 'model/invXFApply/InvXFApplyLine'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import { mixins } from 'vue-class-component'
import ModuleFieldRights from '@/model/default/ModuleFieldRights'
import CategoryQueryRequest from '@/model/default/CategoryQueryRequest'
import { InvXFApplyType } from 'model/invXFApply/InvXFApplyType'
import store from '@/store'
import config from '@/config'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'

@Component({
  components: { CollapseBar, SideBar, HdButton, HdNumberBoxTest }
})
export default class InvXFApplySkuSelect extends mixins(BroadCast) {
  @State('optionList') optionList: ModuleOption[] //店务配置列表
  @Mutation('goodsList') mutationGoodsList // 提交到vuex
  @State('goodsList') goodsList: any[]
  @State('moduleFieldRightsList') moduleFieldRightsList: ModuleFieldRights[] // 获取模块字段权限
  @Getter('hideEmptyCategory') hideEmptyCategory: boolean // 是否隐藏无商品的分类
  counterShopId: string = '' // 需要操作的门店id
  type: string = '' // 操作类型，调入(increasev), 调出(decrease)
  dataList: InvXFApplyGoods[] = [] // 商品列表
  goodsUncategorizedList: Nullable<InvXFApplyGoods[]> = null // 未分类的商品列表
  selectedGoodsList: InvXFApplyGoods[] = [] // 已选商品列表
  goodsCategoryList: Nullable<GoodsCategory[]> = null // 商品分类列表
  selectedCategory: GoodsCategory = new GoodsCategory() // 已选的商品分类
  selectedSubCategory: GoodsCategory = new GoodsCategory() // 已选的二级分类
  categoryGoodsCount: number[] = [] // 购物车中每种大类所选商品个数，用于左侧导航栏显示
  selectedIndex: number = 0 // 选中的一级分类的下标
  // 上拉加载相关
  isLoading: boolean = false // 是否正在加载
  finished: boolean = false // 数据是否加载完成
  page: number = 0 // 数据页码
  pageSize: number = 20 // 每页大小

  // 按钮是否可点击
  get canConfirm() {
    return this.selectedGoodsList && this.selectedGoodsList.length
  }

  /**
   * 限制门店经营商品方案：false-不限制(def),true-限制
   */
  get limitStoreGdSaleScheme() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosInvXFApply
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.limitStoreGdSaleScheme) {
      return moduleConfig[0].options.limitStoreGdSaleScheme === 'true' ? true : false
    }
    return false
  }

  // 商品图片
  get skuImg() {
    return (sku: InvXFApplyGoods) => {
      return sku && sku.images && sku.images.length && CommonUtil.isImageUrl(sku.images[0])
        ? `${sku.images[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
        : `${config.sourceUrl}icon/pic_goods.png`
    }
  }

  get imageList() {
    return (sku: InvXFApplyGoods) => {
      return sku && sku.images && sku.images.filter((item) => CommonUtil.isImageUrl(item)).length
        ? sku.images.filter((item) => CommonUtil.isImageUrl(item))
        : [`${config.sourceUrl}icon/pic_goods.png`]
    }
  }

  onLoad(option) {
    this.counterShopId = option.shopId
    this.type = option.type
    // 从storage中获得已选中商品
    if (this.goodsList.length) {
      this.selectedGoodsList = this.doExchangeModel(this.goodsList)
      this.mutationGoodsList([])
    } else {
      if (uni.getStorageSync('invXFApplyGoodsList')) {
        const before: InvXFApplyLine[] = uni.getStorageSync('invXFApplyGoodsList')
        this.selectedGoodsList = this.doExchangeModel(before)
        // 拿到未分类商品列表
        this.goodsUncategorizedList = CommonUtil.copy(
          this.selectedGoodsList!.filter((selected) => !selected.firstCategory || !selected.firstCategory.uuid)
        )
        uni.removeStorage({ key: 'invXFApplyGoodsList' })
      }
    }
    this.doQueryCategory().then(() => {
      this.getCountCategoryGoods()
      const queryRequest = new QueryRequest()
      // 默认二级分类
      if (this.selectedCategory.children.length > 0) {
        this.selectedSubCategory = this.selectedCategory.children[0]
      }
      if (this.selectedCategory.code) {
        queryRequest.conditions = [{ operation: 'categoryCode:=', parameters: [this.selectedCategory.code] }]
      }
      if (this.selectedSubCategory.code) {
        queryRequest.conditions = [{ operation: 'categoryCode:=', parameters: [this.selectedSubCategory.code] }]
      }
      queryRequest.conditions.push({ operation: 'counterShopId:=', parameters: [this.counterShopId] })
      queryRequest.conditions.push({ operation: 'type:=', parameters: [this.type] })
      this.doQueryGood(queryRequest)
      // 如果已选中的商品里存在未分类的商品，则一级分类添加未分类分组
      const found = this.selectedGoodsList!.find((selected) => !selected.firstCategory || !selected.firstCategory.uuid)
      if (found) {
        const category = new GoodsCategory()
        category.uuid = CommonUtil.uuid()
        category.code = null
        category.name = '未分类'
        this.goodsCategoryList && this.goodsCategoryList.push(category)
      }
    })
  }
  /**
   * 是否展示调拨价 - 默认展示
   */
  get showPrice() {
    const moduleConfig = (this.moduleFieldRightsList || []).find((option: ModuleFieldRights) => option.moduleId == 'sosInvXFApply')
    if (moduleConfig && moduleConfig.fieldRights && moduleConfig.fieldRights.price) {
      return moduleConfig.fieldRights.price === '1'
    }
    return true
  }

  /**
   * 添加商品
   */
  doSelectedGoods(index: number) {
    if (!this.dataList) return
    const matchItem: InvXFApplyGoods = this.dataList[index]
    this.dataList[index].checked = !this.dataList[index].checked

    const i = this.selectedGoodsList.findIndex((item) => item.uuid === matchItem.uuid)

    if (i === -1) {
      this.selectedGoodsList.unshift(matchItem)
    } else {
      this.selectedGoodsList.splice(i, 1)
    }
  }

  // 查询商品分类
  doQueryCategory() {
    this.$showLoading({ delayTime: 200 })
    return new Promise((resolve, reject) => {
      if (this.hideEmptyCategory) {
        const params = new CategoryQueryRequest()
        params.moduleId = 'sosInvXFApply'
        // 是否是调出
        const isDecrease = this.type === InvXFApplyType.decrease
        params.goodsQueryRequest.conditions = [
          {
            operation: 'fromStore:=',
            parameters: [isDecrease ? store.state.store?.id! : this.counterShopId]
          },
          {
            operation: 'toStore:=',
            parameters: [isDecrease ? this.counterShopId : store.state.store?.id!]
          }
        ]
        DataApi.queryCategoryExistsGoods(params)
          .then((resp) => {
            this.goodsCategoryList = resp.data
            if (this.goodsCategoryList && this.goodsCategoryList.length > 0) {
              this.selectedCategory = this.goodsCategoryList[0]
              resolve(this.selectedCategory)
            } else {
              this.$hideLoading()
              reject()
            }
          })
          .catch((e) => {
            this.$hideLoading()
            this.$showToast({ title: e.msg, icon: 'none' })
            reject()
          })
      } else {
        DataApi.sortList()
          .then((resp) => {
            this.goodsCategoryList = resp.data
            if (this.goodsCategoryList && this.goodsCategoryList.length > 0) {
              this.selectedCategory = this.goodsCategoryList[0]
              resolve({})
            } else {
              this.$hideLoading()
              reject()
            }
          })
          .catch((e) => {
            this.$hideLoading()
            this.$showToast({ title: e.msg, icon: 'error' })
            reject()
          })
      }
    })
  }

  /**
   * 查询商品列表
   * @param queryRequest QueryRequest 查询条件
   * @param type 查询类型：1.query:按照商品分类码查询 2.search:根据关键词查询
   */
  doQueryGood(queryRequest: QueryRequest, showLoading: boolean = true) {
    if (showLoading) {
      this.$showLoading({ delayTime: 200 })
    }
    if (this.selectedCategory.name === '未分类') {
      const data = this.goodsUncategorizedList || []
      for (let i = 0; i < data.length; i++) {
        for (let j = 0; j < this.selectedGoodsList.length; j++) {
          data[i].checked = true
        }
      }
      this.dataList = data || []
      if (showLoading) {
        this.$hideLoading()
      }
      this.finished = true
      return
    }
    queryRequest.page = this.page
    queryRequest.pageSize = this.pageSize
    queryRequest.fetchParts = ['category', 'reference', 'image']
    if (this.limitStoreGdSaleScheme) {
      // 是否是调出
      const isDecrease = this.type === InvXFApplyType.decrease
      queryRequest.conditions.push({ operation: 'toStore:=', parameters: [isDecrease ? store.state.store?.id! : this.counterShopId] })
      queryRequest.conditions.push({ operation: 'fromStore:=', parameters: [isDecrease ? this.counterShopId : store.state.store?.id!] })
    }
    InvXFApplyApi.queryGoods(queryRequest)
      .then((resp) => {
        if (showLoading) {
          this.$hideLoading()
        }
        this.page++
        this.isLoading = false
        if (!resp.more) {
          this.finished = true
        }
        if (resp.data && resp.data.length) {
          const data = resp.data
          for (let i = 0; i < data.length; i++) {
            // 初始化数据
            data[i].qty = 0
            data[i].qpcQty = 0
            data[i].total = 0
            data[i].rtlTotal = 0
            if (data[i].lines) {
              data[i].batches = [...data[i].lines]
              delete data[i].lines
            }
            for (let j = 0; j < this.selectedGoodsList.length; j++) {
              if (data[i].inputCode === this.selectedGoodsList[j].inputCode && data[i].uuid === this.selectedGoodsList[j].uuid) {
                data[i].qty = this.selectedGoodsList[j].qty
                data[i].qpcQty = this.selectedGoodsList[j].qpcQty
                data[i].total = this.selectedGoodsList[j].total
                data[i].rtlTotal = this.selectedGoodsList[j].rtlTotal
                data[i].checked = true
              }
            }
          }
          this.dataList.push(...data)
        }
      })
      .catch((e) => {
        if (showLoading) {
          this.$hideLoading()
        }
        this.$showToast({ title: e.msg, icon: 'error' })
      })
  }

  /**
   * 统计每个大类有多少个商品(用于左侧导航栏)
   */
  getCountCategoryGoods() {
    if (this.goodsCategoryList && this.goodsCategoryList.length > 0) {
      // 根据有无未分类，处理arr的长度
      let arr: number[] = []
      const found = this.goodsCategoryList.find((category) => !category.code)
      if (found) {
        // 分类列表存在未分类
        arr = new Array(this.goodsCategoryList.length).fill(0)
      } else {
        // 分类列表不存在未分类
        arr = new Array(this.goodsCategoryList.length + 1).fill(0)
      }
      const lines = this.selectedGoodsList || []
      for (let i = 0; i < this.goodsCategoryList.length; i++) {
        for (let j = 0; j < lines.length; j++) {
          // 获得当前分类中存在多少个已选中的商品
          if (lines[j].firstCategory && lines[j].firstCategory!.uuid === this.goodsCategoryList[i].uuid) {
            arr[i] = arr[i] + 1
          }
        }
      }
      // 获得未分类的已选中的商品数
      if (this.selectedGoodsList && this.selectedGoodsList.length) {
        this.selectedGoodsList.forEach((selected) => {
          if (!selected.firstCategory || !selected.firstCategory.uuid) {
            arr[arr.length - 1] = arr[arr.length - 1] + 1
          }
        })
      }
      this.categoryGoodsCount = arr
    }
  }

  // 上拉加载更多
  doLoadMore() {
    if (this.finished || this.isLoading) {
      return
    }
    this.isLoading = true
    const queryRequest = new QueryRequest()
    if (this.selectedCategory.code) {
      queryRequest.conditions = [
        {
          operation: 'categoryCode:=',
          parameters: [this.selectedCategory.code]
        }
      ]
    }
    if (this.selectedSubCategory.code) {
      queryRequest.conditions = [
        {
          operation: 'categoryCode:=',
          parameters: [this.selectedSubCategory.code]
        }
      ]
    }
    queryRequest.conditions.push({ operation: 'counterShopId:=', parameters: [this.counterShopId] })
    queryRequest.conditions.push({ operation: 'type:=', parameters: [this.type] })
    this.doQueryGood(queryRequest, false)
  }

  /**
   * 搜索商品
   */
  doSearch() {
    const type = this.type
    const shopId = this.counterShopId
    uni.setStorage({
      key: 'invXFApplyGoodsList',
      data: this.doReturnModel(this.selectedGoodsList),
      success: function () {
        uni.navigateTo({
          url: `/pagesShopManage/invXFApply/InvXFApplySkuSearch?from=select&type=${type}&shopId=${shopId}`
        })
      }
    })
  }

  /**
   * 扫描二维码
   */
  doScan() {
    uni.scanCode({
      success: (res) => {
        const scanWord = res.result || ''
        this.doScanAfter(scanWord)
      }
    })
  }

  //PDA扫码回调事件
  doScanAfter(scanWord) {
    uni.setStorage({
      key: 'invXFApplyGoodsList',
      data: this.doReturnModel(this.selectedGoodsList),
      success: () => {
        this.$Router.push({
          name: 'invXFApplySkuSearch',
          params: {
            from: 'select',
            value: scanWord,
            type: this.type,
            shopId: this.counterShopId
          }
        })
      }
    })
  }

  /**
   * 一级分类点击事件
   */
  doFirstCategoryChange(index) {
    this.selectedIndex = index
    this.selectedSubCategory = new GoodsCategory()
    this.selectedCategory = new GoodsCategory()
    this.page = 0 // 页码
    this.finished = false // 是否加载完成
    this.isLoading = false // 是否在加载
    this.dataList = []
    this.$nextTick(() => {
      if (this.goodsCategoryList && this.goodsCategoryList.length > index) {
        this.selectedCategory = this.goodsCategoryList[index]
        if (this.selectedCategory.children.length > 0) {
          this.selectedSubCategory = this.selectedCategory.children[0]
        }
        const queryRequest = new QueryRequest()
        if (this.selectedCategory.code) {
          queryRequest.conditions = [
            {
              operation: 'categoryCode:=',
              parameters: [this.selectedCategory.code]
            }
          ]
        }
        if (this.selectedSubCategory.code) {
          queryRequest.conditions = [
            {
              operation: 'categoryCode:=',
              parameters: [this.selectedSubCategory.code]
            }
          ]
        }
        queryRequest.conditions.push({ operation: 'counterShopId:=', parameters: [this.counterShopId] })
        queryRequest.conditions.push({ operation: 'type:=', parameters: [this.type] })
        this.doQueryGood(queryRequest)
      }
    })
  }

  /**
   * 二级分类点击事件
   */
  doSecondChange(goodsCategory: GoodsCategory) {
    this.selectedSubCategory = goodsCategory
    this.page = 0 // 页码
    this.finished = false // 是否加载完成
    this.isLoading = false // 是否在加载
    this.dataList = []
    const queryRequest = new QueryRequest()
    if (this.selectedSubCategory.code) {
      queryRequest.conditions = [
        {
          operation: 'categoryCode:=',
          parameters: [this.selectedSubCategory.code]
        }
      ]
    }
    queryRequest.conditions.push({ operation: 'counterShopId:=', parameters: [this.counterShopId] })
    queryRequest.conditions.push({ operation: 'type:=', parameters: [this.type] })
    this.doQueryGood(queryRequest)
  }

  /**
   * 确认选择
   */
  doConfirm() {
    uni.setStorage({
      key: 'invXFApplyGoodsList',
      data: this.doReturnModel(this.selectedGoodsList),
      success: function () {
        uni.navigateBack({})
      }
    })
  }

  /**
   * 模型转换
   * @param before 待转换数组
   */
  doExchangeModel(before: InvXFApplyLine[] = []) {
    const after: InvXFApplyGoods[] = []
    for (let index = 0; index < before.length; index++) {
      let temp: InvXFApplyGoods = new InvXFApplyGoods()
      temp = { ...before[index].goods } as InvXFApplyGoods
      temp.traceCodeRule = before[index].traceCodeRule
      temp.useTraceCode = before[index].useTraceCode
      temp.useVd = before[index].useVd
      temp.lines = before[index].lines
      temp.batches = before[index].batches || []
      temp.invQty = before[index].invQty
      temp.qty = before[index].qty
      temp.qpcQty = before[index].qpcQty
      temp.total = before[index].total
      temp.rtlTotal = before[index].rtlTotal
      temp.rtlPrc = before[index].rtlPrc
      temp.rtlQpcPrc = before[index].rtlQpcPrc
      temp.validPeriod = before[index].validPeriod
      temp.images = before[index].goodsImages
      if (before[index].categoryUuid) {
        temp.firstCategory = new Category()
        temp.firstCategory.uuid = before[index].categoryUuid || ''
        temp.firstCategory.code = before[index].categoryCode || ''
        temp.firstCategory.name = before[index].categoryName || ''
      } else {
        temp.firstCategory = null
      }
      after.push(temp)
    }
    return after
  }

  /**
   * 模型还原
   * @param before 待还原模型
   */
  doReturnModel(before: InvXFApplyGoods[] = []) {
    const after: InvXFApplyLine[] = []
    for (let index = 0; index < before.length; index++) {
      const temp: InvXFApplyLine = new InvXFApplyLine()
      temp.goods.code = before[index].code
      temp.goods.inputCode = before[index].inputCode
      temp.goods.minMunit = before[index].minMunit
      temp.goods.munit = before[index].munit
      temp.goods.name = before[index].name
      temp.goods.price = before[index].price
      temp.goods.qpc = before[index].qpc
      temp.goods.qpcStr = before[index].qpcStr
      temp.goods.singlePrice = before[index].singlePrice
      temp.goods.rtlPrc = before[index].rtlPrc
      temp.goodsImages = before[index].images
      temp.rtlPrc = before[index].rtlPrc
      temp.rtlQpcPrc = before[index].rtlQpcPrc
      temp.goods.uuid = before[index].uuid
      temp.total = before[index].total
      temp.rtlTotal = before[index].rtlTotal
      temp.qty = before[index].qty
      temp.qpcQty = before[index].qpcQty
      temp.invQty = before[index].invQty
      temp.validPeriod = before[index].validPeriod
      if (before[index].firstCategory) {
        temp.categoryCode = before[index].firstCategory!.code || ''
        temp.categoryName = before[index].firstCategory!.name || ''
        temp.categoryUuid = before[index].firstCategory!.uuid || ''
      }
      temp.useTraceCode = before[index].useTraceCode
      temp.traceCodeRule = before[index].traceCodeRule
      temp.useVd = before[index].useVd
      temp.lines = before[index].lines || []
      temp.batches = before[index].batches || []
      after.push(temp)
    }
    return after
  }

  /**
   * 预览图片
   */
  handlePreviewImg(sku: InvXFApplyGoods) {
    uni.previewImage({
      current: String(0),
      urls: this.imageList(sku)
    })
  }
}
