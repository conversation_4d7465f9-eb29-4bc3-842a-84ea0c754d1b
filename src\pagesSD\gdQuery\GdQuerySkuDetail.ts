/*
 * @Author: 庞昭昭
 * @Date: 2021-08-03 09:56:18
 * @LastEditTime: 2025-05-15 11:31:47
 * @LastEditors: hanwei
 * @Description: 商品明细（购物车）
 * @FilePath: \soa\src\pagesSD\gdQuery\GdQuerySkuDetail.ts
 * 记得注释
 */
import { Vue, Component } from 'vue-property-decorator'
import AvgSaleDialog from './cmp/AvgSaleDialog.vue'
import BaseResponse from '@/model/base/BaseResponse'

import SysConfigItem from '@/model/sysConfig/SysConfigItem'
import { State } from 'vuex-class'
import UseQueues from '@/components/hd-popover/UseQueues'
import { mixins } from 'vue-class-component'
import AppGdQueryApi from '@/network/gdQuery/AppGdQueryApi'
import GoodsSaleReference from '@/model/gdQuery/GoodsSaleReference'
import GoodsSaleRefRequest from '@/model/gdQuery/GoodsSaleRefRequest'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'

@Component({ components: { AvgSaleDialog, ViewExhibit } })
export default class GdQuerySkuDetail extends mixins(UseQueues) {
  @State('sysConfig') sysConfig: SysConfigItem // 获取全局配置
  @State('optionList') optionList: ModuleOption[] //店务配置列表

  $refs: any
  showAvg: boolean = false // 是否展示日销
  skuInfo: GoodsSaleReference = new GoodsSaleReference() // 商品信息

  //获取字段展示权限
  get showMaster() {
    const masterPermission = {
      showDisplayLocation: false, // 陈列位置
      showTransitInv: false, //在途库存
      showUnshippedInv: false, // 未发货库存
      showSalePrice: false, // 销售价
      showDetailSkuWeekSale: false, // 周销量
      showDetailSkuMonthSale: false, // 月销量
      showDetailSkuSpec: false, // 箱规
      showDetailSkuQualification: false, // 资质图片
      showListSkuExplain: false, // 商品说明
      showSellableInvQty: false, // 可售库存
      showSkuValidPeriod: false, // 保质期天数
      SHOWDAILYAVGSALES: false, // 平均日销量
      showGrossIn30Days: false, // 近30日毛利
      showSkuCategory: false, // 商品品类
      showOrderPrice: false, // 订货价
      showInvPrice: false, // 库存成本价
      showLowInv: false, // 库存下限
      showHighInv: false // 库存上限
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDISPLAYLOCATION)) {
      masterPermission.showDisplayLocation = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWTRANSITINV)) {
      masterPermission.showTransitInv = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWUNSHIPPEDINV)) {
      masterPermission.showUnshippedInv = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSALEPRICE)) {
      masterPermission.showSalePrice = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDETAILSKUWEEKSALE)) {
      masterPermission.showDetailSkuWeekSale = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDETAILSKUMONTHSALE)) {
      masterPermission.showDetailSkuMonthSale = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDETAILSKUSPEC)) {
      masterPermission.showDetailSkuSpec = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDETAILSKUQUALIFICATION)) {
      masterPermission.showDetailSkuQualification = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWLISTSKUEXPLAIN)) {
      masterPermission.showListSkuExplain = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSELLABLEINVQTY)) {
      masterPermission.showSellableInvQty = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSKUVALIDPERIOD)) {
      masterPermission.showSkuValidPeriod = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDAILYAVGSALES)) {
      masterPermission.SHOWDAILYAVGSALES = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWGROSSIN30DAYS)) {
      masterPermission.showGrossIn30Days = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSKUCATEGORY)) {
      masterPermission.showSkuCategory = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWORDERPRICE)) {
      masterPermission.showOrderPrice = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWINVPRICE)) {
      masterPermission.showInvPrice = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWLOWINV)) {
      masterPermission.showLowInv = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWHIGHINV)) {
      masterPermission.showHighInv = true
    }
    return masterPermission
  }

  /**
   * 是否隐藏敏感价格
   */
  get hidePrice() {
    return PermissionMgr.hasPermission(Permission.globalPriceView)
  }

  /**
   * 是否隐藏毛利率
   */
  get hideRate() {
    return PermissionMgr.hasPermission(Permission.globalMarginView)
  }

  /**
   * 是否展示单品数
   */
  get invQtyDisplayStrategy() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGdQuery
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.invQtyDisplayStrategy) {
      return moduleConfig[0].options.invQtyDisplayStrategy === '1'
    }
    return false
  }

  // 品类
  get category() {
    if (this.skuInfo && this.skuInfo.goods.sorts && this.skuInfo.goods.sorts.length > 0) {
      return this.skuInfo.goods.sorts
        .reduce((prev, curr) => {
          return prev + curr.name + '>'
        }, '')
        .slice(0, -1)
    }
    return '--'
  }

  onLoad(option) {
    this.$nextTick(async () => {
      const body = new GoodsSaleRefRequest()
      if (option) {
        body.gdInputCode = option.inputCode
        body.gdUuid = option.uuid
        body.goodsType = option.goodsType
      }
      body.fetchParts = ['weather', 'saleQty', 'image', 'sort', 'tag']
      this.$showLoading({ delayTime: 200 })
      this.skuInfo = await this.getSaleRef(body)
      this.$hideLoading()
    })
  }

  //月销量单品数
  get monthSaleQty() {
    return (
      this.skuInfo.goods &&
      this.skuInfo.goods.monthSaleQty &&
      this.skuInfo.goods.qpc &&
      (this.skuInfo.goods.monthSaleQty % this.skuInfo.goods.qpc).scale(2)
    )
  }

  //月销量
  get monthSaleStr() {
    if (this.skuInfo.goods && this.skuInfo.goods.qpc && (this.skuInfo.goods.monthSaleQty || this.skuInfo.goods.monthSaleQty === 0)) {
      return (
        Math.floor(this.skuInfo.goods.monthSaleQty / this.skuInfo.goods.qpc) +
        this.munit +
        (this.monthSaleQty ? this.monthSaleQty + this.minMunit : '')
      )
    } else {
      return '--'
    }
  }

  //周销量
  get weekSaleStr() {
    if (this.skuInfo.goods && (this.skuInfo.goods.weekSaleQty || this.skuInfo.goods.weekSaleQty === 0)) {
      return this.skuInfo.goods.weekSaleQty + this.minMunit
    } else {
      return '--'
    }
  }

  // 单位
  get munit() {
    return this.skuInfo.goods && this.skuInfo.goods.munit ? `${this.skuInfo.goods.munit}` : ''
  }

  // 单位
  get minMunit() {
    return this.skuInfo.goods && this.skuInfo.goods.minMunit ? `${this.skuInfo.goods.minMunit}` : ''
  }

  // 商品名+含量
  get friendlyStr() {
    return this.skuInfo.goods && this.skuInfo.goods.friendlyStr ? `${this.skuInfo.goods.friendlyStr}` : null
  }

  // 是否新品
  get isNewList() {
    return this.skuInfo.goods ? this.skuInfo.goods.isNewList : false
  }

  /**
   * 毛利率
   */
  get rate() {
    if (this.skuInfo && this.skuInfo.goods && this.skuInfo.goods.singlePrice && this.skuInfo.goods.rtlPrc) {
      return `${(((this.skuInfo.goods.rtlPrc - this.skuInfo.goods.singlePrice) * 100) / this.skuInfo.goods.rtlPrc).scale(2)}%`
    } else {
      return null
    }
  }

  // 是否散货
  get isDisp() {
    return this.skuInfo && this.skuInfo.goods && this.skuInfo.goods.isDisp ? this.skuInfo.goods.isDisp : false
  }

  /**
   * 零售价
   */
  get rtlPrc() {
    if (this.skuInfo && this.skuInfo.goods && typeof this.skuInfo.goods.rtlPrc === 'number') {
      const rtlPrc = this.skuInfo.goods.rtlPrc
      return rtlPrc
    } else {
      return null
    }
  }

  /**
   * 订货价
   */
  get alcPrc() {
    if (this.skuInfo && this.skuInfo.goods && typeof this.skuInfo.goods.price === 'number') {
      const alcPrc = this.skuInfo.goods.price
      return alcPrc
    } else {
      return null
    }
  }

  /**
   * 轮播图列表
   */
  get swiperList() {
    if (!this.skuInfo.goods || !this.skuInfo.goods.images || !this.skuInfo.goods.images.length) {
      return []
    } else {
      return this.skuInfo.goods.images.map((url) => {
        return { image: url }
      })
    }
  }

  /**
   * 是否有多个陈列位置
   */
  get hasMutiple() {
    return (item) => {
      return item && item.split(',').length > 1
    }
  }

  /**
   * 获取商品图标数据
   */
  async getSaleRef(body: GoodsSaleRefRequest) {
    const { data: skuInfo }: BaseResponse<GoodsSaleReference> = await AppGdQueryApi.getSaleRef(body)
      .then((resp) => {
        this.showAvg = true
        return resp
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: error.msg })
        return error
      })
    return skuInfo
  }

  //点击页面关闭气泡
  doCloseOutside() {
    if (this.queueObj && this.queueObj.closeOutside) {
      this.queueObj.closeOutside()
    }
  }

  /**
   * 预览资质图片
   * @param imgs
   */
  handlePreviewQuaImg(imgs: string[]) {
    uni.previewImage({
      current: String(0),
      urls: imgs
    })
  }

  /**
   * 预览banner图片
   * @param imgs
   */
  bannerClick(item: any) {
    const urls = this.swiperList.map((imgData) => imgData.image)
    const index = this.swiperList.findIndex((imgData) => imgData.image === item.image)
    uni.previewImage({
      current: String(index),
      urls
    })
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit() {
    this.$refs.viewExhibit.open()
  }
}
