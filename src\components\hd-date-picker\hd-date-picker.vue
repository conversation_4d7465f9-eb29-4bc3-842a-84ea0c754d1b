<template>
  <view :class="['hd-date-picker', isMaxIndex && 'hd-date-picker-MaxIndex']" v-if="show">
    <view class="g-dp-mask" :class="{ show: show }" @click.stop="onCancel" @touchmove.stop.prevent catchtouchmove="true"></view>
    <view class="g-dp-content" :class="{ show: show }" @touchmove.stop.prevent catchtouchmove="true">
      <view class="g-dp-ctt-head">
        <view class="g-dp-ctt-hd-btn" @click.stop="onCancel">取消</view>
        <text class="title" v-if="title">{{ title }}</text>
        <view class="g-dp-ctt-hd-btn" :style="{ color: themeColor }" @click.stop="onConfirm">确定</view>
      </view>
      <view class="g-dp-ctt-wrapper">
        <picker-view :indicator-style="indicatorStyle" :value="selectedValue" @change="wrapperChange">
          <picker-view-column
            :style="{
              flex: type === 'date' || type === 'year-month' || type === 'date-hour' || type === 'date-hour-minutes' || type === 'date-time' ? 1 : 0
            }"
          >
            <block v-if="type === 'date' || type === 'year-month' || type === 'date-hour' || type === 'date-hour-minutes' || type === 'date-time'">
              <view class="g-dp-ctt-wp-item" v-for="(item, index) in years" :key="index">{{ item }}年</view>
            </block>
          </picker-view-column>
          <picker-view-column
            :style="{
              flex:
                type === 'date' ||
                type === 'year-month' ||
                type === 'month-day' ||
                type === 'date-hour' ||
                type === 'date-hour-minutes' ||
                type === 'date-time'
                  ? 1
                  : 0
            }"
          >
            <block
              v-if="
                type === 'date' ||
                type === 'year-month' ||
                type === 'month-day' ||
                type === 'date-hour' ||
                type === 'date-hour-minutes' ||
                type === 'date-time'
              "
            >
              <view class="g-dp-ctt-wp-item" v-for="(item, index) in months" :key="index">{{ dateFormate(item) }}月</view>
            </block>
          </picker-view-column>
          <picker-view-column
            :style="{
              flex: type === 'date' || type === 'month-day' || type === 'date-hour' || type === 'date-hour-minutes' || type === 'date-time' ? 1 : 0
            }"
          >
            <block v-if="type === 'date' || type === 'month-day' || type === 'date-hour' || type === 'date-hour-minutes' || type === 'date-time'">
              <view class="g-dp-ctt-wp-item" v-for="(item, index) in days" :key="index">{{ dateFormate(item) }}日</view>
            </block>
          </picker-view-column>
          <picker-view-column
            :style="{
              flex: type === 'time' || type === 'hour-minutes' || type === 'date-hour' || type === 'date-hour-minutes' || type === 'date-time' ? 1 : 0
            }"
          >
            <block v-if="type === 'time' || type === 'hour-minutes' || type === 'date-hour' || type === 'date-hour-minutes' || type === 'date-time'">
              <view class="g-dp-ctt-wp-item" v-for="(item, index) in hours" :key="index">{{ dateFormate(item) }}时</view>
            </block>
          </picker-view-column>
          <picker-view-column
            :style="{ flex: type === 'time' || type === 'hour-minutes' || type === 'date-hour-minutes' || type === 'date-time' ? 1 : 0 }"
          >
            <block v-if="type === 'time' || type === 'hour-minutes' || type === 'date-hour-minutes' || type === 'date-time'">
              <view class="g-dp-ctt-wp-item" v-for="(item, index) in minutes" :key="index">{{ dateFormate(item) }}分</view>
            </block>
          </picker-view-column>
          <picker-view-column :style="{ flex: type === 'time' || type === 'date-time' ? 1 : 0 }">
            <block v-if="type === 'time' || type === 'date-time'">
              <view class="g-dp-ctt-wp-item" v-for="(item, index) in seconds" :key="index">{{ dateFormate(item) }}秒</view>
            </block>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    isMaxIndex: {
      type: Boolean,
      default() {
        return false
      }
    },
    themeColor: {
      type: String,
      default() {
        return '#6ba1ff'
      }
    }
  },
  computed: {
    type() {
      return this.$datePickerStore.state.type
    },
    startDate() {
      return this.$datePickerStore.state.startDate
    },
    endDate() {
      return this.$datePickerStore.state.endDate
    },
    currentDate() {
      return this.$datePickerStore.state.currentDate
    },
    show() {
      return this.$datePickerStore.state.show
    },
    selectedValue() {
      return this.$datePickerStore.state.selectedValue
    },
    years() {
      return this.$datePickerStore.state.years
    },
    months() {
      return this.$datePickerStore.state.months
    },
    days() {
      return this.$datePickerStore.state.days
    },
    hours() {
      return this.$datePickerStore.state.hours
    },
    minutes() {
      return this.$datePickerStore.state.minutes
    },
    seconds() {
      return this.$datePickerStore.state.seconds
    },
    title() {
      return this.$datePickerStore.state.title
    }
  },
  data() {
    return {
      // eslint-disable-next-line no-undef
      indicatorStyle: `height: ${uni.upx2px(88)}px;`
    }
  },
  methods: {
    dateFormate(val) {
      if (!val) {
        return '01'
      }
      if (Number(val) > 9) {
        return val
      }
      return '0' + val
    },
    getDateValue(pikerValue) {
      // 'date' | 'time' | 'year-month' | 'month-day' | 'date-hour' | 'date-hour-minutes' | 'date-time' | 'hour-minutes'

      switch (this.type) {
        case 'date':
          return this.years[pikerValue[0]] + '-' + this.dateFormate(this.months[pikerValue[1]]) + '-' + this.dateFormate(this.days[pikerValue[2]])
        case 'time':
          return (
            this.dateFormate(this.hours[pikerValue[3]]) +
            ':' +
            this.dateFormate(this.minutes[pikerValue[4]]) +
            ':' +
            this.dateFormate(this.seconds[pikerValue[5]])
          )
        case 'hour-minutes':
          return this.dateFormate(this.hours[pikerValue[3]]) + ':' + this.dateFormate(this.minutes[pikerValue[4]])
        case 'year-month':
          return this.years[pikerValue[0]] + '-' + this.dateFormate(this.months[pikerValue[1]])
        case 'month-day':
          return this.dateFormate(this.months[pikerValue[1]]) + '-' + this.dateFormate(this.days[pikerValue[2]])
        case 'date-hour':
          return (
            this.years[pikerValue[0]] +
            '-' +
            this.dateFormate(this.months[pikerValue[1]]) +
            '-' +
            this.dateFormate(this.days[pikerValue[2]]) +
            ' ' +
            this.dateFormate(this.hours[pikerValue[3]])
          )
        case 'date-hour-minutes':
          return (
            this.years[pikerValue[0]] +
            '-' +
            this.dateFormate(this.months[pikerValue[1]]) +
            '-' +
            this.dateFormate(this.days[pikerValue[2]]) +
            ' ' +
            this.dateFormate(this.hours[pikerValue[3]]) +
            ':' +
            this.dateFormate(this.minutes[pikerValue[4]])
          )
        case 'date-time':
          return (
            this.years[pikerValue[0]] +
            '-' +
            this.dateFormate(this.months[pikerValue[1]]) +
            '-' +
            this.dateFormate(this.days[pikerValue[2]]) +
            ' ' +
            this.dateFormate(this.hours[pikerValue[3]]) +
            ':' +
            this.dateFormate(this.minutes[pikerValue[4]]) +
            ':' +
            this.dateFormate(this.seconds[pikerValue[5]])
          )
        default:
          return (
            this.years[pikerValue[0]] +
            '-' +
            this.dateFormate(this.months[pikerValue[1]]) +
            '-' +
            this.dateFormate(this.days[pikerValue[2]]) +
            ' ' +
            this.dateFormate(this.hours[pikerValue[3]]) +
            ':' +
            this.dateFormate(this.minutes[pikerValue[4]]) +
            ':' +
            this.dateFormate(this.seconds[pikerValue[5]])
          )
      }
    },
    onCancel() {
      this.$datePickerStore.commit('hidePicker')
      this.$datePickerStore.commit('fail', {
        date: this.getDateValue(this.selectedValue),
        dateValueIndex: this.selectedValue
      })
    },
    onConfirm() {
      this.$datePickerStore.commit('hidePicker')
      this.$datePickerStore.commit('success', {
        date: this.getDateValue(this.selectedValue),
        dateValueIndex: this.selectedValue
      })
    },

    /**
     * picker change时间触发
     * @param {Object} e
     */
    wrapperChange(e) {
      const detailValue = e.detail.value
      this.$datePickerStore.commit('change', detailValue)
    }
  }
}
</script>

<style lang="scss">
.hd-date-picker {
  position: relative;
  // z-index: 1003;

  .g-dp-mask {
    position: fixed;
    z-index: 1004;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
  }
  .g-dp-mask.show {
    visibility: visible;
    opacity: 1;
  }
  .g-dp-content {
    position: fixed;
    z-index: 1005;
    bottom: 0;
    right: 0;
    width: 100%;
    transition: all 0.3s ease;
    transform: translateY(100%);
    .g-dp-ctt-head {
      height: 88upx;
      background-color: #ffffff;
      border-bottom: 1px solid #e5e5e5;
      padding: 0 40upx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .g-dp-ctt-hd-btn {
        color: #888;
        font-size: 34rpx;
      }
      .title {
        font-size: 36rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #32353a;
      }
    }
    .g-dp-ctt-wrapper {
      height: 480upx;
      width: 100%;
      background-color: #ffffff;
      .g-dp-ctt-wp-item {
        text-align: center;
        width: 100%;
        height: 88upx;
        line-height: 88upx;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 30upx;
      }
    }
  }
  .g-dp-content.show {
    transform: translateY(0);
  }
  picker-view-column {
    height: 480rpx !important;
  }
}
.hd-date-picker-MaxIndex {
  z-index: 1004;
}
</style>
