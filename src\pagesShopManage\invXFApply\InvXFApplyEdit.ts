import { Component } from 'vue-property-decorator'
import { Mutation, State, Getter } from 'vuex-class'
import { mixins } from 'vue-class-component'
import swipeMix from '@/components/hd-swipe-action/swipeMix'
import HdCell from '@/components/hd-cell/hd-cell.vue'
import HdNumberBoxTest from '@/components/hd-number-box-test/hd-number-box-test.vue'
import HdOperatorCell from '@/components/hd-operator-cell/hd-operator-cell.vue'
import HdSwipeAction from '@/components/hd-swipe-action/hd-swipe-action.vue'
import HdButton from '@/components/hd-button/hd-button.vue'
import HdNote from '@/components/hd-note/hd-note.vue'
import EditQtyDialog from './cmp/EditQtyDialog.vue'
import EditCard from './cmp/EditCard.vue'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import { InvXFApplyFilter } from '@/pagesShopManage/invXFApply/cmp/InvXFApplyFilter'
import InvXFApplyApi from 'network/invXFApply/InvXFApplyApi'
import InvXFApply from 'model/invXFApply/InvXFApply'
import InvXFApplyLine from 'model/invXFApply/InvXFApplyLine'
import Shop from 'model/data/Shop'
import InvXFApplyCreation from 'model/invXFApply/InvXFApplyCreation'
import InvXFApplyModification from 'model/invXFApply/InvXFApplyModification'
import { InvXFApplyType } from 'model/invXFApply/InvXFApplyType'
import Store from '@/model/basedata/common/Store'
import CodeName from '@/model/base/CodeName'
import SysConfigItem from '@/model/sysConfig/SysConfigItem'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import CommonsApi from '@/network/CommonsApi'
import ModuleFieldRights from '@/model/default/ModuleFieldRights'
import InvXFApplyGoods from '@/model/invXFApply/InvXFApplyGoods'
import QueryRequest from '@/model/base/QueryRequest'
import TraceCodeAnalyzeResult from '@/model/default/TraceCodeAnalyzeResult'
import InvXFApplyBatchBase from '@/model/invXFApply/InvXFApplyBatchBase'
import DateUtil from '@/utils/DateUtil'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import ModifyTransferDialog from './cmp/ModifyTransferDialog.vue'
import InvXFModeCalc from '@/model/invXFApply/InvXFModeCalc'
import PackageCard from './cmp/PackageCard.vue'
import InvXFApplyPackLine from '@/model/invXFApply/InvXFApplyPackLine'
import EditPkaQtyDialog from './cmp/EditPkaQtyDialog.vue'
import InvXFApplyPackLineBase from '@/model/invXFApply/InvXFApplyPackLineBase'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'
import CommonUtil from '@/utils/CommonUtil'
import store from '@/store'

@Component({
  components: {
    HdCell,
    HdOperatorCell,
    HdNote,
    HdButton,
    HdNumberBoxTest,
    HdSwipeAction,
    EditCard,
    EditQtyDialog,
    ModifyTransferDialog,
    PackageCard,
    EditPkaQtyDialog
  },
  filters: InvXFApplyFilter
})
export default class InvXFApplyEdit extends mixins(swipeMix, BroadCast) {
  @Getter('qtyScale') qtyScale: number
  @State('sysConfig') sysConfig: SysConfigItem // 获取全局配置
  @State('moduleFieldRightsList') moduleFieldRightsList: ModuleFieldRights[] // 获取模块字段权限
  @Mutation('goodsList') mutationGoodsList // 提交到vuex
  @State('store') store: Store // 当前门店
  @State('optionList') optionList: ModuleOption[] //店务配置列表
  optionListModuleId = ModuleId //店务配置枚举
  data: InvXFApply = new InvXFApply() // 详情数据
  isEdit: boolean = false // 是否为编辑
  // 分页相关
  pageSize: number = 20 // 每页大小
  pageNum: number = 0 // 页码
  finished: boolean = false // 是否加载完成
  isLoading: boolean = false // 是否在加载
  InvXFApplyType = InvXFApplyType
  modifyTranferLine: InvXFApplyLine = new InvXFApplyLine() // 修改售价的数据
  goodsIndex: number = 0 // 商品明细下标
  allowModifyPrice: string = ''
  showModify: boolean = false
  tabType: string = 'good' // tab类型
  isRenovote: boolean = false // 是否需要刷新
  $refs: any

  footerHeight: number = 0 // 底部按钮部分高度
  headerHeight: number = 0 // 头部按钮部分高度

  reasons: CodeName[] = [
    { code: '0', name: InvXFApplyFilter.deliveryTypeName(0) },
    { code: '1', name: InvXFApplyFilter.deliveryTypeName(1) }
  ] // 配送模式列表

  get rootStyle() {
    return `padding-bottom: ${this.footerHeight}px !important; padding-bottom: calc(${this.footerHeight}px + constant(safe-area-inset-bottom)) !important;padding-bottom: calc(${this.footerHeight}px + env(safe-area-inset-bottom)) !important;padding-top:${this.headerHeight}px`
  }

  //选中配送模式
  get reason() {
    if (this.data.deliveryType || this.data.deliveryType === 0) {
      return { name: InvXFApplyFilter.deliveryTypeName, code: String(this.data.deliveryType) }
    } else {
      return null
    }
  }

  // 获取按钮权限
  get permission() {
    return {
      save: PermissionMgr.hasPermission(Permission.invxfApplyEdit),
      submit: PermissionMgr.hasPermission(Permission.invxfApplySubmit)
    }
  }

  get validate() {
    if (!this.data.packLines || this.data.packLines.length === 0) {
      return this.data.lines.length && !this.data.lines.filter((line) => line.qty === 0).length && this.data.counterShopId
    } else {
      return (
        this.data.lines.length &&
        !this.data.lines.filter((line) => line.qty === 0).length &&
        !this.data.packLines.filter((line) => line.qty === 0).length &&
        this.data.counterShopId
      )
    }
  }

  get counterType() {
    const counterType = this.data.type === 'decrease' ? InvXFApplyType.increase : InvXFApplyType.decrease
    return InvXFApplyFilter.typeName(counterType)
  }

  // 展示商品的列表
  get showList() {
    const list = [...this.data.lines]
    if (list.length > 0 && list.length <= (this.pageNum + 1) * this.pageSize) {
      this.finished = true
    }
    return list.slice(0, (this.pageNum + 1) * this.pageSize)
  }

  //展示配送方式
  get infDeliveryTypeShow() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.INFDELIVERYTYPESHOW)) {
      return true
    }
    return false
  }

  //配送方式
  get deliveryType() {
    return InvXFApplyFilter.deliveryTypeName(this.data.deliveryType, false)
  }

  /**
   * 商品行总金额
   */
  get total() {
    return this.showList.reduce((prev, cur) => {
      return prev.add(cur.total)
    }, 0)
  }

  /**
   * 包材调拨金额
   */
  get packTotal() {
    return this.data.packLines.reduce((prev, cur) => {
      return prev.add(cur.total)
    }, 0)
  }

  /**
   * 合计调拨金额
   */
  get aggTotal() {
    return this.total.add(this.packTotal).scale(this.qtyScale)
  }

  /**
   * 商品行售价总额
   */
  get rtnTotal() {
    return this.showList.reduce((prev, cur) => {
      return prev.add(cur.rtlTotal || 0)
    }, 0)
  }

  /**
   * 是否展示零售价 - 默认不展示
   */
  get showRtlPrice() {
    const moduleConfig = (this.moduleFieldRightsList || []).find((option: ModuleFieldRights) => option.moduleId == 'sosInvXFApply')
    if (moduleConfig && moduleConfig.fieldRights && moduleConfig.fieldRights.rtlPrc) {
      return moduleConfig.fieldRights.rtlPrc === '1'
    }
    return false
  }

  /**
   * 是否展示调拨价 - 默认展示
   */
  get showPrice() {
    const moduleConfig = (this.moduleFieldRightsList || []).find((option: ModuleFieldRights) => option.moduleId == 'sosInvXFApply')
    if (moduleConfig && moduleConfig.fieldRights && moduleConfig.fieldRights.price) {
      return moduleConfig.fieldRights.price === '1'
    }
    return true
  }

  /**
   * 限制门店经营商品方案：false-不限制(def),true-限制
   */
  get limitStoreGdSaleScheme() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosInvXFApply
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.limitStoreGdSaleScheme) {
      return moduleConfig[0].options.limitStoreGdSaleScheme === 'true' ? true : false
    }
    return false
  }

  // 是否展示调拨修改按钮
  get isShowModify() {
    if (this.allowModifyPrice && this.allowModifyPrice.length > 0) {
      return this.allowModifyPriceInvXFMode.includes(this.allowModifyPrice)
    }
    return false
  }

  // 是否有包材明细
  get isHasPack() {
    if (this.data.packLines && this.data.packLines.length > 0) {
      return true
    }
    return false
  }

  /**
   * 获取批次号格式
   */
  get batchNoFormatter() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.batchNoFormatter) {
      return moduleConfig[0].options.batchNoFormatter
    }
    return 'yyyyMMdd'
  }

  /**
   * 是否开启效期 - 默认不开启
   */
  get enableValidDate() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosInvXFApply
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.enableGoodsValidDate) {
      return moduleConfig[0].options.enableGoodsValidDate == '2'
    }
    return false
  }

  /**
   * 允许改价的调拨模式
   */
  get allowModifyPriceInvXFMode() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosInvXFApply
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.allowModifyPriceInvXFMode) {
      return moduleConfig[0].options.allowModifyPriceInvXFMode
    }
    return ''
  }

  // 是否展示包材模块
  get showPack() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosInvXFApply
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.showPack) {
      return moduleConfig[0].options.showPack
    }
    return ''
  }

  onShow() {
    // 获得商品数据
    const list: any[] = uni.getStorageSync('invXFApplyGoodsList')
    // 如果存在其他界面传过来的商品
    if (list) {
      // 如果传过来的商品数量与当前商品数据不等,则重新定位数据
      if (list.length !== this.data.lines.length) {
        this.pageNum = 0
        this.pageSize = 20 // 每页大小
        this.finished = false // 是否加载完成
        this.isLoading = false // 是否在加载
      }
      const lineList: InvXFApplyLine[] = []
      for (let index = 0; index < list.length; index++) {
        lineList.push(list[index])
      }
      // 确定是否显示加载中...
      if (lineList.length > 0 && lineList.length <= (this.pageNum + 1) * this.pageSize) {
        this.finished = true
      }
      this.data.lines = lineList
      uni.removeStorage({ key: 'invXFApplyGoodsList' })
    }
    // 获得当前包材
    const skuPkaList: InvXFApplyPackLine[] = uni.getStorageSync('invXFApplyPkaList')
    // 如果存在其他界面传过来的商品
    if (skuPkaList) {
      this.data.packLines = skuPkaList
      uni.removeStorage({ key: 'invXFApplyPkaList' })
    }
    // 获得当前选择门店
    const shop: Shop = uni.getStorageSync('currentShop')
    if (shop) {
      // 门店切换，商品清空
      if (shop.uuid !== this.data.counterShopId) {
        this.data.lines = []
        this.data.packLines = []
        this.pageNum = 0
        this.pageSize = 20 // 每页大小
        this.finished = true // 是否加载完成
        this.isLoading = false
      }
      this.data.counterShopId = shop.uuid
      this.data.counterShopNo = shop.code
      this.data.counterShopName = shop.name
      if (this.allowModifyPriceInvXFMode && this.allowModifyPriceInvXFMode.length > 0) {
        this.invxfmodeCalc()
      }
    }
    uni.removeStorage({ key: 'currentShop' })
  }

  async onLoad(option) {
    if (option && option.id) {
      if (option.id && option.type) {
        // 新增
        this.data.billId = option.id
        this.data.type = option.type
        const title = this.data.type === 'decrease' ? '新增调出申请' : '新增调入申请'
        uni.setNavigationBarTitle({
          title: title
        })
      } else {
        // 编辑
        this.isEdit = true
        this.data.billId = option.id
        await this.getDataDetail(() => {
          const title = this.data.type === 'decrease' ? '新增调出申请' : '新增调入申请'
          uni.setNavigationBarTitle({
            title: title
          })
        })
        if (this.allowModifyPriceInvXFMode && this.allowModifyPriceInvXFMode.length > 0) {
          await this.invxfmodeCalc()
        }
      }
    }
    this.elementHeight()
    const timer = setTimeout(() => {
      clearTimeout(timer)
      this.elementHeight()
    }, 500)
  }

  onUnload() {
    uni.$emit('PATH', this.isRenovote)
  }

  elementHeight() {
    CommonUtil.getRect('.inv-xf-apply-footer', false, this)
      .then((resp) => {
        this.footerHeight = resp.height || 0
      })
      .catch((error) => {
        console.log(error)
      })

    CommonUtil.getRect('.inv-xf-apply-header', false, this)
      .then((resp) => {
        this.headerHeight = resp.height || 0
      })
      .catch((error) => {
        console.log(error)
      })
  }

  // 获取单据详情
  async getDataDetail(callback?: Function) {
    this.isLoading = true
    this.$showLoading()
    await InvXFApplyApi.get(this.data.billId, 'details')
      .then((resp) => {
        this.$hideLoading()
        this.data = resp.data || new InvXFApply()
        this.isLoading = false
        callback && callback()
      })
      .catch((e) => {
        this.isLoading = false
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  /**
   * 选择门店
   */
  doSelectShop() {
    const hasLines = (this.data.lines || []).length

    this.$Router.push({
      name: 'invXFApplyShopSelect',
      params: {
        id: this.data.counterShopId,
        name: this.data.counterShopName,
        hasLines: hasLines ? true : ''
      }
    })
  }

  /**
   * 步进器change事件
   * @param qpcQty 数量
   * @param index 对应商品下标
   */
  doNumberChange(qty: number, goodsId: string) {
    const matchIndex = this.data.lines.findIndex((item) => item.goods.uuid === goodsId)
    if (matchIndex !== -1) {
      const newData: InvXFApplyLine = JSON.parse(JSON.stringify(this.data.lines[matchIndex]))
      newData.qty = qty
      newData.qpcQty = (qty / newData.goods.qpc).scale(this.qtyScale)
      newData.total = newData.goods.singlePrice.multiply(qty).scale(2)
      newData.rtlTotal = (newData.rtlPrc || 0).multiply(qty).scale(2)
      this.$set(this.data.lines, matchIndex, newData)
    }
  }

  /**
   * 步进器change事件
   * @param qpcQty 数量
   * @param index 对应商品下标
   */
  doPkaNumberChange(qty: number, pkaId: string) {
    const matchIndex = this.data.packLines.findIndex((item) => item.pack.uuid === pkaId)
    if (matchIndex !== -1) {
      const newData: InvXFApplyPackLine = JSON.parse(JSON.stringify(this.data.packLines[matchIndex]))
      newData.qty = qty
      newData.total = newData.pack.price.multiply(qty).scale(2)
      this.$set(this.data.packLines, matchIndex, newData)
    }
  }

  /**
   * 触底展示更多
   */
  onReachBottom() {
    this.loadMore()
  }

  /**
   * 显示更多商品
   */
  loadMore() {
    if (this.finished || this.isLoading) {
      return
    }
    this.isLoading = true
    const timer = setTimeout(() => {
      this.pageNum++
      this.isLoading = false
      clearTimeout(timer)
    }, 500)
  }

  doScan() {
    uni.scanCode({
      success: async (res) => {
        const scanWord = res.result || ''
        this.scanCodeJump(scanWord)
      },
      fail: () => {
        this.mutationGoodsList([])
      }
    })
  }

  // 扫码跳转
  async scanCodeJump(scanWord) {
    try {
      this.$showLoading({ delayTime: 200 })
      const skus = await this.doSearch(scanWord)
      const result = this.doReturnModel(skus)
      if (result.length > 1) {
        this.handleSearch(scanWord)
      } else if (result.length === 1) {
        this.handleSkuScan(result[0], scanWord)
      }
      this.$hideLoading()
    } catch (error) {
      const errMsg = (error as any).msg
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: errMsg })
    }
  }

  // 搜索事件
  doSearch(keyword: string) {
    return new Promise<InvXFApplyGoods[]>((resolve, reject) => {
      const query = new QueryRequest()
      query.page = 0
      query.pageSize = 2
      query.conditions = [{ operation: 'keyword:%=%', parameters: [keyword] }]
      query.conditions.push({ operation: 'counterShopId:=', parameters: [this.data.counterShopId] })
      query.conditions.push({ operation: 'type:=', parameters: [this.data.type!] })
      if (this.limitStoreGdSaleScheme) {
        // 是否是调出
        const isDecrease = this.data.type === InvXFApplyType.decrease
        query.conditions.push({ operation: 'fromStore:=', parameters: [isDecrease ? store.state.store?.id! : this.data.counterShopId] })
        query.conditions.push({ operation: 'toStore:=', parameters: [isDecrease ? this.data.counterShopId : store.state.store?.id!] })
      }
      query.fetchParts = ['category', 'image']
      InvXFApplyApi.queryGoods(query)
        .then((resp) => {
          resolve(resp.data || [])
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 模型还原
   * @param before 待还原模型
   */
  doReturnModel(before: InvXFApplyGoods[] = []) {
    const after: InvXFApplyLine[] = []
    for (let index = 0; index < before.length; index++) {
      const temp: InvXFApplyLine = new InvXFApplyLine()
      temp.goods.code = before[index].code
      temp.goods.inputCode = before[index].inputCode
      temp.goods.minMunit = before[index].minMunit
      temp.goods.munit = before[index].munit
      temp.goods.name = before[index].name
      temp.goods.price = before[index].price
      temp.goods.qpc = before[index].qpc
      temp.goods.qpcStr = before[index].qpcStr
      temp.goods.singlePrice = before[index].singlePrice
      temp.goods.rtlPrc = before[index].rtlPrc
      temp.goods.uuid = before[index].uuid
      temp.goodsImages = before[index].images
      temp.total = before[index].total
      temp.rtlTotal = before[index].rtlTotal
      temp.rtlPrc = before[index].rtlPrc
      temp.rtlQpcPrc = before[index].rtlQpcPrc
      temp.qty = before[index].qty
      temp.qpcQty = before[index].qpcQty
      temp.invQty = before[index].invQty
      temp.useTraceCode = before[index].useTraceCode
      temp.traceCodeRule = before[index].traceCodeRule
      temp.useVd = before[index].useVd
      temp.lines = []
      temp.batches = before[index].lines || []

      temp.validPeriod = before[index].validPeriod
      if (before[index].firstCategory) {
        temp.categoryCode = before[index].firstCategory!.code || ''
        temp.categoryName = before[index].firstCategory!.name || ''
        temp.categoryUuid = before[index].firstCategory!.uuid || ''
      }
      after.push(temp)
    }
    return after
  }

  /**
   * 去搜索
   * @param keyword
   */
  handleSearch(keyword: string) {
    this.mutationGoodsList(this.data.lines)
    this.$Router.push({
      name: 'invXFApplySkuSearch',
      params: {
        from: 'edit',
        value: keyword,
        type: this.data.type,
        shopId: this.data.counterShopId
      }
    })
  }

  /**
   * 处理商品扫码 - 溯源码添加
   * @param sku 商品
   */
  async handleSkuScan(sku: InvXFApplyLine, keyword: string) {
    const existIndex = this.data.lines.findIndex((line) => {
      return line.goods.uuid === sku.goods.uuid
    })
    if (existIndex >= 0) {
      // 如果是气调商品
      if (this.isHasTraceCodes(sku)) {
        const target = this.data.lines[existIndex]
        try {
          this.$showLoading({ delayTime: 200 })
          const traceCode = await this.analyzeTraceCode(keyword, sku.goods.uuid)
          if (!traceCode.traceCode) {
            return this.$showToast({ title: '商品已添加，请勿重复扫描', icon: 'warning' })
          }

          // 批次号是否已存在
          const existVbNum = target.lines.findIndex((item) => {
            return item.vbNum === traceCode.vbNum
          })
          if (existVbNum >= 0) {
            target.lines[existVbNum].qty++
          } else {
            if (!target.batches) {
              try {
                this.$showLoading({ delayTime: 200 })
                const sku = await this.getGoodsByCode(target.goods.code)
                if (sku.length) {
                  target.batches = sku[0].lines
                } else {
                  target.batches = []
                }
                this.$hideLoading()
              } catch (error) {
                this.$hideLoading()

                this.$showToast({
                  title: error.msg,
                  icon: 'error'
                })
              }
            }

            const batchInfo = target.batches!.find((batch) => {
              return batch.vbNum === traceCode.vbNum
            })

            target.lines.push({
              // 批号
              vbNum: traceCode.vbNum || '',
              // 生产日期
              prdDate: traceCode.pdate ? DateUtil.format(new Date(traceCode.pdate.replace(/-/g, '/')), 'yyyy-MM-dd') : null,
              // 到效期
              validDate: traceCode.pdate ? DateUtil.addDay(new Date(traceCode.pdate.replace(/-/g, '/')), target.validPeriod || 0) : '',
              // 申请数量
              qty: 1,
              // 批准数量
              approveQty: null,
              // 溯源码
              traceCode: null,
              // 录入方式:0-扫码,1-手工
              traceCodeEnterMode: 0,
              // 库存数量
              invQty: batchInfo ? batchInfo.invQty : null
            })
          }
          const result = this.handleCalcSku(target)
          this.$set(this.data.lines, existIndex, result)
        } catch (error) {
          const errMsg = (error as any).msg
          this.$showToast({ title: errMsg, icon: 'warning' })
        }
      }
    } else {
      let line = { ...sku }
      if (this.isHasTraceCodes(sku)) {
        try {
          this.$showLoading({ delayTime: 200 })

          const traceCode = await this.analyzeTraceCode(keyword, line.goods.uuid)

          if (traceCode.traceCode) {
            const sku = await this.getGoodsByCode(line.goods.code)
            if (sku.length) {
              line.batches = sku[0].lines
            } else {
              line.batches = []
            }
            const batchInfo = line.batches!.find((batch) => {
              return batch.vbNum === traceCode.vbNum
            })
            line.lines = [
              {
                // 批号
                vbNum: traceCode.vbNum || '',
                // 生产日期
                prdDate: traceCode.pdate ? DateUtil.format(new Date(traceCode.pdate.replace(/-/g, '/')), 'yyyy-MM-dd') : null,
                // 到效期
                validDate: traceCode.pdate ? DateUtil.addDay(new Date(traceCode.pdate.replace(/-/g, '/')), line.validPeriod || 0) : '',
                // 申请数量
                qty: 1,
                // 批准数量
                approveQty: null,
                // 溯源码
                traceCode: null,
                // 录入方式:0-扫码,1-手工
                traceCodeEnterMode: 0,
                invQty: batchInfo ? batchInfo.invQty : null
              }
            ]
          }
          line = this.handleCalcSku(line)
        } catch (error) {
          console.log(error)
          const errMsg = (error as any).msg
          this.$showToast({ title: errMsg, icon: 'warning' })
        }
      } else {
        line.qty = 0
        line.qpcQty = 0
        line.total = 0
        line.rtlTotal = 0
      }
      this.data.lines.unshift(line)
    }
  }

  getGoodsByCode(code: string) {
    return new Promise<InvXFApplyGoods[]>((resolve, reject) => {
      const query = new QueryRequest()
      query.pageSize = 1
      query.conditions = [{ operation: 'code:=', parameters: [code] }]
      InvXFApplyApi.queryGoods(query)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  // 是否是气调商品（周黑鸭）
  isHasTraceCodes(sku: InvXFApplyLine) {
    return sku.useTraceCode == 1 && sku.traceCodeRule == 3
  }

  /**
   * 解析溯源码
   * @param code
   */
  analyzeTraceCode(code: string, gid: string) {
    return new Promise<TraceCodeAnalyzeResult>((resolve, reject) => {
      CommonsApi.analyzeTraceCode({ barCode: code, gduuid: gid })
        .then((resp) => {
          if (resp.data) {
            resolve(resp.data)
          } else {
            reject({ msg: '商品码识别失败' })
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  // PDA扫码回调事件
  async doScanAfter(scanWord) {
    if (this.isLoading) {
      this.$showToast({ title: '正在加载，请稍后重试~' })
      return
    }
    if (scanWord) {
      this.scanCodeJump(scanWord)
    } else {
      this.mutationGoodsList([])
    }
  }

  /**
   * 选择商品
   */
  doSelectGoods() {
    if (!this.data.counterShopId) {
      this.$showToast({ title: '请先选择门店' })
      return
    }
    if (this.tabType === 'good') {
      uni.setStorage({
        key: 'invXFApplyGoodsList',
        data: this.data.lines,
        success: () => {
          uni.navigateTo({ url: `/pagesShopManage/invXFApply/InvXFApplySkuSelect?type=${this.data.type}&shopId=${this.data.counterShopId}` })
        }
      })
    } else {
      uni.setStorage({
        key: 'invXFApplyPkaList',
        data: this.data.packLines,
        success: () => {
          uni.navigateTo({
            url: `/pagesShopManage/invXFApply/InvXFApplyPkaSearch?from=edit&type=${this.data.type}&shopId=${this.data.counterShopId}`
          })
        }
      })
    }
  }

  /**
   * 查询商品
   */
  doSearchGoods() {
    if (!this.data.counterShopId) {
      this.$showToast({ title: '请先选择门店' })
      return
    }
    uni.setStorage({
      key: 'invXFApplyGoodsList',
      data: this.data.lines,
      success: () => {
        uni.navigateTo({ url: `/pagesShopManage/invXFApply/InvXFApplySkuSearch?from=edit&type=${this.data.type}&shopId=${this.data.counterShopId}` })
      }
    })
  }

  /**
   * 删除商品
   */
  doDelete(skuLine: InvXFApplyLine) {
    this.data.lines = this.data.lines.filter((line) => line.goods.uuid !== skuLine.goods.uuid)
  }

  /**
   * 删除包材
   */
  doDeletePka(pkaLine: InvXFApplyPackLine) {
    this.data.packLines = this.data.packLines.filter((line) => line.pack.uuid !== pkaLine.pack.uuid)
  }

  /**
   * 显示备注弹出框
   */
  doNoteShow() {
    this.$refs.note.open()
  }

  /**
   * 关闭备注弹出框
   */
  doNoteClose() {
    this.$refs.note.close()
  }

  /**
   * 备注弹框确认事件
   */
  doNoteConfirm(note: string) {
    this.data.note = note
  }

  /**
   * 保存单据
   */
  doSave() {
    if (this.validate) {
      const request: any = this.isEdit ? InvXFApplyApi.modify : InvXFApplyApi.create
      const requireBody = this.isEdit ? this.exchangeToInvXFApplyModification(this.data) : this.exchangeToInvXFApplyCreation(this.data)
      this.$showLoading()
      request(requireBody)
        .then(() => {
          if (!this.isEdit) {
            this.isEdit = true
          }
          this.isRenovote = true
          this.$hideLoading()
          this.$showToast({ icon: 'success', title: '保存成功' })
        })
        .catch((e) => {
          this.$hideLoading()
          this.$showModal({
            title: '保存失败',
            content: `失败原因:${e.msg}`,
            showCancel: true,
            confirmText: '重试',
            success: (action) => {
              if (action.confirm) {
                this.doSave()
              }
            }
          })
        })
    }
  }

  /**
   * 提交单据
   */
  doSubmit() {
    if (this.validate) {
      if (this.doValidateBatchZero()) {
        return this.$showModal({
          title: '提示',
          content: `存在商品数量为空或0，请修改后提交`,
          confirmText: '知道了',
          showCancel: false,
          success: () => {}
        })
      }
      this.$showModal({
        title: '提示',
        content: `确定提交${InvXFApplyFilter.typeName(this.data.type!)}申请单吗`,
        showCancel: true,
        confirmText: '提交',
        success: (action) => {
          if (action.confirm) {
            this.onSubmit()
          }
        }
      })
    }
  }

  /**
   * 校验是否存在效期行数量为0
   * @returns
   */
  doValidateBatchZero() {
    if (this.data.lines.length) {
      for (let i = 0; i < this.data.lines.length; i++) {
        const line = this.data.lines[i]
        if (line.lines.length) {
          for (let j = 0; j < line.lines.length; j++) {
            const batch = line.lines[j]
            if (!batch.qty) {
              return true
            }
          }
        }
      }
    }
    return false
  }

  onSubmit() {
    const request: any = this.isEdit ? InvXFApplyApi.submit : InvXFApplyApi.createAndSubmit
    const requireBody = this.isEdit ? this.exchangeToInvXFApplyModification(this.data) : this.exchangeToInvXFApplyCreation(this.data)
    this.$showLoading()
    request(requireBody)
      .then(() => {
        this.isRenovote = true
        this.$hideLoading()
        this.$showToast({ icon: 'success', title: '提交成功' })
        setTimeout(() => {
          uni.redirectTo({ url: '/pagesShopManage/invXFApply/InvXFApplyDetail?id=' + this.data.billId })
        }, 1500)
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showModal({
          title: '提交失败',
          content: `失败原因:${e.msg}`,
          showCancel: true,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.onSubmit()
            }
          }
        })
      })
  }

  /**
   * 转类型：before:InvXFApply,after:InvXFApplyCreation
   */
  exchangeToInvXFApplyCreation(before: InvXFApply) {
    const after: InvXFApplyCreation = new InvXFApplyCreation()
    after.billId = before.billId
    after.starterId = this.store.id || ''
    after.starterName = this.store.name || ''
    after.type = before.type
    after.counterShopId = before.counterShopId
    after.counterShopNo = before.counterShopNo
    after.counterShopName = before.counterShopName
    after.note = before.note
    after.deliveryType = before.deliveryType
    if (before.lines && before.lines.length) {
      before.lines.forEach((line) => {
        after.lines.push({ ...line })
      })
    }
    if (before.packLines && before.packLines.length) {
      before.packLines.forEach((line) => {
        after.packLines.push({ ...line })
      })
    }
    return after
  }

  /**
   * 转类型：before:InvXFApply,after:InvXFApplyModification
   */
  exchangeToInvXFApplyModification(before: InvXFApply) {
    const after: InvXFApplyModification = new InvXFApplyModification()
    after.billId = before.billId
    after.counterShopId = before.counterShopId
    after.counterShopNo = before.counterShopNo
    after.counterShopName = before.counterShopName
    after.note = before.note
    after.deliveryType = before.deliveryType
    if (before.lines && before.lines.length) {
      before.lines.forEach((line) => {
        after.lines.push({ ...line })
      })
    }
    if (before.packLines && before.packLines.length) {
      before.packLines.forEach((line) => {
        after.packLines.push({ ...line })
      })
    }
    return after
  }

  // 配送模式弹框确认事件
  doReasonConfirm(reason: CodeName) {
    this.data.deliveryType = Number(reason.code)
    this.doReasonClose()
  }

  // 关闭配送模式弹出框
  doReasonClose() {
    this.$refs.reason.close()
  }

  //打开配送模式选择弹出框
  doDeliveryTypeShow() {
    this.$refs.reason.open()
  }

  /**
   * 删除效期行
   */
  handleRemoveBatch(i: number, batch: InvXFApplyBatchBase) {
    const index = this.data.lines[i].lines.findIndex((item) => {
      return item.vbNum === batch.vbNum
    })
    this.data.lines[i].lines.splice(index, 1)
    const line = this.handleCalcSku(this.data.lines[i])
    this.$set(this.data.lines, i, line)
  }

  /**
   * 编辑效期时间
   * @param i 商品下标
   * @param j 原因下标
   * @param batch 效期行
   */
  handleEditBatchDate(i: number, batch: InvXFApplyBatchBase) {
    this.$showPicker({
      currentDate: batch.prdDate || '',
      endDate: DateUtil.format(new Date(), 'yyyy/MM/dd'),
      type: 'date',
      success: async (res) => {
        if (res.date) {
          const date = res.date
          const vbNum = DateUtil.format(new Date(date.replace(/-/g, '/')), this.batchNoFormatter)
          // 批次号是否已存在
          const existVbNum = this.data.lines[i].lines.find((item) => {
            return item.vbNum === vbNum
          })
          if (vbNum !== batch.vbNum && existVbNum) {
            return this.$showToast({ icon: 'warning', title: '批次号已存在' })
          }
          const index = this.data.lines[i].lines.findIndex((item) => {
            return item.vbNum === batch.vbNum
          })

          batch.vbNum = vbNum
          batch.prdDate = DateUtil.format(new Date(date.replace(/-/g, '/')), 'yyyy-MM-dd')
          batch.validDate = DateUtil.addDay(new Date(batch.prdDate.replace(/-/g, '/')), this.data.lines[i].validPeriod || 0)

          if (!this.data.lines[i].batches) {
            try {
              this.$showLoading({ delayTime: 200 })
              const sku = await this.getGoodsByCode(this.data.lines[i].goods.code)
              if (sku.length) {
                this.data.lines[i].batches = sku[0].lines
              } else {
                this.data.lines[i].batches = []
              }
              this.$hideLoading()
            } catch (error) {
              this.$hideLoading()
              this.$showToast({
                title: error.msg,
                icon: 'error'
              })
            }
          }

          const batchInfo = this.data.lines[i].batches!.find((batch) => {
            return batch.vbNum === vbNum
          })
          batch.invQty = batchInfo ? batchInfo.invQty : null

          this.$set(this.data.lines[i].lines, index, batch)
        }
      },
      fail: () => {}
    })
  }

  /**
   * 新增效期行
   * @param i 商品下标
   */
  handleAddBatch(i: number) {
    this.$showPicker({
      type: 'date',
      currentDate: DateUtil.format(new Date(), 'yyyy-MM-dd'),
      endDate: DateUtil.format(new Date(), 'yyyy/MM/dd'),
      success: async (res) => {
        if (res.date) {
          const date = res.date
          const line = new InvXFApplyBatchBase()
          line.vbNum = DateUtil.format(new Date(date.replace(/-/g, '/')), this.batchNoFormatter)
          line.prdDate = DateUtil.format(new Date(date.replace(/-/g, '/')), 'yyyy-MM-dd')
          line.validDate = DateUtil.addDay(new Date(line.prdDate.replace(/-/g, '/')), this.data.lines[i].validPeriod || 0)
          line.qty = 0
          // 批次号是否已存在
          const existVbNum = this.data.lines[i].lines.find((item) => {
            return item.vbNum === line.vbNum
          })
          if (!existVbNum) {
            if (!this.data.lines[i].batches) {
              try {
                this.$showLoading({ delayTime: 200 })
                const sku = await this.getGoodsByCode(this.data.lines[i].goods.code)
                if (sku.length) {
                  this.data.lines[i].batches = sku[0].lines
                } else {
                  this.data.lines[i].batches = []
                }
                this.$hideLoading()
              } catch (error) {
                this.$hideLoading()
                this.$showToast({
                  title: error.msg,
                  icon: 'error'
                })
              }
            }

            const batchInfo = this.data.lines[i].batches!.find((batch) => {
              return batch.vbNum === line.vbNum
            })
            line.invQty = batchInfo ? batchInfo.invQty : null
            this.data.lines[i].lines.push(line)
          } else {
            this.$showToast({ icon: 'warning', title: '批次号已存在' })
          }
        }
      },
      fail: () => {}
    })
  }

  /**
   * 编辑效期行数量
   */
  handleEditBatchQty(i: number, batch: InvXFApplyBatchBase) {
    // const line = { ...this.data.lines[i] }
    // line.lines = batches
    // const result = this.handleCalcSku(line)
    // this.$set(this.data.lines, i, result)
    this.$refs.edit.open(i, this.data.lines[i], batch)
  }

  /**
   * 编辑包材行数量
   */
  handlePkaEditBatchQty(i: number, batch: InvXFApplyPackLineBase) {
    this.$refs.pkaEdit.open(i, this.data.packLines[i], batch)
  }

  /**
   * 编辑效期行数量确认
   * @param i 商品下标
   * @param j 批次下标
   * @param batch 效期行
   */
  handleEditBatchConfirm(i: number, qty: number, batch: Nullable<InvXFApplyBatchBase>) {
    const line = { ...this.data.lines[i] }
    if (batch) {
      const existIndex = line.lines.findIndex((item) => {
        return item.vbNum === batch.vbNum
      })
      line.lines[existIndex] = batch
      const result = this.handleCalcSku(line)
      this.$set(this.data.lines, i, result)
    } else {
      line.qty = qty
      this.doNumberChange(qty, line.goods.uuid)
    }
  }

  /**
   * 编辑包材数量确认
   * @param i 包材下标
   * @param j 批次下标
   * @param batch 效期行
   */
  handlePkaEditBatchConfirm(i: number, qty: number, batch: Nullable<InvXFApplyPackLineBase>) {
    const line = { ...this.data.packLines[i] }
    line.qty = qty
    this.doPkaNumberChange(qty, line.pack.uuid)
  }

  /**
   * 计算商品qpcqty和total
   */
  handleCalcSku(target: InvXFApplyLine) {
    let lineQty: number = 0
    let lineQpcQty: number = 0
    let lineTotal: number = 0
    let lineRtlTotal: number = 0

    for (let index = 0; index < target.lines.length; index++) {
      lineQty = lineQty + target.lines[index].qty
      lineQpcQty = lineQpcQty + target.lines[index].qty.divide(target.goods.qpc).scale(this.qtyScale)
      const total = target.goods.singlePrice.multiply(target.lines[index].qty)
      const rtlTotal = (target.rtlPrc || 0).multiply(target.lines[index].qty)
      lineTotal = Number(lineTotal).add(total)
      lineRtlTotal = Number(lineRtlTotal).add(rtlTotal)
    }
    target.qty = lineQty.scale(this.qtyScale)
    target.qpcQty = parseFloat(lineQpcQty.toString()).scale(this.qtyScale)
    target.total = lineTotal.scale(2)
    target.rtlTotal = lineRtlTotal.scale(2)
    return target
  }

  /**
   * 调拨模式计算
   *
   */
  async invxfmodeCalc() {
    const body = new InvXFModeCalc()
    if (this.data.type === 'decrease') {
      body.fromShopId = this.store?.id || ''
      body.toShopId = this.data.counterShopId
    } else {
      body.fromShopId = this.data.counterShopId
      body.toShopId = this.store?.id || ''
    }
    await InvXFApplyApi.invxfmodeCalc(body)
      .then((res) => {
        if (res.data) {
          this.allowModifyPrice = res.data.toString()
        }
      })
      .catch((error) => {
        this.$showToast({ icon: 'none', title: error.msg })
      })
  }

  /**
   * 修改调拨价售价
   */
  modifyTransferPrice(i: number, item: InvXFApplyLine) {
    this.goodsIndex = i
    this.modifyTranferLine = { ...item }
    const modifyPricePop: any = this.$refs.modifyPricePop
    modifyPricePop.open()
    this.showModify = true
  }

  /**
   * 取消修改
   */
  modifyCancel() {
    const modifyPricePop: any = this.$refs.modifyPricePop
    modifyPricePop.close()
    this.showModify = false
  }

  /**
   * 确认修改
   */
  modifyConfirm(value) {
    this.showList[this.goodsIndex].goods.singlePrice = Number(value)
    this.showList[this.goodsIndex].total = Number(value).multiply(this.showList[this.goodsIndex].qty)
    this.showList[this.goodsIndex].goods.price = Number(value).multiply(this.showList[this.goodsIndex].goods.qpc).scale(this.qtyScale)
    const modifyPricePop: any = this.$refs.modifyPricePop
    modifyPricePop.close()
    this.showModify = false
  }

  /**
   * tab切换
   */
  getTab(type) {
    console.log('TYPE', type)
    this.tabType = type
  }
}
