import { Component, Watch } from 'vue-property-decorator'
import WeatherSearchBar from './cmp/WeatherSearchBar.vue'
import SideBar from '@/pages/cmp/SideBar.vue'
import WeatherDialog from './cmp/WeatherDialog.vue'
import CollapseBar from '@/pages/cmp/CollapseBar.vue'
import GoodsListItem from './cmp/GoodsListItem.vue'
import GoodsAction from './cmp/GoodsAction.vue'
import Weather from '@/model/data/Weather'
import GoodsCategory from '@/model/data/GoodsCategory'
import QueryRequest from '@/model/base/QueryRequest'
import DataApi from '@/network/data/DataApi'
import RequireApplyCreationLine from '@/model/requireApply/RequireApplyCreationLine'
import DateUtil from '@/utils/DateUtil'
import RequireApplyApi from '@/network/requireApply/RequireApplyApi'
import AvgSaleDialog from './cmp/AvgSaleDialog.vue'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import RequireApplyStoreInfo from '@/model/requireApply/RequireApplyStoreInfo'
import InventoryDiff from './cmp/InventoryDiff.vue'
import swipeMix from '@/components/hd-swipe-action/swipeMix'
import { mixins } from 'vue-class-component'
import AppOpenCheckTabCategoryDTO from '@/model/opencheck/AppOpenCheckTabCategoryDTO'
import AppRequireApplyDraftLineQueryDTO from '@/model/requireApply/draft/AppRequireApplyDraftLineQueryDTO'
import AppRequireApplyDraftDTO from '@/model/requireApply/draft/AppRequireApplyDraftDTO'
import RequireOrderApi from '@/network/requireOrder/RequireOrderApi'
import IllDraftKey from '@/model/default/illDraftKey'
import illDraftLineKey from '@/model/default/illDraftLineKey'
import CheckPagination from './cmp/CheckPagination.vue'
import PagePicker from '@/pagesSE/cmp/PagePicker.vue'
import { RequireApplyType } from '@/model/requireApply/RequireApplyType'
import SkeletonPage from './cmp/Skeleton.vue'
import GoodsSkeleton from './cmp/GoodsSkeleton.vue'
import { Action, State } from 'vuex-class'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import AppRequireApplyTabCategoryFilterDTO from '@/model/requireApply/draft/AppRequireApplyTabCategoryFilterDTO'
import RequireApplyCreationByDraft from '@/model/requireApply/draft/RequireApplyCreationByDraft'
import { RoundType } from '@/model/requireApply/RoundType'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import RequireApplySubmitInvalidDtl from '@/model/requireApply/draft/RequireApplySubmitInvalidDtl'
import RequireApplyStoreOrderModeRule from '@/model/requireApply/RequireApplyStoreOrderModeRule'
import CommonUtil from '@/utils/CommonUtil'
import { debounce } from 'ts-debounce'
import AppRequireApplySubBaseLineDTO from '@/model/requireApply/draft/AppRequireApplySubBaseLineDTO'
import PreparePay from './cmp/PreparePay.vue'
import { RequireApplyPaymentState } from '@/model/requireApply/RequireApplyPaymentState'
import StoreDpsAccountInfo from '@/model/requireApply/StoreDpsAccountInfo'
import { RequireApplyState } from '@/model/requireApply/RequireApplyState'
import AppSysConfigApi from '@/network/AppSysConfigApi/AppSysConfigApi'
import Store from '@/model/store/Store'
import UseQueues from '@/components/hd-popover/UseQueues'
import AppRequireApplyDraftLineKeys from '@/model/requireApply/AppRequireApplyDraftLineKeys'
import ID from '@/model/default/ID'
import RequireApplyBeforeSubmitValidator from '@/model/requireApply/RequireApplyBeforeSubmitValidator'

@Component({
  components: {
    WeatherSearchBar,
    SideBar,
    WeatherDialog,
    CollapseBar,
    GoodsListItem,
    GoodsAction,
    AvgSaleDialog,
    InventoryDiff,
    CheckPagination,
    PagePicker,
    SkeletonPage,
    GoodsSkeleton,
    PreparePay
  }
})
export default class RequireGoodsEditSearch extends mixins(swipeMix, BroadCast, UseQueues) {
  @State('isOrderHasNotSave') isOrderHasNotSave: boolean // 是否提示数据未保存
  @Action('isOrderHasNotSave') actionHasNotSave // 是否提示数据未保存
  @State('optionList') optionList: ModuleOption[] //店务配置列表
  @State('requireApplyStoreInfo') storeInfo //门店叫货配置
  @Action('requireApplyStoreInfo') actionStoreInfo //门店叫货配置
  @State('store') store: Nullable<Store> // 门店信息
  @Action('selfPickUp') actionSelfPickUp //是否自提
  @State('selfPickUp') curSelfPickUp //是否自提
  $refs: any
  // pageStatus 页面状态（是否搜索）
  // 1.normal:正常状态未搜索；2.searching:搜索条聚焦，但是并未搜索；
  // 3.resultNone:搜索结果为空；4.resultNormal:有搜索结果不为空
  pageStatus: string = 'searching'
  isEdit: boolean = false // 要换单编辑/新增状态
  isCopy: boolean = false // 是否为再来一单
  showAvg: boolean = false //是否显示销量统计弹窗
  requireBody: RequireApplyCreationByDraft = new RequireApplyCreationByDraft() // 请求体，与isEdit对应，1.false为create：RequireApplyCreation；2.true为modify：RequireApplyModification
  weatherList: Weather[] = [] // 天气列表
  goodsSearchExtList: AppRequireApplyDraftLineQueryDTO[] = [] // 搜索商品列表
  finished: boolean = false // 是否加载完成
  isLoading: boolean = true // 是否在加载
  searchPage: any = {
    pageSize: 20, // 每页大小
    pageNum: 0, // 页码
    finished: false, // 是否加载完成
    isLoading: false, // 是否在加载
    keyWord: '' // 搜索关键字
  }
  avgData: any = {} // 平均日销数据
  RequireOrderStoreInfo: RequireApplyStoreInfo = new RequireApplyStoreInfo() //门店叫货规则
  strictControl: Nullable<boolean> = null // 是否强制订货时间可订
  showGdImage: boolean = false //展示图片
  isFinish: boolean = false //店铺规则获取完成
  goodsQueryFinish: boolean = false //商品分类获取完成
  goodsUuid: string = '' //当前选中商品uuid
  currentNote: string = '' //当前选中商品备注
  shelveNote: string = '' //当前选中商品上架备注
  disableList: AppRequireApplyDraftLineQueryDTO[] = [] //可订数不足商品列表
  deleteList: AppRequireApplyDraftLineQueryDTO[] = [] //已作废商品列表
  showList: AppRequireApplyDraftLineQueryDTO[] = [] //弹窗展示商品列表
  oldGoodsNum: number = 0 //库存不足提交前商品品项数
  oldGoodsPrice: number = 0 //库存不足提交前商品总金额
  validateLevel4CaseQty: number = 0 // 叫货包装数整规格校验级别:0-不提示(默认)，1-仅提示，2-不允许保存
  goodsPrice: number = 0 //商品总金额
  goodsNum: number = 0 //商品总种类
  totalQpcQty: number = 0 //总规格数量
  total: number = 0 //总数量
  scrollTop: number = 0 // scrollview显示顶部
  curpage: number = 1 // 强制回当前页1
  eachPage: number = 200 // 分页器每页展示几条数据
  hasMore: boolean = true // 是否有更多数据
  showPageBottom: boolean = true // 分页器选择页数时，隐藏确认按钮
  currentPagination: boolean = false //防止分页组件销毁
  isHasNotSave: boolean = false //单据数据未保存
  isHasNotSaveDraft: boolean = false //草稿未保存
  isFromCart: boolean = false // 是否来自购物车页面
  isCartSubmit: boolean = false // 是否是购物车页面的提交操作
  isCartSave: boolean = false // 是否是购物车页面的保存操作
  isDelete: boolean = false //是否正在删除，防止重复调用删除接口
  draftId: string = '' //草稿id
  isUpSort: boolean = true //更新商品分类
  startPageY: number = 0 //滑动开始位置
  endPageY: number = 0 //滑动结束位置
  isBottom: boolean = false //滚动到底部
  isTop: boolean = false //滚动到顶部
  startTopPageY: Nullable<number> = null //顶部下拉滑动开始位置
  endTopPageY: number = 0 //顶部下拉滑动结束位置
  categoryScrollTop: number = 0 //一级类别滚动条位置
  isChangeCategory: boolean = false //正在切换类别
  type: RequireApplyType = RequireApplyType.normal // 订货类型
  forecastArrivalDate: Nullable<string> = null //   预计到货时间
  expireDate: Nullable<string> = null // 要求到货日期
  showTime: boolean = true // 是否展示订货时间
  isPre: boolean = false // 预报货  （无需展示时间和订货金额）
  isPreOrder: boolean = false // 预订单
  showPreTips: boolean = false //是否显示预报货提示
  isRouteConfirm: boolean = false //准备跳转拆单确认页
  paymentSrcContext: any = {} //支付信息
  timer: any = null //查询支付结果计时器
  isNeedGetPay: boolean = false //复制后需要支付结果
  isRenovote: boolean = false // 是否需要刷新
  noPickAmt: number = 0 // 未提货金额
  cartBillId: string = '' // 购物车id
  doScanAfter = debounce(this.doScanAfterLimit, 300, { isImmediate: false }) // 防抖
  doSaveAfter = debounce(this.doSave, 300, { isImmediate: false }) // 防抖
  doSubmitAfter = debounce(this.doSubmit, 300, { isImmediate: false }) // 防抖

  // 是否是钉钉
  get isDingTalk() {
    let value: boolean = false
    // #ifdef  MP-DINGTALK
    value = true
    // #endif
    // #ifndef  MP-DINGTALK
    value = false
    // #endif
    return value
  }

  /**
   * 是否自动计算辅料价格
   * 当accessoryManage为1时，自动计算辅料价格，并将辅料数据计算入主商品的价格中，为2时直接展示辅料商品，用户自行添加数量，不计算入主商品的价格金额中
   */
  get autoCalcAcc() {
    return this.accessoryManage === 1
  }

  /**
   * 查询商品价格时要传的配货日期(偏移天数取 sndDateoffsetDay;)
   */
  get alcDate() {
    if (this.type === RequireApplyType.advance) {
      return this.expireDate || DateUtil.format(new Date(), 'yyyy-MM-dd')
    } else {
      const sndDateOffsetDay = this.currentRule ? this.sndDateOffsetDay : 1
      const now = new Date()
      return DateUtil.format(now.setDate(now.getDate() + this.alcDay + sndDateOffsetDay), 'yyyy-MM-dd')
    }
  }

  // 最小提前叫货天数,默认为7天,不能为负数
  get minAdvanceOrderDay() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosRequireApply
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && 'minAdvanceOrderDay' in moduleConfig[0].options) {
      return moduleConfig[0].options.minAdvanceOrderDay
    }

    return true
  }

  /**
   * 辅料分类前缀
   */
  get accessoryGoodsCategoryNamePrefix() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosRequireApply
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && 'accessoryGoodsCategoryNamePrefix' in moduleConfig[0].options) {
      return moduleConfig[0].options.accessoryGoodsCategoryNamePrefix
    }
    return '-'
  }

  /**
   * 是否开启自动保存
   */
  get enableAutoSave() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosRequireApply
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && 'enableAutoSave' in moduleConfig[0].options) {
      return moduleConfig[0].options.enableAutoSave === '1'
    }
    return true
  }

  /**
   * 配送天数
   */
  get alcDay() {
    return this.RequireOrderStoreInfo.alcDay || 0
  }

  // 门店辅料管理 为1时自动计算辅料，并将辅料数据计算入主商品的价格中，为2时直接展示辅料商品，用户自行添加数量，不计算入主商品的价格金额中
  get accessoryManage() {
    return this.RequireOrderStoreInfo.accessoryManage || 0
  }

  /**
   * h6配置的当前订货类型的规则
   */
  get currentRule() {
    let rule: Nullable<RequireApplyStoreOrderModeRule> = null
    if (this.RequireOrderStoreInfo.orderModeRules && this.RequireOrderStoreInfo.orderModeRules.length) {
      rule =
        this.RequireOrderStoreInfo.orderModeRules.find((rule) => {
          return Number(rule.orderMode) == Number(RequireApplyType.stringToMode(this.type))
        }) ||
        this.RequireOrderStoreInfo.orderModeRules.find((rule) => {
          return Number(rule.orderMode) == -1
        }) ||
        null
    }
    return rule
  }

  get gtyLowLimitlevel() {
    return this.RequireOrderStoreInfo.gtyLowLimitlevel
  }

  get gtyHighLimitlevel() {
    return this.RequireOrderStoreInfo.gtyHighLimitlevel
  }

  /**
   * 配送天数
   */
  get sndDateOffsetDay() {
    let sndDateOffsetDay: number = 0
    if (this.currentRule) {
      sndDateOffsetDay = this.currentRule.sndDateOffsetDay
    } else {
      sndDateOffsetDay = 0
    }
    return sndDateOffsetDay
  }

  //总页数
  get totalPage() {
    return Math.ceil(this.total / this.eachPage)
  }

  // 获取按钮权限
  get btnPermission() {
    const btnPermission = {
      save: false,
      submit: false
    }

    if (this.isPre) {
      btnPermission.save = PermissionMgr.hasPermission(Permission.preOrderEdit)
      btnPermission.submit = PermissionMgr.hasPermission(Permission.preOrderEdit)
    } else {
      if (PermissionMgr.hasPermission(Permission.requireApplyEdit)) {
        btnPermission.save = true
      }
      if (PermissionMgr.hasPermission(Permission.requireApplySubmit) && this.showSubmit) {
        btnPermission.submit = true
      }
    }

    return btnPermission
  }

  /**
   * 店务配置是否可以提交
   */
  get showSubmit() {
    let showSubmit: boolean = true
    if (this.currentRule) showSubmit = this.currentRule.alwStoreSubmit
    return showSubmit
  }

  //隐藏库存数据
  get hideInvQty() {
    return PermissionMgr.hasPermission(Permission.requireApplyHideInvQty)
  }

  /**
   * 商品滚动scroll-view类
   */
  get skuScrollClass() {
    const skuScrollClass: string[] = ['goods-scroll']
    if (this.showTime && !this.isPreOrder) {
      skuScrollClass.push('goods-scroll--long-hastime')
    } else if (this.showTime && this.isPreOrder) {
      skuScrollClass.push('goods-scroll--long-order')
    } else if (!this.showTime && this.isPreOrder) {
      skuScrollClass.push('goods-scroll--long-order-notime')
    } else {
      skuScrollClass.push('goods-scroll--long')
    }

    return skuScrollClass.join(' ')
  }

  // 商品分类滚动
  get sidebarScrollClass() {
    const sidebarScrollClass: string[] = ['side-scroll']
    if ((this.showPreTips && !this.isPreOrder) || (!this.isPre && this.showTime && !this.isPreOrder)) {
      sidebarScrollClass.push('side-scroll--hastime')
    } else if (!this.isPre && this.showTime && this.isPreOrder) {
      sidebarScrollClass.push('side-scroll--order')
    } else if (!this.isPre && !this.showTime && this.isPreOrder) {
      sidebarScrollClass.push('side-scroll--order--notime')
    }
    return sidebarScrollClass.join(' ')
  }

  // 提交和保存按钮是否可以点击
  get clickAble() {
    if (this.validate()) {
      return true
    } else {
      return false
    }
  }

  /**
   * 订货开始时间
   */
  get startTime() {
    let startTime: Nullable<string> = null
    if (this.currentRule && this.currentRule.beginTime) {
      startTime = this.currentRule.beginTime
    }
    return startTime
  }

  /**
   * 订货结束时间
   */
  get endTime() {
    let endTime: Nullable<string> = null
    if (this.currentRule && this.currentRule.beginTime) {
      endTime = this.currentRule.endTime
    }
    return endTime
  }

  /**
   * 订货类型名称
   */
  get orderTypeName() {
    return RequireApplyType.string(this.type)
  }

  // 库存金限制余额，门店限制级别使用
  get balance() {
    return (this.RequireOrderStoreInfo && this.RequireOrderStoreInfo.invTotalLmtConfig && this.RequireOrderStoreInfo.invTotalLmtConfig.balance) || 0
  }

  // 资金账号信息
  get dpsAccountInfo() {
    return (this.RequireOrderStoreInfo && this.RequireOrderStoreInfo.dpsAccountInfo) || new StoreDpsAccountInfo()
  }

  // 是否显示余额
  get showBalance() {
    return this.dpsAccountInfo && this.dpsAccountInfo.verifyDpsAccount
  }

  //可订货金额
  get allAccount() {
    let amount = 0

    amount =
      Number(amount) +
      Number(this.dpsAccountInfo!.cloudFundTotal) +
      Number(this.dpsAccountInfo!.creditTotal) +
      Number(this.dpsAccountInfo!.franchiseTotal) -
      Number(this.dpsAccountInfo!.usedTotal)

    return amount.scale(2)
  }

  //是否显示自提
  get showSelfPickUp() {
    const requireGoodsModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosRequireApply
        })
      : []
    if (requireGoodsModuleConfig.length > 0 && requireGoodsModuleConfig[0].options) {
      return requireGoodsModuleConfig[0].options.showSelfPickUp == '1'
    }
    return false
  }

  //是否走吱口令

  get isByZiCommand() {
    const requireGoodsModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosRequireApply
        })
      : []
    //叫货启用云资金划扣时，如果云资金余额不足的处理方式，0-报错(def)，1-尽可能划扣，不足部分走吱口令
    if (requireGoodsModuleConfig.length > 0 && requireGoodsModuleConfig[0].options) {
      return requireGoodsModuleConfig[0].options.cloudFundBalNotEnoughProcess == '1'
    }
    return false
  }

  //是否提示数据未保存

  @Watch('isHasNotSave', { immediate: true, deep: true })
  onSaveChange() {
    // #ifdef  MP-DINGTALK
    if (this.isHasNotSave && dd.canIUse('enableLeaveConfirm') && !this.enableAutoSave) {
      dd.enableLeaveConfirm({
        effect: ['back', 'close'],
        info: {
          title: '提示',
          content: '需要点击保存按钮，刚刚编辑的数据才能保存，确定需要返回？'
        }
      })
    } else {
      dd.disableLeaveConfirm({
        effect: ['back', 'close'],
        info: {
          title: '提示',
          content: '需要点击保存按钮，刚刚编辑的数据才能保存，确定需要返回？'
        }
      })
    }
    // #endif

    // #ifdef  MP-WEIXIN
    if (this.isHasNotSave && !this.enableAutoSave) {
      wx.enableAlertBeforeUnload({
        message: '需要点击保存按钮，刚刚编辑的数据才能保存，确定需要返回？'
      })
    } else {
      wx.disableAlertBeforeUnload({
        message: '需要点击保存按钮，刚刚编辑的数据才能保存，确定需要返回？'
      })
    }
    // #endif
  }

  //是否提示草稿数据未保存

  @Watch('isHasNotSaveDraft', { immediate: true, deep: true })
  onSaveDarftChange() {
    // #ifdef  MP-DINGTALK
    if (this.isHasNotSaveDraft && dd.canIUse('enableLeaveConfirm') && !this.enableAutoSave) {
      dd.enableLeaveConfirm({
        effect: ['back', 'close'],
        info: {
          title: '提示',
          content: '当前订货数据未保存，确定退出吗'
        }
      })
    } else {
      dd.disableLeaveConfirm({
        effect: ['back', 'close'],
        info: {
          title: '提示',
          content: '当前订货数据未保存，确定退出吗'
        }
      })
    }
    // #endif

    // #ifdef  MP-WEIXIN
    if (this.isHasNotSaveDraft && !this.enableAutoSave) {
      wx.enableAlertBeforeUnload({
        message: '当前订货数据未保存，确定退出吗？'
      })
    } else {
      wx.disableAlertBeforeUnload({
        message: '当前订货数据未保存，确定退出吗？'
      })
    }
    // #endif
  }

  @Watch('type', { immediate: true })
  onTypeChaneg() {
    if (this.type === RequireApplyType.advance) {
      this.showTime = false
    }
    uni.setNavigationBarTitle({
      title: RequireApplyType.string(this.type) || RequireApplyType.string(RequireApplyType.normal)
    })
  }

  onLoad(option) {
    this.RequireOrderStoreInfo = CommonUtil.copy(this.storeInfo)
    this.type = option.type || this.type
    if (this.type === RequireApplyType.pre) {
      this.isPre = true
      this.showPreTips = true
    }
    this.searchPage.keyWord = option.keyWord || ''
    this.actionHasNotSave(false)
    // 获取订单
    this.$nextTick(() => {
      let id: string = ''
      if (option && (option.id || option.draftId)) {
        id = option.id || option.draftId
      }
      if (id) {
        if (option.isEdit) {
          this.isEdit = option.isEdit === 'true' ? true : false
        }
        this.requireBody.billId = id
        if (!option.create) {
          this.getRequireApply(id)
        } else {
          this.requireBody.selfPickUp = this.curSelfPickUp || null
          if (!this.showSelfPickUp) {
            this.requireBody.selfPickUp = null
          }
          // if (this.type === RequireApplyType.advance) {
          //   this.doPickExpireDate()
          // }
          if (option.draftId) {
            this.draftId = option.draftId
            this.doGetDraft()
          } else {
            this.draftId = this.requireBody.billId
          }
          this.requireBody.orderDate = new Date()
        }
        if (this.searchPage.keyWord) {
          const queryRequest = new QueryRequest()
          this.doQueryGood(queryRequest)
        }
      }
    })
    this.getStoreInfo(false)
    this.getOption()
    this.displayUndeliveredAmount()

    // 卸载上一个购物车提交操作监听
    uni.$off('cartSubmitRequire')
    // 重新监听
    uni.$on('cartSubmitRequire', (cartBillId) => {
      this.isRenovote = true
      this.isFromCart = true
      this.isCartSubmit = true
      this.isRouteConfirm = cartBillId ? false : true
      this.cartBillId = cartBillId || ''
    })

    // 卸载上一个购物车数据变动监听
    uni.$off('updateData')
    uni.$on('updateData', (data) => {
      this.isRenovote = true
      if (data.groupPurchase) {
        this.requireBody.groupPurchase = Number(data.groupPurchase)
      }
      if (data.selfPickUp) {
        this.requireBody.selfPickUp = Number(data.selfPickUp)
      }
    })

    // 卸载上一个购物车提交操作保存
    uni.$off('cartSaveRequire')
    // 重新监听
    uni.$on('cartSaveRequire', () => {
      this.isEdit = true
      this.isRenovote = true
    })
  }

  onUnload() {
    clearTimeout(this.timer)
    this.timer = null
    this.$hidePicker()
    uni.$emit('Search', this.isRenovote)
  }

  onShow() {
    if (this.isOrderHasNotSave) {
      this.isHasNotSave = true
    } else {
      this.isHasNotSave = false
    }
    this.requireBody.note = uni.getStorageSync('requireBodyNote') || this.requireBody.note
    uni.removeStorage({
      key: 'requireNote',
      success: function (res) {},
      fail: function (res) {}
    })

    //查询支付结果
    if (this.isNeedGetPay) {
      this.doGetPayResult()
    }

    setTimeout(() => {
      //购物车提交触发
      if (this.isCartSubmit) {
        this.isCartSubmit = false
        if (this.isRouteConfirm) {
          uni.$emit('searchSubmitRequire', this.cartBillId ? this.cartBillId : this.requireBody.billId)
          uni.navigateBack({})
        } else {
          //跳转详情
          uni.$emit('searchSubmitRequire', this.cartBillId ? this.cartBillId : this.requireBody.billId)
          uni.navigateBack({})
        }
      }
      if (this.isFromCart) {
        this.isRenovote = true
        // 非首次进入才走的逻辑
        this.doGetDraft()
        this.searchPage.keyWord && this.upPageData()
      }
      this.isFromCart = false
    }, 500)
  }

  /**
   * 选择是否团购
   * @param check 是否团购
   */
  doGroupPurchase(check: boolean) {
    this.requireBody.groupPurchase = check ? 1 : 0
    // 开启自动保存配置
    if (this.enableAutoSave) {
      this.doDraftSave().then(() => {
        this.$hideLoading()
      })
    }
  }

  /**
   * 选择是否自提
   * @param check 是否自提
   */
  doSelfPickUp(check: boolean) {
    this.requireBody.selfPickUp = check ? 1 : 0
    // 开启自动保存配置
    if (this.enableAutoSave) {
      this.doDraftSave().then(() => {
        this.$hideLoading()
      })
    }
  }

  /**
   * 展示未提货金额
   */
  displayUndeliveredAmount() {
    RequireApplyApi.getUnpickBill()
      .then((res) => {
        if (res.data) {
          this.noPickAmt = res.data.aggUnSubmitTotal || 0
          this.isPreOrder = this.noPickAmt > 0 ? true : false
        }
      })
      .catch((err) => {
        this.$showToast({ icon: 'error', title: err.msg })
      })
  }
  /**
   * 选择要求到货日期
   */
  doPickExpireDate() {
    const now = new Date()
    const startDate = DateUtil.format(now.setDate(now.getDate() + (Number(this.minAdvanceOrderDay) || 7)), 'yyyy-MM-dd')
    if (!this.expireDate) {
      this.expireDate = startDate
    }
    this.searchPage.pageNum = 0
    this.$showPicker({
      title: '请选择期望到货日期',
      currentDate: this.expireDate || '',
      startDate: startDate,
      success: (res) => {
        if (res.date) {
          this.expireDate = res.date
          this.doRefreshQuery()
        } else {
          this.doRefreshQuery()
        }
      }
    })
  }

  //更新页面数据
  upPageData() {
    this.curpage = 1
    this.searchPage.pageNum = 0
    this.searchPage.finished = false
    this.searchPage.isLoading = true
    const queryRequest = new QueryRequest()
    this.goodsSearchExtList = []
    this.doQueryGood(queryRequest, true, true)
  }
  // 获取单据详情
  getRequireApply(id: string) {
    // this.$showLoading({ delayTime: 200 })
    if (this.isCopy) {
      RequireApplyApi.copy2Draft(id)
        .then((resp) => {
          if (resp.data) {
            this.draftId = resp.data.id
            this.requireBody.note = ''
            if (this.requireBody.selfPickUp !== 0 && !this.requireBody.selfPickUp) {
              this.requireBody.selfPickUp = this.curSelfPickUp || null
            }

            if (!this.showSelfPickUp) {
              this.requireBody.selfPickUp = null
            }

            if (resp.data.existsIneffectiveGoods) {
              this.$showModal({
                title: '提示',
                content: `请注意，存在部分商品由于叫货限制被系统过滤`,
                confirmText: '我知道了',
                success: (action) => {}
              })
            }
            // if (this.type === RequireApplyType.advance) {
            //   this.doPickExpireDate()
            // }
            this.doGetPage()
          } else {
            this.$hideLoading()
            this.$showModal({
              title: '提示',
              content: `复制单据失败`,
              showCancel: true,
              confirmText: '重试',
              success: (action) => {
                if (action.confirm) {
                  this.getRequireApply(id)
                }
              }
            })
          }
        })
        .catch((e) => {
          this.isUpSort = false
          this.$hideLoading()
          this.$showToast({ icon: 'error', title: e.msg })
        })
    } else {
      RequireApplyApi.get(id, 'details')
        .then((resp) => {
          if (resp.data) {
            const data = resp.data
            this.draftId = this.requireBody.billId
            this.type = resp.data.type || RequireApplyType.normal
            this.requireBody.note = data.note
            this.requireBody.groupPurchase = data.groupPurchase
            if (data.selfPickUp === 0 || data.selfPickUp) {
              this.requireBody.selfPickUp = data.selfPickUp
            } else {
              this.requireBody.selfPickUp = this.curSelfPickUp || null
            }

            if (!this.showSelfPickUp) {
              this.requireBody.selfPickUp = null
            }

            // this.forecastArrivalDate = resp.data.forecastArrivalDate
            this.expireDate = resp.data.expireDate
            this.doBeforeEdit().then((resp) => {
              this.doGetPage()
            })
          } else {
            this.$hideLoading()
          }
        })
        .catch((e) => {
          this.isUpSort = false
          this.$hideLoading()
          this.$showToast({ icon: 'error', title: e.msg })
        })
    }
  }

  //初始化页面数据

  doGetPage() {
    this.doGetDraft()
  }

  /**
   * 查询商品列表
   * @param queryRequest QueryRequest 查询条件
   * @param type 查询类型：1.query:按照商品分类码查询 2.search:根据关键词查询
   */
  doQueryGood(queryRequest: QueryRequest, showLoading: boolean = true, isKeepLoading = false) {
    const isLoading = false //临时变量保存查询接口调用结果
    if (showLoading) {
      this.$showLoading({ delayTime: 200 })
    }
    queryRequest.page = this.searchPage.pageNum
    queryRequest.pageSize = this.searchPage.pageSize
    queryRequest.fetchParts = ['category', 'reference', 'image']
    queryRequest.conditions.push({ operation: 'orderMode:=', parameters: [RequireApplyType.stringToMode(this.type)] })
    queryRequest.conditions.push({ operation: 'draftId:=', parameters: [this.draftId] })
    queryRequest.conditions.push({ operation: 'alcDate:=', parameters: [this.alcDate] })
    queryRequest.conditions.push({ operation: 'keyword:%=%', parameters: [this.searchPage.keyWord] })

    return new Promise((resolve, reject) => {
      RequireApplyApi.queryDraftGoods(queryRequest)
        .then((res) => {
          if (this.isDingTalk) {
            this.isTop = false //重置是否下拉到顶部（保证有下拉动作）
          }
          this.total = res.total || 0
          this.searchPage.pageNum++
          this.searchPage.isLoading = false
          this.searchPage.finished = !res.more
          if (res.data) {
            res.data.forEach((e) => {
              e.qpcQty = Number(e.qpcQty).toString()
            })
            this.goodsSearchExtList.push(...res.data)
          } else {
            this.goodsSearchExtList = []
          }
          if (this.goodsSearchExtList && this.goodsSearchExtList.length > 0) {
            this.pageStatus = 'resultNormal'
          } else {
            this.pageStatus = 'resultNone'
          }

          this.goodsQueryFinish = true
          if (this.isFinish) {
            if (isKeepLoading && !this.searchPage.isLoading) {
              this.$hideLoading()
            }
            if (!isKeepLoading) {
              this.$hideLoading()
            }
          }

          resolve(true)
        })
        .catch((err) => {
          if (showLoading) {
            this.$hideLoading()
          }

          this.$showToast({ title: err.msg, icon: 'error' })
          if (this.goodsSearchExtList.length > 0) {
            this.pageStatus = 'resultNormal'
          } else {
            this.pageStatus = 'resultNone'
          }
          this.searchPage.isLoading = false

          reject(false)
        })
    })
  }

  // 上拉加载更多
  doLoadMore() {
    if (this.searchPage.finished || this.searchPage.isLoading || this.goodsSearchExtList.length === 0) {
      return
    }
    if (this.goodsSearchExtList.length < this.eachPage) {
      this.hasMore = true
    } else if (this.goodsSearchExtList.length === this.eachPage) {
      this.hasMore = false
      return
    }
    this.searchPage.isLoading = true
    const queryRequest = new QueryRequest()

    this.doQueryGood(queryRequest)
  }

  // 显示天气dialog
  doWeatherShow() {
    this.$refs.weather.open()
  }
  // 关闭天气弹出框
  doWeatherClose() {
    this.$refs.weather.close()
  }

  //关闭可订数不足弹窗和商品上架备注弹窗
  doInventoryClose() {
    this.$refs.shelveNote.close()
    this.$refs.Inventory.close()
  }

  //可订数不足弹窗重新提交
  doInventoryConfirm() {
    this.doSubmit(false)
  }

  //PDA扫码回调事件
  doScanAfterLimit(scanWord) {
    if (this.searchPage.isLoading) {
      this.$showToast({ title: '正在加载，请稍后重试~' })
      return
    }
    this.doSearch(scanWord)
  }

  // 搜索输入框确认搜索事件
  doSearch(keyWord: string) {
    this.doDraftSave(false, true).then(() => {
      this.doUPGoodsExtList()
      this.searchPage.pageNum = 0
      this.searchPage.finished = false
      this.searchPage.isLoading = false
      this.searchPage.keyWord = keyWord
      const queryRequest = new QueryRequest()
      this.doQueryGood(queryRequest)
    })
  }

  /**
   *  取消
   */
  doCancel() {
    uni.navigateBack({})
  }

  doAvgShow(avgData: any) {
    this.avgData = avgData
    this.showAvg = true
    this.$refs.avg.open()
  }
  doAvgClose() {
    this.showAvg = false
    this.$refs.avg.close()
  }
  // 显示备注弹出框
  doNoteShow() {
    this.$refs.note.open()
  }
  // 关闭备注弹出框
  doNoteClose() {
    this.$refs.note.close()
  }
  // 备注弹框确认事件
  doNoteConfirm(note) {
    this.requireBody.note = note
    // 开启自动保存配置
    if (this.enableAutoSave) {
      this.doDraftSave().then(() => {
        this.$hideLoading()
      })
    }
  }

  // 显示上架备注弹出框
  doShowShelveNote(shelveNote) {
    this.shelveNote = shelveNote || ''
    this.$refs.shelveNote.open()
  }

  // 显示商品备注弹出框
  doGoodsNoteShow(goodsLine: RequireApplyCreationLine) {
    if (goodsLine.qpcQty) {
      this.goodsUuid = goodsLine.goods.uuid
      this.currentNote = goodsLine.note || ''
      this.$nextTick(() => {
        // 确保DOM更新完成
        this.$refs.goodsNote.open()
        if (this.$refs.hdNote) {
          // 将缓存的值赋给组件，而不是重置为空
          this.$refs.hdNote.noteValue = this.currentNote
        }
      })
    } else {
      this.$showToast({ title: '请先填写商品数量，再添加备注哦~' })
    }
  }
  // 关闭商品备注弹出框
  doGoodsNoteClose() {
    this.$refs.goodsNote.close()
  }
  // 商品备注弹框确认事件
  doGoodsNoteConfirm(Note) {
    // 遍历商品搜索列表 更新其备注
    for (let index = 0; index < this.goodsSearchExtList.length; index++) {
      if (this.goodsSearchExtList[index].goods.uuid === this.goodsUuid) {
        this.goodsSearchExtList[index].note = Note
        break
      }
    }

    // 开启自动保存配置
    if (this.enableAutoSave) {
      this.doDraftSave().then(() => {
        this.$hideLoading()
      })
    }
  }

  // 购物车图标点击事件
  doCartClick() {
    if (this.goodsNum > 0) {
      this.doDraftSave().then(() => {
        this.$hideLoading()
        this.isFromCart = true
        const note = this.requireBody.note || ''
        uni.setStorage({
          key: 'requireNote',
          data: note,
          success: () => {
            const params: any = {
              isEdit: this.isEdit,
              id: this.isCopy && this.enableAutoSave ? this.draftId : this.requireBody.billId,
              draftId: this.draftId,
              groupPurchase: this.requireBody.groupPurchase || 0,
              type: this.type,
              selfPickUp: this.requireBody.selfPickUp || 0
            }
            // 提前叫货 预计到货时间和期望到货时间一致
            if (this.type === RequireApplyType.advance) {
              this.forecastArrivalDate = this.expireDate
            }
            if (!this.forecastArrivalDate) {
              const now = new Date()
              this.forecastArrivalDate = DateUtil.format(now.setDate(now.getDate() + this.alcDay + this.sndDateOffsetDay), 'yyyy-MM-dd')
            }
            if (this.forecastArrivalDate) {
              params['forecastArrivalDate'] = this.forecastArrivalDate
            }
            if (this.expireDate) {
              params['expireDate'] = this.expireDate
            }
            if (this.noPickAmt) {
              params['noPickAmt'] = this.noPickAmt
            }
            this.$Router.push({
              name: 'requireGoodsCart',
              params: params
            })
          }
        })
      })
    }
  }

  // 步进器数值改变触发事件
  async doNumberChange(qpcQty: number, goodsLine: AppRequireApplyDraftLineQueryDTO) {
    this.startPageY = 0
    if (this.isChangeCategory) {
      return
    }
    if (this.isEdit) {
      this.isHasNotSave = true
      this.actionHasNotSave(true)
    }
    if (qpcQty) {
      if (!this.isEdit) {
        this.isHasNotSaveDraft = true
      }
      this.doDraftSaveLine(qpcQty, goodsLine)
      // 开启自动保存配置
      if (this.enableAutoSave) {
        const result: any = await this.doDraftSave()
          .then((resp) => {
            this.$hideLoading()
            return resp
          })
          .catch((error) => {
            return error
          })
        if (!result) {
          const index2 = this.goodsSearchExtList.findIndex((item) => item.goods.uuid === goodsLine.goods.uuid)
          if (index2 !== -1) {
            this.$set(this.goodsSearchExtList, index2, goodsLine)
          }
        }
        return
      }
    } else {
      this.doDraftRemove(goodsLine)
    }
  }

  // 组装请求体
  doAssembleBody() {
    const body = new RequireApplyCreationByDraft()
    body.billId = this.isCopy && this.enableAutoSave ? this.draftId : this.requireBody.billId
    body.draftId = this.draftId
    body.note = this.requireBody.note
    body.orderDate = new Date()
    body.expireDate = this.expireDate
    body.type = this.type
    body.groupPurchase = this.requireBody.groupPurchase || 0
    body.selfPickUp = this.requireBody.selfPickUp
    this.actionSelfPickUp(body.selfPickUp)
    return body
  }

  // 校验请求体
  validate() {
    if (!this.goodsNum) {
      return false
    }
    return true
  }

  // 提交按钮事件
  doSubmit(isSave = true) {
    this.oldGoodsNum = this.goodsNum
    this.oldGoodsPrice = this.goodsPrice
    if (this.validate()) {
      if (isSave) {
        this.doDraftSave().then(() => {
          this.doGetDraftByCode().then(async () => {
            this.doBalanceValidate()
          })
        })
      } else {
        this.doBalanceValidate()
      }
    } else {
      this.$showToast({ icon: 'none', title: '请填写商品数量～' })
    }
  }

  // 提交按钮事件
  submit() {
    this.doNormalSubmitThreshold()
  }

  // 当日正常叫货提交单据数大于等于当日正常叫货单据提交提示阈值
  async doNormalSubmitThreshold() {
    try {
      const body = this.doAssembleBody()
      const resp = await RequireApplyApi.validateBeforeSubmit(body)
      if (resp.data && resp.data.code == '1') {
        this.$showModal({
          title: '提示',
          content: resp.data.message || '',
          showCancel: true,
          confirmText: '确定',
          cancelText: '取消',
          success: (action) => {
            if (action.confirm) {
              this.doFinalSubmit()
            }
          }
        })
      } else {
        this.doFinalSubmit()
      }
    } catch (error) {
      this.doFinalSubmit()
    }
  }

  //库存金额校验
  doBalanceValidate() {
    this.doSubmitValidate()
  }

  //提交前验证
  doSubmitValidate() {
    // 如果当前订货类型在H6配置了订货规则，就不对店务配置的规则进行校验
    if (this.currentRule || this.type === RequireApplyType.advance) {
      this.$hideLoading()
      this.$showModal({
        title: this.noPickAmt > 0 ? '提交确认' : '提示',
        content: this.noPickAmt > 0 ? `未提货完成的预订货订单将与本次报货一起发货` : `确定提交订货申请吗`,
        showCancel: true,
        confirmText: '提交',
        cancelText: '取消',
        success: (action) => {
          if (action.confirm) {
            this.submit()
          }
        }
      })
    } else {
      this.$showLoading()
      RequireApplyApi.getPeriod()
        .then((resp) => {
          this.$hideLoading()
          const period = resp.data
          if (period) {
            const now = new Date()
            const startTime = new Date(period.startTime.replace(/-/g, '/'))
            const endTime = new Date(period.endTime.replace(/-/g, '/'))
            if (now.getTime() < startTime.getTime() || now.getTime() > endTime.getTime()) {
              if (this.strictControl) {
                this.$hideLoading()
                this.$showModal({
                  title: '提交失败',
                  content: `当前时间不在叫货时间范围内，不可叫货`,
                  showCancel: true,
                  confirmText: '保存后返回',
                  cancelText: '直接返回',
                  success: (action) => {
                    if (action.confirm) {
                      this.save(true)
                    } else {
                      uni.navigateBack({})
                    }
                  }
                })
              } else {
                this.$hideLoading()
                this.$showModal({
                  title: '非正常订货时间',
                  content: '将生成新单，是否继续提交此单',
                  showCancel: true,
                  confirmText: '继续提交',
                  success: (action) => {
                    if (action.confirm) {
                      this.submit()
                    }
                  }
                })
              }
            } else {
              this.$showModal({
                title: this.noPickAmt > 0 ? '提交确认' : '提示',
                content: this.noPickAmt > 0 ? `未提货完成的预订货订单将与本次报货一起发货` : `确定提交订货申请吗`,
                showCancel: true,
                confirmText: '提交',
                cancelText: '取消',
                success: (action) => {
                  if (action.confirm) {
                    this.submit()
                  }
                }
              })
            }
          } else {
            this.$showModal({
              title: '提示',
              content: `确定提交订货申请吗`,
              showCancel: true,
              confirmText: '提交',
              cancelText: '取消',
              success: (action) => {
                if (action.confirm) {
                  this.submit()
                }
              }
            })
          }
        })
        .catch((e) => {
          this.$hideLoading()
          this.$showToast({ icon: 'error', title: e.msg })
        })
    }
  }

  // 保存按钮事件
  doSave() {
    if (this.validate()) {
      this.doDraftSave().then(() => {
        this.doGetDraftByCode().then(() => {
          this.save()
        })
      })
    } else {
      this.$showToast({ icon: 'none', title: '请填写商品数量～' })
    }
  }

  async save(backTo = false) {
    const request: any = this.isEdit ? RequireApplyApi.modifyByDraft : RequireApplyApi.createByDraft
    this.$showLoading()

    const body = this.doAssembleBody()
    request(body)
      .then((resp) => {
        this.isHasNotSave = false
        this.isRenovote = true
        this.actionHasNotSave(false)
        if (!this.isEdit) {
          this.isEdit = true
        }
        this.$hideLoading()
        this.$showToast({ icon: 'success', title: '保存成功' })
        if (backTo) {
          setTimeout(() => {
            uni.navigateBack({})
          }, 300)
        }
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showModal({
          title: '保存失败',
          content: `失败原因:${e.msg}`,
          showCancel: true,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.save(backTo)
            }
          }
        })
      })
  }

  // 提交按钮事件
  doFinalSubmit() {
    const request: any = this.isEdit ? RequireApplyApi.modifyAndSubmitByDraft : RequireApplyApi.createAndSubmitByDraft
    this.$showLoading()
    const body = this.doAssembleBody()
    request(body)
      .then((res) => {
        this.$hideLoading()
        if (res.data && res.data.code == 5001) {
          this.$showLoading()
          this.disableList = []
          this.deleteList = []
          const arr = res.data.details || []
          const inputCodeArr = arr.map((e) => {
            return e.gdInputCode
          })

          const queryRequest = new QueryRequest()
          queryRequest.page = 0
          queryRequest.pageSize = -1
          queryRequest.conditions = [{ operation: 'gdInputCode:in', parameters: inputCodeArr }]
          queryRequest.fetchParts = ['category', 'reference', 'image']
          queryRequest.conditions.push({ operation: 'draftId:=', parameters: [this.draftId] })
          queryRequest.conditions.push({ operation: 'alcDate:=', parameters: [this.alcDate] })

          RequireApplyApi.queryDraftLine(queryRequest)
            .then((draft) => {
              if (draft.data) {
                this.goodsPrice = Number(draft.data.total).add(Number(draft.data.accTotal)).scale(2)
                this.goodsNum = draft.data.recCnt || 0
                // 辅料管理模式为2才需要加上辅料数量
                if (this.accessoryManage === 2) {
                  this.totalQpcQty = Number(draft.data.qpcQty).add(Number(draft.data.accQpcQty))
                } else {
                  this.totalQpcQty = Number(draft.data.qpcQty)
                }
              }

              const deleteArr: any[] = []
              if (draft.data && draft.data.lines && draft.data.lines.length) {
                res.data.details.forEach((item) => {
                  for (let index = 0; index < draft.data!.lines.length; index++) {
                    const draftLine = draft.data!.lines[index]

                    if (draftLine.goods.uuid === item.gdUuid) {
                      //更新商品信息
                      const oldQpcQty = Number(draftLine.qpcQty)
                      // e.goods.currentQpcQty = e.qpcQty

                      //validCode校验结果代码：0-数量超限，1-无效商品，2价格变动
                      if (item.validCode != '1') {
                        if (item.validCode == '0') {
                          draftLine.goods.allowQty = item.allowQty
                          draftLine.goods.isLimit = item.isLimit

                          draftLine.qty = Number(item.allowQty)

                          if (!draftLine.qty) {
                            if (Number(draftLine.qpcQty)) {
                              this.goodsNum = Number(this.goodsNum.add(-1))
                            }
                          }

                          draftLine.qpcQty = `${(Number(draftLine.qty) / draftLine.goods.qpc).floorScale(4) || 0}`
                        }
                        // 先判断商品是否有辅料行，如果由辅料行则不计算爆品价
                        // 如果accessoryManage不为1 也不用计算
                        const hasAccLines = draftLine.accLines && draftLine.accLines.length && this.accessoryManage === 1
                        // 爆品创建明细行的时候，需要取price字段，这里price字段被污染了，需要一个字段留作备用
                        // draftLine.goods.price = item.price
                        draftLine.goods.tempPrice = hasAccLines && this.autoCalcAcc ? item.price : this.getDisLinePrice(draftLine, item)
                        draftLine.goods.qpc = item.qpc

                        let oldTotal = draftLine.goods.tempPrice.multiply(oldQpcQty).scale(2)
                        if (draftLine.goodsType == 4 && draftLine.goods.allowActQpcQty) {
                          oldTotal = draftLine.total
                        }
                        const oldAccTotal: number = draftLine.accTotal || 0 // 原来的辅料合计
                        const oldAccQpcQty: number = draftLine.accQpcQty || 0 // 原来的辅料数量合计
                        draftLine.total = draftLine.goods.tempPrice.multiply(Number(draftLine.qpcQty)).scale(2)

                        // 更新数量之后
                        draftLine.accLines &&
                          draftLine.accLines.forEach((line) => {
                            const qpcQty: number = this.autoCalcAcc
                              ? Number(draftLine.qty)
                                  .multiply(Number(line.accessoryRate))
                                  .divide(line.goodsRate || 1)
                                  .divide(line.qpc)
                              : Number(line.qpcQty)
                            const qty: number = this.autoCalcAcc
                              ? Number(draftLine.qty)
                                  .multiply(Number(line.accessoryRate))
                                  .divide(line.goodsRate || 1)
                              : qpcQty.multiply(line.qpc)
                            if (this.autoCalcAcc) {
                              if (line.roundType === RoundType.ceil) {
                                line.qty = Math.ceil(qty)
                                line.qpcQty = `${qpcQty.scale(4)}`
                                line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
                              } else {
                                line.qty = qty.scale(4)
                                line.qpcQty = `${qpcQty.scale(4)}`
                                line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
                              }
                            } else {
                              line.qty = qty.scale(4)
                              line.qpcQty = `${qpcQty.scale(4)}`
                              line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
                            }
                            draftLine.accTotal = Number(draftLine.accTotal).add(line.total)
                            draftLine.accQty = Number(draftLine.accQty).add(line.qty)
                            draftLine.accQpcQty = Number(draftLine.accQpcQty).add(Number(line.qpcQty))
                          })
                        draftLine.accTotal = Number(draftLine.accTotal).scale(2)
                        draftLine.accQty = Number(draftLine.accQty).scale(4)
                        draftLine.accQpcQty = Number(draftLine.accQpcQty).scale(4)

                        //重算总金额总数量
                        this.goodsPrice = Number(this.goodsPrice - oldTotal - oldAccTotal + draftLine.accTotal + draftLine.total).scale(2)

                        if (this.accessoryManage === 2) {
                          this.totalQpcQty = Number(
                            this.totalQpcQty - Number(oldQpcQty).scale(0) + Number(draftLine.qpcQty).scale(0) - oldAccQpcQty + draftLine.accQpcQty
                          ).floorScale(0)
                        } else {
                          this.totalQpcQty = Number(this.totalQpcQty - Number(oldQpcQty).scale(0) + Number(draftLine.qpcQty).scale(0)).scale(0)
                        }

                        this.disableList.push(draftLine)
                        if (!draftLine.qty) {
                          deleteArr.push(draftLine)
                        }
                      } else {
                        this.goodsPrice = (this.goodsPrice - draftLine.total - Number(draftLine.accTotal)).scale(2)

                        if (this.accessoryManage === 2) {
                          this.totalQpcQty = Number(this.totalQpcQty - Number(draftLine.qpcQty) - Number(draftLine.accQpcQty)).scale(0)
                        } else {
                          this.totalQpcQty = Number(this.totalQpcQty - Number(draftLine.qpcQty)).scale(0)
                        }

                        if (Number(draftLine.qpcQty)) {
                          this.goodsNum = Number(this.goodsNum.minus(1))
                        }

                        draftLine.qty = 0
                        draftLine.qpcQty = '0'
                        draftLine.total = 0

                        this.deleteList.push(draftLine)
                      }

                      break
                    }
                  }
                })
              } else {
                this.$hideLoading()
              }

              this.showList = this.deleteList.concat(this.disableList)

              let saveLoading = true
              let deleteLoading = true
              if (this.deleteList.length || deleteArr.length) {
                deleteLoading = false
              }

              if (this.disableList.length) {
                saveLoading = false
                this.doDraftSave(false, false, true).then(
                  () => {
                    saveLoading = true
                    if (deleteLoading) {
                      this.$hideLoading()
                    }
                  },
                  () => {
                    saveLoading = true
                  }
                )
                this.disableList.forEach((disableLine) => {
                  for (let index = 0; index < this.goodsSearchExtList.length; index++) {
                    if (disableLine.goods.uuid === this.goodsSearchExtList[index].goods.uuid) {
                      const price = disableLine.goods.price
                      this.goodsSearchExtList[index].goods = JSON.parse(JSON.stringify({ ...disableLine.goods, price }))
                      this.goodsSearchExtList[index].qty = disableLine.qty
                      this.goodsSearchExtList[index].qpcQty = disableLine.qpcQty
                      this.goodsSearchExtList[index].total = disableLine.total
                      this.goodsSearchExtList[index].accLines = disableLine.accLines
                      this.goodsSearchExtList[index].accTotal = disableLine.accTotal
                      this.goodsSearchExtList[index].accQty = disableLine.accQty
                      this.goodsSearchExtList[index].accQpcQty = disableLine.accQpcQty
                      break
                    }
                  }
                })
              }

              if (this.deleteList.length || deleteArr.length) {
                const isRefresh = this.deleteList.length
                const body = new AppRequireApplyDraftLineKeys()
                body.draftId = this.draftId

                const removeLines = deleteArr.concat(this.deleteList)
                body.lineUuids = removeLines.map((e) => {
                  return e.lineUuid
                })
                this.$showLoading()
                this.isDelete = true //防止触发商品行删除
                RequireApplyApi.removeDraftLines(body)
                  .then((resp) => {
                    deleteLoading = true
                    removeLines.forEach((sku) => {
                      this.doReMoveGoodLine(sku, false)
                    })
                    this.isDelete = false
                    if (isRefresh) {
                      //更新无效商品
                      this.upPageData()
                    } else {
                      if (saveLoading) {
                        this.$hideLoading()
                      }
                    }
                  })
                  .catch((e) => {
                    this.isDelete = false
                    deleteLoading = true
                    this.$hideLoading()
                    this.$showToast({ icon: 'error', title: e.msg })
                  })
              }

              this.$refs.Inventory.open()
            })
            .catch((e) => {
              this.$hideLoading()
              this.$showToast({ icon: 'error', title: e.msg })
            })
        } else if (res.data && res.data.code == 5002) {
          this.$showLoading()
          RequireApplyApi.get(this.requireBody.billId)
            .then((resp) => {
              if (resp.data) {
                const data = resp.data

                this.paymentSrcContext = JSON.parse(data.paymentSrcContext) || {}
                this.$hideLoading()
                this.$refs.perparepay.open()
              } else {
                this.$hideLoading()
              }
            })
            .catch((e) => {
              this.$hideLoading()
              this.$showToast({ icon: 'error', title: e.msg })
            })
        } else if (res.data && res.data.code == 5004) {
          this.$hideLoading()
          this.$showModal({
            title: '提示',
            content: `${res.data.message}`,
            showCancel: true,
            confirmText: '重新提交',
            success: (action) => {
              if (action.confirm) {
                this.doRefreshSubmit()
              } else {
                //跳转详情
                uni.$emit('searchSubmitRequire', res.data.billId ? res.data.billId : this.requireBody.billId)
                uni.navigateBack({})
              }
            }
          })
        } else if (res.data && res.data.code == 5007) {
          this.$hideLoading()
          this.$showModal({
            title: '提交失败',
            content: `失败原因:${res.data.message}`,
            showCancel: false,
            confirmText: '我知道了',
            success: (action) => {
              if (action.confirm) {
                // this.getStoreInfo()
              }
            }
          })
        } else if (res.data && res.data.code == 5000) {
          this.$hideLoading()
          this.$showModal({
            title: '提交失败',
            content: `失败原因:${res.data.message}`,
            showCancel: false,
            confirmText: '我知道了',
            success: (action) => {
              if (action.confirm) {
                this.getStoreInfo()
              }
            }
          })
        } else {
          this.isHasNotSave = false
          this.isRenovote = true
          this.actionHasNotSave(false)
          this.$showToast({ icon: 'success', title: '提交成功' })
          setTimeout(() => {
            if (res.data.billId) {
              //跳转详情
              uni.$emit('searchSubmitRequire', res.data.billId ? res.data.billId : this.requireBody.billId)
              uni.navigateBack({})
              console.log('跳转详情', res.data.billId ? res.data.billId : this.requireBody.billId)
            } else {
              this.$Router.replace({
                name: 'requireGoodsConfirm'
              })
            }
          }, 1500)
        }
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showModal({
          title: '提交失败',
          content: `失败原因:${e.msg}`,
          showCancel: true,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.submit()
            }
          }
        })
      })
  }

  /**
   * 获取库存不足弹窗商品的显示价格
   * 存在爆品活动的时候，需要取爆品活动价，超出爆品加购数量时取平均数
   */
  getDisLinePrice(lines: AppRequireApplyDraftLineQueryDTO, item: RequireApplySubmitInvalidDtl) {
    // 获取最新的活动数量和活动价格
    // 商品数量 - 现在的问题时，活动可加购数量只有活动数量变化的时候才会有值
    // 所以allowActQty无值,则说明数量没有变化,取草稿行里面的可加购活动数量
    const actQty = Number(Number(item.allowActQty).divide(item.qpc) || lines.goods.allowActQpcQty || 0).floorScale(0)
    // 如果不是活动商品价格变动,actPrice是没有值的,这个时候取srcPrice - 应对情况,爆品可加购数量变化
    const actPrice = item.actPrice || item.srcPrice || 0
    // 商品本身的加购数量和价格
    const qpcQty = Number(lines.qpcQty) || 1
    const price = item.price || 0
    // 如果没有活动加购数量，直接返回商品原价即可
    if (!actQty) {
      return item.price
    }

    // 剩下的情况默认有活动可加购数量
    let total = 0
    if (qpcQty > actQty) {
      // 加购数量大于爆品活动限购数量时，多出的数量需要按照原价进行金额计算
      const diffQty = Number(qpcQty) - actQty
      const activeTotal = actPrice.multiply(Number(actQty)).scale(2)
      const diffTotal = price.multiply(Number(diffQty)).scale(2)
      total = activeTotal.add(diffTotal)
    } else {
      // total = actPrice.multiply(Number(qpcQty)).scale(2)
      return actPrice
    }
    return (total / qpcQty).scale(4)
  }

  // 获取门店叫货配置
  getOption() {
    RequireApplyApi.getOption()
      .then((resp) => {
        if (this.isFinish && this.goodsQueryFinish) {
          this.searchPage.isLoading = false
        }

        this.strictControl = resp.data && resp.data.strictControl
        this.validateLevel4CaseQty = (resp.data && resp.data.validateLevel4CaseQty) || 0
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })

    // 获取商铺叫货规则

    RequireOrderApi.getStoreInfo()
      .then((resp) => {
        if (this.isFinish && this.goodsQueryFinish) {
          this.searchPage.isLoading = false
        }
        this.showGdImage = resp.data && resp.data.option && resp.data.option.displayGdImage
      })
      .catch((e) => {
        this.searchPage.isLoading = false
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  // 获取商铺叫货规则
  getStoreInfo(isLoading = true) {
    if (isLoading) {
      this.$showLoading({ delayTime: 200 })
    }

    RequireApplyApi.getStoreInfo()
      .then((resp) => {
        this.isFinish = true
        if (this.goodsQueryFinish) {
          this.$hideLoading()
        }

        this.RequireOrderStoreInfo = resp.data
        this.actionStoreInfo(this.RequireOrderStoreInfo)
      })
      .catch((e) => {
        this.isFinish = true
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  /**
   * 删除已选商品
   * @param uuid 商品uuid
   * @param validCode  是否下架
   */
  doDeleteLine(line) {
    if (!line.validCode) {
      this.doDraftRemove(line)
    }

    for (let index = 0; index < this.showList.length; index++) {
      if (this.showList[index].goods.uuid === line.goods.uuid) {
        this.showList.splice(index, 1)
        break
      }
    }
  }

  /**
   * 准备创建数据
   */

  doBeforeEdit() {
    const body = new IllDraftKey()
    body.billId = this.requireBody.billId
    body.draftId = this.draftId
    return new Promise((resolve, reject) => {
      RequireApplyApi.doBeforeEdit(body)
        .then((res) => {
          resolve(true)
        })

        .catch((e) => {
          this.isUpSort = false
          this.$hideLoading()
          this.$showToast({ title: e.msg, icon: 'none' })
          reject(false)
        })
    })
  }

  /**
   * 获取草稿单商品种类和金额
   */

  doGetDraft() {
    const query = new QueryRequest()
    query.page = 0

    query.pageSize = 1
    query.conditions.push({ operation: 'draftId:=', parameters: [this.draftId] })
    query.conditions.push({ operation: 'alcDate:=', parameters: [this.alcDate] })
    query.fetchParts = ['category', 'reference', 'image']
    RequireApplyApi.queryDraftLine(query)
      .then((res) => {
        if (res.data) {
          this.goodsPrice = Number(res.data.total).add(Number(res.data.accTotal)).scale(2)
          this.goodsNum = res.data.recCnt || 0
          if (this.accessoryManage === 2) {
            this.totalQpcQty = (res.data.qpcQty || 0).add(res.data.accQpcQty || 0)
          } else {
            this.totalQpcQty = res.data.qpcQty || 0
          }
        }
      })
      .catch((e) => {
        this.isUpSort = false
        this.$hideLoading()
        this.$showToast({ title: e.msg, icon: 'none' })
      })
      .finally(() => {
        this.searchPage.isLoading = false
      })
  }

  /**
   * 确认删除草稿
   */
  doDraftRemove(sku) {
    if (!sku.lineUuid) {
      this.doReMoveGoodLine(sku)
    } else {
      if (this.isDelete) {
        return
      }
      this.$showLoading()
      this.isDelete = true
      const removebody: illDraftLineKey = new illDraftLineKey()
      removebody.draftId = this.draftId
      removebody.lineUuid = sku.lineUuid

      RequireApplyApi.removeDraftLine(removebody)
        .then((resp) => {
          this.isDelete = false
          this.$hideLoading()
          this.doReMoveGoodLine(sku)
        })
        .catch((e) => {
          this.isDelete = false
          this.$hideLoading()
          this.$showToast({ icon: 'error', title: e.msg })
        })
    }
  }

  //移除商品行
  doReMoveGoodLine(sku, refesh = true) {
    let total: number = 0 //当前商品总价
    let qpcQty: number = 0 //当前商品总规格数
    let accTotal: number = 0 // 当前商品辅料总金额
    let accQpcQty: number = 0 // 当前商品辅料合计数量
    for (let index = 0; index < this.goodsSearchExtList.length; index++) {
      if (sku.goods.uuid === this.goodsSearchExtList[index].goods.uuid) {
        total = this.goodsSearchExtList[index].total || 0
        accTotal = this.goodsSearchExtList[index].accTotal || 0
        qpcQty = Number(qpcQty || this.goodsSearchExtList[index].qpcQty)
        accQpcQty = Number(accQpcQty || this.goodsSearchExtList[index].accQpcQty)
        this.goodsSearchExtList[index].lineUuid = ''
        this.goodsSearchExtList[index].qty = 0
        this.goodsSearchExtList[index].qpcQty = '0'
        this.goodsSearchExtList[index].total = 0
        this.goodsSearchExtList[index].accTotal = 0
        this.goodsSearchExtList[index].accQty = 0
        this.goodsSearchExtList[index].accQpcQty = 0
        this.goodsSearchExtList[index].accLines &&
          this.goodsSearchExtList[index].accLines.forEach((accLine) => {
            accLine.qty = 0
            accLine.total = 0
            accLine.qpcQty = '0'
          })
        break
      }
    }

    if (refesh) {
      this.goodsPrice = (this.goodsPrice - total - accTotal).scale(2)
      if (this.accessoryManage === 2) {
        this.totalQpcQty = Number(this.totalQpcQty - Number(qpcQty) - Number(accQpcQty)).scale(0)
      } else {
        this.totalQpcQty = Number(this.totalQpcQty - Number(qpcQty)).scale(0)
      }
      if (Number(qpcQty)) {
        this.goodsNum = Number(this.goodsNum.minus(1))
      }
    }
  }

  /**
   * 计算商品数量和总价
   * @param qpcQty
   * @param sku
   * @returns
   */
  handleCalSkuByQpcQty(qpcQty: number, sku: AppRequireApplyDraftLineQueryDTO) {
    sku.qpcQty = Number(qpcQty).toString()
    sku.qty = Number(Number(qpcQty).multiply(Number(sku.goods.qpc))).scale(4)
    const allowActQpcQty = Number(sku.goods.allowActQpcQty || 0).floorScale(0)

    // 如果是爆品且有爆品活动价
    if (sku.goods.goodsType == 4 && allowActQpcQty) {
      const actPrice = sku.goods.actPrice || 0
      if (qpcQty > allowActQpcQty) {
        // 加购数量大于爆品活动限购数量时，多出的数量需要按照原价进行金额计算
        const diffQty = Number(sku.qpcQty) - allowActQpcQty
        const activeTotal = actPrice.multiply(Number(allowActQpcQty)).scale(2)
        const diffTotal = sku.goods.price.multiply(Number(diffQty)).scale(2)
        sku.total = activeTotal.add(diffTotal)
      } else {
        // 正常情况下按照爆品活动价进行价格计算
        sku.total = actPrice.multiply(Number(sku.qpcQty)).scale(2)
      }
    } else {
      // 计算商品总价
      sku.total = sku.goods.price.multiply(Number(sku.qpcQty)).scale(2)
    }

    let skuAccTotal: number = 0
    let skuAccQty: number = 0
    let skuAccQpcQty: number = 0

    sku.accLines &&
      sku.accLines.forEach((line) => {
        const qpcQty: number = this.autoCalcAcc
          ? Number(sku.qty)
              .multiply(Number(line.accessoryRate))
              .divide(line.goodsRate || 1)
              .divide(line.qpc)
          : Number(line.qpcQty)
        const qty: number = this.autoCalcAcc
          ? Number(sku.qty)
              .multiply(Number(line.accessoryRate))
              .divide(line.goodsRate || 1)
          : qpcQty.multiply(line.qpc)
        // 自动计算才适用此逻辑，将total计算入总金额
        if (this.autoCalcAcc) {
          if (line.roundType === RoundType.ceil) {
            line.qty = Math.ceil(qty)
            line.qpcQty = `${qpcQty.scale(4)}`
            line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
          } else {
            line.qpcQty = `${qpcQty.scale(4)}`
            line.qty = qty.scale(4)
            line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
          }
        } else {
          line.qty = qty.scale(4)
          line.qpcQty = `${qpcQty.scale(4)}`
          line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
        }
        skuAccTotal = Number(skuAccTotal).add(line.total)
        skuAccQty = Number(skuAccQty).add(line.qty)
        skuAccQpcQty = Number(skuAccQpcQty).add(Number(line.qpcQty))
      })
    sku.accTotal = Number(skuAccTotal).scale(2)
    sku.accQty = Number(skuAccQty).scale(4)
    sku.accQpcQty = skuAccQpcQty.scale(4)

    return sku
  }

  /**
   * 编辑数量
   */
  doDraftSaveLine(qpcQty: number, sku: AppRequireApplyDraftLineQueryDTO) {
    sku = this.handleCalSkuByQpcQty(qpcQty, sku)
    let total: number = 0 // 原商品总价
    let oldQpcQty: number = 0 //原商品总规格数
    let accTotal: number = 0 // 原商品的总辅料价格
    let accQpcQty: number = 0 // 原商品的总辅料数量
    for (let index = 0; index < this.goodsSearchExtList.length; index++) {
      if (sku.goods.uuid === this.goodsSearchExtList[index].goods.uuid) {
        total = this.goodsSearchExtList[index].total
        accTotal = this.goodsSearchExtList[index].accTotal || 0
        oldQpcQty = oldQpcQty || Number(this.goodsSearchExtList[index].qpcQty)
        accQpcQty = accQpcQty || Number(this.goodsSearchExtList[index].accQpcQty)
        this.goodsSearchExtList[index].qty = sku.qty
        this.goodsSearchExtList[index].qpcQty = sku.qpcQty
        this.goodsSearchExtList[index].total = sku.total
        this.goodsSearchExtList[index].accLines = sku.accLines
        this.goodsSearchExtList[index].accTotal = sku.accTotal
        this.goodsSearchExtList[index].accQty = sku.accQty
        this.goodsSearchExtList[index].accQpcQty = sku.accQpcQty
        break
      }
    }
    const currenTtotal = Number(sku.total).add(Number(sku.accTotal))
    this.goodsPrice = Number(this.goodsPrice - total - accTotal + currenTtotal).scale(2)
    // 辅料管理模式为2才需要加上辅料数量
    if (this.accessoryManage === 2) {
      this.totalQpcQty = Number(this.totalQpcQty - Number(oldQpcQty) - accQpcQty + Number(sku.accQpcQty) + Number(qpcQty)).scale(0)
    } else {
      this.totalQpcQty = Number(this.totalQpcQty - Number(oldQpcQty) + Number(qpcQty)).scale(0)
    }
    if (!Number(oldQpcQty)) {
      this.goodsNum = Number(this.goodsNum.add(1))
    }
  }

  /**
   * 确认保存草稿
   * removeGoods 清除商品列表
   * removeSearch清除商品搜索列表
   */
  doDraftSave(removeGoods = false, removeSearch = false, isSaveDisable = false, isLoading = true) {
    if (isLoading) {
      this.$showLoading()
    }
    const body: AppRequireApplyDraftDTO = new AppRequireApplyDraftDTO()
    body.billId = this.isCopy && this.enableAutoSave ? this.draftId : this.requireBody.billId
    body.draftId = this.draftId
    body.type = this.type
    body.orderDate = this.requireBody.orderDate
    body.note = this.requireBody.note
    body.groupPurchase = this.requireBody.groupPurchase
    body.expireDate = this.expireDate
    body.selfPickUp = this.requireBody.selfPickUp
    this.actionSelfPickUp(body.selfPickUp)
    if (isSaveDisable) {
      body.lines = JSON.parse(JSON.stringify(this.disableList))
        .filter((item) => {
          return Number(item.qpcQty)
        })
        .map((item: AppRequireApplyDraftLineQueryDTO) => {
          if (item.qty !== Number(Number(item.qpcQty).multiply(Number(item.goods.qpc))).scale(4)) {
            item = this.handleCalSkuByQpcQty(Number(item.qpcQty), item)
          }
          let accTotal: number = 0
          let accQty: number = 0
          let accQpcQty: number = 0

          if (item.accLines) {
            item.accLines.forEach((line) => {
              accTotal = accTotal.add(line.total || 0)
              accQty = accQty.add(line.qty || 0)
              accQpcQty = accQpcQty.add(Number(line.qpcQty) || 0)
            })
          }
          const subLines = this.getSubLines(item, isSaveDisable)

          // 服务端要求，如果是爆品且加购数量在爆品限购数量之内，goods.price字段需要取爆品活动价
          let price = item.goods.price
          let singlePrice = item.goods.singlePrice
          if (item.goodsType == 4 && item.goods.allowActQpcQty && Number(item.qpcQty) <= item.goods.allowActQpcQty) {
            price = item.goods.actPrice || 0
            singlePrice = price.divide(item.goods.qpc).scale(4)
          }
          return {
            ...item,
            accTotal: accTotal.scale(2),
            accQty: accQty.scale(4),
            accQpcQty: accQpcQty.scale(4),
            subLines,
            goods: { ...item.goods, price, singlePrice }
          }
        })
    } else {
      const lines2 = this.goodsSearchExtList.filter((item) => {
        return Number(item.qpcQty)
      })
      body.lines = JSON.parse(JSON.stringify(lines2)).map((item) => {
        if (item.qty !== Number(Number(item.qpcQty).multiply(Number(item.goods.qpc))).scale(4)) {
          item = this.handleCalSkuByQpcQty(Number(item.qpcQty), item)
        }
        let accTotal: number = 0
        let accQty: number = 0
        let accQpcQty: number = 0

        if (item.accLines) {
          item.accLines.forEach((line) => {
            accTotal = accTotal.add(line.total || 0)
            accQty = accQty.add(line.qty || 0)
            accQpcQty = accQpcQty.add(Number(line.qpcQty))
          })
        }
        const subLines = this.getSubLines(item)
        // 服务端要求，如果是爆品且加购数量在爆品限购数量之内，goods.price字段需要取爆品活动价
        let price = item.goods.price
        let singlePrice = item.goods.singlePrice

        if (item.goodsType == 4 && item.goods.allowActQpcQty && Number(item.qpcQty) <= item.goods.allowActQpcQty) {
          price = item.goods.actPrice || 0
          singlePrice = price.divide(item.goods.qpc).scale(4)
        }
        return { ...item, accTotal: accTotal.scale(2), accQty: accQty.scale(4), subLines, goods: { ...item.goods, price, singlePrice } }
      })
    }
    return new Promise((resolve, reject) => {
      if (!body.lines.length) {
        if (removeSearch) {
          this.goodsSearchExtList = []
        }
        resolve(true)
      } else {
        RequireApplyApi.saveDraft(body)
          .then((resp) => {
            if (removeSearch) {
              this.goodsSearchExtList = []
            } else {
              this.goodsSearchExtList.forEach((item) => {
                const idx = resp.data.lines.findIndex((line) => {
                  return line.gdUuid === item.goods.uuid && line.gdInputCode === item.goods.inputCode
                })
                if (idx > -1 && item.goods.inputCode === resp.data.lines[idx].gdInputCode && item.goods.uuid === resp.data.lines[idx].gdUuid) {
                  item.lineUuid = resp.data.lines[idx].lineUuid || ''
                }
              })
            }
            this.isHasNotSaveDraft = false
            this.isRenovote = true
            resolve(true)
          })
          .catch((e) => {
            this.$hideLoading()
            this.$showToast({ icon: 'error', title: e.msg })
            reject(false)
          })
      }
    })
  }

  /**
   * 保存商品行时构造子明细
   * 只有存在爆品和普通商品的时候才需要构造，如果都是爆品或者都是普通商品则不用构造
   */
  getSubLines(skuItem: AppRequireApplyDraftLineQueryDTO, isSaveDisable = false) {
    const data = JSON.parse(JSON.stringify(skuItem))
    const subLines: AppRequireApplySubBaseLineDTO[] = []
    const allowActQpcQty = Number(data.goods.allowActQpcQty || 0).floorScale(0)

    if (data.goodsType == 4 && allowActQpcQty) {
      const lineInfo = new AppRequireApplySubBaseLineDTO()
      lineInfo.goodsType = 4
      lineInfo.note = data.note
      lineInfo.price = data.goods.actPrice || 0
      lineInfo.qpcQty = data.qpcQty
      lineInfo.qty = data.qty
      lineInfo.singlePrice = lineInfo.price.divide(data.goods.qpc).scale(4)
      lineInfo.total = data.total

      const qpcQty = Number(data.qpcQty)
      const actPrice = data.goods.actPrice || 0
      if (qpcQty > allowActQpcQty) {
        // 加购数量大于爆品活动限购数量时，多出的数量需要按照原价进行金额计算
        const diffQty = Number(data.qpcQty) - allowActQpcQty
        const activeTotal = actPrice.multiply(Number(allowActQpcQty)).scale(2)
        const diffTotal = data.goods.price.multiply(Number(diffQty)).scale(2)
        // data.total = activeTotal.add(diffTotal)
        subLines.push({
          ...lineInfo,
          qpcQty: String(allowActQpcQty),
          qty: allowActQpcQty.multiply(data.goods.qpc).scale(4),
          total: activeTotal
        })
        subLines.push({
          ...lineInfo,
          lineNo: 1,
          goodsType: 0,
          // 对于价格/库存变动导致提交失败的弹窗，price字段会受到污染，此处可取备用字段
          price: data.goods.price,
          singlePrice: data.goods.singlePrice,
          qpcQty: String(diffQty),
          qty: diffQty.multiply(data.goods.qpc),
          total: diffTotal
        })
      }
    }
    return subLines
  }

  /**
   * 更新商品行lineUuid
   */

  doGetDraftByCode() {
    this.$showLoading()
    const query = new QueryRequest()

    const list = this.goodsSearchExtList.filter((e) => {
      return Number(e.qpcQty)
    })
    const inputCodeArr: string[] = list.map((e) => {
      return e.goods.inputCode
    })
    query.page = 0
    query.pageSize = -1
    query.conditions = [{ operation: 'gdInputCode:in', parameters: inputCodeArr }]
    query.conditions.push({ operation: 'draftId:=', parameters: [this.draftId] })
    query.conditions.push({ operation: 'alcDate:=', parameters: [this.alcDate] })
    query.fetchParts = ['category', 'reference', 'image']
    return new Promise((resolve, reject) => {
      RequireApplyApi.queryDraftLine(query)
        .then((res) => {
          if (res.data) {
            this.goodsPrice = (Number(res.data.total) + Number(res.data.accTotal)).scale(2)
            this.goodsNum = res.data.recCnt || 0
            // 辅料管理模式为2才需要加上辅料数量
            if (this.accessoryManage === 2) {
              this.totalQpcQty = Number(res.data.qpcQty).add(Number(res.data.accQpcQty))
            } else {
              this.totalQpcQty = Number(res.data.qpcQty)
            }
            res.data.lines = res.data.lines || []
            res.data.lines.forEach((e) => {
              for (let index = 0; index < this.goodsSearchExtList.length; index++) {
                if (e.goods.uuid === this.goodsSearchExtList[index].goods.uuid) {
                  this.goodsSearchExtList[index].goods.price = e.goods.price
                  this.goodsSearchExtList[index].total = e.total
                  this.goodsSearchExtList[index].lineUuid = e.lineUuid
                  this.goodsSearchExtList[index].accLines = e.accLines
                  this.goodsSearchExtList[index].accTotal = e.accTotal

                  break
                }
              }
            })
          }
          resolve(true)
        })

        .catch((e) => {
          this.$hideLoading()
          this.$showToast({ title: e.msg, icon: 'none' })
          reject(false)
        })
    })
  }

  // 显示分页器需要展示的页
  doShow(curpage: number) {
    this.showPageBottom = true

    const oldCurpage = this.curpage
    const oldPageNum = this.searchPage.pageNum

    this.curpage = curpage
    this.searchPage.pageNum = (curpage - 1) * 10
    this.searchPage.isLoading = true
    const queryRequest = new QueryRequest()
    this.currentPagination = true
    this.doDraftSave(false, false, false, false).then(
      () => {
        const goodsSearchExtList = JSON.parse(JSON.stringify(this.goodsSearchExtList))
        this.goodsSearchExtList = []
        this.doQueryGood(queryRequest, true)
          .then(
            () => {
              this.hasMore = true
              this.$nextTick(() => {
                this.scrollTop = 0
              })

              this.$showToast({ icon: 'none', title: '当前页面为第' + this.curpage + '页数据' })
            },
            () => {
              this.goodsSearchExtList = goodsSearchExtList
              this.searchPage.isLoading = false
              this.curpage = oldCurpage
              this.$nextTick(() => {
                if (this.$refs.checkpick) {
                  this.$refs.checkpick.currentPage = this.curpage
                } else {
                  this.$refs.skeleton.$refs.goodSkeleton.$refs.checkpick.currentPage = this.curpage
                }
              })

              this.searchPage.pageNum = oldPageNum
            }
          )
          .catch()
      },
      () => {
        this.searchPage.isLoading = false
        this.curpage = oldCurpage
        this.$nextTick(() => {
          if (this.$refs.checkpick) {
            this.$refs.checkpick.currentPage = this.curpage
          } else {
            this.$refs.skeleton.$refs.goodSkeleton.$refs.checkpick.currentPage = this.curpage
          }
        })
        this.searchPage.pageNum = oldPageNum
      }
    )
  }

  // 分页器隐藏时触发
  doHide(state: boolean) {
    this.showPageBottom = state
  }

  //分页器弹窗确认
  doPageConfirm(page: number) {
    if (this.$refs.checkpick) {
      this.$refs.checkpick.doConfirm(page)
    } else {
      this.$refs.skeleton.$refs.goodSkeleton.$refs.checkpick.doConfirm(page)
    }
  }

  //分页器弹窗取消
  doPageCancel() {
    const oldCurpage = this.curpage
    this.curpage = -1
    this.doHide(true)
    this.$nextTick(() => {
      this.curpage = oldCurpage
    })
  }

  //展示或隐藏页码选择
  doShowPagePick(isShow: boolean) {
    if (isShow) {
      this.$refs.pagepick.show()
    } else {
      this.$refs.pagepick.close()
    }
  }

  //更新商品数据
  doUPGoodsExtList() {
    this.$showLoading()
    const query = new QueryRequest()
    const list = this.goodsSearchExtList.filter((e) => {
      return Number(e.qpcQty) && !e.lineUuid
    })
    const inputCodeArr: string[] = list.map((e) => {
      return e.goods.inputCode
    })
    query.page = 0
    query.pageSize = -1
    query.conditions = [{ operation: 'gdInputCode:in', parameters: inputCodeArr }]
    query.conditions.push({ operation: 'draftId:=', parameters: [this.draftId] })
    query.conditions.push({ operation: 'alcDate:=', parameters: [this.alcDate] })
    query.fetchParts = ['category', 'reference', 'image']
    return new Promise((resolve, reject) => {
      if (!list.length) {
        resolve(true)
      } else {
        RequireApplyApi.queryDraftLine(query)
          .then((res) => {
            if (res.data) {
              let accTotal: number = 0
              res.data.lines.forEach((line) => {
                accTotal = Number(line.accTotal) + accTotal
              })
              this.goodsPrice = (Number(res.data.total) + accTotal.scale(2)).scale(2)
              this.goodsNum = res.data.recCnt || 0
              // 辅料管理模式为2才需要加上辅料数量
              if (this.accessoryManage === 2) {
                this.totalQpcQty = Number(res.data.qpcQty).add(Number(res.data.accQpcQty))
              } else {
                this.totalQpcQty = Number(res.data.qpcQty)
              }
              res.data.lines = res.data.lines || []
              res.data.lines.forEach((e) => {
                for (let index = 0; index < this.goodsSearchExtList.length; index++) {
                  if (e.goods.uuid === this.goodsSearchExtList[index].goods.uuid) {
                    this.goodsSearchExtList[index].lineUuid = e.lineUuid
                    this.goodsSearchExtList[index].goods.price = e.goods.price
                    this.goodsSearchExtList[index].total = e.total
                    this.goodsSearchExtList[index].accLines = e.accLines
                    this.goodsSearchExtList[index].accTotal = e.accTotal
                    break
                  }
                }
              })
            }
            resolve(true)
          })

          .catch((e) => {
            this.$hideLoading()
            this.$showToast({ title: e.msg, icon: 'none' })
            reject(false)
          })
      }
    })
  }

  // 返回确认
  onBackPress() {
    const modalText = this.isHasNotSaveDraft ? '当前订货数据未保存，确定退出吗？' : '需要点击保存按钮，刚刚编辑的数据才能保存，确定需要返回？'

    if ((this.isHasNotSave || this.isHasNotSaveDraft) && !this.enableAutoSave) {
      this.$showModal({
        title: '提示',
        content: modalText,
        showCancel: true,
        confirmText: '确定',
        cancelText: '取消',
        success: (action) => {
          if (action.confirm) {
            this.isHasNotSave = false
            this.isHasNotSaveDraft = false
            uni.navigateBack({})
          }
        }
      })

      return true
    } else {
      return false
    }
  }

  //关闭提示

  doCloseTips() {
    this.showPreTips = false
  }

  //重新查询商品信息
  doRefreshQuery() {
    const queryRequest = new QueryRequest()
    this.doQueryGood(queryRequest, true)
  }

  //关闭支付订单弹窗
  doPerparePayClose() {
    this.isNeedGetPay = false
    this.$refs.perparepay.close()
    //跳转详情
    uni.$emit('searchSubmitRequire', this.requireBody.billId)
    uni.navigateBack({})
  }

  //支付订单弹窗复制
  doPerparePayConfirm() {
    this.isNeedGetPay = true
  }

  //查询吱口令支付结果

  doGetPayResult() {
    clearInterval(this.timer)
    this.doRefreshBill(true)
    this.timer = setInterval(() => {
      this.doRefreshBill()
    }, 3000)
  }

  //更新单据

  doRefreshBill(showLoading: boolean = false) {
    showLoading && this.$showLoading()
    RequireApplyApi.get(this.requireBody.billId)
      .then((resp) => {
        this.$hideLoading()
        if (resp.data) {
          this.paymentSrcContext = JSON.parse(resp.data.paymentSrcContext) || {}
          if (
            resp.data.state !== RequireApplyState.initial ||
            (resp.data.paymentState !== RequireApplyPaymentState.initial && resp.data.paymentState !== RequireApplyPaymentState.failed)
          ) {
            this.isNeedGetPay = false
            clearInterval(this.timer)
            this.$showToast({ icon: 'success', title: '支付成功，订单已提交' })
            setTimeout(() => {
              this.doPerparePayClose()
            }, 1000)
          } else if (this.paymentSrcContext.ziCommandState === 'expired') {
            clearInterval(this.timer)
            this.$refs.perparepay.close()

            this.$showModal({
              title: '支付超时',
              content: '订单支付超时，原吱口令已失效，请点击继续支付，复制新的吱口令完成支付',
              showCancel: true,
              confirmText: '继续支付',
              cancelText: '取消',
              success: (action) => {
                if (action.confirm) {
                  //更新吱口令
                  this.doRefreshZiCommand()
                } else {
                  //跳转详情
                  uni.$emit('searchSubmitRequire', this.requireBody.billId)
                  uni.navigateBack({})
                }
              }
            })
          }
        } else {
          this.isNeedGetPay = false
          this.$refs.perparepay.close()
          clearInterval(this.timer)
          this.$showToast({ icon: 'success', title: '支付成功，订单已提交' })
          setTimeout(() => {
            this.$Router.replace({
              name: 'requireGoodsConfirm'
            })
          }, 1000)
        }
      })
      .catch((e) => {
        this.$hideLoading()
      })
  }

  //刷新吱口令
  doRefreshZiCommand() {
    this.$showLoading()

    RequireApplyApi.regenZiCommand({ id: this.requireBody.billId })
      .then((res) => {
        this.paymentSrcContext.zicommand = JSON.parse(res.data || '') || {}
        this.$hideLoading()
        this.$refs.perparepay.open()
      })
      .catch((error) => {
        this.$hideLoading()
        console.log(error)
        this.$showModal({
          title: '提示',
          content: `${error.msg}`,
          showCancel: false,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.doRefreshZiCommand()
            }
          }
        })
      })
  }

  // 重新提交按钮事件
  async doRefreshSubmit() {
    this.$showLoading()

    RequireApplyApi.retrySubmit({ id: this.requireBody.billId })
      .then((res) => {
        if (res.data && res.data.success) {
          this.$hideLoading()
          this.$showToast({ icon: 'success', title: '提交成功' })
          setTimeout(() => {
            if (res.data.billId) {
              //跳转详情
              uni.$emit('searchSubmitRequire', res.data.billId ? res.data.billId : this.requireBody.billId)
              uni.navigateBack({})
            } else {
              this.$Router.replace({
                name: 'requireGoodsConfirm'
              })
            }
          }, 1500)
        } else {
          if (res.data && res.data.code == 5002) {
            RequireApplyApi.get(this.requireBody.billId)
              .then((resp) => {
                if (resp.data) {
                  const data = resp.data

                  this.paymentSrcContext = JSON.parse(data.paymentSrcContext) || {}
                  this.$hideLoading()
                  this.$refs.perparepay.open()
                } else {
                  this.$hideLoading()
                }
              })
              .catch((e) => {
                this.$hideLoading()
                this.$showToast({ icon: 'error', title: e.msg })
              })
          } else if (res.data && res.data.code == 5004) {
            this.$hideLoading()
            this.$showModal({
              title: '提示',
              content: `${res.data.message}`,
              showCancel: true,
              confirmText: '重新提交',
              success: (action) => {
                if (action.confirm) {
                  this.doRefreshSubmit()
                } else {
                  //跳转详情
                  uni.$emit('searchSubmitRequire', res.data.billId ? res.data.billId : this.requireBody.billId)
                  uni.navigateBack({})
                }
              }
            })
          } else {
            this.$hideLoading()
            this.$showModal({
              title: '提交失败',
              content: `${res.data.message}`,
              showCancel: false,
              confirmText: '重试',
              success: (action) => {
                if (action.confirm) {
                  this.doRefreshSubmit()
                }
              }
            })
          }
        }
      })
      .catch((error) => {
        this.$hideLoading()
        console.log(error)
        this.$showModal({
          title: '提交失败',
          content: `${error.msg}`,
          showCancel: false,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.doRefreshSubmit()
            }
          }
        })
      })
  }

  //判断是否快到截止时间
  doCloseOutside() {
    if (this.queueObj && this.queueObj.closeOutside) {
      this.queueObj.closeOutside()
    }
  }

  // 关闭预订货提示
  closePreOrder() {
    this.isPreOrder = false
  }
}
