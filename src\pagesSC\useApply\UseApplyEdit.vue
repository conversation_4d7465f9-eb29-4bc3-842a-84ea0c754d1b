<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2023-05-15 15:22:41
 * @LastEditTime: 2025-06-13 15:54:28
 * @LastEditors: yuzhipi
 * @Description: 
 * @FilePath: /soa/src/pagesSC/useApply/UseApplyEdit.vue
 * 记得注释
-->
<template>
  <view class="use-apply-edit">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <view class="header">
      <hd-cell
        hasLine
        title="申请类型"
        placeholder="请选择申请类型"
        :value="requestBody.useTypeName"
        :isLink="true"
        direction="right"
        @onClick="doTypeShow"
      ></hd-cell>

      <hd-cell title="备注" placeholder="请填写备注信息" :value="requestBody.note" :isLink="true" direction="right" @onClick="doNoteShow"></hd-cell>
    </view>
    <view class="operator">
      <hd-operator-cell title="商品明细" @search="doSearchSku" @scan="doScanSku" :show-add="false"></hd-operator-cell>
    </view>
    <scroll-view v-if="skuList.length > 0" class="main" scroll-y>
      <hd-swipe-action
        v-for="(item, index) in skuList"
        :key="item.goods.uuid"
        @onDelete="doDelete(index)"
        :index="index"
        :swipeAble="true"
        :moveIndex="moveIndex"
        @updateIndex="onUpdateIndex"
      >
        <view class="main-edit-card">
          <view class="sku-edit">
            <view class="sku-left">
              <image lazy-load class="sku-img" :src="skuImg(item)" @click.stop="handlePreviewImg(item)" />
              <view class="info__scale">
                <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
              </view>
            </view>
            <view class="main-title">{{ item.goods.name | empty }}</view>
          </view>
          <view class="main-qpc">
            <text class="qpc-munit">代码：{{ item.goods.code | empty }}</text>
            <text class="qpc-munit">条码：{{ item.goods.inputCode | empty }}</text>
          </view>
          <view class="main-qpc">
            <text class="qpc-munit">单位：{{ item.goods.minMunit | empty }}</text>
            <text class="qpc-munit">规格：{{ item.goods.qpcStr | empty }}</text>
            <text class="qpc-munit">单价：￥{{ item.singlePrice | empty }}/{{ item.goods.minMunit | empty }}</text>
          </view>
          <view class="main-qty">
            <view class="main-qty-left">
              <text class="qpc-munit">当前库存：{{ item.invQty | empty }}{{ item.goods.minMunit | empty }}</text>
            </view>

            <hd-number-box-test
              class="main-qty-right"
              v-model="skuList[index].qty"
              :max="99999999"
              :scale="4"
              @change="doNumberChange($event, index)"
            ></hd-number-box-test>
          </view>
        </view>
      </hd-swipe-action>
    </scroll-view>
    <view v-else class="main-empty">
      <image class="main-empty-icon" :src="'/static/img/img_empty_goods.png' | oss"></image>
      <text class="main-empty-txt">还没有商品呢～</text>
    </view>
    <view class="footer">
      <hd-button :type="permision.submit ? 'white' : 'primary'" v-if="permision.edit" :disabled="disabled" @click="doSave">保存</hd-button>
      <hd-button type="primary" v-if="permision.submit" :disabled="disabled" @click="doSubmit">提交</hd-button>
    </view>
    <uni-popup ref="note" type="bottom">
      <hd-note :maxLength="50" :value="requestBody.note" @confirm="doNoteConfirm" @close="doNoteClose"></hd-note>
    </uni-popup>
    <uni-popup ref="type" type="bottom">
      <hd-reason title="请选择申请类型" @confirm="doTypeConfirm" :reasons="useTypeList" :reason="selectUseType" @close="doTypeClose"></hd-reason>
    </uni-popup>
  </view>
</template>

<script lang="ts" src="./UseApplyEdit.ts"></script>

<style lang="scss" scoped>
.use-apply-edit {
  width: 750rpx;
  min-height: 100vh;
  box-sizing: border-box;
  background: $color-bg-primary;
  display: flex;
  flex-direction: column;
  padding-bottom: 124rpx !important;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 124rpx) !important;
  padding-bottom: calc(env(safe-area-inset-bottom) + 124rpx) !important;

  .header {
    margin-bottom: 16rpx;
    &-file {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      &-type {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16rpx;
        width: 156rpx;
        height: 56rpx;
        background: $color-bg-primary;
        border-radius: 8rpx;
        font-size: 26rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: $color-text-secondary;
        &--active {
          background: linear-gradient(132deg, #2e84fe 0%, #1c64fd 100%);
          color: #ffffff;
        }
      }
    }
  }
  .operator {
    position: relative;
    &::after {
      position: absolute;
      z-index: 1;
      bottom: 0;
      left: 0;
      content: ' ';
      width: 100%;
      height: 2rpx;
      background: #e1e1e1;
      transform: scaleY(0.5);
    }
  }

  .main {
    background: #ffffff;
    .main-edit-card {
      position: relative;
      display: flex;
      flex-direction: column;
      width: 100%;
      box-sizing: border-box;
      padding: 24rpx 0;
      background: rgba(255, 255, 255, 1);

      &:not(:last-child)::after {
        position: absolute;
        box-sizing: border-box;
        content: ' ';
        pointer-events: none;
        bottom: 0;
        left: 19rpx;
        width: calc(100% - 38rpx);
        border-bottom: 2rpx solid rgba(227, 228, 232, 1);
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
      }
      .sku-edit {
        display: flex;
        padding: 0 24rpx;
        box-sizing: border-box;
        margin-bottom: 12rpx;
      }
      .sku-left {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        flex: 0 0 auto;
        margin-right: 16rpx;

        .sku-img {
          width: 120rpx;
          height: 120rpx;
        }
        .info__scale {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 24rpx;
          height: 24rpx;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 8rpx 0px 8rpx 0rpx;
          text-align: center;
          @include flex();
          &-img {
            width: 16rpx;
            height: 16rpx;
          }
        }
      }
      .main-title {
        width: 100%;
        font-size: 30rpx;
        font-family: $font-medium;
        font-weight: 500;
        color: rgba(40, 44, 52, 1);
        line-height: 40rpx;
      }
      .main-qpc {
        position: relative;
        margin-bottom: 16rpx;
        width: 100%;
        box-sizing: border-box;
        padding: 0 18rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .qpc-munit {
          font-size: 24rpx;
          font-weight: 400;
          margin-left: 12rpx;
          color: rgba(88, 90, 94, 1);
          line-height: 40rpx;
          width: 50%;
          text-align: left;
        }
        .qpc-detail {
          margin-left: 20rpx;
          font-size: 24rpx;
          color: rgba(148, 150, 154, 1);
          line-height: 40rpx;
          width: 50%;
          text-align: left;
        }
      }
      .main-qty {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        width: 100%;
        box-sizing: border-box;
        padding: 0 18rpx;
        &-left {
          display: flex;
          align-items: center;
          font-size: 24rpx;
          color: rgba(88, 90, 94, 1);
        }

        .qty-price {
          font-size: 32rpx;
          font-weight: 600;
          color: rgba(255, 136, 0, 1);
          line-height: 40rpx;
        }
      }
    }
    .loading {
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: rgb(148, 150, 154);
    }
  }
  .main-empty {
    display: flex;
    align-items: center;
    flex-direction: column;

    .main-empty-icon {
      margin-top: 118rpx;
      width: 300rpx;
      height: 300rpx;
    }
    .main-empty-txt {
      margin-top: 10rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(148, 150, 154, 1);
      line-height: 40rpx;
    }
  }

  .footer {
    display: flex;
    position: fixed;
    background: #ffffff;
    z-index: 2;
    bottom: 0;
    left: 0;
    height: 100rpx;
    width: 100%;
    padding-bottom: calc(constant(safe-area-inset-bottom)) !important;
    padding-bottom: calc(env(safe-area-inset-bottom)) !important;
  }
}
</style>
