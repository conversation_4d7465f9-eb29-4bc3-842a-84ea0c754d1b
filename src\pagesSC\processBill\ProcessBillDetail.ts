import { Component, Vue } from 'vue-property-decorator'
import SaleList from './cmp/SaleList.vue'
import ProcessBillApi from '@/network/processBill/ProcessBillApi'
import FlowDialog from '../cmp/FlowDialog.vue'
import DateUtil from '@/utils/DateUtil'
import ProcessBill from '@/model/processBill/ProcessBill'
import { ProcessBillState } from '@/model/processBill/ProcessBillState'
import ProcessBillLog from '@/model/processBill/ProcessBillLog'
import OperatorCell from './cmp/OperatorCell.vue'
import { ProcessGoodsState } from '@/model/processBill/ProcessGoodsState'

@Component({
  components: { SaleList, FlowDialog, OperatorCell }
})
export default class ProcessBillDetail extends Vue {
  data: ProcessBill = new ProcessBill() // 单据详情
  state: string = '' // 订单状态
  tabList = [{ name: '原料' }, { name: '成品' }] // 选项卡选项
  current: number = 0 // 激活tab
  $refs: any

  // 获取行
  get lines() {
    return this.data.raws
  }

  // 显示去打印
  get showPrint() {
    return this.current === 1 && (this.data.state === 'submitted' || this.data.state === 'approved')
  }

  // 是显示流程
  get processAble() {
    return this.data.state && ProcessBillState.string(this.data.state) !== '加工已提交'
  }

  // 标题
  get type() {
    let type: string = ''
    if (this.current === 0) {
      type = ProcessGoodsState.material
    } else if (this.current === 1) {
      type = ProcessGoodsState.product
    }
    return type
  }

  // 商品总类别数
  get count() {
    let count: number = 0
    if (this.current === 0) {
      count = this.data.rawRecCnt
    } else if (this.current === 1) {
      count = this.data.pdtRecCnt
    }
    return Number(count).scale(4)
  }

  // 商品总金额
  get total() {
    let total: number = 0
    if (this.current === 0) {
      total = this.data.rawTotal
    } else if (this.current === 1) {
      total = this.data.pdtTotal
    }
    return Number(total).scale(2)
  }

  onLoad(option) {
    this.$nextTick(() => {
      this.data.billId = option.id
      this.getDetail()
    })
  }

  // 获取单据详情
  getDetail() {
    this.$showLoading({ delayTime: 200 })
    ProcessBillApi.get(this.data.billId, Object.keys(this.data).concat('details').join(','))
      .then((resp) => {
        this.$hideLoading()
        this.data = resp.data || new ProcessBill()
        this.$nextTick(() => {
          this.state = `${ProcessBillState.string(this.data.state as ProcessBillState)}` || ''
        })
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'none', title: e.msg })
      })
  }

  // tab点击事件
  doHandler(tab, index) {
    this.current = index
  }

  // 查看全部商品
  doViewMore() {
    uni.navigateTo({
      url: '/pagesShopManage/returnApply/ReturnApplyLineTotal?id=' + this.data.billId
    })
  }

  // 显示步骤条
  doShowProcess() {
    if (!this.processAble) {
      return
    } else {
      this.$showLoading({ delayTime: 200 })
      ProcessBillApi.getLog(this.data.billId)
        .then((resp) => {
          this.$hideLoading()
          const ReturnApplyLog: ProcessBillLog[] = resp.data || []
          const items: any[] = []
          for (let index = 0; index < ReturnApplyLog.length; index++) {
            const title = ProcessBillState.string(ReturnApplyLog[index].state)
            const desc = ReturnApplyLog[index].modifierName
            let type = ''
            if (ProcessBillState.string(ReturnApplyLog[index].state) === ProcessBillState.string(ProcessBillState.approved)) {
              type = 'success'
            } else if (ProcessBillState.string(ReturnApplyLog[index].state) === ProcessBillState.string(ProcessBillState.rejected)) {
              type = 'error'
            }
            const date = DateUtil.format(new Date(ReturnApplyLog[index].modified!.toString().replace(/-/g, '/')), 'MM-dd')
            const time = DateUtil.format(new Date(ReturnApplyLog[index].modified!.toString().replace(/-/g, '/')), 'HH:mm:ss')
            items.unshift({ title: title, desc: desc, type: type, date: date, time: time })
          }
          this.$showPopup({
            ref: this.$refs.flowDialog,
            title: '加工申请流程',
            items: items
          })
        })
        .catch((e) => {
          this.$hideLoading()
          this.$showToast({ icon: 'error', title: e.msg })
        })
    }
  }

  isAndroid() {
    let isAndroid: boolean = false
    // #ifdef APP-PLUS
    isAndroid = uni.getSystemInfoSync().platform === 'android'
    // #endif
    return isAndroid
  }

  // 去打印
  goPrint() {
    uni.setStorage({
      key: 'finishedProduct',
      data: this.data.pdts,
      success: () => {
        this.$Router.push({ name: this.isAndroid() ? 'finishedProductPrintAndroid' : 'finishedProductPrint' })
      }
    })
  }
}
