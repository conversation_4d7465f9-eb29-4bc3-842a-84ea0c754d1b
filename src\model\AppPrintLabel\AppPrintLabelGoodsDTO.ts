/*
 * @Author: yuzhip<PERSON>
 * @Date: 2025-03-28 14:03:33
 * @LastEditTime: 2025-04-10 16:44:16
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: /soa/src/model/AppPrintLabel/AppPrintLabelGoodsDTO.ts
 * 记得注释
 */
import GoodsExtDTO from './GoodsExtDTO'

export default class AppPrintLabelGoodsDTO extends GoodsExtDTO {
  // 保质期天数
  validPeriod: Nullable<number> = null
  // 陈列位置，多个以英文逗号分隔
  displayLocation: Nullable<string> = null
  // 会员价
  mbrPrc: Nullable<number> = null
}
