<template>
  <view class="content process-bill-edit" @click="onUpdateIndex(null)">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <view class="process-bill-header">
      <block>
        <hd-cell
          v-if="enableNoneFormule"
          title="配方"
          :value="requestBody.pscpCode ? requestBody.pscpName : '不需要配方'"
          :isLink="true"
          direction="right"
          @onClick="doSelectFormula"
        >
          <view class="psb-cell-value" slot="value">
            <text class="name">{{ requestBody.pscpCode ? requestBody.pscpName : '不需要配方' }}</text>
            <text class="num" v-if="requestBody.multiple && requestBody.pscpCode">x{{ requestBody.multiple }}份</text>
          </view>
        </hd-cell>
        <hd-cell
          title="配方"
          placeholder="请选择配方"
          :value="requestBody.pscpName"
          :isLink="true"
          direction="right"
          @onClick="doSelectFormula"
          v-else
        >
          <view class="psb-cell-value" slot="value" v-if="requestBody.multiple && requestBody.pscpCode">
            <text class="name">{{ requestBody.pscpName }}</text>
            <text class="num">x{{ requestBody.multiple }}份</text>
          </view>
        </hd-cell>
      </block>

      <hd-cell title="备注" placeholder="请填写备注信息" :value="requestBody.note" :isLink="true" direction="right" @onClick="doNoteShow"></hd-cell>
    </view>
    <view class="process-bill-operator">
      <hd-tabs :tabList="tabList" :activeIndex="0" @handler="doHandler"></hd-tabs>
      <operator-cell :type="type" :clickable="clickable" @add="doSelectGoods" @search="doSearchGoods" :total="total" :count="count"></operator-cell>
      <scroll-view v-if="requestBody.raws.length > 0 && current === 1" class="process-bill-body" scroll-y>
        <hd-swipe-action
          v-for="(line, index) in requestBody.raws"
          :key="line.goods.uuid"
          @onDelete="doDelete(line)"
          :index="index"
          :moveIndex="moveIndex"
          @updateIndex="onUpdateIndex"
        >
          <view class="goods-wrap">
            <view class="sku-info">
              <view class="sku-left">
                <image lazy-load class="sku-img" :src="skuImg(line)" @click.stop="handlePreviewImg(line)" />
                <view class="info__scale">
                  <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
                </view>
              </view>
              <view class="goods-name">{{ line.goods.name }}</view>
            </view>
            <view class="goods-code">
              <view>代码：{{ line.goods.code | cds(14) }}</view>
              <view class="good-total">条码：{{ line.goods.inputCode | cds(14) }}</view>
            </view>
            <view class="goods-code" v-if="btnPermission.globalPriceView">
              <view v-if="line.total > 0 || requestBody.raws[index].qpcQty > 0">成本金额：￥{{ line.total | fmt('0.00') | amountView }}</view>
            </view>
            <view class="goods-info">
              <view class="left">
                <view class="goods-price">
                  <text class="currency">￥</text>
                  {{ line.goods.price | empty | amountView }}
                </view>
                <view class="goods-unit">/{{ line.goods.munit | empty }}</view>
                <text class="qpc-detail">规格：{{ line.goods.qpcStr | empty }}</text>
              </view>
              <hd-number-box-test
                v-model="requestBody.raws[index].qpcQty"
                @change="doNumberChange($event, index)"
                :max="99999999"
                :min="0"
                :scale="4"
              ></hd-number-box-test>
            </view>
          </view>
        </hd-swipe-action>
      </scroll-view>
      <scroll-view v-else-if="requestBody.pdts.length > 0 && current === 0" class="process-bill-body" scroll-y>
        <hd-swipe-action
          v-for="(line, index) in requestBody.pdts"
          :key="line.goods.uuid"
          @onDelete="doDelete(line)"
          :index="index"
          :moveIndex="moveIndex"
          @updateIndex="onUpdateIndex"
        >
          <view class="goods-wrap">
            <view class="sku-info">
              <view class="sku-left">
                <image lazy-load class="sku-img" :src="skuImg(line)" @click.stop="handlePreviewImg(line)" />
                <view class="info__scale">
                  <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
                </view>
              </view>
              <view class="goods-name">{{ line.goods.name }}</view>
            </view>
            <view class="goods-code">
              <view>代码：{{ line.goods.code | cds(14) }}</view>
              <view>条码：{{ line.goods.inputCode | cds(14) }}</view>
            </view>
            <view class="goods-code">
              <view v-if="line.total > 0 || requestBody.pdts[index].qpcQty > 0">零售金额：￥{{ line.total | fmt('0.00') | amountView }}</view>
            </view>
            <view class="goods-info">
              <view class="left">
                <view class="goods-price">
                  <text class="currency">￥</text>
                  {{ line.goods.price | empty | amountView }}
                </view>
                <view class="goods-unit">/{{ line.goods.munit | empty }}</view>
                <text class="qpc-detail">规格：{{ line.goods.qpcStr | empty }}</text>
              </view>
              <hd-number-box-test
                v-model="requestBody.pdts[index].qpcQty"
                @change="doNumberChange($event, index)"
                :max="99999999"
                :min="0"
                :scale="4"
              ></hd-number-box-test>
            </view>
          </view>
        </hd-swipe-action>
      </scroll-view>

      <view class="process-bill-empty" v-else>
        <image class="process-bill-empty-icon" :src="'/static/img/img_empty_goods.png' | oss"></image>
        <text class="process-bill-empty-txt">还没有商品呢~</text>
      </view>
    </view>
    <view class="process-bill-footer">
      <hd-button :type="btnPermission.submit ? 'white' : 'primary'" v-if="btnPermission.save" :disabled="!btnClickable" @click="doSave">
        保存
      </hd-button>
      <hd-button type="primary" v-if="btnPermission.submit" :disabled="!btnClickable" @click="doSubmit">提交</hd-button>
    </view>
    <uni-popup ref="note" type="bottom">
      <hd-note :value="requestBody.note" @confirm="doNoteConfirm" @close="doNoteClose"></hd-note>
    </uni-popup>
  </view>
</template>

<script lang="ts" src="./ProcessBillEdit.ts"></script>

<style lang="scss" scoped>
.process-bill-edit {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100vh;
  .process-bill-header {
    margin-bottom: 16rpx;
    flex: 0 0 auto;
    .psb-cell-value {
      font-size: 30rpx;
      color: #585a5e;
      display: flex;
      align-items: center;
      height: 44rpx;
      .name {
        display: inline-block;
        height: 44rpx;
        max-width: 340rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 44rpx;
      }
      .num {
        display: inline-block;
        height: 44rpx;
        line-height: 44rpx;
        margin-left: 20rpx;
      }
    }
  }
  .process-bill-operator {
    overflow-y: auto;
    @include flex(column);
    flex: 1 1 auto;
    background: #ffffff;
    ::v-deep .tab-list {
      flex: 0 0 auto;
    }
    ::v-deep .operator-cell {
      flex: 0 0 auto;
    }
  }
  .process-bill-body {
    overflow-y: auto;
    width: 750rpx;
    flex: 0 0 auto;
    height: calc(100vh - 492rpx);
    background: #ffffff;
    .goods-wrap {
      box-sizing: border-box;
      width: 100%;
      padding: 24rpx;
      border-bottom: 1rpx solid #e3e4e8;
      .sku-info {
        display: flex;
      }
      .sku-left {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        flex: 0 0 auto;
        margin-right: 16rpx;

        .sku-img {
          width: 120rpx;
          height: 120rpx;
        }
        .info__scale {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 24rpx;
          height: 24rpx;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 8rpx 0px 8rpx 0rpx;
          text-align: center;
          @include flex();
          &-img {
            width: 16rpx;
            height: 16rpx;
          }
        }
      }

      .goods-name {
        font-size: 30rpx;
        font-weight: 500;
        color: #282c34;
        line-height: 40rpx;
      }

      .goods-code {
        display: flex;
        font-size: 26rpx;
        color: #94969a;
        line-height: 36rpx;
        justify-content: space-between;
        margin-top: 12rpx;
        .good-total {
          margin-left: 24rpx;
        }
      }

      .goods-info {
        @include flex(row, space-between, center);
        padding-top: 20rpx;

        .left {
          @include flex(row, flex-start, center);
          line-height: 32rpx;

          .goods-price {
            color: #ff8800;
            font-size: 32rpx;

            .currency {
              font-size: 24rpx;
            }
          }

          .goods-unit {
            padding-left: 10rpx;
            font-size: 24rpx;
            color: #585a5e;
          }
          .qpc-detail {
            margin-left: 20rpx;
            font-size: 24rpx;
            color: rgba(148, 150, 154, 1);
            line-height: 40rpx;
          }

          .goods-spec {
            padding-left: 20rpx;
            font-size: 24rpx;
            color: #585a5e;

            text {
              color: #94969a;
            }
          }
        }
      }
    }
    .process-bill-edit-card:after {
      position: absolute;
      box-sizing: border-box;
      content: ' ';
      pointer-events: none;
      bottom: 0;
      left: 19rpx;
      width: calc(100% - 38rpx);
      border-bottom: 2rpx solid rgba(227, 228, 232, 1);
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
    }
    .loading {
      height: 72rpx;
      @include flex(row, center, center);
      font-size: 24rpx;
      color: rgb(148, 150, 154);
    }
  }
  .process-bill-empty {
    flex: 1 1 auto;
    @include flex(column, center, center);

    .process-bill-empty-icon {
      width: 300rpx;
      height: 300rpx;
    }
    .process-bill-empty-txt {
      margin-top: 10rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(148, 150, 154, 1);
      line-height: 40rpx;
    }
  }
  .process-bill-footer {
    display: flex;
    flex: 0 0 auto;
  }
}
</style>
