/*
 * @Author: yuzhipi
 * @Date: 2025-03-28 14:03:33
 * @LastEditTime: 2025-04-16 10:37:41
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: /soa/src/network/AppPrintLabelApi/AppPrintLabelApi.ts
 * 记得注释
 */
import fly from '@/network/fly'
import AppPrintLabelGoodsDTO from 'model/AppPrintLabel/AppPrintLabelGoodsDTO'
import AppPrintLabelPreviewRequestDTO from 'model/AppPrintLabel/AppPrintLabelPreviewRequestDTO'
import AppPrintLabelPreviewResultDTO from 'model/AppPrintLabel/AppPrintLabelPreviewResultDTO'
import AppPrintLabelTemplateDTO from 'model/AppPrintLabel/AppPrintLabelTemplateDTO'
import AppPrintLabelRprcAdjDTO from 'model/AppPrintLabel/AppPrintLabelRprcAdjDTO'
import AppPrintLabelRprcAdjResultDTO from 'model/AppPrintLabel/AppPrintLabelRprcAdjResultDTO'
import AppPrintLabelPromBillDTO from 'model/AppPrintLabel/AppPrintLabelPromBillDTO'
import AppPrintLabelPromBillResultDTO from 'model/AppPrintLabel/AppPrintLabelPromBillResultDTO'
import AppPrintLabelTpGoodsDTO from 'model/AppPrintLabel/AppPrintLabelTpGoodsDTO'
import QueryRequest from 'model/default/QueryRequest'
import aseResponse from 'model/default/aseResponse'

export default class AppPrintLabelApi {
  /**
   * 查询生效的促销单
   *
   * @param body 预览请求
   * @param tenant 租户标识
   */
  static listEffectPromBill(body: AppPrintLabelPromBillDTO): Promise<aseResponse<AppPrintLabelPromBillResultDTO[]>> {
    return fly.post(`sos/v1/{tenant}/print/label/listEffectPromBill`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询生效的售价改价单
   *
   * @param body 预览请求
   * @param tenant 租户标识
   */
  static listEffectRprcAdj(body: AppPrintLabelRprcAdjDTO): Promise<aseResponse<AppPrintLabelRprcAdjResultDTO[]>> {
    return fly.post(`sos/v1/{tenant}/print/label/listEffectRprcAdj`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取全部打印模板
   *
   * @param tenant 租户标识
   */
  static listTemplate(): Promise<aseResponse<AppPrintLabelTemplateDTO[]>> {
    return fly.post(`sos/v1/{tenant}/print/label/template/list`, {}, {}).then((res) => {
      return res
    })
  }

  /**
   * 预览
   *
   * @param body 预览请求
   * @param tenant 租户标识
   */
  static preview(body: AppPrintLabelPreviewRequestDTO): Promise<aseResponse<AppPrintLabelPreviewResultDTO>> {
    return fly.post(`sos/v1/{tenant}/print/label/preview`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询商品
   *
   * @param body 查询请求
   * @param tenant 租户标识
   */
  static queryGoods(body: QueryRequest): Promise<aseResponse<AppPrintLabelGoodsDTO[]>> {
    return fly.post(`sos/v1/{tenant}/print/label/goods/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 转换为标价签打印标准商品对象
   *
   * @param body 预览请求
   * @param tenant 租户标识
   */
  static transferTpGoods(body: AppPrintLabelTpGoodsDTO[]): Promise<aseResponse<AppPrintLabelGoodsDTO[]>> {
    return fly.post(`sos/v1/{tenant}/print/label/transferTpGoods`, body, {}).then((res) => {
      return res
    })
  }
}
