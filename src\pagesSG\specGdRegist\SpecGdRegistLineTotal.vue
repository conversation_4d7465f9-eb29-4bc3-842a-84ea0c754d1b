<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2025-04-18 18:43:10
 * @LastEditTime: 2025-04-23 13:34:34
 * @LastEditors: weisheng
 * @Description: 特殊品登记明细总列表
 * @FilePath: /soa/src/pagesSG/specGdRegist/SpecGdRegistLineTotal.vue
 * 记得注释
-->
<template>
  <view class="spec-gd-regist-line-total">
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <spec-gd-regist-goods-list :lines="billLines" :show-more="false" :is-sticky="true"></spec-gd-regist-goods-list>

    <!-- 加载更多提示 -->
    <view class="loading-more" v-if="isLoading && pageNum > 0">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多数据提示 -->
    <view class="no-more" v-if="!hasMore && billLines.length > 0">
      <text class="no-more-text">没有更多数据了</text>
    </view>

    <!-- 空数据提示 -->
    <view class="empty-list" v-if="!isLoading && billLines.length === 0">
      <image class="empty-image" :src="'/static/img/img_empty_goods.png' | oss"></image>
      <text class="empty-text">暂无商品明细</text>
    </view>
  </view>
</template>

<script lang="ts" src="./SpecGdRegistLineTotal.ts"></script>

<style lang="scss" scoped>
.spec-gd-regist-line-total {
  background-color: #ffffff;
  margin-bottom: 20rpx;

  .loading-more,
  .no-more {
    width: 100%;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;

    .loading-text,
    .no-more-text {
      font-size: 26rpx;
      color: #999999;
    }
  }

  .empty-list {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 60rpx 0;
    background-color: #ffffff;

    .empty-image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999999;
    }
  }
}
</style>
