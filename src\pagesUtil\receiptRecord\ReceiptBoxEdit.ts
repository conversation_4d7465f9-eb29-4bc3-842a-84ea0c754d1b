import { Component } from 'vue-property-decorator'

import { mixins } from 'vue-class-component'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import QueryRequest from '@/model/receipt/default/QueryRequest'
import BoxEditCard from './cmp/BoxEditCard.vue'
import BoxEditCardDialog from './cmp/BoxEditCardDialog.vue'
import ID from '@/model/receipt/default/ID'
import UserInfo from '@/model/user/UserInfo'
import { Getter, State } from 'vuex-class'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import AppReceiptRecordApi from '@/network/AppReceiptRecordApi/AppReceiptRecordApi'
import AppReceiptRecordDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordDTO'
import AppReceiptRecordBoxDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxDTO'
import AppReceiptRecordBoxCheckDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxCheckDTO'
import AppReceiptRecordBoxCheckGoodsDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxCheckGoodsDTO'
import AppReceiptRecordBoxFullCheckDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxFullCheckDTO'
import ReceiptApi from '@/network/receipt/ReceiptApi'
import AppReceiptMultipleRcvCloser from '@/model/receipt/AppReceiptMultipleRcvCloser'
import AppReceiptMultipleRcvCloseResult from '@/model/receipt/AppReceiptMultipleRcvCloseResult'
import RecordDiffDialog from './cmp/RecordDiffDialog.vue'
import swipeMix from '@/components/hd-swipe-action/swipeMix'
import CommonUtil from '@/utils/CommonUtil'
import SkuRecordDialog from '@/pages/cmp/SkuRecordDialog.vue'
import Slot from '@/model/data/Slot'
import GoodsSlotBinder from '@/model/data/GoodsSlotBinder'
import SelectExhibit from '@/components/select-exhibit/SelectExhibit.vue'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import ResetExhibit from '@/components/select-exhibit/ResetExhibit.vue'
import AppReceiptRecordBoxGoodsDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxGoodsDTO'
import AppReceiptBoxDiffSumDTO from '@/model/receipt/AppReceiptBoxDiffSumDTO'
import ReceiptApiV2 from '@/network/receipt/ReceiptApiV2'
import Receipt from '@/model/receipt/Receipt'
import AppReceiptRecordInitializer from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordInitializer'
import AppReceiptMultipleRcvRecordQueryer from '@/model/receipt/AppReceiptMultipleRcvRecordQueryer'
import AppReceiptMultipleRcvGdRecordDTO from '@/model/receipt/AppReceiptMultipleRcvGdRecordDTO'
import CheckPagination from '../cmp/CheckPagination.vue'
import PagePicker from '../cmp/PagePicker.vue'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import { runJob } from '@/common/Job'
import AppReceiptRecordSubmitterDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordSubmitterDTO'

@Component({
  components: {
    BoxEditCard,
    BoxEditCardDialog,
    RecordDiffDialog,
    SkuRecordDialog,
    SelectExhibit,
    ViewExhibit,
    ResetExhibit,
    CheckPagination,
    PagePicker
  }
})
export default class ReceiptBoxEdit extends mixins(swipeMix, BroadCast) {
  @State('userInfo') userInfo: UserInfo // 当前用户信息
  @Getter('qtyScale') qtyScale: number
  @State('optionList') optionList: ModuleOption[]

  $refs: any

  receipt: AppReceiptRecordDTO = new AppReceiptRecordDTO() // 单据信息
  boxList: AppReceiptRecordBoxDTO[] = [] // 箱码列表
  // 分页相关
  pageSize: number = 10 // 每页大小
  pageNum: number = 0 // 页码
  finished: boolean = false // 是否加载完成
  isLoading: boolean = false // 是否在加载

  total: number = 0 // 总数
  eachPage: number = 100 // 每页条数（分页器分页每页条数）
  currentPage: number = 0 // 当前页码(分页器分页的页码)
  // 分页器全部页数
  get totalPage() {
    return Math.ceil(this.total / this.eachPage)
  }
  /**
   * 是否展示分页器
   * 当finished时显示
   * 或者当前列表数据大于等于每页条数时展示分页器
   */
  get showPagination() {
    return this.totalPage > 1 && (this.boxList.length >= this.eachPage || this.finished)
  }

  summary: AppReceiptRecordDTO = new AppReceiptRecordDTO() // 箱子汇总信息
  unConfirmPack: Nullable<number> = null // 未确认箱数
  confirmedPack: Nullable<number> = null // 已确认总箱数
  showConfirmed: boolean = false // 是否展示已收
  viewMine: boolean = false // 仅看自己
  scaning: boolean = false // 是否连扫中
  billId: string = '' // 单据id
  isRenovote: boolean = false // 列表页是否需要刷新
  num: string = '' // 收货单单号（整单单号，非本次）

  hasSelelcExhibit: Slot = new Slot() // 已选中的陈列位置
  viewExhibitInfo: AppReceiptRecordBoxGoodsDTO = new AppReceiptRecordBoxGoodsDTO() // 陈列位置弹窗数据
  exhibitIndex: number = 0 // 陈列位置下标

  // 获取按钮权限
  get btnPermission() {
    const btnPermission = {
      submit: PermissionMgr.hasPermission(Permission.receiptSubmit),
      save: PermissionMgr.hasPermission(Permission.receiptOneClick),
      finish: PermissionMgr.hasPermission(Permission.receiptFinish)
    }
    return btnPermission
  }

  /**
   * 提交加工方式：0-异步；1-同步
   */
  get submitProcessMode() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []

    if (moduleConfig.length > 0) {
      return moduleConfig.findIndex((item) => item.options && item.options.submitProcessMode === '1') > -1
    }
    return false
  }

  // 启用单品多人收货，0-否，1-是
  get enableSingleGoodsMulRcver() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []

    if (moduleConfig.length > 0) {
      return moduleConfig.findIndex((item) => item.options && item.options.enableSingleGoodsMulRcver === '1') > -1
    }
    return false
  }

  async onLoad(option) {
    if (this.enableSingleGoodsMulRcver) {
      this.viewMine = true
    }
    if (option && option.id) {
      this.billId = option.id
      this.$nextTick(async () => {
        await this.handleInit(option.id)
        if (option.keyword) {
          let keyword: string = ''
          try {
            keyword = decodeURIComponent(option.keyword)
          } catch (error) {
            keyword = option.keyWord
          }
          this.doScanAfter(keyword)
        }
        if (option.searchword) {
          let searchword: string = ''
          try {
            searchword = decodeURIComponent(option.searchword)
          } catch (error) {
            searchword = option.searchword
          }
          this.$Router.push({
            name: 'receiptRecordBoxSearch',
            params: {
              id: this.receipt.billId,
              receiptBillId: this.billId,
              searchword: searchword
            }
          })
        }
      })
    }

    // 卸载
    uni.$off('boxQuery')
    // 重新监听
    uni.$on('boxQuery', () => {
      this.isRenovote = true
      this.doResetPage()
      this.loadBoxList()
    })

    // 卸载
    uni.$off('update-un-confirm')
    // 重新监听
    uni.$on('update-un-confirm', (res) => {
      this.isRenovote = true
      if (res) {
        this.unConfirmPack = res.unConfirmPack
        this.confirmedPack = res.confirmedPack
      }
    })
    uni.$off('refreshExhibit')
    uni.$on('refreshExhibit', () => {
      this.$refs.exhibit.refresh()
      this.$refs.resetExhibit.refresh()
    })
  }

  onUnload() {
    // 卸载
    uni.$off('boxQuery')
    uni.$off('update-un-confirm')
    uni.$emit('PATH', this.isRenovote)
    uni.$off('refreshExhibit')
  }

  // 显示更多商品
  async onReachBottom() {
    if (this.finished || this.isLoading || this.boxList.length === this.eachPage) {
      return
    }
    try {
      this.$showLoading()
      await this.queryBox()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '查询失败',
        icon: 'error'
      })
    }
  }

  /**
   * 切换页码
   * @param curentPage
   */
  handlePageChange(curentPage: number) {
    this.currentPage = curentPage
    this.pageNum = ((curentPage - 1) * (this.eachPage / this.pageSize)).scale(0)
    this.finished = false
    this.boxList = []
    this.loadBoxList()
    this.$nextTick(() => {
      uni.pageScrollTo({
        scrollTop: 0
      })
    })
  }
  //分页器弹窗确认
  handlePagePickerConfirm(page: number) {
    this.$refs.checkpick.doConfirm(page)
  }

  async handleInit(billId: string) {
    try {
      this.$showLoading({ delayTime: 200 })
      const bill = await this.getBill(billId)
      if (bill.state !== 'initial' && bill.state !== 'receiving') {
        this.$hideLoading()
        this.$showModal({
          title: '提示',
          content: '该单据已被处理',
          showCancel: false,
          confirmText: '我知道了',
          success: (action) => {
            if (action.confirm) {
              this.isRenovote = true
              this.$Router.replace({
                name: 'receiptDetail',
                params: { id: billId }
              })
            }
          }
        })
        return
      }
      this.num = bill.num
      this.receipt = await this.getReceipt(billId)
      this.unConfirmPack = this.receipt.unConfirmPack
      this.confirmedPack = this.receipt.confirmedPack
      if (this.receipt.recCnt === 0) {
        this.$hideLoading()
        this.$showModal({
          title: '提示',
          content: '该单据没有商品可以继续收货',
          confirmText: '结束收货',
          cancelText: '返回',
          success: async (action) => {
            if (action.confirm) {
              this.handleFinishDirect(billId)
            } else {
              this.$Router.back(1)
            }
          }
        })
        return
      }

      await this.queryBox()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({ icon: 'error', title: error.msg })
    }
  }

  /**
   * 直接关单
   */
  async handleFinishDirect(billId: string, force: boolean = false) {
    try {
      this.$showLoading()
      const result = await this.finish({
        billId: billId,
        confirmSubmitReceiptRecord: force
      })
      this.$hideLoading()
      // code为0时，表示结束收货成功
      if (result.code === '0') {
        this.isRenovote = true
        this.$showToast({ icon: 'success', title: '操作成功' })
        const timer = setTimeout(() => {
          clearTimeout(timer)
          this.$Router.replace({
            name: 'receiptDetail',
            params: {
              id: billId
            }
          })
        }, 1500)
      } else if (result.code === '1') {
        // code为1时，表示结束时存在其他未提交但是已清点的商品，需要用户确认
        this.$showModal({
          title: '提醒',
          content: `${result.errMsg}`,
          showCancel: true,
          confirmText: '继续结束',
          success: (action) => {
            if (action.confirm) {
              this.handleFinishDirect(billId, true)
            }
          }
        })
      } else {
        this.$showModal({
          title: '结束收货失败',
          content: `失败原因:${result.errMsg}`,
          showCancel: true,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.handleFinishDirect(billId)
            }
          }
        })
      }
    } catch (error) {
      this.$hideLoading()
      this.$showModal({
        title: '结束收货失败',
        content: `失败原因:${error.msg}`,
        showCancel: true,
        confirmText: '重试',
        cancelText: '返回',
        success: (action) => {
          if (action.confirm) {
            this.handleFinishDirect(billId)
          } else {
            this.$Router.back(1)
          }
        }
      })
    }
  }

  handleViewMine() {
    this.viewMine = !this.viewMine
    this.doResetPage()
    this.loadBoxList()
  }

  doSearch() {
    this.$Router.push({
      name: 'receiptRecordBoxSearch',
      params: {
        receiptBillId: this.billId,
        id: this.receipt.billId
      }
    })
  }

  // 扫码按钮点击事件
  doScan() {
    uni.scanCode({
      success: (res) => {
        const scanWord = res.result || ''
        this.doScanAfter(scanWord)
      },
      fail: () => {
        this.scaning = false
      }
    })
  }

  /**
   * 中断连续扫码
   */
  doStopScan() {
    this.scaning = false
  }

  /**
   * PDA扫码回调事件
   * @param scanWord 扫码文字
   * @param isScan 是否扫码
   * @returns
   */
  async doScanAfter(scanWord: string, isScan: boolean = true) {
    if (this.isLoading) {
      this.$showToast({ title: '正在加载，请稍后重试~' })
      return
    }

    try {
      this.$showLoading()
      this.scaning = true
      const boxList = await this.queryBox(isScan, scanWord)
      this.$hideLoading()
      if (!boxList || !boxList.length) {
        // 无搜索结果跳转至搜索页面
        this.$Router.push({
          name: 'receiptRecordBoxSearch',
          params: {
            id: this.receipt.billId,
            receiptBillId: this.billId,
            keyWord: scanWord
          }
        })
      } else if (boxList.length === 1) {
        if (boxList[0].type === 'split') {
          this.handleCardClick(boxList[0])
        } else {
          this.handleEdit(boxList[0], 0)
        }
      } else {
        this.$Router.push({
          name: 'receiptRecordBoxSearch',
          params: {
            id: this.receipt.billId,
            receiptBillId: this.billId,
            keyWord: scanWord
          }
        })
      }
    } catch (error) {
      this.scaning = false
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 重置分页参数
   */
  doResetPage() {
    this.pageNum = 0
    this.currentPage = 0
    this.total = 0
    this.isLoading = false
    this.finished = false
  }

  /**
   * 拆零箱点击
   * @param box
   */
  handleCardClick(box: AppReceiptRecordBoxDTO) {
    if (box.type === 'split') {
      this.$Router.push({
        name: 'receiptRecordBoxLineEdit',
        params: {
          boxNo: box.boxNo,
          uuid: box.uuid,
          id: this.receipt.billId,
          receiptBillId: this.billId
        }
      })
    }
  }

  /**
   * 查看商品行收货记录
   * @param box 箱
   */
  async handleViewDetail(box: AppReceiptRecordBoxDTO) {
    try {
      this.$showLoading()
      const recordList = await this.listMultipleRcvRecord({
        billId: this.billId,
        gdInputCode: box.boxGoodss[0].goods.inputCode
      })
      this.$hideLoading()

      if (recordList.length === 0) {
        this.$showToast({
          title: '暂无收货记录'
        })
      } else {
        this.$refs.recordDialog.open(recordList)
      }
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败'
      })
    }
  }

  /**
   * 查询商品行多次收货记录
   * @param body
   * @returns
   */
  listMultipleRcvRecord(body: AppReceiptMultipleRcvRecordQueryer) {
    return new Promise<AppReceiptMultipleRcvGdRecordDTO[]>((resolve, reject) => {
      ReceiptApi.listMultipleRcvRecord(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 确认收货前置
   * @param box
   */
  handleBeforeConfirm(box: AppReceiptRecordBoxDTO) {
    this.$showModal({
      title: '确定已完成该商品收货？',
      success: async (action) => {
        if (action.confirm) {
          this.handleConfirm(box)
        }
      }
    })
  }

  /**
   * 待收货的箱子确认收货
   * @param box
   */
  async handleConfirm(box: AppReceiptRecordBoxDTO, confirm: boolean = true) {
    try {
      this.$showLoading()
      const boxGoodss: AppReceiptRecordBoxCheckGoodsDTO[] = box.boxGoodss.map((item) => {
        return {
          uuid: item.uuid,
          gdUuid: item.goods.uuid,
          receiptQty: item.receiptQty || 0,
          receiptQpcQty: item.receiptQpcQty || '0',
          receiptTotal: item.goods.isDisp
            ? Number(item.receiptQty).multiply(item.goods.price).divide(item.goods.qpc).scale(2)
            : box.receiptQty.multiply(item.goods.price).scale(2),
          bindingSlots: null
        }
      })
      const body: AppReceiptRecordBoxCheckDTO = {
        confirmed: confirm,
        billId: this.receipt.billId,
        boxGoodss: boxGoodss,
        boxNo: box.boxNo,
        receiptQty: box.receiptQty
      }
      this.summary = await this.modifyBox(body)
      this.unConfirmPack = this.summary.unConfirmPack
      this.confirmedPack = this.summary.confirmedPack
      this.isRenovote = true

      this.$hideLoading()
      this.doResetPage()
      this.loadBoxList()

      if (this.scaning) {
        this.doScan()
      }
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 取消收货
   * @param sku 商品
   */
  handleCancel(box: AppReceiptRecordBoxDTO) {
    const target: AppReceiptRecordBoxDTO = CommonUtil.deepClone(box)
    this.handleConfirm(target, false)
  }

  /**
   * 编辑箱
   * @param box
   */
  handleEdit(box: AppReceiptRecordBoxDTO, index: number) {
    this.$refs.edit.open(box)
    this.exhibitIndex = index
    console.log('IN', this.exhibitIndex)
  }

  handleEditReason(box: AppReceiptRecordBoxDTO) {
    this.$refs.reason.open(box)
  }

  /**
   * 查看未确认
   */
  handleShowUnconfirmed() {
    if (this.showConfirmed) {
      this.showConfirmed = false
      this.doResetPage()
      this.loadBoxList()
      this.onUpdateIndex(null)
    }
  }

  /**
   * 查看已确认
   */
  async handleShowConfirmed() {
    if (!this.showConfirmed) {
      this.showConfirmed = true
      this.doResetPage()
      this.loadBoxList()
    }
  }

  async loadBoxList() {
    try {
      this.$showLoading()
      await this.queryBox()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '查询失败',
        icon: 'error'
      })
    }
  }

  /**
   * 保存按钮事件 无实际逻辑，自动保存
   */
  handleSave() {
    this.$showToast({ icon: 'success', title: '保存成功' })
  }

  handlePreFinish() {
    this.$showModal({
      title: '提示',
      content: '确认关闭收货后，该单后续将无法继续收货，不影响已收货商品',
      success: async (action) => {
        if (action.confirm) {
          this.handleFinish()
        }
      }
    })
  }

  /**
   * 结束收货
   * 先submit
   * 再查询差异
   * 最后finish
   */
  async handleFinish(force: boolean = false) {
    try {
      this.$showLoading()
      if (this.submitProcessMode) {
        const success = await this.submitAndProcess() // 进行加工处理
        if (!success) return
      } else {
        const body = new AppReceiptRecordSubmitterDTO()
        body.billId = this.receipt.billId
        if (this.enableSingleGoodsMulRcver) {
          body.scope = this.viewMine ? '0' : '1'
        }
        await this.modifyAndSubmitByBox(body)
      }
      const queryRequest = new QueryRequest()
      queryRequest.page = 0
      queryRequest.pageSize = 1
      queryRequest.conditions = [{ operation: 'billId:=', parameters: [this.billId] }]
      const diff = await this.queryBoxDiff(queryRequest)
      if (diff && diff.boxes && diff.boxes.length) {
        this.$showLoading()
        this.$refs.diff.open(this.billId)
      } else {
        const result = await this.finish({
          billId: this.billId,
          confirmSubmitReceiptRecord: force
        })
        this.$hideLoading()
        // code为0时，表示结束收货成功
        if (result.code === '0') {
          this.isRenovote = true
          this.$showToast({ icon: 'success', title: '操作成功' })
          const timer = setTimeout(() => {
            clearTimeout(timer)
            this.$Router.replace({
              name: 'receiptDetail',
              params: {
                id: this.billId
              }
            })
          }, 1500)
        } else if (result.code === '1') {
          // code为1时，表示结束时存在其他未提交但是已清点的商品，需要用户确认
          this.$showModal({
            title: '提醒',
            content: `${result.errMsg}`,
            showCancel: true,
            confirmText: '继续结束',
            success: (action) => {
              if (action.confirm) {
                this.handleFinishDirect(this.billId, true)
              }
            }
          })
        } else {
          this.$showModal({
            title: '结束收货失败',
            content: `失败原因:${result.errMsg}`,
            showCancel: true,
            confirmText: '重试',
            success: (action) => {
              if (action.confirm) {
                this.handleFinish()
              }
            }
          })
        }
      }
    } catch (error) {
      this.$hideLoading()
      this.$showModal({
        title: '结束收货失败',
        content: `失败原因:${error.msg}`,
        showCancel: true,
        confirmText: '重试',
        success: (action) => {
          if (action.confirm) {
            this.handleFinish()
          }
        }
      })
    }
  }
  handlePreSubmit() {
    this.$showModal({
      title: '确定提交当前收货单？',
      success: async (action) => {
        if (action.confirm) {
          this.handleSubmit()
        }
      }
    })
  }

  async handleSubmit() {
    try {
      this.$showLoading()
      if (this.submitProcessMode) {
        const success = await this.submitAndProcess() // 进行加工处理
        if (success) {
          this.isRenovote = true
          this.$hideLoading()
          this.$showToast({ icon: 'success', title: '提交成功' })
          const timer = setTimeout(() => {
            clearTimeout(timer)
            this.$Router.replace({
              name: 'receiptRecordDetail',
              params: {
                id: this.receipt.billId,
                receiptBillId: this.billId
              }
            })
          }, 1500)
        }
      } else {
        const body = new AppReceiptRecordSubmitterDTO()
        body.billId = this.receipt.billId
        if (this.enableSingleGoodsMulRcver) {
          body.scope = this.viewMine ? '0' : '1'
        }
        await this.modifyAndSubmitByBox(body)
        this.isRenovote = true
        this.$hideLoading()
        this.$showToast({ icon: 'success', title: '提交成功' })
        const timer = setTimeout(() => {
          clearTimeout(timer)
          this.$Router.replace({
            name: 'receiptRecordDetail',
            params: {
              id: this.receipt.billId,
              receiptBillId: this.billId
            }
          })
        }, 1500)
      }
    } catch (error) {
      this.$hideLoading()
      this.$showModal({
        title: '提交失败',
        content: `失败原因:${error.msg}`,
        showCancel: true,
        confirmText: '重试',
        success: (action) => {
          if (action.confirm) {
            this.handleSubmit()
          }
        }
      })
    }
  }

  // 异步加工接口
  async submitAndProcess() {
    return new Promise<boolean>((resolve, reject) => {
      const body = new AppReceiptRecordSubmitterDTO()
      body.billId = this.receipt.billId
      if (this.enableSingleGoodsMulRcver) {
        body.scope = this.viewMine ? '0' : '1'
      }
      AppReceiptRecordApi.submitAndProcess(body)
        .then((res) => {
          if (res.data.state === 'executing') {
            runJob(
              this,
              res.data.id,
              () => {
                resolve(true)
              },
              (fail) => {
                this.$showToast({ icon: 'error', title: fail.message || '提交失败' })
                resolve(false)
              }
            )
          } else if (res.data.state === 'completed') {
            resolve(true)
          } else {
            resolve(false)
            this.$showToast({ icon: 'error', title: res.data.message || '提交失败' })
          }
        })
        .catch((err) => {
          resolve(false)
          this.$showToast({ icon: 'error', title: err.msg || '提交失败' })
        })
        .finally(() => {
          this.$hideLoading()
        })
    })
  }

  // 获取本次收货单据详情
  getReceipt(billId: string) {
    return new Promise<AppReceiptRecordDTO>((resolve, reject) => {
      const params = new AppReceiptRecordInitializer()
      params.id = billId
      params.source = this.receipt.source
      AppReceiptRecordApi.getInitialByReceipt(params)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  // 获取收货单单据详情
  getBill(billId: string) {
    return new Promise<Receipt>((resolve, reject) => {
      ReceiptApiV2.get(billId)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查询箱码列表
   * @param body
   * @returns
   */
  queryBox(isScan: boolean = false, keyWord?: string) {
    this.isLoading = true
    const body = new QueryRequest()
    body.page = this.pageNum
    body.pageSize = this.pageSize
    body.sorts = [
      { asc: false, field: 'type' },
      { asc: true, field: 'lineNo' }
    ]
    body.conditions = [{ operation: 'billId:=', parameters: [this.receipt.billId] }]

    if (this.showConfirmed) {
      body.sorts.push({ field: 'defSlotCode', asc: true })
      body.conditions.push({ operation: 'confirmed:=', parameters: ['true'] })
      if (this.viewMine) {
        body.conditions.push({ operation: 'consigneeId:=', parameters: [this.userInfo.loginId || ''] })
      }
    } else {
      body.conditions.push({ operation: 'confirmed:=', parameters: ['false'] })
    }

    if (keyWord) {
      body.conditions.push({ operation: 'keyword:%=%', parameters: [keyWord] })
    }
    if (isScan) {
      body.page = 0
      body.pageSize = 2
      body.conditions.push({ operation: 'isScan:=', parameters: ['true'] })
    }

    if (!isScan && !keyWord && body.page === 0) {
      body.fetchParts = ['lineTotal']
    }

    return new Promise<AppReceiptRecordBoxDTO[]>((resolve, reject) => {
      AppReceiptRecordApi.queryBox(body)
        .then((resp) => {
          if (body.fetchParts.includes('lineTotal')) {
            this.total = resp.total || 0
          }
          if (!isScan) {
            this.pageNum++
            if (!resp.more) {
              this.finished = true
            }
            if (body.page === 0) {
              this.boxList = resp.data
            } else {
              this.boxList.push(...resp.data)
            }
          }
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
        .finally(() => {
          this.isLoading = false
        })
    })
  }

  /**
   * 提交
   * @param body
   * @returns
   */
  modifyAndSubmitByBox(body: AppReceiptRecordSubmitterDTO) {
    return new Promise<void>((resolve, reject) => {
      AppReceiptRecordApi.submit(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查询单据差异明细
   * @param body
   * @returns
   */
  queryBoxDiff(body: QueryRequest) {
    return new Promise<AppReceiptBoxDiffSumDTO>((resolve, reject) => {
      ReceiptApi.queryBoxDiff(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 结束收货
   * @param body
   * @returns
   */
  finish(body: AppReceiptMultipleRcvCloser) {
    return new Promise<AppReceiptMultipleRcvCloseResult>((resolve, reject) => {
      ReceiptApi.closeMultipleRcv(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 编辑箱码
   * @param body
   * @returns
   */
  modifyBox(body: AppReceiptRecordBoxCheckDTO) {
    return new Promise<AppReceiptRecordDTO>((resolve, reject) => {
      AppReceiptRecordApi.boxCheckLine(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  handlePreFullCheck() {
    this.$showModal({
      title: '提示',
      content: '一键确认后，将对当前单据上的所有商品，按待收数确认到已确认页签',
      success: async (action) => {
        if (action.confirm) {
          try {
            this.$showLoading()
            this.summary = await this.boxFullCheck({ billId: this.receipt.billId, lines: [] })
            this.unConfirmPack = this.summary.unConfirmPack
            this.confirmedPack = this.summary.confirmedPack
            this.showConfirmed = true
            this.isRenovote = true
            this.$hideLoading()
            this.doResetPage()
            this.loadBoxList()
          } catch (error) {
            this.$hideLoading()
            this.$showToast({ icon: 'error', title: error.msg })
          }
        }
      }
    })
  }

  /**
   * 一键确认
   * @param body
   * @returns
   */
  boxFullCheck(body: AppReceiptRecordBoxFullCheckDTO) {
    return new Promise<AppReceiptRecordDTO>((resolve, reject) => {
      AppReceiptRecordApi.boxFullCheck(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 差异结束
   */
  handleDiffFinish() {
    this.handleFinishDirect(this.billId)
  }

  /**
   * 差异弹框取消结束收货
   */
  handleDiffCancel() {
    this.$showModal({
      title: '提示',
      content: '本次收货单【已收】中确认的商品已自动提交',
      showCancel: false,
      confirmText: '我知道了',
      success: (action) => {
        if (action.confirm) {
          this.$Router.replace({
            name: 'receiptBoxRecordEdit',
            params: {
              id: this.billId
            }
          })
        }
      }
    })
  }

  /**
   * 绑定陈列位置
   */
  bindExhibit(info: AppReceiptRecordBoxGoodsDTO, index: number) {
    this.exhibitIndex = index ? index : this.exhibitIndex
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.open(this.hasSelelcExhibit, slotGoods, 'bindExhibit')
  }

  /**
   * 陈列位置调整
   */
  resetExhibit(info: AppReceiptRecordBoxGoodsDTO, index: number) {
    this.hasSelelcExhibit = new Slot()
    this.exhibitIndex = index ? index : this.exhibitIndex
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.$refs.resetExhibit.open(slotGoods)
  }

  /**
   * 获取该商品信息
   */
  getRequestBody(info: AppReceiptRecordBoxGoodsDTO) {
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.success(this.hasSelelcExhibit, slotGoods)
  }

  /**
   * 输入框的值组装
   */
  inputExhibit(code) {
    this.hasSelelcExhibit.code = code
    this.hasSelelcExhibit.name = code
    this.hasSelelcExhibit.uuid = ''
  }

  /**
   * 陈列位置绑定成功
   */
  async confirmExhibit() {
    this.doResetPage()
    await this.loadBoxList()
    if (this.boxList[this.exhibitIndex]) {
      this.$refs.edit.open(this.boxList[this.exhibitIndex], false)
    }
    this.hasSelelcExhibit = new Slot()
  }

  /**
   * 添加陈列位置
   */
  addExhibit() {
    this.$Router.push({
      name: 'addExhibitLocation'
    })
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info: AppReceiptRecordBoxGoodsDTO) {
    this.viewExhibitInfo = { ...info }
    this.$refs.viewExhibit.open()
  }
}
