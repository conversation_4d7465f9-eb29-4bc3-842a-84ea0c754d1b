<template>
  <view class="sku-card">
    <view class="sku-card-header">
      <view class="sku-card-header__img">
        <image lazy-load class="sku-card-header__img-sku" :src="img" @click.stop="handlePreviewImg" />
        <view class="sku-card-header__scale">
          <image :src="'/static/icon/img_enlarge2.png' | oss" class="sku-card-header__scale-img"></image>
        </view>
      </view>
      <view class="sku-card-header__title">
        <view class="sku-card-header__title-name" @click="debounceClick">
          <text class="new" v-if="isNewList">新</text>
          <text :class="[sku.busGateCode === 'S00001' ? 'green' : 'yellow']" v-if="sku.busGateCode">
            {{ sku.busGateName | trimString }}
          </text>
          <text>{{ friendlyStr | empty }}</text>
        </view>
        <!-- 商品tag -->
        <view class="sku-card-header__title-code">
          <view class="tag tag--sku" v-if="isDisp">散称</view>
          <view class="tag tag--sku" v-if="recommandType">{{ recommandType }}</view>
          <text class="tag">{{ code }}</text>
          <text class="tag">{{ inputCode }}</text>
        </view>
      </view>
    </view>
    <!-- 陈列位置 -->
    <view class="main-half" v-if="showMaster.showDisplayLocation">
      <view :class="[hasMutiple(sku.displayLocation) ? 'goods-one' : '']">
        陈列位置：
        <text class="goods-text">{{ sku.displayLocation | empty }}</text>
      </view>
      <image class="good-img" :src="'/static/icon/ic_right_grey.png' | oss" v-if="hasMutiple(sku.displayLocation)" @click="viewExhibit" />
    </view>
    <!-- 商品品类 -->
    <view class="sku-sorts" v-if="showMaster.showSkuCategory">
      <text>品类：</text>
      <text class="sku-sorts-value">{{ category }}</text>
    </view>
    <!-- 商品标签 -->
    <view class="sku-tags" v-if="sku.tags && sku.tags.length">
      <view class="sku-tags-content" :class="{ 'sku-tags-content--collapsed': isExpand }">
        <view class="sku-tags-item" v-for="(item, index) in sku.tags" :key="index">{{ item.name }}</view>
      </view>
      <image class="sku-tags-img" :class="[isExpand ? 'sku-tags-expand' : '']" :src="'/static/icon/ic_down_grey.png' | oss" @click="toggleExpand" />
    </view>
    <view class="sku-card-main">
      <!-- 箱规 -->
      <view class="sku-card-main__item" v-if="showMaster.showSkuSpec">
        <text class="label">箱规：</text>
        <text class="value">{{ bulkPackDesc | empty }}</text>
      </view>
      <!-- 可售库存 -->
      <view class="sku-card-main__item" v-if="showMaster.showSellableInvQty">
        <hd-popover>
          <view style="display: inline-flex; align-items: center">
            <text class="label">可售库存</text>
            <image class="img" :src="'/static/icon/<EMAIL>' | oss"></image>
            <text class="label">：</text>
          </view>
          <text class="value" v-if="isDisp || invQtyDisplayStrategy">{{ sku.invQty | empty }}{{ sku.minMunit | empty }}</text>
          <text class="value" v-else>{{ sku.invQpcQtyTitle | empty }}</text>
          <template #bubble>
            <view style="width: 232rpx">指门店的实物库存</view>
          </template>
        </hd-popover>
      </view>
      <!-- 在途库存 -->
      <view class="sku-card-main__item">
        <hd-popover>
          <view style="display: inline-flex; align-items: center">
            <text class="label">在途库存</text>
            <image class="img" :src="'/static/icon/<EMAIL>' | oss"></image>
            <text class="label">：</text>
          </view>
          <text class="value" v-if="isDisp || invQtyDisplayStrategy">{{ sku.inTransitInvQty | empty }}{{ sku.minMunit | empty }}</text>
          <text class="value" v-else>{{ sku.inTransitInvQpcQty | empty }}</text>
          <template #bubble>
            <view style="width: 232rpx">指发货方已发出但暂未收货的库存</view>
          </template>
        </hd-popover>
      </view>
      <!-- 未发货库存 -->
      <view class="sku-card-main__item">
        <hd-popover>
          <view style="display: inline-flex; align-items: center">
            <text class="label">未发货库存</text>
            <image class="img" :src="'/static/icon/<EMAIL>' | oss"></image>
            <text class="label">：</text>
          </view>
          <text class="value" v-if="isDisp || invQtyDisplayStrategy">{{ sku.inOrderInvQty | empty }}{{ sku.minMunit | empty }}</text>
          <text class="value" v-else>{{ sku.inOrderInvQpcQty | empty }}</text>
          <template #bubble>
            <view style="width: 232rpx">指订单已确认但尚未发货的库存</view>
          </template>
        </hd-popover>
      </view>
      <!-- 库存上限 -->
      <view class="sku-card-main__item" v-if="showMaster.showHighInv">
        <text class="label">库存上限：</text>
        <text class="value">{{ sku.highInv | empty }}{{ sku.minMunit | empty }}</text>
      </view>
      <!-- 库存下限 -->
      <view class="sku-card-main__item" v-if="showMaster.showLowInv">
        <text class="label">库存下限：</text>
        <text class="value">{{ sku.lowInv | empty }}{{ sku.minMunit | empty }}</text>
      </view>
      <!-- 库存成本价 -->
      <view class="sku-card-main__item" v-if="!hidePrice && showMaster.showInvPrice">
        <text class="label">库存成本价：</text>
        <text class="value">￥{{ sku.invPrc | empty }}/{{ sku.minMunit }}</text>
      </view>
      <!-- 售价 -->
      <view class="sku-card-main__item">
        <text class="label">售价：</text>
        <text class="value">￥{{ sku.rtlPrc | empty }}/{{ sku.minMunit }}</text>
      </view>
      <!-- 月销量 -->
      <view class="sku-card-main__item" v-if="showMaster.showSkuMonthSale">
        <text class="label">月销量：</text>
        <text class="value">{{ sku.monthSaleQty | empty }}{{ sku.minMunit | empty }}</text>
      </view>
      <!-- 周销量 -->
      <view class="sku-card-main__item" v-if="showMaster.showSkuWeekSale">
        <text class="label">周销量：</text>
        <text class="value">{{ sku.weekSaleQty | empty }}{{ sku.minMunit | empty }}</text>
      </view>
      <view
        class="sku-card-main__item"
        v-if="showMaster.showListSkuQualification && sku.quaImages && sku.quaImages.length"
        @click.stop="handlePreviewQuaImg(sku.quaImages)"
      >
        <text class="label">商品资质：</text>
        <text class="value">查看</text>
        <image class="img" :src="'/static/icon/ic_right_grey.png' | oss"></image>
      </view>
      <view class="sku-card-main__description" v-if="showMaster.showListSkuExplain && sku.description" @click.stop="handleShowDesc">
        说明：{{ sku.description }}
      </view>
    </view>
    <view class="exhibit__btn">
      <view class="add item-btn" v-if="isShowExhibit && isShowExhibitBtn && slotSource" @click="resetExhibit">陈列调整</view>
      <view class="add item-btn" v-if="isShowPrintListAddBtn" @click="doAddSkuIntoPrintList">加入打印列表</view>
      <view class="operate item-btn" v-if="isShowOperate && hideOperateInvQty" @click="operateInvQty">库存调整</view>
    </view>
  </view>
</template>

<script lang="ts" src="./SkuCard.ts"></script>

<style lang="scss" scoped>
.sku-card {
  position: relative;
  width: 100%;
  padding: 24rpx;
  box-sizing: border-box;

  &::after {
    position: absolute;
    content: ' ';
    width: calc(100% - 48rpx);
    height: 2rpx;
    background: #eeeeee;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%) scaleY(0.5);
  }

  &-header {
    display: flex;
    margin-bottom: 12rpx;
    &__img {
      position: relative;
      width: 120rpx;
      height: 120rpx;
      flex: 0 0 auto;
      &-sku {
        width: 120rpx;
        height: 120rpx;
      }
    }

    &__scale {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 24rpx;
      height: 24rpx;
      background: rgba(0, 0, 0, 0.4);
      border-radius: 8rpx 0px 8rpx 0rpx;
      text-align: center;
      @include flex();
      &-img {
        width: 16rpx;
        height: 16rpx;
      }
    }

    &__title {
      margin-left: 12rpx;
      text-align: left;
      flex: 1 1 auto;
      @include flex(column, flex-start, flex-start);
      &-name {
        width: 100%;
        box-sizing: border-box;
        font-family: $font-medium;
        font-weight: 550;
        font-size: 30rpx;
        color: #282c34;
        text-align: left;
        font-style: normal;
        line-height: 40rpx;
        // @include ellipsis(2);
        margin-bottom: 10rpx;

        .new {
          background: linear-gradient(180deg, #fd4f6e 0%, #fa273b 100%);
          border-radius: 4rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #ffffff;
          padding: 0 4rpx;
          text-align: left;
          font-style: normal;
          margin-right: 8rpx;
        }
        .green {
          padding: 0 4rpx;
          font-weight: 550;
          font-size: 24rpx;
          color: #08b07c;
          background: #e9fff1;
          border-radius: 4rpx;
          border: 1rpx solid rgba(8, 176, 124, 0.65);
          margin-right: 8rpx;
          @include ellipsis();
        }

        .yellow {
          background: #fffbf6;
          border-radius: 4rpx;
          border: 1rpx solid #fd9b1c;
          font-weight: 550;
          padding: 0 4rpx;
          font-size: 24rpx;
          color: #fd9b1c;
          line-height: 32rpx;
          margin-right: 8rpx;
          @include ellipsis();
        }
      }

      &-code {
        display: flex;
        flex-wrap: wrap;
        gap: 8rpx;
        .tag {
          flex: 0 0 auto;
          padding: 2rpx 8rpx;
          background: #f5f6f7;
          border-radius: 4rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 20rpx;
          color: #666666;
          line-height: 28rpx;
          max-width: 100%;
          @include ellipsis();
          &--sku {
            background: #e9f0ff;
            color: $color-primary;
          }
        }
      }
    }
  }

  &-main {
    display: flex;
    flex-wrap: wrap;
    &__item {
      flex: 0 0 auto;
      width: 50%;
      gap: 8rpx;
      word-break: break-all;
      display: flex;
      align-items: center;
      min-height: 42rpx;

      .label {
        font-size: 24rpx;
        color: #94969a;
        line-height: 32rpx;
      }
      .value {
        font-size: 24rpx;
        color: #585a5e;
        line-height: 32rpx;
        flex: 1;
      }
      .img {
        width: 32rpx;
        height: 32rpx;
      }
      .good-img {
        width: 32rpx;
        height: 32rpx;
      }
      .ex_value {
        @include ellipsis();
      }
    }

    &__description {
      @include ellipsis();
      margin-top: 16rpx;
      width: 100%;
      background: #f5f6f7;
      border-radius: 8rpx;
      box-sizing: border-box;
      padding: 12rpx;
      font-size: 24rpx;
      color: #94969a;
      line-height: 32rpx;
    }
  }

  .main-half {
    width: 100%;
    flex: 0 0 auto;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    display: flex;
    align-items: center;
    margin: 12rpx 0;
    background: #f5f5f5;
    border-radius: 8rpx;
    padding: 8rpx 16rpx;
    box-sizing: border-box;

    .good-img {
      width: 32rpx;
      height: 32rpx;
      display: inline-table;
    }

    .goods-text {
      color: #333333;
      font-size: 26rpx;
      font-weight: 500;
    }

    .goods-one {
      @include ellipsis();
    }
  }

  .sku-sorts {
    width: 100%;
    min-height: 42rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 42rpx;
    text-align: left;
    display: flex;
    margin-bottom: 8rpx;

    &-value {
      flex: 1;
      @include ellipsis();
      color: #585a5e;
    }
  }

  .exhibit__btn {
    margin-top: 12rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .item-btn {
      height: 56rpx;
      border-radius: 32rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26rpx;
      padding: 0 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
    }

    .item-btn + .item-btn {
      margin-left: 16rpx;
    }

    .add {
      height: 56rpx;
      background: #ffffff;
      color: #1c64fd;
      border: 2rpx solid #1c64fd;
    }

    .exhibit {
      width: 196rpx;
      background: #e0eaff;
      color: #1c64fd;
      font-style: normal;
    }

    .operate {
      background: #e0eaff;
      color: #1c64fd;
    }
  }

  .sku-tags {
    width: 100%;
    margin-bottom: 8rpx;
    display: flex;
    justify-content: space-between;

    &-content {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 8rpx;
      overflow: hidden;
      max-height: 38rpx; /* 控制显示一行的高度 */
      transition: max-height 0.3s ease;

      &--collapsed {
        max-height: none; /* 展开时显示所有内容 */
      }
    }

    &-item {
      border-radius: 4rpx;
      border: 1rpx solid #cccccc;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 20rpx;
      color: #666666;
      line-height: 32rpx;
      padding: 0 8rpx;
      box-sizing: border-box;
      max-width: 326rpx;
      @include ellipsis();
    }

    &-img {
      width: 36rpx;
      height: 36rpx;
    }

    &-expand {
      transform: rotate(180deg);
    }
  }
}
</style>
