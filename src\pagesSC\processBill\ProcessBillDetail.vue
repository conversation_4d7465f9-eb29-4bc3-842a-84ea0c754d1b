<template>
  <view class="process-bill-detail">
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <!-- 头部状态 -->
    <view class="sticky">
      <view class="top-header">
        <view class="top-left">
          <view class="title" @click="doShowProcess">
            {{ state }}
            <image v-if="processAble" class="title-arrow" :src="'/static/icon_Flat/ic_noticebar_right_white.png' | oss" mode="aspectFill"></image>
          </view>
          <view class="bill-no">{{ data.num | empty }}</view>
        </view>
      </view>
      <!-- 头部汇总数据 -->
      <view class="hd-tabs-before"></view>
      <hd-tabs :tabList="tabList" :activeIndex="0" @handler="doHandler"></hd-tabs>
    </view>
    <operator-cell :type="type" :clickable="false" :total="total" :count="count"></operator-cell>
    <sale-list :type="type" :lines="current === 0 ? data.raws : data.pdts" :isSticky="true"></sale-list>
    <view class="bottom-info">
      <view class="field">
        <view class="label">备注</view>
        <view class="data">{{ data.note | empty }}</view>
      </view>
      <view class="field">
        <view class="label">创建人</view>
        <view class="data">{{ data.creatorName | empty }}</view>
      </view>
      <view class="field">
        <view class="label">提交时间</view>
        <view class="data">{{ data.submitTime | date('yyyy-MM-dd HH:mm:ss') | empty }}</view>
      </view>
    </view>
    <view class="footer-btn safe-area-inset-bottom" v-if="current === 1">
      <hd-button type="primary" @click="goPrint">去打印</hd-button>
    </view>
    <uni-popup type="bottom" ref="flowDialog">
      <flow-dialog title="加工流程"></flow-dialog>
    </uni-popup>
  </view>
</template>
<script lang="ts" src="./ProcessBillDetail.ts"></script>
<style lang="scss" scoped>
.process-bill-detail {
  height: 100%;
  width: 750rpx;
  min-height: 100vh;
  background: $list-bg-color-lx;

  position: relative;
  .sticky {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 2;
  }
  .top-header {
    width: 750rpx;
    height: 160rpx;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    justify-content: space-between;
    padding: 20rpx $base-padding 48rpx;
    background: $color-primary;
    .top-left {
      .title {
        display: flex;
        align-items: center;

        font-size: 44rpx;
        height: 48rpx;
        line-height: 48rpx;
        font-weight: 500;
        color: $uni-bg-color;
        .title-arrow {
          width: 32rpx;
          height: 32rpx;
        }
      }
      .bill-no {
        margin-top: 12rpx;

        font-size: 30rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 500;
        color: $uni-bg-color;
        opacity: 0.8;
      }
    }
  }
  .hd-tabs-before {
    width: 750rpx;
    height: 12rpx;
    border-radius: 12rpx 12rpx 0 0;
    margin-top: -12rpx;
    background: #ffffff;
  }
  .bottom-info {
    border-radius: 12rpx;
    overflow: hidden;
    background-color: #ffffff;
    margin: 16rpx 0;
    padding: 24rpx $base-padding;
    box-sizing: border-box;
    .field {
      width: 100%;
      display: flex;
      font-size: 28rpx;
      .label {
        flex: 0 0 auto;
        min-width: 140rpx;
        color: $font-color-darklight;
        height: 40rpx;
      }
      .data {
        word-break: break-all;
        color: $color-text-primary;
        margin-left: 15rpx;
        flex: 1 1 auto;
        flex-wrap: wrap;
        min-height: 40rpx;
        text-align: right;
      }
      & + .field {
        margin-top: 24rpx;
      }
    }
  }

  .footer-btn {
    width: 100%;
    position: fixed;
    background-color: #fff;
    bottom: 0;
    left: 0;
    z-index: 2;
  }

  .category-group {
    width: 100%;
    margin-top: 15rpx;
    background-color: $uni-bg-color;
    position: relative;
    .category-group-toggle {
      position: absolute;
    }
  }
}
</style>
