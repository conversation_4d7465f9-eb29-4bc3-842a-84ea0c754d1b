import { Component, Vue } from 'vue-property-decorator'
import CollapseBar from '@/pages/cmp/CollapseBar.vue'
import SideBar from '@/pages/cmp/SideBar.vue'
import HdNumberBoxTest from '@/components/hd-number-box-test/hd-number-box-test.vue'
import HdButton from '@/components/hd-button/hd-button.vue'
import QueryRequest from 'model/base/QueryRequest'
import ProcessBillApi from '@/network/processBill/ProcessBillApi'
import ProcessBillGoods from '@/model/processBill/ProcessBillGoods'
import AbstractProcessBillLine from '@/model/processBill/AbstractProcessBillLine'
import Category from '@/model/data/Category'
import { Filter } from '@/pagesSC/processBill/cmd/Filter'
import { Mutation, State, Action } from 'vuex-class'
import config from '@/config'
import CommonUtil from '@/utils/CommonUtil'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'

@Component({
  components: { CollapseBar, SideBar, HdNumberBoxTest, HdButton },
  filters: Filter
})
export default class ProcessBillGoodSearch extends Vue {
  @Mutation('goodsList') mutationGoodsList // 提交到vuex
  @Action('isGoods') actionIsGoods // 是否为选择商品存到vuex中
  @State('goodsList') goodsList: any[]
  $refs: any
  dataList: ProcessBillGoods[] = [] // 查询结果列表
  selectedList: ProcessBillGoods[] = [] // 已选商品
  keyWord: string = '' // 搜索关键词
  showResult: boolean = false // 是否显示结果
  from: string = '' // 来源路由

  // 上拉加载相关
  isLoading: boolean = false // 是否正在加载
  finished: boolean = false // 数据是否加载完成
  page: number = 0 // 数据页码
  pageSize: number = 20 // 每页大小

  title: string = '' // 标题

  get disabled() {
    return this.selectedList.length <= 0
  }

  // 商品图片
  get skuImg() {
    return (sku: ProcessBillGoods) => {
      return sku && sku.images && sku.images.length && CommonUtil.isImageUrl(sku.images[0])
        ? `${sku.images[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
        : `${config.sourceUrl}icon/pic_goods.png`
    }
  }

  get imageList() {
    return (sku: ProcessBillGoods) => {
      return sku && sku.images && sku.images.filter((item) => CommonUtil.isImageUrl(item)).length
        ? sku.images.filter((item) => CommonUtil.isImageUrl(item))
        : [`${config.sourceUrl}icon/pic_goods.png`]
    }
  }

  // 展示金额
  get showPrice() {
    if (this.title === '搜索成品') {
      return true
    } else {
      return !PermissionMgr.hasPermission(Permission.globalPriceView)
    }
  }

  onLoad(option) {
    if (option) {
      this.$nextTick(() => {
        this.from = option.from || ''
        this.keyWord = option.value || ''
        if (option.title) {
          try {
            this.title = decodeURIComponent(option.title)
          } catch (error) {
            this.title = option.title
          }

          uni.setNavigationBarTitle({
            title: this.title
          })
        }
        if (this.keyWord) {
          this.$nextTick(() => {
            this.doSearch(true)
          })
        }
      })
    }
  }

  mounted() {
    if (this.goodsList.length > 0) {
      const before: AbstractProcessBillLine[] = this.goodsList
      this.selectedList = this.doExchangeModel(before)
      this.mutationGoodsList([])
    } else {
      if (uni.getStorageSync('processGoodsList')) {
        const before: AbstractProcessBillLine[] = uni.getStorageSync('processGoodsList')
        this.selectedList = this.doExchangeModel(before)
        uni.removeStorage({ key: 'processGoodsList' })
      }
    }
  }

  // 搜索事件
  doSearch(showLoading: boolean = true) {
    const query = new QueryRequest()
    query.page = this.page
    query.pageSize = this.pageSize
    query.conditions = [{ operation: 'keyword:%=%', parameters: [this.keyWord] }]
    query.fetchParts = ['category', 'image']
    if (showLoading) {
      this.$showLoading({ delayTime: 200 })
    }
    // #ifndef APP-PLUS
    uni.hideKeyboard()
    // #endif
    // #ifdef APP-PLUS
    plus.key.hideSoftKeybord()
    // #endif
    ProcessBillApi.queryGoods(query)
      .then((resp) => {
        if (showLoading) {
          this.$hideLoading()
        }
        this.isLoading = false
        this.page++
        if (!resp.more) {
          this.finished = true
        }
        this.showResult = true
        if (resp.data && resp.data.length > 0) {
          const data = resp.data
          for (let i = 0; i < data.length; i++) {
            data[i].qty = 0
            data[i].qpcQty = 0
            data[i].total = 0
            for (let j = 0; j < this.selectedList.length; j++) {
              if (
                data[i].inputCode === this.selectedList[j].inputCode &&
                data[i].uuid === this.selectedList[j].uuid &&
                data[i].qpcStr === this.selectedList[j].qpcStr
              ) {
                data[i].qty = this.selectedList[j].qty
                data[i].qpcQty = this.selectedList[j].qpcQty
                data[i].total = this.selectedList[j].total
              }
            }
          }
          this.dataList.push(...data)
        } else {
          this.finished = true
        }
      })
      .catch((e) => {
        if (showLoading) {
          this.$hideLoading()
        }
        this.showResult = true
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  /**
   * 步进器change事件
   * @param qpcQty 规格数量
   * @param index 对应商品下标
   */
  doNumberChange(qpcQty: number, index: number) {
    this.dataList[index].qty = qpcQty.multiply(this.dataList[index].qpc).scale(4)
    this.dataList[index].total = this.dataList[index].price.multiply(qpcQty)
    const goods: ProcessBillGoods = this.dataList[index]
    if (goods) {
      const index = this.selectedList.findIndex((item) => {
        return item.uuid === goods.uuid && item.inputCode === goods.inputCode && item.qpcStr === goods.qpcStr
      })
      if (index === -1 && qpcQty > 0) {
        this.selectedList.unshift(goods)
      } else {
        if (qpcQty <= 0) {
          this.selectedList.splice(index, 1)
        } else {
          this.selectedList[index].qty = goods.qty
          this.selectedList[index].qpcQty = goods.qpcQty
          this.selectedList[index].total = goods.total
        }
      }
    }
  }

  // 输入框回车事件
  doInpConfirm() {
    this.dataList = []
    this.isLoading = false
    this.finished = false
    this.page = 0
    this.doSearch(true)
  }

  // 清空按钮事件
  doClear() {
    this.keyWord = ''
  }

  //  取消按钮点击事件
  doCancel() {
    this.dataList = []

    if (uni.getStorageSync('processGoodsList')) {
      uni.removeStorage({
        key: 'processGoodsList',
        success: () => {
          uni.navigateBack({
            delta: 1
          })
        }
      })
    } else {
      uni.navigateBack({
        delta: 1
      })
    }
  }

  // 确认按钮点击事件
  doConfirm() {
    uni.setStorage({
      key: 'processGoodsList',
      data: this.doReturnModel(this.selectedList),
      success: () => {
        this.actionIsGoods(true)
        if (this.from === 'select') {
          uni.navigateBack({
            delta: 2
          })
        } else {
          uni.navigateBack({
            delta: 1
          })
        }
      }
    })
  }

  /**
   * 模型转换
   * @param before 待转换数组
   */
  doExchangeModel(before: AbstractProcessBillLine[] = []) {
    const after: ProcessBillGoods[] = []
    for (let index = 0; index < before.length; index++) {
      let temp: ProcessBillGoods = new ProcessBillGoods()
      temp = { ...before[index].goods } as ProcessBillGoods
      temp.total = before[index].total
      temp.qty = before[index].qty
      temp.qpcQty = before[index].qpcQty
      temp.images = before[index].goodsImages
      if (before[index].categoryCode) {
        temp.firstCategory = new Category()
        temp.firstCategory.code = before[index].categoryCode || ''
        temp.firstCategory.name = before[index].categoryName || ''
        temp.firstCategory.uuid = before[index].categoryUuid || ''
      } else {
        temp.firstCategory = null
      }

      after.push(temp)
    }
    return after
  }

  /**
   * 模型还原
   * @param before 待还原模型
   */
  doReturnModel(before: ProcessBillGoods[] = []) {
    const after: AbstractProcessBillLine[] = []
    for (let index = 0; index < before.length; index++) {
      const temp: AbstractProcessBillLine = new AbstractProcessBillLine()
      temp.goods.code = before[index].code
      temp.goods.inputCode = before[index].inputCode
      temp.goods.minMunit = before[index].minMunit
      temp.goods.munit = before[index].munit
      temp.goods.name = before[index].name
      temp.goods.price = before[index].price
      temp.goods.qpc = before[index].qpc
      temp.goods.qpcStr = before[index].qpcStr
      temp.goods.singlePrice = before[index].singlePrice
      temp.goods.uuid = before[index].uuid
      temp.total = before[index].total
      temp.qty = before[index].qty
      temp.qpcQty = before[index].qpcQty
      temp.goodsImages = before[index].images
      if (before[index].firstCategory) {
        temp.categoryCode = before[index].firstCategory!.code || ''
        temp.categoryName = before[index].firstCategory!.name || ''
        temp.categoryUuid = before[index].firstCategory!.uuid || ''
      }
      after.push(temp)
    }
    return after
  }

  // 上拉加载更多
  doLoadMore() {
    if (this.finished || this.isLoading) {
      return
    }
    this.isLoading = true
    this.doSearch(false)
  }

  /**
   * 预览图片
   */
  handlePreviewImg(sku: ProcessBillGoods) {
    uni.previewImage({
      current: String(0),
      urls: this.imageList(sku)
    })
  }
}
