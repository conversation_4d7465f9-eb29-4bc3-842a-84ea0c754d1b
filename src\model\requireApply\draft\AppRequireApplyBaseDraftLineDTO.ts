/*
 * @Author: we<PERSON><PERSON>
 * @Date: 2024-04-24 15:19:19
 * @LastEditTime: 2025-04-25 14:34:56
 * @LastEditors: hanwei
 * @Description:
 * @FilePath: \soa\src\model\requireApply\draft\AppRequireApplyBaseDraftLineDTO.ts
 * 记得注释
 */
import AppRequireApplyDraftGoodsDTO from './AppRequireApplyDraftGoodsDTO'
import Sort from '../Sort'
import rand from '../rand'
import AppRequireApplySubBaseLineDTO from './AppRequireApplySubBaseLineDTO'
import AppRequireApplyAccDraftLineDTO from './AppRequireApplyAccDraftLineDTO'

export default class AppRequireApplyBaseDraftLineDTO {
  // 商品
  goods: AppRequireApplyDraftGoodsDTO = new AppRequireApplyDraftGoodsDTO()
  // 面板分类大类Uuid
  categoryUuid: Nullable<string> = null
  // 面板分类大类代码
  categoryCode: Nullable<string> = null
  // 面板分类大类名称
  categoryName: Nullable<string> = null
  // 建议数量
  suggestQty: Nullable<number> = null
  // 已要货数量
  requiredQty: Nullable<number> = null
  // 数量
  qty: number = 0
  // 金额
  total: number = 0
  // 规格数量
  qpcQty: string = ''
  // 叫货原因代码
  reasonCode: Nullable<string> = null
  // 叫货原因名称
  reasonName: Nullable<string> = null
  // 类别
  sort: Nullable<Sort> = null
  // 品牌
  brand: Nullable<rand> = null
  // 备注
  note: Nullable<string> = null
  // 是否散货
  isDisp: Nullable<boolean> = null
  // 原价
  srcPrice: Nullable<number> = null
  // 来源类型，0-自主叫货，1-建议叫货
  srcType: Nullable<number> = null
  // 上架备注
  shelveNote: Nullable<string> = null
  // 是否限量,0-否，1-是
  isLimitQty: Nullable<number> = null
  // 活动标识
  activityId: Nullable<string> = null
  // 商品类型 ,0-普通商品，4-爆品
  goodsType: Nullable<number> = null
  // 方案编码
  schemeNo: Nullable<string> = null
  // 含辅料
  hasAccessory: Nullable<number> = null
  // 辅料数量
  accQty: Nullable<number> = null
  // 辅料规格数量
  accQpcQty: Nullable<number> = null
  // 辅料金额
  accTotal: Nullable<number> = null
  // 辅料审批金额
  approvalAccTotal: Nullable<number> = null
  // 溯源码管理：0-否；1-是
  useTraceCode: Nullable<number> = null
  // 溯源码规则：0-标准；1-按肉品；2-酒类源码；3-唯一码
  traceCodeRule: Nullable<number> = null
  // 必订品：0-否；1-是
  reqOrd: Nullable<number> = null
  // 订货倍数
  ordMultiple: Nullable<number> = null
  // 来源行号
  srcLineNo: Nullable<number> = null
  // 标准陈列量
  stdShowQty: Nullable<number> = null
  // 活动陈列量
  activeShowQty: Nullable<number> = null
  // 辅料明细列表
  accLines: AppRequireApplyAccDraftLineDTO[] = []
  // 商品子列表
  subLines: AppRequireApplySubBaseLineDTO[] = []
}
