<template>
  <view class="invXF-detail">
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-modal></hd-modal>
    <hd-water-mark></hd-water-mark>
    <scroll-view
      scroll-y
      class="scroll"
      :class="{ hasAction: needShip || needReceive, hasPackage: (needShip || needReceive) && !showRtlPrice && (!showPrice || isHasPack) }"
    >
      <!-- 头部状态 -->
      <view class="top-header">
        <view class="top-left">
          <view class="title" @click="doShowProcess">
            <text v-if="data.state === InvXFState.initial">总部批准</text>
            <text v-else-if="data.state === InvXFState.shipped">已确认发货</text>
            <text v-else-if="data.state === InvXFState.receipted">已完成</text>
            <text v-else-if="data.state === InvXFState.aborted">已作废</text>
            <image v-if="processAble" class="title-arrow" :src="'/static/icon_Flat/ic_noticebar_right_white.png' | oss" mode="aspectFill"></image>
          </view>
          <view class="bill-no">{{ data.num | empty }}</view>
        </view>
      </view>
      <!-- 头部汇总数据 -->
      <view class="top-summary">
        <view class="field transfer">
          <view class="transfer-shop">{{ data.type === InvXFType.increase ? data.counterShopName : data.shopName }}</view>
          <image class="transfer-icon" :src="'/static/icon/ic_transferin.png' | oss" mode="aspectFill"></image>
          <view class="transfer-shop">{{ data.type === InvXFType.increase ? data.shopName : data.counterShopName }}</view>
        </view>
        <!-- <view class="field line" v-if="needShip || needReceive">
          <view class="label">{{ data.type === InvXFType.increase ? '调出' : '调入' }}门店</view>
          <view class="data ellipsis">{{ data.counterShopName }}</view>
        </view>
        <view class="field" v-if="needShip || needReceive">
          <view class="label">备注</view>
          <view class="data">{{ data.note | empty }}</view>
        </view>
        <view class="field" v-if="(needShip || needReceive) && infDeliveryTypeShow">
          <view class="label">配送模式</view>
          <view class="data">{{ deliveryType | empty }}</view>
        </view> -->
      </view>
      <!-- tab切换 -->
      <view class="inv-xf-tab" v-if="isHasPack">
        <view class="inv-xf-goods" @click="getTab('good')">
          <text :class="[tabType === 'good' ? 'inv-xf-goods-txt' : 'inv-xf-goods-txt-sub']">商品明细</text>
          <view v-if="tabType === 'good'" class="inv-xf-goods-btn"></view>
        </view>
        <view class="inv-xf-goods" @click="getTab('package')">
          <text :class="[tabType === 'package' ? 'inv-xf-goods-txt' : 'inv-xf-goods-txt-sub']">包材明细</text>
          <view v-if="tabType === 'package'" class="inv-xf-goods-btn"></view>
        </view>
      </view>
      <!--列表-->
      <block v-if="tabType === 'good'">
        <view v-if="needShip" class="ship-lines-box">
          <!-- <hd-operator-cell title="商品明细" :clickable="false"></hd-operator-cell> -->
          <view class="list-title" :class="{ 'title-sticky': isSticky }">
            <view class="list-title-item" style="text-align: left">
              {{ `品名/条码/代码${showPrice ? '/调拨价' : ''}${showRtlPrice ? '/售价' : ''}` }}
            </view>
            <view class="list-title-item">应调/实调</view>
            <view class="list-title-item" v-if="canEdit || state === InvXFState.receipted">实收数量</view>
          </view>
          <view v-if="data.lines.length > 0" class="dir-alc-bck-body">
            <view class="sku-edit-card" v-for="(item, index) in data.lines" :key="item.goods.uuid">
              <view class="sku-goods">
                <view class="sku-left">
                  <image lazy-load class="sku-img" :src="skuImg(item)" @click.stop="handlePreviewImg(item)" />
                  <view class="info__scale">
                    <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
                  </view>
                </view>
                <view class="sku-name">{{ item.goods.name | empty }}</view>
              </view>
              <view class="sku-code">
                <view class="info-tag">{{ item.goods.inputCode | empty }}</view>
                <view class="info-tag">{{ item.goods.code | empty }}</view>
                <view class="info-tag" v-if="showPrice">￥{{ item.goods.singlePrice | empty }}/{{ item.goods.minMunit | empty }}</view>
                <view class="info-tag" v-if="showRtlPrice">￥{{ item.rtlPrc | empty }}/{{ item.goods.minMunit | empty }}</view>
              </view>
              <view class="sku-info">
                <view class="qty flexCE">
                  {{ item.approvalQty }}/
                  <view
                    :class="{ 'number-input-box': enableModifyShip && !getBatches(item).length }"
                    @click="() => enableModifyShip && !getBatches(item).length && handleEditBatchQty(index)"
                  >
                    {{ item.shipQty }}
                  </view>
                </view>
              </view>
              <!-- <view class="second-row">
              <view>
                <text class="sku-code">
                  条码：
                  <text class="color-black">{{ item.goods.code | empty }}</text>
                </text>
                <text class="sku-qpc">
                  规格：
                  <text class="color-black">{{ item.goods.qpcStr | empty }}</text>
                </text>
              </view>
              <text class="sku-munit" v-if="showPrice">
                <text class="color-warning goods-info-price">{{ item.goods.singlePrice | empty }}</text>
                /{{ item.goods.minMunit | empty }}
              </text>
            </view> -->
              <!-- <view class="apply-qty mt-12" v-if="showRtlPrice">
              售价：
              <text class="apply-qty__num">￥{{ item.rtlPrc }}/{{ item.goods.minMunit | empty }}</text>
            </view> -->
              <!-- <view class="third-row">
              <view class="apply-qty">
                应调数量：
                <text class="apply-qty__num">{{ item.approvalQty }}</text>
              </view>
              <view class="number-input-box" @click="handleEditBatchQty(index)">{{ item.shipQty }}</view>
            </view> -->
              <view class="sku-batch" v-if="getBatches(item).length">
                <view class="sku-batch-header">
                  <view class="sku-batch-long batch-date">生产日期</view>
                  <view class="sku-batch-short">应调/实调</view>
                </view>
                <view class="sku-batch-content" v-for="(batch, i) in getBatches(item)" :key="i">
                  <view class="sku-batch-long batch-date">{{ batch.prdDate }}</view>
                  <view class="sku-batch-long batch-tracecode" :class="{ 'short-tracecode': enableModifyShip }">{{ batch.traceCode || '' }}</view>
                  <view class="sku-batch-short flexCE">
                    {{ batch.approveQty | empty }}/
                    <view :class="{ 'batch-input-box': enableModifyShip }" @click="handleEditBatchQty(index, i, batch)">
                      {{ batch.shipQty | empty }}
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <sale-list
          v-else
          :canEdit="needReceive && btnPermission"
          :modifyReceiptQty="modifyReceiptQty"
          :lines="lines"
          :showPrice="showPrice"
          :showRtlPrice="showRtlPrice"
          :enableValidDate="enableValidDate"
          @change="doNumberChange"
          @total="doViewMore"
          :state="data.state"
          :enableModify="enableModifyShip"
          @qtyChange="handleEditBatchQty"
        ></sale-list>
      </block>
      <block v-if="tabType === 'package'">
        <package-list
          :needShip="needShip"
          :modifyReceiptQty="modifyReceiptQty"
          :canEdit="needReceive"
          :state="data.state"
          :lines="data.packLines"
          @editBatchQty="handleEditPackageQty"
        ></package-list>
      </block>

      <view class="bottom-info">
        <block v-if="!isHasPack">
          <view class="field" v-if="showPrice">
            <view class="label">{{ totalDesc }}</view>
            <view class="data total">￥{{ linesTotal | empty }}</view>
          </view>
          <view class="field" v-if="showRtlPrice">
            <view class="label">售价总额</view>
            <view class="data total">￥{{ linesRtlTotal | empty }}</view>
          </view>
        </block>
        <block v-else>
          <view class="field">
            <view class="label">调拨金额</view>
            <view class="data total">￥{{ aggAllTotal | empty }}</view>
          </view>
          <view class="field">
            <view class="label">货品调拨金额</view>
            <view class="data total">￥{{ goodsTotal | empty }}</view>
          </view>
          <view class="field">
            <view class="label">包材调拨金额</view>
            <view class="data total">￥{{ packTotal | empty }}</view>
          </view>
        </block>
        <view class="field">
          <view class="label">{{ data.type === InvXFType.increase ? '调出' : '调入' }}门店</view>
          <view class="data ellipsis">{{ data.counterShopName | empty }}</view>
        </view>
        <view class="field">
          <view class="label">备注</view>
          <view class="data">{{ data.note | empty }}</view>
        </view>
        <view class="field" v-if="infDeliveryTypeShow">
          <view class="label">配送模式</view>
          <view class="data">{{ deliveryType | empty }}</view>
        </view>
        <view class="field" v-if="data.applyNum" @click="invXFApplyPermission && doApplyNum()">
          <view class="label">申请单号</view>
          <view class="data">
            {{ data.applyNum | empty }}
            <image class="arrow" v-if="invXFApplyPermission" :src="'/static/icon/ic_right_grey.png' | oss"></image>
          </view>
        </view>
        <view class="field" v-if="data.state === InvXFState.initial">
          <view class="label">提交人</view>
          <view class="data">{{ data.creatorName | empty }}</view>
        </view>
        <view class="field" v-if="data.state === InvXFState.initial">
          <view class="label">提交时间</view>
          <view class="data">{{ data.created | empty }}</view>
        </view>
        <view class="field" v-if="data.state === InvXFState.shipped">
          <view class="label">发货人</view>
          <view class="data">{{ data.shipperName | empty }}</view>
        </view>
        <view class="field" v-if="data.state === InvXFState.shipped">
          <view class="label">发货提交时间</view>
          <view class="data">{{ data.shipTime | empty }}</view>
        </view>
        <view class="field" v-if="data.state === InvXFState.receipted">
          <view class="label">收货人</view>
          <view class="data">{{ data.consigneeName | empty }}</view>
        </view>
        <view class="field" v-if="data.state === InvXFState.receipted">
          <view class="label">收货提交时间</view>
          <view class="data">{{ data.receiveTime | empty }}</view>
        </view>
      </view>
    </scroll-view>
    <view class="footer-total-bottom">
      <view
        class="footer-total-bar"
        :class="{
          flexC: showRtlPrice && !showPrice,
          flexCE: !showRtlPrice && showPrice && !isHasPack,
          flexCF: !showRtlPrice && (!showPrice || isHasPack)
        }"
        v-if="needShip || needReceive"
      >
        <view v-if="showRtlPrice">
          售价总额：
          <text class="total-price">{{ rtlTotal }}</text>
        </view>
        <view v-if="showPrice && !isHasPack">
          调拨金额：
          <text class="total-price">{{ aggTotal }}</text>
        </view>
      </view>

      <hd-button cname="confirm-btn" v-if="needShip && btnPermission" :disabled="!canShip" type="primary" @click="doShip">确认发货</hd-button>
      <hd-button cname="confirm-btn" v-if="needReceive && btnPermission" type="primary" @click="doReceive">确认收货</hd-button>
    </view>
    <edit-qty-dialog ref="edit" :needShip="needShip" @confirm="handleEditBatchConfirm"></edit-qty-dialog>

    <edit-pka-qty-dialog ref="pkaEdit" :needShip="needShip" @confirm="handlePkaEditBatchConfirm"></edit-pka-qty-dialog>
    <uni-popup type="bottom" ref="flowDialog">
      <flow-dialog title="退货流程"></flow-dialog>
    </uni-popup>
  </view>
</template>
<script lang="ts" src="./InvXFDetail.ts"></script>
<style lang="scss">
.invXF-detail {
  height: 100vh;
  background: $list-bg-color-lx;
  overflow-x: hidden;
  .scroll {
    height: 100%;
    &.hasAction {
      height: calc(100vh - 188rpx);
    }
    &.hasPackage {
      height: calc(100vh - 100rpx);
    }
  }
  ::v-deep .uni-numbox {
    justify-content: flex-end;
  }
  .top-header {
    width: 750rpx;
    height: 160rpx;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    justify-content: space-between;
    padding: 20rpx $base-padding 48rpx;
    background: $color-primary;
    .top-left {
      .title {
        display: flex;
        align-items: center;

        font-size: 44rpx;
        height: 48rpx;
        line-height: 48rpx;
        font-weight: 500;
        color: $uni-bg-color;
        .title-arrow {
          width: 32rpx;
          height: 32rpx;
        }
      }
      .bill-no {
        margin-top: 12rpx;

        font-size: 30rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 500;
        color: $uni-bg-color;
        opacity: 0.8;
      }
    }
  }

  .top-summary {
    margin-top: -12rpx;
    border-radius: 12rpx;
    overflow: hidden;
    padding: 0 $base-padding;
    background: #ffffff;
    margin-bottom: 16rpx;
    .transfer {
      width: auto;
      margin: 0 -24rpx;
      background: #f7f8fa;
      display: flex;
      align-items: center;
      height: 72rpx;
      padding-left: $base-padding;
      .transfer-icon {
        width: 122rpx;
        height: 32rpx;
      }
      .transfer-shop {
        @include ellipsis();
        text-align: center;
        flex: 1;
        font-size: $font-size-default;
        width: 274rpx;
        font-weight: 500;
        color: #282c34;
      }
    }
    .field {
      padding: 20rpx 0;
      & + .field {
        margin-top: 0;
      }
      .label {
        color: #282c34;
        font-size: 30rpx;
      }
      .data {
        color: #585a5e;
        font-size: 30rpx;
      }
    }
    .line {
      @include onePxBorderButtom();
    }
  }

  .inv-xf-tab {
    width: 100%;
    height: 100rpx;
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: #ffffff;
    border-bottom: 2rpx solid #eeeeee;

    .inv-xf-goods {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      margin-right: 16rpx;

      .inv-xf-goods-txt {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32rpx;
        color: #1a1a1a;
        text-align: center;
        font-style: normal;
        margin-top: 32rpx;
      }

      .inv-xf-goods-txt-sub {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        text-align: center;
        font-style: normal;
        margin-top: 34rpx;
      }

      .inv-xf-goods-btn {
        width: 64rpx;
        height: 7rpx;
        background: #1c64fd;
        border-radius: 4rpx;
      }
    }
  }

  .bottom-info {
    border-radius: 12rpx;
    overflow: hidden;
    background-color: #ffffff;
    margin: 16rpx 0;
    padding: 24rpx $base-padding;
    box-sizing: border-box;
  }
  .field {
    width: 100%;
    display: flex;
    font-size: 28rpx;
    position: relative;
    .label {
      flex: 0 0 auto;
      min-width: 140rpx;
      color: $font-color-darklight;
      height: 40rpx;
    }
    .data {
      word-break: break-all;
      color: $color-text-primary;
      margin-left: 15rpx;
      flex: 1 1 auto;
      flex-wrap: wrap;
      min-height: 40rpx;
      text-align: right;
    }
    .arrow {
      width: 32rpx;
      height: 32rpx;
      vertical-align: middle;
    }
    .ellipsis {
      @include ellipsis();
    }
    & + .field {
      margin-top: 24rpx;
    }
  }

  .category-group {
    width: 100%;
    margin-top: 15rpx;
    background-color: $uni-bg-color;
    position: relative;
    .category-group-toggle {
      position: absolute;
    }
  }
  .ship-lines-box {
    .list-title {
      width: 100%;
      height: 76rpx;
      padding: 0 $base-padding;
      box-sizing: border-box;
      position: relative;
      display: flex;
      box-sizing: border-box;
      align-items: center;
      justify-content: space-between;
      font-size: 26rpx;
      color: $font-color-darklight;
      background: #ffffff;
      text-align: right;
      &-item {
        flex: 1;
        white-space: nowrap;
      }
    }
  }

  .dir-alc-bck-body {
    background: #ffffff;
    .sku-edit-card {
      position: relative;
      display: flex;
      flex-direction: column;
      min-height: 196rpx;
      width: 100%;
      box-sizing: border-box;
      padding: 24rpx;
      background: rgba(255, 255, 255, 1);
      .second-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        margin-top: 8rpx;
      }
      .third-row {
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: space-between;
        margin-top: 14rpx;
      }
      .sku-goods {
        display: flex;
        margin-bottom: 12rpx;
      }

      .sku-left {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        flex: 0 0 auto;
        margin-right: 16rpx;

        .sku-img {
          width: 120rpx;
          height: 120rpx;
        }
        .info__scale {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 24rpx;
          height: 24rpx;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 8rpx 0px 8rpx 0rpx;
          text-align: center;
          @include flex();
          &-img {
            width: 16rpx;
            height: 16rpx;
          }
        }
      }
      .sku-name {
        height: 100%;
        line-height: 40rpx;
        font-size: 30rpx;
        font-weight: 500;
        color: $color-text-primary;
        @include ellipsis(2);
      }
      .sku-code {
        font-size: 30rpx;
        margin-top: 8rpx;
        line-height: 40rpx;
      }
      .sku-info {
        line-height: 40rpx;
        display: flex;
        align-items: center;
        margin-top: 10rpx;
        justify-content: flex-end;
        color: #585a5e;
        font-size: 26rpx;
        .qty {
          box-sizing: border-box;
          min-width: 176rpx;
          text-align: right;
          font-size: 32rpx;
          font-weight: bold;
        }
      }
      .sku-qpc {
        font-size: 24rpx;
        margin-left: 20rpx;
        color: #94969a;
      }
      .color-black {
        color: #585a5e;
      }
      .sku-munit {
        font-size: 24rpx;
        margin-left: 20rpx;
        color: #585a5e;
        .color-warning {
          color: $color-warning;
        }
        .goods-info-price {
          font-size: 30rpx;
          font-weight: 500;
          &::before {
            content: '￥';
            font-size: 24rpx;
            font-weight: 400;
          }
        }
      }

      .apply-qty {
        font-size: 24rpx;
        color: #94969a;
        .apply-qty__num {
          color: #585a5e;
        }
      }
    }
    .number-input-box {
      width: 188rpx;
      height: 48rpx;
      line-height: 36rpx;
      margin-left: 12rpx;
      padding: 6rpx 12rpx;
      background: #f5f6f7;
      border-radius: 4rpx;
      box-sizing: border-box;
      text-align: right;
    }
    .mt-12 {
      margin-top: 12rpx;
    }

    .sku-batch {
      margin-top: 12rpx;
      padding: 16rpx;
      background: #f5f6f7;
      border-radius: 8rpx;
      box-sizing: border-box;
      .sku-batch-header {
        margin-bottom: 16rpx;
        font-size: 24rpx;
        color: #666;
        display: flex;
        align-items: center;
      }
      .sku-batch-content {
        font-size: 24rpx;
        color: #333;
        display: flex;
        align-items: center;
        &:not(:last-child) {
          margin-bottom: 16rpx;
        }
      }
      .sku-batch-long {
        width: 380rpx;
        flex: 0 0 auto;
        display: flex;
        align-items: center;
      }
      .sku-batch-short {
        flex: 1 1 auto;
        text-align: right;
        margin-left: 12rpx;
      }
      .batch-date {
        width: 146rpx;
      }
      .batch-tracecode {
        display: block;
        @include ellipsis();
        &.short-tracecode {
          width: 340rpx;
        }
      }
      .batch-input-box {
        flex: 1 1 auto;
        height: 48rpx;
        line-height: 36rpx;
        padding: 6rpx 12rpx;
        margin-left: 12rpx;
        background-color: #fff;
        box-sizing: border-box;
        text-align: right;
        font-size: 26rpx;
        color: #333333;
      }
    }
  }

  .list-title:after,
  .sku-edit-card:after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    bottom: 0;
    left: 19rpx;
    width: calc(100% - 38rpx);
    border-bottom: 2rpx solid rgba(227, 228, 232, 1);
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }

  .footer-total-bar {
    width: 750rpx;
    height: 88rpx;
    padding: 0 24rpx;
    background: #fff7e0;
    font-size: 24rpx;
    color: #999;
    @include flex(row, space-between, center);
    box-sizing: border-box;
    .total-price {
      font-size: 32rpx;
      font-family: HelveticaNeue, HelveticaNeue;
      font-weight: bold;
      color: #333;
      &::before {
        content: '￥';
        font-size: 24rpx;
      }
    }
  }
  .footer-total-bottom {
    width: 100%;
    position: absolute;
    bottom: 0;
    display: flex;
    flex-direction: column;
  }
  .flexC {
    display: flex;
    align-items: center;
  }
  .flexCE {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  .flexCF {
    display: none;
  }
  .confirm-btn {
    position: fixed;
    bottom: 0;
    width: 100%;
  }
}
</style>
