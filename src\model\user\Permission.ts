/*
 * @Author: 徐庆凯
 * @Date: 2021-02-26 11:01:15
 * @LastEditTime: 2025-05-24 14:42:56
 * @LastEditors: hanwei
 * @Description: 权限
 * @FilePath: /soa/src/model/user/Permission.ts
 * 记得注释
 */
export enum Permission {
  //全局控制成本价毛利率显示
  globalPriceView = 'SOS.GLOBAL.COSTPRICE.VIEW', // 隐藏成本价
  globalMarginView = 'SOS.GLOBAL.GROSSMARGIN.VIEW', // 隐藏毛利率

  // 订货
  requireApplyView = 'SOS.REQUIRE_APPLY.VIEW', // 查看
  requireApplySubmit = 'SOS.REQUIRE_APPLY.SUBMIT', // 提交
  requireApplyEdit = 'SOS.REQUIRE_APPLY.EDIT', // 编辑
  requireApplyDelete = 'SOS.REQUIRE_APPLY.DELETE', // 删除
  requireApplyAdd = 'SOS.REQUIRE_APPLY.ADD', // 加单
  requireApplyUrgent = 'SOS.REQUIRE_APPLY.URGENT', // 加急叫货
  requireApplyAdvance = 'SOS.REQUIRE_APPLY.ADVANCE', // 提前叫货
  requireApplyVirtual = 'SOS.REQUIRE_APPLY.VIRTUAL', // 多货补单
  requireApplyReduce = 'SOS.REQUIRE_APPLY.REDUCE', // 减量
  requireApplyRevoke = 'SOS.REQUIRE_APPLY.REVOKE', // 撤回

  //   1、显示昨日叫货（说明：有的客户不关心这个字段）
  // 2、显示是否团购要货（说明：即默认没有团购要货）
  // 3、显示期望到货时间（说明：有的客户不关心这个字段）
  requireApplyYestedayShow = 'SOS.REQUIRE_APPLY.YESTEDAY.SHOW', // 显示昨日叫货
  requireApplyGroupShow = 'SOS.REQUIRE_APPLY.GROUP.SHOW', // 显示是否团购要货
  requireApplyExpireDateShow = 'SOS.REQUIRE_APPLY.EXPIRE.SHOW', // 显示期望到货时间

  //预报货

  preOrderView = 'SOS.PRE_ORDER.VIEW', //查看
  preOrderEdit = 'SOS.PRE_ORDER.EDIT', //维护

  requireApplyHideInvQty = 'SOS.REQUIRE_APPLY.HIDEINVQTY', // 不显示库存

  // 川鼎汇订货
  requireOrderView = 'SOS.REQUIRE_ORDER.VIEW', // 查看
  requireOrderSubmit = 'SOS.REQUIRE_ORDER.SUBMIT', // 提交
  requireOrderEdit = 'SOS.REQUIRE_ORDER.EDIT', // 编辑
  requireOrderDelete = 'SOS.REQUIRE_ORDER.DELETE', // 删除
  requireOrderAdd = 'SOS.REQUIRE_ORDER.ADD', // 新增

  // 单品打折
  AppCouponCodeView = 'SOS.APP_COUPON_CODE.VIEW', // 查看

  // 标价签申请
  TallyApplyView = 'SOS.TALLY_APPLY.VIEW', // 查看
  TallyApplyDelete = 'SOS.TALLY_APPLY.DELETE', // 删除
  TallyApplyAdd = 'SOS.TALLY_APPLY.ADD', // 新增
  TallyApplyEdit = 'SOS.TALLY_APPLY.EDIT', // 编辑
  TallyApplySubmit = 'SOS.TALLY_APPLY.SUBMIT', // 提交

  //账单支付
  payApplyView = 'SOS.PAYAPPLY.VIEW', //查看
  payApplyPay = 'SOS.PAYAPPLY.PAY', //支付
  payApplyCancelPay = 'SOS.PAYAPPLY.CANCELPAY', //取消支付

  // 自采
  selfPurchaseView = 'SOS.SELF_PURCHASE.VIEW', // 查看
  selfPurchaseSubmit = 'SOS.SELF_PURCHASE.SUBMIT', // 提交
  selfPurchaseEdit = 'SOS.SELF_PURCHASE.EDIT', // 编辑
  selfPurchaseDelete = 'SOS.SELF_PURCHASE.DELETE', // 删除
  selfPurchaseAdd = 'SOS.SELF_PURCHASE.ADD', // 新增

  // 直送收货
  directReceiptView = 'SOS.DIRECTRECEIPT.VIEW', // 查看
  directReceiptSubmit = 'SOS.DIRECTRECEIPT.SUBMIT', // 提交
  directReceiptEdit = 'SOS.DIRECTRECEIPT.EDIT', // 编辑
  directReceiptPrice = 'SOS.DIRECTRECEIPT.SHOWPRICE', //显示配货价
  directReceiptDelete = 'SOS.DIRECTRECEIPT.DELETE', // 删除

  // 直送退货
  directBckView = 'SOS.DIRECTBCK.VIEW', // 查看
  directBckSubmit = 'SOS.DIRECTBCK.SUBMIT', // 提交
  directBckEdit = 'SOS.DIRECTBCK.EDIT', // 编辑
  directBckPrice = 'SOS.DIRECTBCK.SHOWPRICE', //显示配货价
  directBckDelete = 'SOS.DIRECTBCK.DELETE', // 删除

  // 收货
  receiptView = 'SOS.RECEIPT.VIEW', // 查看
  receiptSubmit = 'SOS.RECEIPT.SUBMIT', // 提交
  receiptEdit = 'SOS.RECEIPT.EDIT', // 编辑
  receiptShowprice = 'SOS.RECEIPT.SHOWPRICE', //显示配货价
  receiptBoxDataAccess = 'SOS.RECEIPT.BOX.DATAACCESS', // 可以操作核对差异
  receiptFinish = 'SOS.RECEIPT.FINISH', // 完成收货
  receiptOneClick = 'SOS.RECEIPT.ONECLICK', // 一键收货

  // 收货差异
  receiptDiffView = 'SOS.RECEIPT_DIFF.VIEW', // 查看

  // 调拨申请
  invxfApplyView = 'SOS.INV_XF_APPLY.VIEW', // 查看
  invxfApplySubmit = 'SOS.INV_XF_APPLY.SUBMIT', // 提交
  invxfApplyEdit = 'SOS.INV_XF_APPLY.EDIT', // 编辑
  invxfApplyDelete = 'SOS.INV_XF_APPLY.DELETE', // 删除
  invxfApplyAbort = 'SOS.INV_XF_APPLY.ABORT', // 作废
  invxfApplyIn = 'SOS.INV_XF_APPLY.IN', // 调入
  invxfApplyOut = 'SOS.INV_XF_APPLY.OUT', // 调出

  // 调拨
  invxfView = 'SOS.INV_XF.VIEW', // 查看
  invxfSubmit = 'SOS.INV_XF.SUBMIT', //提交
  invxfEdit = 'SOS.INV_XF.EDIT', //编辑

  // 退货申请
  returnApplyView = 'SOS.RETURN_APPLY.VIEW', // 查看
  returnApplySubmit = 'SOS.RETURN_APPLY.SUBMIT', // 提交
  returnApplyEdit = 'SOS.RETURN_APPLY.EDIT', // 编辑
  returnApplyDelete = 'SOS.RETURN_APPLY.DELETE', // 删除
  returnApplyRevoke = 'SOS.RETURN_APPLY.REVOKE', // 驳回

  // 退仓
  stkOutBckView = 'SOS.STK_OUT_BCK.VIEW', // 查看
  stkOutBckSubmit = 'SOS.STK_OUT_BCK.SUBMIT', // 提交
  stkOutBckEdit = 'SOS.STK_OUT_BCK.EDIT', // 编辑

  // 退供应商
  dirAlcBckView = 'SOS.DIR_ALC_BCK.VIEW', // 查看
  dirAlcBckSubmit = 'SOS.DIR_ALC_BCK.SUBMIT', // 提交
  dirAlcBckEdit = 'SOS.DIR_ALC_BCK.EDIT', // 编辑

  // 退公司
  croOrgSaleBckView = 'SOS.ORG_SALE_BCK.VIEW', // 查看
  croOrgSaleBckSubmit = 'SOS.ORG_SALE_BCK.SUBMIT', // 提交
  croOrgSaleBckEdit = 'SOS.ORG_SALE_BCK.EDIT', // 编辑

  // 报损
  lossApplyView = 'SOS.LOSS_APPLY.VIEW', // 查看
  lossApplySubmit = 'SOS.LOSS_APPLY.SUBMIT', // 提交
  lossApplyEdit = 'SOS.LOSS_APPLY.EDIT', // 编辑
  lossApplyDelete = 'SOS.LOSS_APPLY.DELETE', // 删除

  // 报溢
  overApplyView = 'SOS.OVER_APPLY.VIEW', // 查看
  overApplySubmit = 'SOS.OVER_APPLY.SUBMIT', // 提交
  overApplyEdit = 'SOS.OVER_APPLY.EDIT', // 编辑
  overApplyDelete = 'SOS.OVER_APPLY.DELETE', // 删除

  // 商品效期
  SkuValidDateView = 'SOP.SKUVALIDDATE.VIEW', // 查看

  // 商品查询
  goodsSearchView = 'SOS.GOODS_SEARCH.VIEW', // 查看
  //订单查询
  orderSearchView = 'SOS.ORDER_SEARCH.VIEW', // 查看
  //价格带
  shopSkuTagView = 'SOS.SHOPSKUTAG.VIEW', //查看
  shopSkuTagSave = 'SOS.SHOPSKUTAG.SAVE', //保存
  shopSkuTagCancel = 'SOS.SHOPSKUTAG.CANCEL', //删除

  //交易流水权限
  billManageView = 'SOS.BILLMANAGE.VIEW', // 查看

  //账户流水查看权限

  fundAccountView = 'SOS.FUNDACCOUNT.VIEW', // 查看

  // 加工
  processBillView = 'SOS.PROCESS_BILL.VIEW', // 查看
  processBillSubmit = 'SOS.PROCESS_BILL.SUBMIT', // 提交
  processBillEdit = 'SOS.PROCESS_BILL.EDIT', // 编辑
  processBillDelete = 'SOS.PROCESS_BILL.DELETE', // 删除

  //订货活动
  orderExplosiveView = 'SOS.ORDEREXPLOSIVE.VIEW', //查看
  orderExplosiveEdit = 'SOS.ORDEREXPLOSIVE.EDIT', //编辑和提交
  // 加盟对账
  billView = 'SOP.BILL.VIEW', // 查看 废弃

  // 质量反馈
  feedbackView = 'SOS.FEEDBACK.VIEW', // 查看
  feedbackSubmit = 'SOS.FEEDBACK.SUBMIT', // 提交
  feedbackHiddenApplyAmount = 'SOS.FEEDBACK.HIDDEN_APPLY_AMOUNT', // 质量反馈是否展示申请金额

  // 常见问题
  sopProManagementView = 'SOP.FAQ.VIEW',

  // 任务模块权限
  taskModuleView = 'SOP.TASKMODULE.VIEW', // 任务查看
  taskModuleEdit = 'SOP.TASKMODULE.EDIT', // 任务编辑

  // 营销玩法
  promRuleView = 'SOP.PROM_RULE.VIEW', // 查看
  promRuleModify = 'SOP.PROM_RULE.MODIFY', // 维护
  promRuleTerminate = 'SOP.PROM_RULE.TERMINATE', // 终止

  // 首页"全部"按钮权限
  showAllApp = 'showAllApp', // 显示"全部"按钮的权限

  // 公告阅读
  anncSubmit = 'SOP.ANNC.SUBMIT',

  // 报表查看权限
  reportView = 'SOP.REPORT.VIEW',

  // 重点商品权限
  skuCheckView = 'SOS.SKU_CHECK.VIEW', // 查看

  // 盘点模块权限
  checkView = 'SOS.CHECK.VIEW',
  checkShowPack = 'SOS.CHECKDATA.SHOWPACK', // 展示实盘包数
  checkDataView = 'SOS.CHECKDATA.VIEW', // 是否显示盈亏数据（盈亏金额，盈亏数量）
  openCheckView = 'SOS.CHECK.OPENCHECK', //不停业盘点权限
  checkDataPrepare = 'SOS.CHECKDATA.PREPARE', // 开始准备
  checkDataNormalFinish = 'SOS.CHECKDATA.NORMALFINISH', // 结束初盘
  checkDataSetRepeat = 'SOS.CHECKDATA.SETREPEAT', // 设置复盘商品
  checkDataFinish = 'SOS.CHECKDATA.FINISH', // 结束盘点
  checkDataTerminal = 'SOS_CHECKDATA_TERMINAL', // 允许盘中结束盘点

  // 自主盘点模块权限
  cusInvCheckView = 'SOS.CUSINVCHECK.VIEW',
  cusInvCheckDataView = 'SOS.CUSINVCHECK.DATAVIEW', // 是否显示盈亏数据（盈亏金额，盈亏数量）
  cusInvCheckEdit = 'SOS.CUSINVCHECK.EDIT', //编辑
  cusInvCheckSubmit = 'SOS.CUSINVCHECK.SUBMIT', // 提交

  // 门店自主盘点任务
  checkTaskView = 'SOS.CHECKTASK.VIEW', //查看
  checkTaskSave = 'SOS.CHECKTASK.SAVE', //编辑
  checkTaskSubmit = 'SOS.CHECKTASK.SUBMIT', // 提交
  checkTaskDelete = 'SOS.CHECKTASK.DELETE', // 删除
  checkTaskConfirmResult = 'SOS.CHECKTASK.CONFIRMRESULT', // 确认盘点结果
  checkTaskConfirmAdjust = 'SOS.CHECKTASK.CONFIRMADJUST', // 确认调整库存
  checkTaskCancel = 'SOS.CHECKTASK.CANCEL', //取消
  checkTaskDataView = 'SOS.CHECKTASK.DATAVIEW', // 盈亏查看
  checkTaskCalc = 'SOS.CHECKTASK.CALC', // 重新计算盈亏
  checkTaskReject = 'SOS.CHECKTASK.REJECT', // 退回重盘
  checkTaskDiffInvTotalView = 'SOS.CHECKTASK.DIFFINVTOTALVIEW', // 查看盈亏成本合计金额（盈亏金额，盈亏数量(已删除)）
  checkTaskDiffSaleTotalView = 'SOS.CHECKTASK.DIFFSALEVIEW', // 查看盈亏销售金额
  checkTaskInvalid = 'SOS.CHECKTASK.INVALID', //作废

  // 门店盘点
  taskCheckView = 'SOS.TASKCHECK.VIEW', //查看
  taskCheckSave = 'SOS.TASKCHECK.SAVE', //保存
  taskCheckSubmit = 'SOS.TASKCHECK.SUBMIT', // 提交
  taskCheckDelete = 'SOS.TASKCHECK.DELETE', // 删除
  taskCheckReject = 'SOS.TASKCHECK.REJECT', // 退回复盘单

  //线路调整

  lineAdjView = 'SOS.LINEADJ.VIEW', //查看模块权限
  lineDetailView = 'SOS.LINEDETAIL.VIEW', //路线查看权限
  lineSetView = 'SOS.LINESET.VIEW', //路线设置权限

  //大数据管理权限
  bigDataView = 'SOS.BIGDATA.VIEW', //查看数据模块
  GoalAchieved = 'SOS.BIGDATA.GOALACHIEVED', //目标达成
  PeriodOverview = 'SOS.BIGDATA.PERIODOVERVIEW', //时段概览
  Member = 'SOS.BIGDATA.MEMBER', //会员查看
  EarlyWarn = 'SOS.BIGDATA.EARLYWARN', //预警查看

  //学习提升
  knowledgeBaseView = 'SOS.KNOWLEDGEBASE.VIEW', //知识库查看
  examPaperSubmit = 'SOS.KNOWLEDGEBASE.SUBMIT', //试题提交

  //提成绩效
  commissionPerformance = 'SOS.COMMISSIONPERFORM.VIEW',

  //库存上下限调整
  invLmtAdjEdit = 'SOS.INVLMTADT.EDIT', //编辑
  invLmtAdjSubmit = 'SOS.INVLMTADT.SUBMIT', //提交
  invLmtAdjView = 'SOS.INVLMTADT.VIEW', //查看

  // 利润核算
  rechgBookView = 'SOS.RECHGBOOK.VIEW', //查看
  rechgBookModify = 'SOS.RECHGBOOK.MODIFY', //维护

  // 线上商品管理
  platfromSkuView = 'SOP.PLATFORMSKU.VIEW', // 查看
  platfromSkuMeituanPrice = 'SOP.PLATFORMSKU.PRICE_PLATFORM_MEITUAN', //        设置美团平台商品价格
  platfromSkuDaojiaPrice = 'SOP.PLATFORMSKU.PRICE_PLATFORM_DAOJIA', // 设置到家平台商品价格
  platfromSkuElemePrice = 'SOP.PLATFORMSKU.PRICE_PLATFORM_BE_ELEME', //设置饿百平台商品价格
  platfromSkuStock = 'SOP.PLATFORMSKU.STOCK', // 设置库存
  platfromSkuShelf = 'SOP.PLATFORMSKU.SHELF', // 上下架

  // 文件返回登记
  fileRegistView = 'SOS.FILE_REGIST.VIEW', // 查看
  fileRegistSubmit = 'SOS.FILE_REGIST.SUBMIT', // 提交
  fileRegistEdit = 'SOS.FILE_REGIST.EDIT', // 编辑
  fileRegistDelete = 'SOS.FILE_REGIST.DELETE', // 删除

  //周转筐管理
  packManageView = 'SOS.PACK_MANAGE.VIEW', // 查看

  // 门店领用
  useApplyView = 'SOS.USE_APPLY.VIEW', // 查看
  useApplySubmit = 'SOS.USE_APPLY.SUBMIT', // 提交
  useApplyEdit = 'SOS.USE_APPLY.EDIT', // 编辑
  useApplyDelete = 'SOS.USE_APPLY.DELETE', // 删除
  // 异常商品
  exceptionGoodsView = 'SOS.EXCEPTION_GOODS.VIEW', //查看
  // 试吃
  foreTasteView = 'SOS.FORE_TASTE.VIEW', // 查看
  foreTasteEdit = 'SOS.FORE_TASTE.EDIT', // 编辑

  // 批号调整申请
  batchAdjustApplyView = 'SOS.BATCH_ADJUST_APPLY.VIEW', // 查看
  batchAdjustApplyEdit = 'SOS.BATCH_ADJUST_APPLY.EDIT', // 编辑
  // 配货差异申请
  receiptDiffApplyView = 'SOS.RECEIPT_DIFF_APPLY.VIEW', // 查看
  receiptDiffApplyEdit = 'SOS.RECEIPT_DIFF_APPLY.EDIT', // 编辑
  receiptDiffApplySubmit = 'SOS.RECEIPT_DIFF_APPLY.SUBMIT', // 提交

  // 周边门店库存
  circumStoreInvView = 'SOS.CIRCUMSTOREINV.VIEW', // 查看

  //调价申请
  priceAdjApplyView = 'SOS.PRICEADJAPPLY.VIEW', //查看
  priceAdjApplyAdd = 'SOS.PRICEADJAPPLY.ADD', // 新增
  priceAdjApplyDelete = 'SOS.PRICEADJAPPLY.DELETE', // 删除
  priceAdjApplyEdit = 'SOS.PRICEADJAPPLY.EDIT', // 编辑
  priceAdjApplySubmit = 'SOS.PRICEADJAPPLY.SUBMIT', // 提交

  // 店务商品查询
  sosSkuQueryView = 'SOS.SKU_QUERY.VIEW', // 查看

  // 门店考评
  shopScoreView = 'SOP.SHOPSCORE.VIEW', // 查看

  // 标价签打印
  labelPrintingView = 'SOS.LABELPRINTING.VIEW', // 查看

  // 预订单活动
  preOrderActivityView = 'SOS.PREORDERACTIVITY.VIEW', // 查看
  preOrderActivitySignUp = 'SOS.PREORDERACTIVITY.SIGNUP', // 报名

  // 预订单订单
  preOrderOrderView = 'SOS.PREORDER.VIEW', // 预订单查看
  preOrderOrder = 'SOS.PREORDER.ORDER', // 预订单订货

  // 商品淘汰
  gdeLiminationView = 'SOS.GDELIMINATION.VIEW', // 查看
  gdeLiminationSubmit = 'SOS.GDELIMINATION.SUBMIT', // 提交
  gdeLiminationStockModify = 'SOS.GDELIMINATION.STOCK_MODIFY', // 库存调整

  // 门店对账
  storeConciliationView = 'SOS.STORERECONCILIATION.VIEW', // 查看
  storeConciliationConfirmSign = 'SOS.STORERECONCILIATION.CONFIRMSIGN', // 签字确认
  storeConciliationRefuseSign = 'SOS.STORERECONCILIATION.REFUSESIGN', // 拒签签字
  // 爱折扣 软折平台
  azkRZPlatformView = 'AZK.H5.JMSZD.VIEW', // 查看

  // 爱折扣 经营分析
  azkSJKBView = 'AZK.H5.SJKB.VIEW', // 查看

  // 店务管理/店务商品查询  陈列位置调整
  displayLocationAdjust = 'SOS.DISPLAY_LOCATION.ADJUST', // 陈列位置调整
  // 店务管理/店务商品查询  加入打印列表
  printListAdd = 'SOS.PRINT_LIST.ADD', // 加入打印列表
  // 店务管理/店务商品查询  立即打印
  printListImmediately = 'SOS.PRINT_LIST.IMMEDIATELY', // 立即打印
  // 店务管理/店务商品查询  价签申请
  priceTagRequest = 'SOS.PRICE_TAG.REQUEST', // 价签申请
  // 店务管理/店务商品查询  价签打印
  priceTagPrinting = 'SOS.PRICE_TAG.PRINTING', // 价签打印

  // 店务管理/收货  绑定陈列位置
  receiptDisplayLocationBind = 'SOS.RECEIPT.DISPLAY_LOCATION.BIND', // 绑定陈列位置
  // 店务管理/直送收货 ,绑定陈列位置
  directDisplayLocationBind = 'SOS.DIRECT_RECEIPT.DISPLAY_LOCATION.BIND', // 绑定陈列位置
  // 店务管理/门店自采  绑定陈列位置
  selfDisplayLocationBind = 'SOS.SELF_PURCHASE.DISPLAY_LOCATION.BIND', // 绑定陈列位置

  // 履约助手
  orderFulfillmentPickingMaintain = 'mas.dly.order.fulfillment.picking.maintain', // 订单拣货维护
  orderFulfillmentPickingView = 'mas.dly.order.fulfillment.picking.view', // 订单拣货查看
  orderFulfillmentChargebacksMaintain = 'mas.dly.order.fulfillment.chargebacks.maintain', // 退单处理维护
  orderFulfillmentChargebacksView = 'mas.dly.order.fulfillment.chargebacks.view', // 退单处理查看
  merchandiseManagementEnable = 'mas.dly.merchandise.management.enable', // 商品上下架
  merchandiseManagementView = 'mas.dly.merchandise.management.view', // 商品查看
  merchandiseManagementCreate = 'mas.dly.merchandise.management.create', // 添加商品
  selfwithdrawalManagementView = 'mas.dly.order.selftake.view', // 自提核销查看
  selfwithdrawalMaintainView = 'mas.dly.order.selftake.maintain', // 维护权限
  orderReviewsRespond = 'mas.dly.order.reviews.respond', // 回复评价
  orderReviewsView = 'mas.dly.order.reviews.view', // 评价查看
  customerMessagesRespond = 'mas.dly.customer.messages.respond', // 回复消息
  customerMessagesView = 'mas.dly.customer.messages.view', // 消息查看
  pickingTasksView = 'mas.dly.picking.tasks.view', // 任务查看
  boothSkuView = 'mas.dly.booth.sku.view', // 订单拣货维护
  boothSkuConfirmVendorPrice = 'mas.dly.booth.sku.confirmVendorPrice', // 确认供货价
  boothSkuEnable = 'mas.dly.booth.sku.enable', // 商品上下架
  boothBillsView = 'mas.dly.booth.bills.view', // 查看账单
  editVendorPrice = 'mas.dly.booth.sku.editVendorPrice', //修改供货价
  cancelVendorPrice = 'mas.dly.booth.sku.cancelVendorPrice', //取消供货价
  maintainEnable = 'mas.dly.platform.shop.sku.maintainEnable', //上下架按钮权限
  maintainPrice = 'mas.dly.platform.shop.sku.maintainPrice', //修改价格按钮权限
  platformShopSkuView = 'mas.dly.platform.shop.sku.view', //查看平台商品模块权限
  pickBillWithDraw = 'mas.dly.booth.bills.withDraw', // 查看摊位账单提现模块权限
  pickBillsView = 'mas.dly.booth.bills.view', // 摊位账单模块显示权限
  pickTasksFinish = 'mas.dly.picking.tasks.finish', // 拣货任务拣货完成权限
  vendorManageView = 'mas.dly.vendormanage.view', // 供应商查看权限
  productLocationView = 'mas.dly.product.location.view', // 商品货位查看权限
  orderCancel = 'soms.order.cancel', // 订单取消
  orderPartRefund = 'soms.order.part.refund', // 部分退款
  orderAmountRefund = 'soms.order.amount.refund', // 金额退差
  orderWeightRefund = 'soms.order.weight.refund', // 缺重补差
  hzxView = 'mas.dly.lyzs.hzx.view', // 汇真鲜入口
  changeMaintain = 'mas.dly.order.change.maintain', // 换货权限
  pickTaskClaim = 'mas.dly.store.pick.task.claim', // 门店拣货任务领取
  pickTaskView = 'mas.dly.store.pick.task.view', // 门店拣货任务查看
  printMaintain = 'mas.dly.order.sku.print.maintain', // 拣货任务打印商品标签
  orderPrintMaintain = 'mas.dly.order.print.maintain', // 打印小票权限
  outofstockMaintain = 'mas.dly.picking.tasks.outofstock.maintain', // 缺货登记权限
  jcjpView = 'mas.dly.lyzs.jcjp.view', // 集采集配入口
  profitlossorderView = 'mas.dly.lyzs.booth.profitlossorder.view', // 订单履约盈利分析入口

  // 经营助手权限表
  MODULE_INV = 'MODULE_INV', // 库存
  MODULE_CONCERN = 'MODULE_CONCERN', // 关注
  MODULE_CUSTOMIZE_INDEX = 'MODULE_CUSTOMIZE_INDEX', // 定制指标
  MODULE_STORE = 'MODULE_STORE', // 门店
  MODULE_MEMBER = 'MODULE_MEMBER', // 会员
  MODULE_PAY = 'MODULE_PAY', // 支付
  MODULE_DAILY = 'MODULE_DAILY', // 每日
  MODULE_GOODS = 'MODULE_GOODS', // 商品
  MODULE_GOOD_QUERY = 'MODULE_GOOD_QUERY', // 商品查询
  MODULE_CATEGORY = 'MODULE_CATEGORY', // 分类
  MODULE_OVERVIEW = 'MODULE_OVERVIEW', // 概览

  // 特殊品管理
  specGdRegistView = 'SOS.SPEC_GD_REGIST.VIEW', // 查看
  specGdRegistSubmit = 'SOS.SPEC_GD_REGIST.SUBMIT', // 提交
  specGdRegistEdit = 'SOS.SPEC_GD_REGIST.EDIT', // 编辑
  specGdRegistDelete = 'SOS.SPEC_GD_REGIST.DELETE', // 删除
  // 特殊品库存查询
  specGdRegistInvView = 'SOS.SPEC_GD_REGIST_INV.VIEW', // 查看

  //促销申请
  promApplyView = 'SOS.PROMAPPLY.VIEW', // 查看
  promApplyAdd = 'SOS.PROMAPPLY.ADD', // 新增
  promApplyDelete = 'SOS.PROMAPPLY.DELETE', // 删除
  promApplyEdit = 'SOS.PROMAPPLY.EDIT', // 编辑
  promApplySubmit = 'SOS.PROMAPPLY.SUBMIT' // 提交
}
