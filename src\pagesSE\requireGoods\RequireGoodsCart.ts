/*
 * @Author: 庞昭昭
 * @Date: 2021-08-03 09:56:18
 * @LastEditTime: 2025-05-27 16:05:02
 * @LastEditors: hanwei
 * @Description: 商品明细（购物车）
 * @FilePath: /soa/src/pagesSE/requireGoods/RequireGoodsCart.ts
 * 记得注释
 */
import { Component, Watch } from 'vue-property-decorator'
import { Action, State } from 'vuex-class'
import GoodsAction from './cmp/GoodsAction.vue'
import GoodsListItem from './cmp/GoodsListItem.vue'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import NumberUtil from '@/utils/NumberUtil'
import config from '@/config'
import AppOpenCheckTabCategoryDTO from '@/model/opencheck/AppOpenCheckTabCategoryDTO'
import QueryRequest from '@/model/base/QueryRequest'
import GoodsCategory from '@/model/data/GoodsCategory'
import illDraftLineKey from '@/model/default/illDraftLineKey'
import DraftID from '@/model/default/DraftID'
import Pagination from '@/pagesSE/cmp/Pagination.vue'
import RequireApplyApi from '@/network/requireApply/RequireApplyApi'
import AppRequireApplyDraftDTO from '@/model/requireApply/draft/AppRequireApplyDraftDTO'
import InventoryDiff from './cmp/InventoryDiff.vue'
import AppRequireApplyDraftLineQueryDTO from '@/model/requireApply/draft/AppRequireApplyDraftLineQueryDTO'
import RequireOrderApi from '@/network/requireOrder/RequireOrderApi'
import RequireApplyCreationByDraft from '@/model/requireApply/draft/RequireApplyCreationByDraft'
import { RequireApplyType } from '@/model/requireApply/RequireApplyType'
import { RoundType } from '@/model/requireApply/RoundType'
import DateUtil from '@/utils/DateUtil'
import RequireApplyStoreOrderModeRule from '@/model/requireApply/RequireApplyStoreOrderModeRule'
import RequireApplySubmitInvalidDtl from '@/model/requireApply/draft/RequireApplySubmitInvalidDtl'
import AppRequireApplySubBaseLineDTO from '@/model/requireApply/draft/AppRequireApplySubBaseLineDTO'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import PreparePay from './cmp/PreparePay.vue'
import { RequireApplyPaymentState } from '@/model/requireApply/RequireApplyPaymentState'
import StoreDpsAccountInfo from '@/model/requireApply/StoreDpsAccountInfo'
import { RequireApplyState } from '@/model/requireApply/RequireApplyState'
import UseQueues from '@/components/hd-popover/UseQueues'
import { mixins } from 'vue-class-component'
import AppRequireApplyDraftLineKeys from '@/model/requireApply/AppRequireApplyDraftLineKeys'
import AppRequireApplyTabCategoryFilterDTO from '@/model/requireApply/draft/AppRequireApplyTabCategoryFilterDTO'
import { debounce } from 'ts-debounce'

// 辅料商品 自主下单时，需要保存query时查到的金额数量，保存的时候仍将query到的金额数量传回去
class AppRequireApplyDraftLineQueryDTOExt extends AppRequireApplyDraftLineQueryDTO {
  oldTotal: number = 0
  oldQty: number = 0
  oldQpcQty: string = ''
}
@Component({ components: { GoodsAction, GoodsListItem, Pagination, InventoryDiff, PreparePay } })
export default class RequireGoodsCart extends mixins(UseQueues) {
  @State('optionList') optionList: ModuleOption[] //店务配置列表
  @Action('isOrderHasNotSave') actionHasNotSave // 是否提示数据未保存
  @State('requireApplyStoreInfo') storeInfo //门店叫货配置
  @Action('requireApplyStoreInfo') actionStoreInfo //门店叫货配置
  $refs: any
  clientWidth: number = 0 // 手机宽度
  clientHeight: number = 0 // 手机高度
  categoryListHeight: number = 0 // categoryList高度
  cartHeaderHeight: number = 96 // 购物车顶部高度
  goodsActionHeight: number = 120 // 购物车操作导航条高度
  activeIndex: number = -1 // 被选中选项
  activeUuid: string = '' // 被选中选项的uuid
  collapseStatus: boolean = true // 折叠状态 true:折叠，false:非折叠
  // 分页相关
  pageSize: number = 20 // 每页大小
  pageNum: number = 0 // 页码
  finished: boolean = false // 是否加载完成
  isLoading: boolean = false // 是否在加载
  moveIndex: number | null = null // 适用商品移动下标
  cloudAccout: string = '' //云资金账户余额
  goodsCategoryList: AppOpenCheckTabCategoryDTO[] = [] // 商品分类列表
  categoryGoodsCount: number[] = [] // 购物车中每种大类所选商品个数，用于左侧导航栏显示
  selectedCategory: GoodsCategory = new GoodsCategory() //被选中的商品类别
  goodsPrice: number = 0 //商品总金额
  goodsNum: number = 0 //商品总种类数
  totalQpcQty: number = 0 //商品总规格数
  showGoodsList: AppRequireApplyDraftLineQueryDTO[] = []
  scrollTop: number = 0 // scrollview显示顶部
  curpage: number = 1 // 强制回当前页1
  eachPage: number = 200 // 分页器每页展示几条数据
  hasMore: boolean = true // 是否有更多数据
  total: number = 0 // 当前请求列表总数
  showBottom: boolean = true // 分页器选择页数时，隐藏确认按钮
  isDelete: boolean = false //是否正在删除
  goodsQueryFinish: boolean = false //商品分类获取完成
  storeFinish: boolean = false //店铺规则获取完成
  showGdImage: boolean = false //是否显示商品图片
  validateLevel4CaseQty: number = 0 // 叫货包装数整规格校验级别:0-不提示(默认)，1-仅提示，2-不允许保存
  billId: string = '' //单据id
  disableList: AppRequireApplyDraftLineQueryDTO[] = [] //可订数不足商品列表
  deleteList: AppRequireApplyDraftLineQueryDTO[] = [] //已作废商品列表
  showList: AppRequireApplyDraftLineQueryDTO[] = [] //弹窗展示商品列表
  oldGoodsNum: number = 0 //库存不足提交前商品品项数
  oldGoodsPrice: number = 0 //库存不足提交前商品总金额
  note: string = '' //订单备注
  strictControl: Nullable<boolean> = null // 是否强制订货时间可订
  isEdit: boolean = false // 要换单编辑/新增状态
  draftId: string = '' //草稿id
  loadText: string = '加载中···' //底部加载文案
  isHasNotSave: boolean = false //单据数据未保存
  isHasNotSaveDraft: boolean = false //草稿未保存
  groupPurchase: number = 0 // 是否团购
  type: RequireApplyType = RequireApplyType.normal // 订货类型
  forecastArrivalDate: Nullable<string> = null //   预计到货时间
  expireDate: Nullable<string> = null // 要求到货日期
  isPre: boolean = false // 预报货  （无需展示时间和订货金额）
  selfPickUp: number = 0 //是否自提
  paymentSrcContext: any = {} //支付信息
  timer: any = null //查询支付结果计时器
  isNeedGetPay: boolean = false //复制后需要支付结果
  noPickAmt: number = 0 // 未提货金额
  saveAll: boolean = false // 是否保存所有
  selectGoosLine: AppRequireApplyDraftLineQueryDTO = new AppRequireApplyDraftLineQueryDTO() // 选中的商品
  doSaveAfter = debounce(this.doSave, 300, { isImmediate: false }) // 防抖
  doSubmitAfter = debounce(this.doSubmit, 300, { isImmediate: false }) // 防抖

  /**
   * 是否开启自动保存
   */
  get enableAutoSave() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosRequireApply
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && 'enableAutoSave' in moduleConfig[0].options) {
      return moduleConfig[0].options.enableAutoSave === '1'
    }
    return true
  }

  // 门店辅料管理 为1时自动计算辅料，并将辅料数据计算入主商品的价格中，为2时直接展示辅料商品，用户自行添加数量，不计算入主商品的价格金额中
  get accessoryManage() {
    return this.storeInfo.accessoryManage || 0
  }

  /**
   * 辅料分类前缀
   */
  get accessoryGoodsCategoryNamePrefix() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosRequireApply
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && 'accessoryGoodsCategoryNamePrefix' in moduleConfig[0].options) {
      return moduleConfig[0].options.accessoryGoodsCategoryNamePrefix
    }
    return '-'
  }

  /**
   * 是否自动计算辅料价格
   * 当accessoryManage为1时，自动计算辅料价格，并将辅料数据计算入主商品的价格中，为2时直接展示辅料商品，用户自行添加数量，不计算入主商品的价格金额中
   */
  get autoCalcAcc() {
    return this.accessoryManage === 1
  }

  /**
   * 查询商品价格时要传的配货日期(TODO偏移天数取什么值)
   */
  get alcDate() {
    if (this.type === RequireApplyType.advance) {
      return this.expireDate || DateUtil.format(new Date(), 'yyyy-MM-dd')
    } else {
      const now = new Date()
      const sndDateOffsetDay = this.currentRule ? this.sndDateOffsetDay : 1
      return DateUtil.format(now.setDate(now.getDate() + this.alcDay + sndDateOffsetDay), 'yyyy-MM-dd')
    }
  }

  /**
   * 配送天数
   */
  get alcDay() {
    return this.storeInfo.alcDay || 0
  }

  /**
   * h6配置的当前订货类型的规则
   */
  get currentRule() {
    let rule: Nullable<RequireApplyStoreOrderModeRule> = null
    if (this.storeInfo.orderModeRules && this.storeInfo.orderModeRules.length) {
      rule =
        this.storeInfo.orderModeRules.find((rule) => {
          return Number(rule.orderMode) == Number(RequireApplyType.stringToMode(this.type))
        }) ||
        this.storeInfo.orderModeRules.find((rule) => {
          return Number(rule.orderMode) == -1
        }) ||
        null
    }
    return rule
  }

  /**
   * 配送天数
   */
  get sndDateOffsetDay() {
    let sndDateOffsetDay: number = 0
    if (this.currentRule) {
      sndDateOffsetDay = this.currentRule.sndDateOffsetDay
    } else {
      sndDateOffsetDay = 0
    }
    return sndDateOffsetDay
  }

  //总页数
  get totalPage() {
    return Math.ceil(this.total / this.eachPage)
  }
  // 保存提交按钮是否可以点击
  get clickAble() {
    if (this.goodsNum) {
      return true
    } else {
      return false
    }
  }

  // 获取按钮权限
  get btnPermission() {
    const btnPermission = {
      save: false,
      submit: false
    }

    if (this.isPre) {
      btnPermission.save = PermissionMgr.hasPermission(Permission.preOrderEdit)
      btnPermission.submit = PermissionMgr.hasPermission(Permission.preOrderEdit)
    } else {
      if (PermissionMgr.hasPermission(Permission.requireApplyEdit)) {
        btnPermission.save = true
      }
      if (PermissionMgr.hasPermission(Permission.requireApplySubmit) && this.showSubmit) {
        btnPermission.submit = true
      }
    }

    return btnPermission
  }

  get gtyLowLimitlevel() {
    return this.storeInfo.gtyLowLimitlevel
  }

  get gtyHighLimitlevel() {
    return this.storeInfo.gtyHighLimitlevel
  }

  /**
   * 店务配置是否可以提交
   */
  get showSubmit() {
    let showSubmit: boolean = true
    if (this.storeInfo.orderModeRules && this.storeInfo.orderModeRules.length) {
      const rule = this.storeInfo.orderModeRules.find((rule) => {
        return Number(rule.orderMode) == Number(RequireApplyType.stringToMode(this.type))
      })
      if (rule) {
        showSubmit = rule.alwStoreSubmit
      } else {
        showSubmit = true
      }
    }
    return showSubmit
  }

  //隐藏库存数据
  get hideInvQty() {
    return PermissionMgr.hasPermission(Permission.requireApplyHideInvQty)
  }

  // 显示全部按钮的icon
  get collapseIcon() {
    return this.collapseStatus ? config.sourceUrl + 'icon/ic_down_chevron_grey.png' : config.sourceUrl + 'icon/ic_up_chevron_grey.png'
  }

  // 商品列表高度
  get cartHeight() {
    return `calc(100vh - 72rpx - ${this.cartHeaderHeight}px)`
  }

  // 显示全部按钮是否显示
  get showCollapse() {
    if (this.goodsCategoryList.length > 12) {
      return true
    } else {
      return false
    }
  }

  // 库存金限制余额，门店限制级别使用
  get balance() {
    return (this.storeInfo && this.storeInfo.invTotalLmtConfig && this.storeInfo.invTotalLmtConfig.balance) || 0
  }

  // 资金账号信息
  get dpsAccountInfo() {
    return (this.storeInfo && this.storeInfo.dpsAccountInfo) || new StoreDpsAccountInfo()
  }

  // 是否显示余额
  get showBalance() {
    return this.dpsAccountInfo && this.dpsAccountInfo.verifyDpsAccount
  }

  //可订货金额
  get allAccount() {
    let amount = 0

    amount =
      Number(amount) +
      Number(this.dpsAccountInfo!.cloudFundTotal) +
      Number(this.dpsAccountInfo!.creditTotal) +
      Number(this.dpsAccountInfo!.franchiseTotal) -
      Number(this.dpsAccountInfo!.usedTotal)

    return amount.scale(2)
  }

  //是否走吱口令

  get isByZiCommand() {
    const requireGoodsModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosRequireApply
        })
      : []
    //叫货启用云资金划扣时，如果云资金余额不足的处理方式，0-报错(def)，1-尽可能划扣，不足部分走吱口令
    if (requireGoodsModuleConfig.length > 0 && requireGoodsModuleConfig[0].options) {
      return requireGoodsModuleConfig[0].options.cloudFundBalNotEnoughProcess == '1'
    }
    return false
  }

  /**
   * 是否有爆品
   */
  get hasHotGoods() {
    return this.showGoodsList.some((item) => item.goods.goodsType == 4 && item.goods.allowActQpcQty)
  }

  //是否显示自提
  get showSelfPickUp() {
    const requireGoodsModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosRequireApply
        })
      : []
    if (requireGoodsModuleConfig.length > 0 && requireGoodsModuleConfig[0].options) {
      return requireGoodsModuleConfig[0].options.showSelfPickUp == '1'
    }
    return false
  }

  //是否提示数据未保存

  @Watch('isHasNotSaveDraft', { immediate: true, deep: true })
  onSaveChange(newVal, oldVal) {
    // #ifdef  MP-DINGTALK
    if (this.isHasNotSaveDraft && dd.canIUse('enableLeaveConfirm')) {
      dd.enableLeaveConfirm({
        effect: ['back', 'close'],
        info: {
          title: '提示',
          content: '当前订货数据未保存，确定退出吗'
        }
      })
    } else {
      dd.disableLeaveConfirm({
        effect: ['back', 'close'],
        info: {
          title: '提示',
          content: '当前订货数据未保存，确定退出吗'
        }
      })
    }
    // #endif

    // #ifdef  MP-WEIXIN
    if (this.isHasNotSaveDraft && !this.enableAutoSave) {
      wx.enableAlertBeforeUnload({
        message: '当前订货数据未保存，确定退出吗？'
      })
    } else {
      wx.disableAlertBeforeUnload({
        message: '当前订货数据未保存，确定退出吗？'
      })
    }
    // #endif
  }

  onLoad(option) {
    this.note = uni.getStorageSync('requireNote') || ''

    uni.getSystemInfo({
      success: (res) => {
        if (res && res.windowWidth && res.windowHeight) {
          this.clientWidth = res.windowWidth
          this.clientHeight = res.windowHeight
        }
      }
    })

    this.noPickAmt = Number(option.noPickAmt)
    this.billId = option.id
    this.isEdit = option.isEdit == 'true' ? true : false
    this.draftId = option.draftId
    this.groupPurchase = Number(option.groupPurchase || '0')
    this.selfPickUp = Number(option.selfPickUp || '0')
    this.type = option.type || this.type
    if (this.type === RequireApplyType.pre) {
      this.isPre = true
    }
    this.forecastArrivalDate = option.forecastArrivalDate ? decodeURIComponent(option.forecastArrivalDate) : this.forecastArrivalDate
    this.expireDate = option.expireDate ? decodeURIComponent(option.expireDate) : this.expireDate
    this.doQuerySort().then((resp) => {
      const queryRequest = new QueryRequest()
      this.doGetDraft(queryRequest)
    })

    // 获取商铺叫货规则
    this.getStoreInfo()
    this.getOption()
  }

  mounted() {
    setTimeout(() => {
      this.doGetDomInfo()
    }, 500)
  }

  onShow() {
    //查询支付结果
    if (this.isNeedGetPay) {
      this.doGetPayResult()
    }
  }

  /**
   * 团购
   * @param check 选中团购
   */
  doGroupPurchase(check: boolean) {
    this.groupPurchase = check ? 1 : 0
    // 开启自动保存配置
    if (this.enableAutoSave) {
      this.doDraftSave().then(() => {
        this.$hideLoading()
      })
    }
    uni.$emit('updateData', { groupPurchase: String(this.groupPurchase) })
  }

  /**
   * 选择是否自提
   * @param check 是否自提
   */
  doSelfPickUp(check: boolean) {
    this.selfPickUp = check ? 1 : 0
    // 开启自动保存配置
    if (this.enableAutoSave) {
      this.doDraftSave().then(() => {
        this.$hideLoading()
      })
    }
    uni.$emit('updateData', { selfPickUp: String(this.selfPickUp) })
  }

  // 商品类别点击事件
  doChange(item, index, force = false) {
    this.saveAll = true
    if (this.activeIndex == index && !force) {
      return
    }
    this.doDraftSave().then(() => {
      this.activeIndex = index
      this.selectedCategory = item
      if (!force || (this.totalPage == this.curpage && !this.showGoodsList.length)) {
        this.curpage = 1
      }
      this.showGoodsList = []
      this.pageNum = (this.curpage - 1) * 10 // 页码
      this.finished = false // 是否加载完成
      this.isLoading = true // 是否在加载

      this.$nextTick(async () => {
        await this.doQuerySort()
        const queryRequest = new QueryRequest()
        await this.doGetDraft(queryRequest)
      })
    })
  }

  // 显示更多商品
  loadMoreGoods() {
    if (this.finished || this.isLoading) {
      return
    }
    if (this.showGoodsList.length < this.eachPage) {
      this.hasMore = true
    } else if (this.showGoodsList.length === this.eachPage) {
      this.hasMore = false
      return
    }
    this.isLoading = true
    const queryRequest = new QueryRequest()

    this.doGetDraft(queryRequest, true)
  }

  // 左划删除事件
  doDeleteLine(sku: AppRequireApplyDraftLineQueryDTOExt) {
    this.doNumberChange(0, sku)
    if (sku.goods.firstCategory && sku.goods.firstCategory.name.startsWith(this.accessoryGoodsCategoryNamePrefix) && this.accessoryManage === 2) {
      this.doSave()
    }
  }

  //  清空购物车
  doClear() {
    if (this.goodsNum === 0) {
      return
    }
    this.$showModal({
      title: '提示',
      content: '确定清空所有商品吗？',
      showCancel: true,
      confirmText: '清空',
      success: (action) => {
        if (action.confirm) {
          this.$showLoading()
          const body = new DraftID()
          body.id = this.draftId
          RequireApplyApi.removeDraft(body)
            .then((res) => {
              if (this.isEdit) {
                this.actionHasNotSave(true)
              }
              this.$hideLoading()
              setTimeout(() => {
                uni.navigateBack({})
              }, 200)
            })
            .catch((e) => {
              this.$hideLoading()
              this.$showToast({ title: e.msg, icon: 'none' })
            })
        }
      }
    })
  }

  // 展开全部按钮点击事件
  doShowMore() {
    this.collapseStatus = !this.collapseStatus
    this.$nextTick(() => {
      this.doGetDomInfo()
    })
  }

  // 获取categoryList高度
  doGetDomInfo() {
    const query = uni.createSelectorQuery()
    query.select('#categoryList').boundingClientRect(() => {})
    query.exec((res) => {
      this.categoryListHeight = res[0] && res[0].height
    })
    query.select('#cartHeader').boundingClientRect(() => {})
    query.exec((res) => {
      this.cartHeaderHeight = res[1] && res[1].height
    })
    query.select('#goodsAction').boundingClientRect(() => {})
    query.exec((res) => {
      this.goodsActionHeight = res[2] && res[2].height
    })
  }

  onUpdateIndex(index) {
    this.moveIndex = index
  }

  // 查询商品分类
  doQuerySort() {
    this.$showLoading()
    return new Promise((resolve, reject) => {
      const body: AppRequireApplyTabCategoryFilterDTO = new AppRequireApplyTabCategoryFilterDTO()
      body.draftId = this.draftId
      RequireApplyApi.listCategory(body)
        .then((resp) => {
          const arr = resp.data || []

          this.goodsCategoryList = arr.filter((item) => {
            return item.recCnt
          })
          if (this.goodsCategoryList && this.goodsCategoryList.length > 0) {
            this.selectedCategory = this.goodsCategoryList[0]
            this.activeIndex = 0
            resolve(this.selectedCategory)
          } else {
            this.goodsQueryFinish = true
            if (this.storeFinish) {
              this.$hideLoading()
            }
            reject()
          }
        })
        .catch((err) => {
          this.$hideLoading()
          this.$showToast({ title: err.msg, icon: 'error' })
          reject()
        })
    })
  }

  /**
   * 获取草稿单商品种类和金额
   */

  doGetDraft(queryRequest, showLoading = true) {
    this.loadText = '加载中···'
    if (showLoading) {
      this.$showLoading()
    }
    if (this.selectedCategory.code) {
      if (this.selectedCategory.code == '未知') {
        this.selectedCategory.code = 'unknown'
      }
      queryRequest.conditions = [
        {
          operation: 'categoryCode:=',
          parameters: [this.selectedCategory.code]
        }
      ]
    }
    queryRequest.page = this.pageNum
    queryRequest.pageSize = this.pageSize

    queryRequest.conditions.push({ operation: 'draftId:=', parameters: [this.draftId] })
    queryRequest.conditions.push({ operation: 'alcDate:=', parameters: [this.alcDate] })
    queryRequest.fetchParts = ['category', 'reference', 'image']
    queryRequest.conditions.push({ operation: 'queryFrom:=', parameters: ['1'] })

    return new Promise((resolve, reject) => {
      RequireApplyApi.queryDraftLine(queryRequest)
        .then((res) => {
          this.goodsQueryFinish = true
          if (this.storeFinish) {
            this.$hideLoading()
          }
          this.isLoading = false
          if (res.data) {
            this.goodsPrice = (Number(res.data.total) + Number(res.data.accTotal)).scale(2)
            this.goodsNum = res.data.recCnt || 0
            if (this.accessoryManage === 2) {
              this.totalQpcQty = Number(res.data.qpcQty).add(Number(res.data.accQpcQty))
            } else {
              this.totalQpcQty = Number(res.data.qpcQty)
            }

            const lines = (res.data.lines || []) as AppRequireApplyDraftLineQueryDTOExt[]
            lines.forEach((item) => {
              if (
                item.goods.firstCategory &&
                item.goods.firstCategory.name.startsWith(this.accessoryGoodsCategoryNamePrefix) &&
                this.accessoryManage === 2
              ) {
                item.oldQty = item.qty
                item.oldQpcQty = item.qpcQty
                item.oldTotal = item.total
                item.qty = item.goodsAggQty || 0
                item.qpcQty = `${Number(item.qty).divide(item.goods.qpc)}`
                item.total = Number(item.qty).multiply(item.goods.price).divide(item.goods.qpc)
              }
            })
            if (this.pageNum === 0) {
              this.showGoodsList = [...lines]
            } else {
              this.showGoodsList.push(...lines)
            }
            this.total = res.total || 0
            this.pageNum++
            this.finished = !res.more
          }
          resolve(true)
        })

        .catch((e) => {
          this.goodsQueryFinish = true
          this.loadText = '加载失败'
          this.$hideLoading()
          this.$showToast({ title: e.msg, icon: 'none' })
          this.isLoading = false
          reject()
        })
    })
  }

  // 步进器数值改变触发事件
  async doNumberChange(qpcQty: number, goodsLine: AppRequireApplyDraftLineQueryDTOExt) {
    this.saveAll = false
    this.selectGoosLine = { ...goodsLine }
    if (this.isEdit) {
      this.isHasNotSave = true
    }
    if (
      qpcQty ||
      (goodsLine.goods.firstCategory &&
        goodsLine.goods.firstCategory.name.startsWith(this.accessoryGoodsCategoryNamePrefix) &&
        this.accessoryManage === 2)
    ) {
      this.isHasNotSaveDraft = true
      this.doModifyDraft(qpcQty, goodsLine)
      // 开启自动保存配置
      if (this.enableAutoSave) {
        const result: any = await this.doDraftSave()
          .then((resp) => {
            this.$hideLoading()
            return resp
          })
          .catch((error) => {
            return error
          })

        if (!result) {
          const index = this.showGoodsList.findIndex((item) => item.goods.uuid === goodsLine.goods.uuid)
          if (index !== -1) {
            this.$set(this.showGoodsList, index, goodsLine)
          }
        }
        return
      }
    } else {
      this.isHasNotSaveDraft = false
      this.doDraftRemove(goodsLine)
    }
  }

  /**
   * 确认删除草稿
   */
  doDraftRemove(sku: AppRequireApplyDraftLineQueryDTO) {
    if (!sku.lineUuid) {
      return
    }
    if (this.isDelete) {
      return
    }
    this.$showLoading()
    this.isDelete = true
    const removebody: illDraftLineKey = new illDraftLineKey()
    removebody.draftId = this.draftId
    removebody.lineUuid = sku.lineUuid
    let total: number = 0 //当前商品总价
    let qpcQty: number = 0 //当前商品总规格数
    let accTotal: number = 0 // 当前商品的辅料总金额
    RequireApplyApi.removeDraftLine(removebody)
      .then(() => {
        if (this.isEdit) {
          this.actionHasNotSave(true)
        }
        this.isDelete = false
        for (let index = 0; index < this.showGoodsList.length; index++) {
          if (sku.goods.uuid === this.showGoodsList[index].goods.uuid) {
            total = this.showGoodsList[index].total || 0
            accTotal = this.showGoodsList[index].accTotal || 0
            qpcQty = Number(qpcQty || this.showGoodsList[index].qpcQty)
            this.showGoodsList.splice(index, 1)
            break
          }
        }

        this.goodsPrice = (this.goodsPrice - total - accTotal).scale(2)
        this.goodsNum = Number(this.goodsNum.minus(1))
        if (this.accessoryManage === 2) {
          this.totalQpcQty = Number(this.totalQpcQty - Number(sku.qpcQty) - Number(sku.accQpcQty)).scale(0)
        } else {
          this.totalQpcQty = Number(this.totalQpcQty - Number(sku.qpcQty)).scale(0)
        }
        if (this.goodsNum == 0 || this.goodsNum < 0) {
          uni.navigateBack({})
          return
        }

        this.goodsCategoryList.forEach((e) => {
          if (e.uuid == sku.categoryUuid) {
            e.recCnt = (e.recCnt - 1).scale(0)
          }
        })
        this.goodsCategoryList = this.goodsCategoryList.filter((e) => {
          return e.recCnt
        })
        let currentIndex = this.activeIndex

        if (this.goodsCategoryList.length == this.activeIndex) {
          currentIndex = 0
        }

        this.doChange(this.goodsCategoryList[currentIndex], currentIndex, true)
      })
      .catch((e) => {
        this.$hideLoading()
        this.isDelete = false
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  /**
   * 计算商品数量和总价
   * @param qpcQty
   * @param sku
   * @returns
   */
  handleCalSkuByQpcQty(qpcQty: number, sku: AppRequireApplyDraftLineQueryDTOExt): AppRequireApplyDraftLineQueryDTOExt {
    // 修改商品数量
    sku.qpcQty = Number(qpcQty).toString()
    // 计算商品数量
    sku.qty = Number(qpcQty.multiply(Number(sku.goods.qpc))).scale(4)
    const allowActQpcQty = Number(sku.goods.allowActQpcQty || 0).floorScale(0)

    // 如果是爆品且有爆品活动价
    if (sku.goods.goodsType == 4 && allowActQpcQty) {
      const actPrice = sku.goods.actPrice || 0
      if (qpcQty > allowActQpcQty) {
        // 加购数量大于爆品活动限购数量时，多出的数量需要按照原价进行金额计算
        const diffQty = Number(sku.qpcQty).minus(Number(allowActQpcQty))
        const activeTotal = actPrice.multiply(Number(allowActQpcQty)).scale(2)
        const diffTotal = sku.goods.price.multiply(Number(diffQty)).scale(2)
        sku.total = activeTotal.add(diffTotal)
      } else {
        // 正常情况下按照爆品活动价进行价格计算
        sku.total = actPrice.multiply(Number(sku.qpcQty)).scale(2)
      }
    } else {
      // 计算商品总价
      sku.total = sku.goods.price.multiply(Number(sku.qpcQty)).scale(2)
    }

    let skuAccTotal: number = 0
    let skuAccQty: number = 0
    let skuAccQpcQty: number = 0

    // 计算辅料总价 - 爆品无需计算
    sku.accLines &&
      sku.accLines.forEach((line) => {
        const qpcQty: number = this.autoCalcAcc
          ? Number(sku.qty)
              .multiply(Number(line.accessoryRate))
              .divide(line.goodsRate || 1)
              .divide(line.qpc)
          : Number(line.qpcQty)
        const qty: number = this.autoCalcAcc
          ? Number(sku.qty)
              .multiply(Number(line.accessoryRate))
              .divide(line.goodsRate || 1)
          : qpcQty.multiply(line.qpc)

        if (this.autoCalcAcc) {
          if (line.roundType === RoundType.ceil) {
            line.qty = Math.ceil(qty)
            line.qpcQty = `${qpcQty.scale(4)}`
            line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
          } else {
            line.qty = qty.scale(4)
            line.qpcQty = `${qpcQty.scale(4)}`
            line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
          }
        } else {
          line.qty = qty.scale(4)
          line.qpcQty = `${qpcQty.scale(4)}`
          line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
        }
        skuAccTotal = Number(skuAccTotal).add(line.total)
        skuAccQty = Number(skuAccQty).add(line.qty)
        skuAccQpcQty = Number(skuAccQpcQty).add(Number(line.qpcQty))
      })
    sku.accTotal = Number(skuAccTotal).scale(2)
    sku.accQty = Number(skuAccQty).scale(4)
    sku.accQpcQty = skuAccQpcQty.scale(4)

    return sku
  }

  /**
   * 确认编辑
   */
  doModifyDraft(qpcQty: number, sku: AppRequireApplyDraftLineQueryDTOExt) {
    // 修改商品数量
    sku = this.handleCalSkuByQpcQty(qpcQty, sku)

    let total: number = 0 // 原商品总价
    let oldQpcQty: number = 0 //原商品总规格数
    let accTotal: number = 0 // 原商品的总辅料价格
    let accQpcQty: number = 0 // 原商品的总辅料数量

    // 遍历商品列表，找到当前商品，并更新商品信息
    for (let index = 0; index < this.showGoodsList.length; index++) {
      if (sku.goods.uuid === this.showGoodsList[index].goods.uuid) {
        total = total || this.showGoodsList[index].total
        oldQpcQty = oldQpcQty || Number(this.showGoodsList[index].qpcQty)
        accTotal = this.showGoodsList[index].accTotal || 0
        accQpcQty = accQpcQty || Number(this.showGoodsList[index].accQpcQty)

        this.showGoodsList[index].qty = sku.qty
        this.showGoodsList[index].qpcQty = String(sku.qpcQty)
        this.showGoodsList[index].total = sku.total
        this.showGoodsList[index].accLines = sku.accLines
        this.showGoodsList[index].accTotal = sku.accTotal
        this.showGoodsList[index].accQty = sku.accQty
        this.showGoodsList[index].accQpcQty = sku.accQpcQty

        break
      }
    }

    // 计算当前商品总价
    const currenTtotal = Number(sku.total).add(Number(sku.accTotal))
    // 计算总价格
    this.goodsPrice = Number(this.goodsPrice - total - accTotal + currenTtotal).scale(2)
    // 计算总规格数
    if (this.accessoryManage === 2) {
      this.totalQpcQty = Number(this.totalQpcQty - Number(oldQpcQty) - accQpcQty + Number(sku.accQpcQty) + Number(qpcQty)).scale(0)
    } else {
      this.totalQpcQty = Number(this.totalQpcQty - Number(oldQpcQty) + Number(qpcQty)).scale(0)
    }
  }

  /**
   * 确认保存草稿
   */
  doDraftSave(remove = false, isSaveDisable = false) {
    this.$showLoading()
    const body: AppRequireApplyDraftDTO = new AppRequireApplyDraftDTO()
    body.billId = this.billId
    body.draftId = this.draftId
    body.type = this.type
    body.orderDate = new Date()
    body.note = this.note
    body.groupPurchase = this.groupPurchase
    body.expireDate = this.expireDate
    body.selfPickUp = this.selfPickUp
    if (isSaveDisable) {
      body.lines = JSON.parse(JSON.stringify(this.disableList))
        .filter((item: AppRequireApplyDraftLineQueryDTOExt) => {
          return (
            Number(item.qpcQty) ||
            (item.goods.firstCategory &&
              item.goods.firstCategory.name.startsWith(this.accessoryGoodsCategoryNamePrefix) &&
              this.accessoryManage === 2)
          )
        })
        .map((item: AppRequireApplyDraftLineQueryDTOExt) => {
          let accTotal: number = 0
          let accQty: number = 0
          if (item.qty !== Number(Number(item.qpcQty).multiply(Number(item.goods.qpc))).scale(4)) {
            item = this.handleCalSkuByQpcQty(Number(item.qpcQty), item)
          }
          if (item.accLines) {
            item.accLines.forEach((line) => {
              accTotal = accTotal.add(line.total || 0)
              accQty = accQty.add(line.qty || 0)
            })
          }
          const subLines = this.getSubLines(item, isSaveDisable)
          // 服务端要求，如果是爆品且加购数量在爆品限购数量之内，goods.price字段需要取爆品活动价
          let price = item.goods.price
          let singlePrice = item.goods.singlePrice
          if (item.goodsType == 4 && item.goods.allowActQpcQty && Number(item.qpcQty) <= item.goods.allowActQpcQty) {
            price = item.goods.actPrice || 0
            singlePrice = price.divide(item.goods.qpc).scale(4)
          }
          if (
            item.goods.firstCategory &&
            item.goods.firstCategory.name.startsWith(this.accessoryGoodsCategoryNamePrefix) &&
            this.accessoryManage === 2
          ) {
            item.goodsAggQty = item.qty || 0
            item.qty = item.oldQty || 0
            item.qpcQty = item.oldQpcQty || ''
            item.total = item.oldTotal || 0
          }
          return {
            ...item,
            accTotal: accTotal.scale(2),
            accQty: accQty.scale(4),
            subLines,
            goods: { ...item.goods, price, singlePrice }
          }
        })

      if (!this.saveAll) {
        body.lines = body.lines.filter((line) => {
          return line.lineUuid === this.selectGoosLine.lineUuid
        })
      }
    } else {
      body.lines = JSON.parse(JSON.stringify(this.showGoodsList))
        .filter((item) => {
          return (
            Number(item.qpcQty) ||
            (item.goods.firstCategory &&
              item.goods.firstCategory.name.startsWith(this.accessoryGoodsCategoryNamePrefix) &&
              this.accessoryManage === 2)
          )
        })
        .map((item) => {
          if (item.qty !== Number(Number(item.qpcQty).multiply(Number(item.goods.qpc))).scale(4)) {
            item = this.handleCalSkuByQpcQty(Number(item.qpcQty), item)
          }
          let accTotal: number = 0
          let accQty: number = 0

          if (item.accLines) {
            item.accLines.forEach((line) => {
              accTotal = accTotal.add(line.total || 0)
              accQty = accQty.add(line.qty || 0)
            })
          }
          const subLines = this.getSubLines(item)

          // 服务端要求，如果是爆品且加购数量在爆品限购数量之内，goods.price字段需要取爆品活动价
          let price = item.goods.price
          let singlePrice = item.goods.singlePrice
          if (item.goodsType == 4 && item.goods.allowActQpcQty && Number(item.qpcQty) <= item.goods.allowActQpcQty) {
            price = item.goods.actPrice || 0
            singlePrice = price.divide(item.goods.qpc).scale(4)
          }
          if (
            item.goods.firstCategory &&
            item.goods.firstCategory.name.startsWith(this.accessoryGoodsCategoryNamePrefix) &&
            this.accessoryManage === 2
          ) {
            item.goodsAggQty = item.qty || 0
            item.qty = item.oldQty || 0
            item.qpcQty = item.oldQpcQty || ''
            item.total = item.oldTotal || 0
          }

          return {
            ...item,
            accTotal: accTotal.scale(2),
            accQty: accQty.scale(4),
            subLines,
            goods: { ...item.goods, price, singlePrice }
          }
        })

      if (!this.saveAll) {
        body.lines = body.lines.filter((line) => {
          return line.lineUuid === this.selectGoosLine.lineUuid
        })
      }
    }

    return new Promise((resolve, reject) => {
      body.saveFrom = 1
      RequireApplyApi.saveDraft(body)
        .then((resp) => {
          if (this.isHasNotSave) {
            this.actionHasNotSave(true)
          }
          if (remove) {
            this.showGoodsList = []
          } else {
            ;(this.showGoodsList as AppRequireApplyDraftLineQueryDTOExt[]).forEach((item) => {
              const idx = resp.data.lines.findIndex((line) => {
                return line.gdUuid === item.goods.uuid && line.gdInputCode === item.goods.inputCode
              })
              if (idx > -1 && item.goods.inputCode === resp.data.lines[idx].gdInputCode && item.goods.uuid === resp.data.lines[idx].gdUuid) {
                item.lineUuid = resp.data.lines[idx].lineUuid || ''
                item.oldQty = resp.data.lines[idx].qty ? resp.data.lines[idx].qty : item.oldQty // 保存后需重新赋值
                item.oldQpcQty = `${Number(item.oldQty).divide(item.goods.qpc)}`
                item.oldTotal = Number(item.oldQty).multiply(item.goods.price).divide(item.goods.qpc)
              }
            })
          }
          this.isHasNotSaveDraft = false
          resolve(true)
        })
        .catch((e) => {
          this.$hideLoading()
          this.$showToast({ icon: 'error', title: e.msg })
          reject(false)
        })
    })
  }

  /**
   * 保存商品行时构造子明细
   * 只有存在爆品和普通商品的时候才需要构造，如果都是爆品或者都是普通商品则不用构造
   */
  getSubLines(skuItem: AppRequireApplyDraftLineQueryDTO, isSaveDisable = false) {
    const data: AppRequireApplyDraftLineQueryDTO = JSON.parse(JSON.stringify(skuItem))
    const allowActQpcQty = Number(data.goods.allowActQpcQty || 0).floorScale(0)

    const subLines: AppRequireApplySubBaseLineDTO[] = []
    if (data.goodsType == 4 && allowActQpcQty) {
      const lineInfo = new AppRequireApplySubBaseLineDTO()
      lineInfo.goodsType = 4
      lineInfo.note = data.note
      lineInfo.price = data.goods.actPrice || 0
      lineInfo.qpcQty = data.qpcQty
      lineInfo.qty = data.qty
      lineInfo.singlePrice = lineInfo.price.divide(data.goods.qpc).scale(4)
      lineInfo.total = data.total

      const qpcQty = Number(data.qpcQty)
      const actPrice = data.goods.actPrice || 0
      if (qpcQty > allowActQpcQty) {
        // 加购数量大于爆品活动限购数量时，多出的数量需要按照原价进行金额计算
        const diffQty = Number(data.qpcQty).minus(Number(allowActQpcQty))
        const activeTotal = actPrice.multiply(Number(allowActQpcQty)).scale(2)
        const diffTotal = data.goods.price.multiply(Number(diffQty)).scale(2)
        // data.total = activeTotal.add(diffTotal)
        subLines.push({
          ...lineInfo,
          qpcQty: String(allowActQpcQty),
          qty: allowActQpcQty.multiply(data.goods.qpc).scale(4),
          total: activeTotal
        })
        subLines.push({
          ...lineInfo,
          lineNo: 1,
          goodsType: 0,
          price: data.goods.price,
          singlePrice: data.goods.singlePrice,
          qpcQty: String(diffQty),
          qty: diffQty.multiply(data.goods.qpc),
          total: diffTotal
        })
      }
    }
    return subLines
  }

  // 显示分页器需要展示的页
  doShow(curpage: number) {
    this.showBottom = true

    const oldCurpage = this.curpage
    const oldPageNum = this.pageNum

    this.curpage = curpage
    this.pageNum = (curpage - 1) * 10
    this.isLoading = true
    this.doDraftSave().then(
      () => {
        const queryRequest = new QueryRequest()
        const goodsExtList = JSON.parse(JSON.stringify(this.showGoodsList))
        this.showGoodsList = []
        this.doGetDraft(queryRequest).then(
          () => {
            this.hasMore = true
            this.$nextTick(() => {
              uni.pageScrollTo({
                scrollTop: 0,
                duration: 300
              })
            })
          },
          () => {
            this.showGoodsList = goodsExtList

            this.isLoading = false
            this.$nextTick(() => {
              this.curpage = oldCurpage
            })
            this.pageNum = oldPageNum
          }
        )

        this.$showToast({ icon: 'none', title: '当前页面为第' + this.curpage + '页数据' })
      },
      () => {
        this.isLoading = false
        this.$nextTick(() => {
          this.curpage = oldCurpage
        })
        this.pageNum = oldPageNum
      }
    )
  }

  // 分页器隐藏时触发
  doHide(state: boolean) {
    const oldCurpage = this.curpage
    this.curpage = -1

    this.$nextTick(() => {
      this.curpage = oldCurpage
    })
    this.showBottom = state
  }

  // 获取门店叫货配置
  getOption() {
    this.$showLoading({ delayTime: 200 })
    RequireApplyApi.getOption()
      .then((resp) => {
        if (this.storeFinish && this.goodsQueryFinish) {
          this.$hideLoading()
        }

        this.strictControl = resp.data && resp.data.strictControl
        this.validateLevel4CaseQty = (resp.data && resp.data.validateLevel4CaseQty) || 0
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })

    // 获取商铺叫货规则
    this.$showLoading({ delayTime: 200 })
    RequireOrderApi.getStoreInfo()
      .then((resp) => {
        if (this.storeFinish && this.goodsQueryFinish) {
          this.$hideLoading()
        }
        this.showGdImage = resp.data && resp.data.option && resp.data.option.displayGdImage
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  // 获取商铺叫货规则
  getStoreInfo() {
    this.$showLoading({ delayTime: 200 })
    RequireApplyApi.getStoreInfo()
      .then((resp) => {
        this.storeFinish = true
        if (this.goodsQueryFinish) {
          this.$hideLoading()
        }
        this.actionStoreInfo(resp.data)
      })
      .catch((e) => {
        this.storeFinish = true
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: e.msg })
      })
  }

  // 显示备注弹出框
  doNoteShow() {
    this.$refs.note.open()
  }
  // 关闭备注弹出框
  doNoteClose() {
    this.$refs.note.close()
  }
  // 备注弹框确认事件
  doNoteConfirm(note) {
    this.note = note
    uni.setStorage({
      key: 'requireNote',
      data: note,
      success: () => {}
    })
  }

  //关闭可订数不足弹窗和商品上架备注弹窗
  doInventoryClose() {
    this.$refs.Inventory.close()
  }

  //可订数不足弹窗重新提交
  doInventoryConfirm() {
    this.doSubmit(false)
  }

  /**
   * 删除已选商品
   * @param uuid 商品uuid
   * @param validCode  是否下架
   */
  doDeleteDisableLine(line) {
    if (!line.validCode) {
      this.doDraftRemove(line)
    }

    for (let index = 0; index < this.showList.length; index++) {
      if (this.showList[index].goods.uuid === line.goods.uuid) {
        this.showList.splice(index, 1)
        break
      }
    }
  }

  // 组装请求体
  doAssembleBody() {
    const body = new RequireApplyCreationByDraft()
    body.billId = this.billId
    body.draftId = this.draftId
    body.note = this.note
    body.orderDate = new Date()
    body.expireDate = this.expireDate
    body.type = this.type
    body.groupPurchase = this.groupPurchase
    body.selfPickUp = this.selfPickUp
    return body
  }

  // 校验请求体
  validate() {
    if (!this.goodsNum) {
      return false
    }
    return true
  }

  // 提交按钮事件
  doSubmit(isSave = true) {
    this.oldGoodsNum = this.goodsNum
    this.oldGoodsPrice = this.goodsPrice
    if (this.validate()) {
      if (isSave) {
        this.doDraftSave().then(() => {
          this.doBalanceValidate()
          // 辅料逻辑与普通商品不一致，需要重新查询
          if (
            this.accessoryManage === 2 &&
            this.selectedCategory.name &&
            this.selectedCategory.name.startsWith(this.accessoryGoodsCategoryNamePrefix)
          ) {
            this.pageNum = 0
            this.doGetDraft(new QueryRequest())
          }
        })
      } else {
        this.doBalanceValidate()
      }
    } else {
      this.$showToast({ icon: 'none', title: '请填写商品数量～' })
    }
  }

  //库存金额校验
  doBalanceValidate() {
    this.doSubmitValidate()
  }

  //提交前验证
  doSubmitValidate() {
    // 如果当前订货类型在H6配置了订货规则，就不对店务配置的规则进行校验
    if (this.currentRule || this.type === RequireApplyType.advance) {
      this.$hideLoading()
      this.$showModal({
        title: this.noPickAmt > 0 ? '提交确认' : '提示',
        content: this.noPickAmt > 0 ? `未提货完成的预订货订单将与本次报货一起发货` : `确定提交订货申请吗`,
        showCancel: true,
        confirmText: '提交',
        cancelText: '取消',
        success: (action) => {
          if (action.confirm) {
            this.submit()
          }
        }
      })
    } else {
      this.$showLoading()
      RequireApplyApi.getPeriod()
        .then((resp) => {
          this.$hideLoading()
          const period = resp.data
          if (period) {
            const now = new Date()
            const startTime = new Date(period.startTime.replace(/-/g, '/'))
            const endTime = new Date(period.endTime.replace(/-/g, '/'))
            if (now.getTime() < startTime.getTime() || now.getTime() > endTime.getTime()) {
              if (this.strictControl) {
                this.$hideLoading()
                this.$showModal({
                  title: '提交失败',
                  content: `当前时间不在叫货时间范围内，不可叫货`,
                  showCancel: true,
                  confirmText: '保存后返回',
                  cancelText: '直接返回',
                  success: (action) => {
                    if (action.confirm) {
                      this.save(true)
                    } else {
                      uni.navigateBack({ delta: 2 })
                    }
                  }
                })
              } else {
                this.$hideLoading()
                this.$showModal({
                  title: '非正常订货时间',
                  content: '将生成新单，是否继续提交此单',
                  showCancel: true,
                  confirmText: '继续提交',
                  success: (action) => {
                    if (action.confirm) {
                      this.submit()
                    }
                  }
                })
              }
            } else {
              this.$showModal({
                title: '提示',
                content: `确定提交订货申请吗`,
                showCancel: true,
                confirmText: '提交',
                cancelText: '取消',
                success: (action) => {
                  if (action.confirm) {
                    this.submit()
                  }
                }
              })
            }
          } else {
            this.$showModal({
              title: '提示',
              content: `确定提交订货申请吗`,
              showCancel: true,
              confirmText: '提交',
              cancelText: '取消',
              success: (action) => {
                if (action.confirm) {
                  this.submit()
                }
              }
            })
          }
        })
        .catch((e) => {
          this.$hideLoading()
          this.$showToast({ icon: 'error', title: e.msg })
        })
    }
  }

  // 保存按钮事件
  doSave() {
    this.saveAll = true
    if (this.validate()) {
      this.doDraftSave().then(() => {
        // 辅料逻辑与普通商品不一致，需要重新查询
        if (
          this.accessoryManage === 2 &&
          this.selectedCategory.name &&
          this.selectedCategory.name.startsWith(this.accessoryGoodsCategoryNamePrefix)
        ) {
          this.pageNum = 0
          this.doGetDraft(new QueryRequest())
        }
        this.save()
      })
    } else {
      this.$showToast({ icon: 'none', title: '请填写商品数量～' })
    }
  }

  save(backTo = false) {
    const request: any = this.isEdit ? RequireApplyApi.modifyByDraft : RequireApplyApi.createByDraft
    this.$showLoading()
    const body = this.doAssembleBody()
    request(body)
      .then((resp) => {
        this.isHasNotSave = false
        this.actionHasNotSave(false)
        if (!this.isEdit) {
          uni.$emit('cartSaveRequire')
          this.isEdit = true
        }
        this.$hideLoading()
        this.$showToast({ icon: 'success', title: '保存成功' })
        if (backTo) {
          setTimeout(() => {
            uni.navigateBack({ delta: 2 })
          }, 300)
        }
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showModal({
          title: '保存失败',
          content: `失败原因:${e.msg}`,
          showCancel: true,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.save()
            }
          }
        })
      })
  }

  // 当日正常叫货提交单据数大于等于当日正常叫货单据提交提示阈值
  async doNormalSubmitThreshold() {
    try {
      const body = this.doAssembleBody()
      const resp = await RequireApplyApi.validateBeforeSubmit(body)
      if (resp.data && resp.data.code == '1') {
        this.$showModal({
          title: '提示',
          content: resp.data.message || '',
          showCancel: true,
          confirmText: '确定',
          cancelText: '取消',
          success: (action) => {
            if (action.confirm) {
              this.doFinalSubmit()
            }
          }
        })
      } else {
        this.doFinalSubmit()
      }
    } catch (error) {
      this.doFinalSubmit()
    }
  }

  // 提交按钮事件
  submit() {
    this.doNormalSubmitThreshold()
  }

  // 提交按钮事件
  doFinalSubmit() {
    const request: any = this.isEdit ? RequireApplyApi.modifyAndSubmitByDraft : RequireApplyApi.createAndSubmitByDraft
    this.$showLoading()
    const body = this.doAssembleBody()
    request(body)
      .then((res) => {
        this.$hideLoading()
        if (res.data && res.data.code == 5001) {
          this.$showLoading()
          this.disableList = []
          this.deleteList = []
          const arr = res.data.details || []
          const inputCodeArr = arr.map((e) => {
            return e.gdInputCode
          })

          const queryRequest = new QueryRequest()
          queryRequest.page = 0
          queryRequest.pageSize = -1
          queryRequest.conditions = [{ operation: 'gdInputCode:in', parameters: inputCodeArr }]
          queryRequest.fetchParts = ['category', 'reference', 'image']
          queryRequest.conditions.push({ operation: 'draftId:=', parameters: [this.draftId] })
          queryRequest.conditions.push({ operation: 'alcDate:=', parameters: [this.alcDate] })
          queryRequest.conditions.push({ operation: 'queryFrom:=', parameters: ['1'] })

          RequireApplyApi.queryDraftLine(queryRequest)
            .then((draft) => {
              if (draft.data) {
                this.goodsPrice = (Number(draft.data.total) + Number(draft.data.accTotal)).scale(2)
                this.goodsNum = draft.data.recCnt || 0
                if (this.accessoryManage === 2) {
                  this.totalQpcQty = Number(draft.data.qpcQty).add(Number(draft.data.accQpcQty))
                } else {
                  this.totalQpcQty = Number(draft.data.qpcQty)
                }
              }
              const deleteArr: any[] = []
              if (draft.data && draft.data.lines && draft.data.lines.length) {
                res.data.details.forEach((item) => {
                  for (let index = 0; index < draft.data!.lines.length; index++) {
                    const draftLine = draft.data!.lines[index]

                    if (draftLine.goods.uuid === item.gdUuid) {
                      //更新商品信息
                      const oldQpcQty = Number(draftLine.qpcQty)

                      // e.goods.currentQpcQty = e.qpcQty || 0

                      // e.goods.srcPrice = item.srcPrice

                      //validCode校验结果代码：0-数量超限，1-无效商品，2价格变动
                      if (item.validCode != '1') {
                        if (item.validCode == '0') {
                          draftLine.goods.allowQty = item.allowQty
                          draftLine.goods.isLimit = item.isLimit

                          draftLine.qty = Number(item.allowQty)
                          if (!draftLine.qty) {
                            if (Number(draftLine.qpcQty)) {
                              this.goodsNum = Number(this.goodsNum.add(-1))
                              this.goodsCategoryList.forEach((item) => {
                                if (item.uuid == draftLine.categoryUuid) {
                                  item.recCnt = (item.recCnt - 1).scale(0)
                                }
                              })
                              this.goodsCategoryList = this.goodsCategoryList.filter((e) => {
                                return e.recCnt
                              })
                            }
                          }

                          draftLine.qpcQty = `${(Number(draftLine.qty) / draftLine.goods.qpc).floorScale(4) || 0}`
                        }
                        // 先判断商品是否有辅料行，如果由辅料行则不计算爆品价
                        const hasAccLines = draftLine.accLines && draftLine.accLines.length
                        // 爆品创建明细行的时候，需要取price字段，这里price字段被污染了，需要一个字段留作备用
                        // draftLine.goods.price = item.price
                        draftLine.goods.tempPrice = hasAccLines ? item.price : this.getDisLinePrice(draftLine, item)
                        draftLine.goods.qpc = item.qpc
                        // e.validCode = item.validCode

                        let oldTotal = draftLine.goods.tempPrice.multiply(oldQpcQty).scale(2)
                        if (draftLine.goodsType == 4 && draftLine.goods.allowActQpcQty) {
                          oldTotal = draftLine.total
                        }
                        const oldAccTotal: number = draftLine.accTotal || 0 // 原来的辅料合计
                        const oldAccQpcQty: number = draftLine.accQpcQty || 0 // 原来的辅料数量合计

                        draftLine.total = draftLine.goods.tempPrice.multiply(Number(draftLine.qpcQty)).scale(2)

                        // 更新数量之后
                        draftLine.accLines &&
                          draftLine.accLines.forEach((line) => {
                            const qpcQty: number = this.autoCalcAcc
                              ? Number(draftLine.qty)
                                  .multiply(Number(line.accessoryRate))
                                  .divide(line.goodsRate || 1)
                                  .divide(line.qpc)
                              : Number(line.qpcQty)
                            const qty: number = this.autoCalcAcc
                              ? Number(draftLine.qty)
                                  .multiply(Number(line.accessoryRate))
                                  .divide(line.goodsRate || 1)
                              : qpcQty.multiply(line.qpc)

                            if (this.autoCalcAcc) {
                              if (line.roundType === RoundType.ceil) {
                                line.qty = Math.ceil(qty)
                                line.qpcQty = `${qpcQty.scale(4)}`
                                line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
                              } else {
                                line.qpcQty = `${qpcQty.scale(4)}`
                                line.qty = qty.scale(4)
                                line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
                              }
                            } else {
                              line.qty = qty.scale(4)
                              line.qpcQty = `${qpcQty.scale(4)}`
                              line.total = line.qty.multiply(line.price).divide(line.qpc).scale(2)
                            }
                            draftLine.accTotal = Number(draftLine.accTotal).add(line.total)
                            draftLine.accQty = Number(draftLine.accQty).add(line.qty)
                            draftLine.accQpcQty = Number(draftLine.accQpcQty).add(Number(line.qpcQty))
                          })
                        draftLine.accTotal = Number(draftLine.accTotal).scale(2)
                        draftLine.accQty = Number(draftLine.accQty).scale(4)
                        draftLine.accQpcQty = Number(draftLine.accQpcQty).scale(4)

                        //重算总金额总数量
                        this.goodsPrice = Number(this.goodsPrice - oldTotal - oldAccTotal + draftLine.accTotal + draftLine.total).scale(2)

                        if (this.accessoryManage === 2) {
                          this.totalQpcQty = Number(
                            this.totalQpcQty - Number(oldQpcQty).scale(0) + Number(draftLine.qpcQty).scale(0) - oldAccQpcQty + draftLine.accQpcQty
                          ).scale(0)
                        } else {
                          this.totalQpcQty = Number(this.totalQpcQty - Number(oldQpcQty).scale(0) + Number(draftLine.qpcQty).scale(0)).scale(0)
                        }

                        this.disableList.push(draftLine)
                        if (!draftLine.qty) {
                          deleteArr.push(draftLine)
                        }
                      } else {
                        this.goodsPrice = (this.goodsPrice - draftLine.total - Number(draftLine.accTotal)).scale(2)
                        if (this.accessoryManage === 2) {
                          this.totalQpcQty = Number(this.totalQpcQty - Number(draftLine.qpcQty) - Number(draftLine.accQpcQty)).scale(0)
                        } else {
                          this.totalQpcQty = Number(this.totalQpcQty - Number(draftLine.qpcQty)).scale(0)
                        }
                        if (Number(draftLine.qpcQty)) {
                          this.goodsNum = Number(this.goodsNum.add(-1))
                          this.goodsCategoryList.forEach((item) => {
                            if (item.uuid == draftLine.categoryUuid) {
                              item.recCnt = (item.recCnt - 1).scale(0)
                            }
                          })
                          this.goodsCategoryList = this.goodsCategoryList.filter((e) => {
                            return e.recCnt
                          })
                        }

                        draftLine.qty = 0
                        draftLine.qpcQty = '0'
                        draftLine.total = 0

                        this.deleteList.push(draftLine)
                      }

                      break
                    }
                  }
                })
              } else {
                this.$hideLoading()
              }

              this.showList = this.deleteList.concat(this.disableList)
              let saveLoading = true
              let deleteLoading = true
              if (this.deleteList.length || deleteArr.length) {
                deleteLoading = false
              }

              if (this.disableList.length) {
                saveLoading = false
                this.doDraftSave(false, true).then(
                  () => {
                    saveLoading = true
                    if (deleteLoading) {
                      this.$hideLoading()
                    }
                  },
                  () => {
                    saveLoading = true
                  }
                )
                this.disableList.forEach((disableLine) => {
                  for (let index = 0; index < this.showGoodsList.length; index++) {
                    if (disableLine.goods.uuid === this.showGoodsList[index].goods.uuid && Number(disableLine.qpcQty)) {
                      const price = disableLine.goods.price
                      this.showGoodsList[index].goods = JSON.parse(JSON.stringify({ ...disableLine.goods, price }))
                      this.showGoodsList[index].qty = disableLine.qty
                      this.showGoodsList[index].qpcQty = disableLine.qpcQty
                      this.showGoodsList[index].total = disableLine.total
                      this.showGoodsList[index].accLines = disableLine.accLines
                      this.showGoodsList[index].accTotal = disableLine.accTotal
                      this.showGoodsList[index].accQty = disableLine.accQty

                      break
                    }
                  }
                })
              }

              if (this.deleteList.length || deleteArr.length) {
                const body = new AppRequireApplyDraftLineKeys()
                body.draftId = this.draftId
                const removeLines = deleteArr.concat(this.deleteList)
                body.lineUuids = removeLines.map((e) => {
                  return e.lineUuid
                })
                this.$showLoading()
                this.isDelete = true //防止触发商品行删除
                RequireApplyApi.removeDraftLines(body)
                  .then((resp) => {
                    deleteLoading = true
                    removeLines.forEach((sku) => {
                      for (let index = 0; index < this.showGoodsList.length; index++) {
                        if (sku.goods.uuid === this.showGoodsList[index].goods.uuid) {
                          this.showGoodsList.splice(index, 1)
                          break
                        }
                      }
                    })
                    this.isDelete = false
                    let currentIndex = this.activeIndex
                    if (this.goodsCategoryList.length == 0) {
                      uni.navigateBack({})
                    }
                    if (this.goodsCategoryList.length == this.activeIndex) {
                      currentIndex = 0
                    }
                    //更新无效商品
                    this.doChange(this.goodsCategoryList[currentIndex], currentIndex, true)
                  })
                  .catch((e) => {
                    this.isDelete = false
                    deleteLoading = true
                    this.$hideLoading()
                    this.$showToast({ icon: 'error', title: e.msg })
                  })
              }
              this.$refs.Inventory.open()
            })
            .catch((e) => {
              this.$hideLoading()
              this.$showToast({ icon: 'error', title: e.msg })
            })
        } else if (res.data && res.data.code == 5002) {
          this.$showLoading()
          RequireApplyApi.get(this.billId)
            .then((resp) => {
              if (resp.data) {
                const data = resp.data
                this.paymentSrcContext = JSON.parse(data.paymentSrcContext) || {}
                this.$hideLoading()
                this.$refs.perparepay.open()
              } else {
                this.$hideLoading()
              }
            })
            .catch((e) => {
              this.$hideLoading()
              this.$showToast({ icon: 'error', title: e.msg })
            })
        } else if (res.data && res.data.code == 5004) {
          this.$hideLoading()
          this.$showModal({
            title: '提示',
            content: `${res.data.message}`,
            showCancel: true,
            confirmText: '重新提交',
            success: (action) => {
              if (action.confirm) {
                this.doRefreshSubmit()
              } else {
                uni.$emit('cartSubmitRequire', res.data.billId || '')
                uni.navigateBack({})
              }
            }
          })
        } else if (res.data && res.data.code == 5000) {
          this.$hideLoading()
          this.$showModal({
            title: '提交失败',
            content: `失败原因:${res.data.message}`,
            showCancel: false,
            confirmText: '我知道了',
            success: (action) => {
              if (action.confirm) {
                this.getStoreInfo()
              }
            }
          })
        } else {
          this.isHasNotSave = false
          this.actionHasNotSave(false)
          this.$hideLoading()
          uni.removeStorage({
            key: 'requireBodyNote',
            success: function (res) {},
            fail: function (res) {}
          })
          this.$showToast({ icon: 'success', title: '提交成功' })
          uni.$emit('cartSubmitRequire', res.data.billId || '')
          setTimeout(() => {
            this.$Router.back(1)
          }, 1000)
        }
      })
      .catch((e) => {
        this.$hideLoading()
        this.$showModal({
          title: '提交失败',
          content: `失败原因:${e.msg}`,
          showCancel: true,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.submit()
            }
          }
        })
      })
  }

  /**
   * 获取库存不足弹窗商品的显示价格
   * 存在爆品活动的时候，需要取爆品活动价，超出爆品加购数量时取平均数
   */
  getDisLinePrice(lines: AppRequireApplyDraftLineQueryDTO, item: RequireApplySubmitInvalidDtl) {
    // 获取最新的活动数量和活动价格
    // 商品数量 - 现在的问题时，活动可加购数量只有活动数量变化的时候才会有值
    // 所以allowActQty无值,则说明数量没有变化,取草稿行里面的可加购活动数量
    const actQty = Number(Number(item.allowActQty).divide(item.qpc) || lines.goods.allowActQpcQty || 0).floorScale(0)
    // 如果不是活动商品价格变动,actPrice是没有值的,这个时候取srcPrice - 应对情况,爆品可加购数量变化
    const actPrice = item.actPrice || item.srcPrice || 0
    // 商品本身的加购数量和价格
    const qpcQty = Number(lines.qpcQty) || 1
    const price = item.price || 0
    // 如果没有活动加购数量，直接返回商品原价即可
    if (!actQty) {
      return item.price
    }

    // 剩下的情况默认有活动可加购数量
    let total = 0
    if (qpcQty > actQty) {
      // 加购数量大于爆品活动限购数量时，多出的数量需要按照原价进行金额计算
      const diffQty = Number(qpcQty).minus(Number(actQty))
      const activeTotal = actPrice.multiply(Number(actQty)).scale(2)
      const diffTotal = price.multiply(Number(diffQty)).scale(2)
      total = activeTotal.add(diffTotal)
    } else {
      // total = actPrice.multiply(Number(qpcQty)).scale(2)
      return actPrice
    }
    return total.divide(qpcQty).scale(4)
  }

  // 返回确认
  onBackPress() {
    const modalText = '当前订货数据未保存，确定退出吗？'

    if (this.isHasNotSaveDraft && !this.enableAutoSave) {
      this.$showModal({
        title: '提示',
        content: modalText,
        showCancel: true,
        confirmText: '确定',
        cancelText: '取消',
        success: (action) => {
          if (action.confirm) {
            this.isHasNotSaveDraft = false
            uni.navigateBack({})
          }
        }
      })

      return true
    } else {
      return false
    }
  }

  //关闭支付订单弹窗
  doPerparePayClose() {
    this.isNeedGetPay = false
    this.$refs.perparepay.close()
    //跳转详情
    uni.$emit('cartSubmitRequire', this.billId)
    uni.navigateBack({})
  }

  //支付订单弹窗复制
  doPerparePayConfirm() {
    this.isNeedGetPay = true
  }

  //查询吱口令支付结果

  doGetPayResult() {
    clearInterval(this.timer)
    this.doRefreshBill(true)
    this.timer = setInterval(() => {
      this.doRefreshBill()
    }, 3000)
  }

  //更新单据

  doRefreshBill(showLoading: boolean = false) {
    showLoading && this.$showLoading()
    RequireApplyApi.get(this.billId)
      .then((resp) => {
        this.$hideLoading()
        if (resp.data) {
          this.paymentSrcContext = JSON.parse(resp.data.paymentSrcContext) || {}
          if (
            resp.data.state !== RequireApplyState.initial ||
            (resp.data.paymentState !== RequireApplyPaymentState.initial && resp.data.paymentState !== RequireApplyPaymentState.failed)
          ) {
            this.isNeedGetPay = false
            clearInterval(this.timer)
            this.$showToast({ icon: 'success', title: '支付成功，订单已提交' })
            setTimeout(() => {
              this.doPerparePayClose()
            }, 1000)
          } else if (this.paymentSrcContext.ziCommandState === 'expired') {
            clearInterval(this.timer)
            this.$refs.perparepay.close()

            this.$showModal({
              title: '支付超时',
              content: '订单支付超时，原吱口令已失效，请点击继续支付，复制新的吱口令完成支付',
              showCancel: true,
              confirmText: '继续支付',
              cancelText: '取消',
              success: (action) => {
                if (action.confirm) {
                  //更新吱口令
                  this.doRefreshZiCommand()
                } else {
                  //跳转详情
                  uni.$emit('cartSubmitRequire', this.billId)
                  uni.navigateBack({})
                }
              }
            })
          }
        } else {
          this.isNeedGetPay = false
          this.$refs.perparepay.close()
          clearInterval(this.timer)
          this.$showToast({ icon: 'success', title: '支付成功，订单已提交' })
          setTimeout(() => {
            uni.$emit('cartSubmitRequire')
            uni.navigateBack({})
          }, 1000)
        }
      })
      .catch((e) => {
        this.$hideLoading()
      })
  }

  //刷新吱口令
  doRefreshZiCommand() {
    this.$showLoading()

    RequireApplyApi.regenZiCommand({ id: this.billId })
      .then((res) => {
        this.paymentSrcContext.zicommand = JSON.parse(res.data || '') || {}
        this.$hideLoading()
        this.$refs.perparepay.open()
      })
      .catch((error) => {
        this.$hideLoading()
        console.log(error)
        this.$showModal({
          title: '提示',
          content: `${error.msg}`,
          showCancel: false,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.doRefreshZiCommand()
            }
          }
        })
      })
  }

  // 重新提交按钮事件
  async doRefreshSubmit() {
    this.$showLoading()

    RequireApplyApi.retrySubmit({ id: this.billId })
      .then((res) => {
        if (res.data && res.data.success) {
          this.$hideLoading()
          this.$showToast({ icon: 'success', title: '提交成功' })
          setTimeout(() => {
            uni.$emit('cartSubmitRequire', res.data.billId || '')
            uni.navigateBack({})
          }, 1500)
        } else {
          if (res.data && res.data.code == 5002) {
            RequireApplyApi.get(this.billId)
              .then((resp) => {
                if (resp.data) {
                  const data = resp.data

                  this.paymentSrcContext = JSON.parse(data.paymentSrcContext) || {}
                  this.$hideLoading()
                  this.$refs.perparepay.open()
                } else {
                  this.$hideLoading()
                }
              })
              .catch((e) => {
                this.$hideLoading()
                this.$showToast({ icon: 'error', title: e.msg })
              })
          } else if (res.data && res.data.code == 5004) {
            this.$hideLoading()
            this.$showModal({
              title: '提示',
              content: `${res.data.message}`,
              showCancel: true,
              confirmText: '重新提交',
              success: (action) => {
                if (action.confirm) {
                  this.doRefreshSubmit()
                } else {
                  uni.$emit('cartSubmitRequire', res.data.billId || '')
                  uni.navigateBack({})
                }
              }
            })
          } else {
            this.$hideLoading()
            this.$showModal({
              title: '提交失败',
              content: `${res.data.message}`,
              showCancel: false,
              confirmText: '重试',
              success: (action) => {
                if (action.confirm) {
                  this.doRefreshSubmit()
                }
              }
            })
          }
        }
      })
      .catch((error) => {
        this.$hideLoading()
        console.log(error)
        this.$showModal({
          title: '提交失败',
          content: `${error.msg}`,
          showCancel: false,
          confirmText: '重试',
          success: (action) => {
            if (action.confirm) {
              this.doRefreshSubmit()
            }
          }
        })
      })
  }

  onUnload() {
    clearTimeout(this.timer)
    this.timer = null
  }

  //判断是否快到截止时间
  doCloseOutside() {
    if (this.queueObj && this.queueObj.closeOutside) {
      this.queueObj.closeOutside()
    }
  }
}
