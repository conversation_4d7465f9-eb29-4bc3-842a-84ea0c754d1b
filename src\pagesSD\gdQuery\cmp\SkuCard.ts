import config from '@/config'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import PermissionMgr from '@/mgr/PermissionMgr'
import { ModuleId } from '@/model/common/OptionListModuleId'
import ModuleOption from '@/model/default/ModuleOption'
import GoodsQueryInfo from '@/model/gdQuery/GoodsQueryInfo'
import SysConfigItem from '@/model/sysConfig/SysConfigItem'
import { FieldsConfig } from '@/model/user/FieldsConfig'
import { Permission } from '@/model/user/Permission'
import { debounce } from 'ts-debounce'
import { Vue, Component, Prop } from 'vue-property-decorator'
import { State } from 'vuex-class'
import AppGdQueryApi from '@/network/gdQuery/AppGdQueryApi'

// eslint-disable-next-line @typescript-eslint/ban-ts-ignore
// @ts-ignore
@Component({
  components: {},
  options: {
    virtualHost: true
  }
})
export default class SkuCard extends Vue {
  @State('sysConfig') sysConfig: SysConfigItem // 获取全局配置
  @State('optionList') optionList: ModuleOption[] //店务配置列表
  @Prop({ type: Object, default: () => new GoodsQueryInfo() }) sku: GoodsQueryInfo // 商品信息
  @Prop({ type: Boolean, default: true }) isShowOperate: boolean // 是否展示库存调整按钮
  @Prop({ type: Boolean, default: true }) isShowExhibit: boolean // 是否展示陈列位置调整按钮
  $refs: any
  debounceClick = debounce(this.handleClick, 500, { isImmediate: true })
  isExpand: boolean = false // 标签是否展开

  /**
   * 是否展示陈列位置按钮
   */
  get isShowExhibitBtn() {
    return PermissionMgr.hasPermission(Permission.displayLocationAdjust)
  }

  /**
   * 是否展示加入打印列表按钮
   */
  get isShowPrintListAddBtn() {
    return PermissionMgr.hasPermission(Permission.printListAdd)
  }

  /**
   * 是否展示立即打印按钮
   */
  get isShowPrintListImmediatelyBtn() {
    return PermissionMgr.hasPermission(Permission.printListImmediately)
  }

  /**
   * 是否展示价签申请按钮
   */
  get isShowPriceTagRequestBtn() {
    return PermissionMgr.hasPermission(Permission.priceTagRequest)
  }

  /**
   * 是否展示价签打印按钮
   */
  get isShowPriceTagPrintingBtn() {
    return PermissionMgr.hasPermission(Permission.priceTagPrinting)
  }

  // 立即打印、加入打印列表、价签申请、价签打印”4个按钮权限 拥有几个权限
  get isHasPermissionLength() {
    let length: number = 0
    if (this.isShowPrintListAddBtn) {
      length += 1
    }
    if (this.isShowPrintListImmediatelyBtn) {
      length += 1
    }
    if (this.isShowPriceTagRequestBtn) {
      length += 1
    }
    if (this.isShowPriceTagPrintingBtn) {
      length += 1
    }
    return length
  }

  // 立即打印、加入打印列表、价签申请、价签打印”4个按钮权限  权限按钮名称
  get isHasPermissioBtnName() {
    let name: string = ''
    if (this.isHasPermissionLength === 1) {
      if (this.isShowPrintListAddBtn) {
        name = '加入打印列表'
      }
      if (this.isShowPrintListImmediatelyBtn) {
        name = '加入打印列表'
      }
      if (this.isShowPriceTagRequestBtn) {
        name = '加入打印列表'
      }
      if (this.isShowPriceTagPrintingBtn) {
        name = '加入打印列表'
      }
    } else {
      name = '打印'
    }
    return name
  }

  //获取字段展示权限
  get showMaster() {
    const masterPermission = {
      showDisplayLocation: false, // 陈列位置
      showTransitInv: false, //在途库存
      showUnshippedInv: false, // 未发货库存
      showSalePrice: false, // 销售价
      showSkuWeekSale: false, // 周销量
      showSkuMonthSale: false, // 月销量
      showSkuSpec: false, // 箱规
      showListSkuQualification: false, // 资质图片
      showListSkuExplain: false, // 商品说明
      showSellableInvQty: false, // 可售库存
      showSkuCategory: false, // 商品分类
      showInvPrice: false, // 库存成本价
      showLowInv: false, // 库存下限
      showHighInv: false // 库存上限
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDISPLAYLOCATION)) {
      masterPermission.showDisplayLocation = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWTRANSITINV)) {
      masterPermission.showTransitInv = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWUNSHIPPEDINV)) {
      masterPermission.showUnshippedInv = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSALEPRICE)) {
      masterPermission.showSalePrice = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSKUWEEKSALE)) {
      masterPermission.showSkuWeekSale = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSKUMONTHSALE)) {
      masterPermission.showSkuMonthSale = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSKUSPEC)) {
      masterPermission.showSkuSpec = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWLISTSKUQUALIFICATION)) {
      masterPermission.showListSkuQualification = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWLISTSKUEXPLAIN)) {
      masterPermission.showListSkuExplain = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSELLABLEINVQTY)) {
      masterPermission.showSellableInvQty = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWSKUCATEGORY)) {
      masterPermission.showSkuCategory = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWINVPRICE)) {
      masterPermission.showInvPrice = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWLOWINV)) {
      masterPermission.showLowInv = true
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWHIGHINV)) {
      masterPermission.showHighInv = true
    }
    return masterPermission
  }

  /**
   * 是否展示单品数
   */
  get invQtyDisplayStrategy() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGdQuery
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.invQtyDisplayStrategy) {
      return moduleConfig[0].options.invQtyDisplayStrategy === '1'
    }
    return false
  }

  /**
   * 是否允许一品多货位
   */
  get allowOneGoodsMultipleSlot() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.allowOneGoodsMultipleSlot) {
      return moduleConfig[0].options.allowOneGoodsMultipleSlot === 'true'
    }
    return false
  }

  /**
   * 数据来源：0，H6，不显示陈列位置相关；1，鼎力云，显示
   */
  get slotSource() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.slotSource) {
      return moduleConfig[0].options.slotSource === '1'
    }
    return false
  }

  // 商品图片
  get img() {
    return this.sku && this.sku.images && this.sku.images.length
      ? `${this.sku.images[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
      : `${config.sourceUrl}icon/pic_goods.png`
  }

  // 品类
  get category() {
    if (this.sku && this.sku.sorts && this.sku.sorts.length > 0) {
      return this.sku.sorts
        .reduce((prev, curr) => {
          return prev + curr.name + '>'
        }, '')
        .slice(0, -1)
    }
    return '--'
  }

  get imageList() {
    return this.sku && this.sku && this.sku.images && this.sku.images.length ? this.sku.images : [`${config.sourceUrl}icon/pic_goods.png`]
  }

  // 是否新品
  get isNewList() {
    return this.sku ? this.sku.isNewList : false
  }

  // 商品名+含量
  get friendlyStr() {
    return this.sku && this.sku.friendlyStr ? `${this.sku.friendlyStr}` : null
  }

  // 商品代码
  get code() {
    return this.sku && this.sku.code ? `${this.sku.code}` : null
  }

  // 商品条码
  get inputCode() {
    return this.sku && this.sku.inputCode ? `${this.sku.inputCode}` : null
  }

  // 是否散货
  get isDisp() {
    return this.sku && this.sku.isDisp ? this.sku.isDisp : false
  }

  // 推介类型
  get recommandType() {
    return this.sku && this.sku.recommandType ? `${this.sku.recommandType}` : null
  }

  /**
   * 箱规
   */
  get bulkPackDesc() {
    return this.sku && this.sku.bulkPackDesc ? `${this.sku.bulkPackDesc}` : null
  }

  /**
   * 是否隐藏库存调整按钮
   * 报损或报溢的查看权限以及报损或报溢的编辑或提交权限
   */
  get hideOperateInvQty() {
    return (
      (PermissionMgr.hasPermission(Permission.lossApplyView) && PermissionMgr.hasPermission(Permission.lossApplySubmit)) ||
      (PermissionMgr.hasPermission(Permission.overApplyView) && PermissionMgr.hasPermission(Permission.overApplySubmit))
    )
  }

  /**
   * 是否隐藏成本价格
   */
  get hidePrice() {
    return PermissionMgr.hasPermission(Permission.globalPriceView)
  }

  /**
   * 是否有多个陈列位置
   */
  get hasMutiple() {
    return (item) => {
      return item && item.split(',').length > 1
    }
  }

  handlePreviewImg() {
    uni.previewImage({
      current: String(0),
      urls: this.imageList
    })
  }

  /**
   * 预览资质图片
   * @param imgs
   */
  handlePreviewQuaImg(imgs: string[]) {
    uni.previewImage({
      current: String(0),
      urls: imgs
    })
  }

  handleClick() {
    this.$emit('detail', this.sku)
  }

  handleShowDesc() {
    this.$emit('showDesc', this.sku.description)
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit() {
    this.$emit('viewExhibit', this.sku)
  }

  /**
   * 陈列位置调整
   */
  resetExhibit() {
    this.$emit('resetExhibit', this.sku)
  }

  /**
   * 调整库存
   */
  operateInvQty() {
    this.$emit('operateInvQty', this.sku)
  }

  /**
   * 标签展开
   */
  toggleExpand() {
    this.isExpand = !this.isExpand
  }

  // 加入打印列表
  doAddSkuIntoPrintList() {
    this.$showLoading()
    AppGdQueryApi.addPrintList({ gdUuid: this.sku.uuid })
      .then(() => {
        this.$hideLoading()
        this.$showToast({ icon: 'success', title: '加入成功' })
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast({ icon: 'error', title: error.msg })
      })
  }
}
