<!--
 * @Author: we<PERSON><PERSON>
 * @Date: 2023-07-10 15:00:44
 * @LastEditTime: 2025-05-12 10:42:13
 * @LastEditors: weisheng
 * @Description: 
 * @FilePath: /soa/src/pagesUtil/receiptRecord/ReceiptBoxLineEdit.vue
 * 记得注释
-->
<template>
  <view class="receipt-box-edit" @click="doCloseOutside">
    <hd-modal></hd-modal>
    <hd-toast></hd-toast>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <hd-video-preview id="hd-video-preview"></hd-video-preview>
    <!-- #ifdef MP-WEIXIN -->
    <hd-privacy-popup id="privacy-popup"></hd-privacy-popup>
    <!-- #endif -->

    <box-split-edit-reason-dialog ref="reason" @confirm="handleSkuChange"></box-split-edit-reason-dialog>
    <sku-record-dialog ref="recordDialog"></sku-record-dialog>

    <view class="header">
      <view class="header-title">
        <view class="header-num">
          <image :src="'/static/icon/ic_odd_numbers.png' | oss" class="header-num-img"></image>
          <text class="header-num-txt">箱码：{{ box.boxNo }}</text>
        </view>
        <hd-search-bar placeholder="商品名称/代码/条码" :allowActive="false" :value="keyWord" @clear="doClear" @search="doSearch"></hd-search-bar>
      </view>
    </view>
    <view class="main" v-if="showList.length">
      <view class="main-body">
        <box-edit-split-card
          v-for="(line, index) in showList"
          :openUserName="box.consigneeName"
          :confirmed="box.confirmed"
          :key="index"
          :sku="line"
          :index="index + 1"
          @change="handleSkuChange"
          @edit-reason="handleEditReason"
          @mark-confirmed="handleMarkConfirmed"
          @getRequestBody="getRequestBody"
          @viewExhibit="viewExhibit"
          @detail="handleViewDetail"
          @bindExhibit="(info) => bindExhibit(info, index)"
          @resetExhibit="(info) => resetExhibit(info, index)"
        ></box-edit-split-card>
      </view>
    </view>
    <hd-empty v-else :img="'/static/img/img_empty_goods.png' | oss" title="搜索不到相关内容 ヽ(・×・ )"></hd-empty>

    <view class="footer">
      <view class="footer-total">
        <text class="count">
          共
          <text class="count-num">{{ count }}</text>
          种
        </text>
      </view>
      <view class="footer-operation">
        <hd-button type="white" v-if="!box.confirmed" @click="doBack">晚点再收</hd-button>
        <hd-button type="primary" @click="doConfirm">确认</hd-button>
      </view>
    </view>

    <!-- 选择陈列位置 -->
    <select-exhibit ref="exhibit" @addExhibit="addExhibit" @success="confirmExhibit"></select-exhibit>

    <!-- 陈列位置调整 -->
    <reset-exhibit ref="resetExhibit" @addExhibit="addExhibit" @success="confirmExhibit"></reset-exhibit>

    <!-- 查看多个陈列位置 -->
    <uni-popup ref="viewExhibit" type="bottom">
      <view-exhibit :name="viewExhibitInfo.goods.name" :displayLocation="viewExhibitInfo.displayLocation" @doClose="closeViewExhibit"></view-exhibit>
    </uni-popup>
  </view>
</template>

<script lang="ts" src="./ReceiptBoxLineEdit.ts"></script>

<style lang="scss" scoped>
.receipt-box-edit {
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  box-sizing: border-box;
  background: #f5f6f7;
  padding-bottom: 184rpx !important;
  padding-bottom: calc(184rpx + constant(safe-area-inset-bottom)) !important;
  padding-bottom: calc(184rpx + env(safe-area-inset-bottom)) !important;

  .header {
    position: sticky;
    z-index: 10;
    width: 100%;
    box-sizing: border-box;
    top: 0;
    left: 0;
    background: #ffffff;

    &-tip {
      width: 100%;
      padding: 20rpx 24rpx;
      box-sizing: border-box;
      background: rgba(253, 155, 28, 0.21);
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26rpx;
      color: #fd9b1c;
      line-height: 36rpx;
    }

    &-title {
      width: 100%;
      padding: 24rpx;
      box-sizing: border-box;
    }

    &-num {
      display: flex;
      align-items: center;
      width: 100%;
      height: 44rpx;
      align-items: center;
      font-size: 32rpx;
      color: $color-text-secondary;
      margin-bottom: 24rpx;

      &-img {
        width: 44rpx;
        height: 44rpx;
      }

      &-txt {
        margin-left: 4rpx;
      }
    }
    ::v-deep .hd-search-bar {
      padding: 0;
    }
  }

  .main {
    margin: 24rpx auto;
    border-radius: 16rpx;
    box-sizing: border-box;
    padding: 0 24rpx;

    &-body {
      &-select {
        animation: scaleAndRestore 0.8s ease-in-out 1; /* 这里2s是动画持续时间，可以根据需要调整 */
      }
    }
  }

  .empty {
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    flex-direction: column;

    .empty-icon {
      margin-top: 118rpx;
      width: 300rpx;
      height: 300rpx;
    }
    .empty-txt {
      margin-top: 10rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(148, 150, 154, 1);
      line-height: 40rpx;
    }
  }

  .footer {
    position: fixed;
    z-index: 10;
    display: flex;
    flex-direction: column;
    width: 100%;
    bottom: 0;
    left: 0;
    background: $color-bg-primary;
    padding-bottom: 0 !important;
    padding-bottom: constant(safe-area-inset-bottom) !important;
    padding-bottom: env(safe-area-inset-bottom) !important;

    &-total {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 88rpx;
      box-sizing: border-box;
      padding: 0 24rpx;
      background-color: #fff7e0;
      font-size: 24rpx;
      color: #94969a;
      text {
        display: inline-flex;
        align-items: center;
        height: 88rpx;
      }
      .count {
        color: #94969a;
        font-size: 24rpx;
        margin-right: 16rpx;
      }
      .count-num {
        color: #585a5e;
        margin: 0 8rpx;
        font-size: 24rpx;
        font-family: HelveticaNeue-Bold, HelveticaNeue;
        font-weight: bold;
      }
      .total-flag {
        color: #585a5e;
        font-size: 24rpx;
        font-family: HelveticaNeue-Bold, HelveticaNeue;
        font-weight: bold;
      }
      .total-num {
        font-size: 32rpx;
        font-family: HelveticaNeue-Bold, HelveticaNeue;
        font-weight: bold;
        color: #585a5e;
      }
    }

    &-operation {
      display: flex;
    }
  }
}
</style>
