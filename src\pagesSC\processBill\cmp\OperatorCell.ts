/*
 * @LastEditors: hanwei
 */
import { Vue, Component, Prop } from 'vue-property-decorator'
import { ProcessGoodsState } from '@/model/processBill/ProcessGoodsState'
import { Filter } from '@/pagesSC/processBill/cmd/Filter'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'

@Component({
  components: {},
  filters: Filter
})
export default class OperatorCell extends Vue {
  @Prop({ default: ProcessGoodsState.material }) type: ProcessGoodsState.material // 标题
  @Prop({ type: Boolean, default: true }) clickable: boolean // 是否可以点击
  @Prop({ type: Number, default: 0 }) total: number // 总金额
  @Prop({ type: Number, default: 0 }) count: number // 种类数

  // 价格描述
  get describe() {
    if (this.type === ProcessGoodsState.material) {
      return '成本金额：'
    } else if (this.type === ProcessGoodsState.product) {
      return '零售金额：'
    }
  }

  // 展示金额
  get showPrice() {
    if (this.type === ProcessGoodsState.material) {
      return !PermissionMgr.hasPermission(Permission.globalPriceView)
    } else {
      return true
    }
  }

  // 标题
  get title() {
    return ProcessGoodsState.string(this.type)
  }

  // 新增按钮点击事件
  doAdd() {
    this.$emit('add')
  }

  // 搜索按钮点击事件
  doSearch() {
    this.$emit('search')
  }
}
