/*
 * @Author: 刘湘
 * @Date: 2022-02-09 16:47:56
 * @LastEditTime: 2024-01-30 16:38:38
 * @LastEditors: 程成
 * @Description:
 * @FilePath: \soa\src\model\invXFApply\InvXFApply.ts
 * 记得注释
 */
import InvXFApplyLine from 'model/invXFApply/InvXFApplyLine'
import { InvXFApplyState } from 'model/invXFApply/InvXFApplyState'
import { InvXFApplyType } from 'model/invXFApply/InvXFApplyType'
import InvXFApplyPackLine from './InvXFApplyPackLine'

export default class InvXFApply {
  // 单据标识
  billId: string = ''
  // 单号
  num: string = ''
  // 发起方数据标识
  starterId: string = ''
  // 发起方名称
  starterName: string = ''
  // 调拨类型，取值范围：increase-调入，decrease-调出
  type: Nullable<InvXFApplyType> = null
  // 状态,取值范围：initial-未提交，submitted-已提交，rejected-已拒绝,approved-已批准
  state: Nullable<InvXFApplyState> = null
  // 门店标识,调拨类型为调入时，它为调入门店；调拨类型为调出时，它为调出门店
  shopId: string = ''
  // 门店代码
  shopNo: string = ''
  // 门店名称
  shopName: string = ''
  // 相对方门店标识,调拨类型为调入时，它为调出门店；调拨类型为调出时，它为调入门店
  counterShopId: string = ''
  // 相对方门店代码
  counterShopNo: string = ''
  // 相对方门店名称
  counterShopName: string = ''
  // 品项数
  recCnt: number = 0
  // 申请总数
  qty: number = 0
  // 申请总规格数
  qpcQty: number = 0
  // 申请总额
  total: number = 0
  // 批准总数
  approvalQty: Nullable<number> = null
  // 批准总额
  approvalTotal: Nullable<number> = null
  // 批准总规格数
  approvalQpcQty: Nullable<number> = null
  // 创建时间
  created: Date = new Date()
  // 创建人代码
  creatorId: Nullable<string> = null
  // 创建人名称
  creatorName: Nullable<string> = null
  // 提交时间
  submitTime: Nullable<Date> = null
  // 提交人代码
  submitterId: Nullable<string> = null
  // 提交人名称
  submitterName: Nullable<string> = null
  // 最后修改时间
  lastModified: Date = new Date()
  // 最后修改人代码
  lastModifierId: Nullable<string> = null
  // 最后修改人名称
  lastModifierName: Nullable<string> = null
  // 审核时间
  auditTime: Nullable<Date> = null
  // 审核人代码
  auditorId: Nullable<string> = null
  // 审核人名称
  auditorName: Nullable<string> = null
  // 拒绝原因
  rejectReason: Nullable<string> = null
  // 备注
  note: Nullable<string> = null
  // 配送模式
  deliveryType: Nullable<number> = null
  // 调拨模式
  invXfMode: Nullable<number> = null
  // 零售金额
  rtlTotal: Nullable<number> = null
  // 批准零售金额
  approvalRtlTotal: Nullable<number> = null
  // 来源应用
  appId: Nullable<string> = null
  // 包材金额
  packTotal: Nullable<number> = null
  // 批准包材金额
  approvalPackTotal: Nullable<number> = null
  // 合计金额
  aggTotal: Nullable<number> = null
  // 合计零售金额
  aggRtlTotal: Nullable<number> = null
  // 合计批准金额
  aggApprovalTotal: Nullable<number> = null
  // 合计批准零售金额
  aggApprovalRtlTotal: Nullable<number> = null
  // 商品明细
  lines: InvXFApplyLine[] = []
  // 包材明细
  packLines: InvXFApplyPackLine[] = []
}
