<template>
  <view class="goods-list-container">
    <view class="list-title" :class="{ 'title-sticky': isSticky }">
      <view class="name">品名/代码/条码/规格</view>
      <view class="qty">登记数量</view>
    </view>
    <view class="divide"></view>
    <view class="list" v-for="(line, index) in lines" :key="index" :class="{ 'list-none-border': !showMore && index == lines.length - 1 }">
      <view class="sku-goods">
        <view class="sku-left">
          <image lazy-load class="sku-img" :src="skuImg(line)" @click.stop="handlePreviewImg(line)" />
          <view class="info__scale">
            <image :src="'/static/icon/img_enlarge2.png' | oss" class="info__scale-img"></image>
          </view>
        </view>
        <view class="sku-name">{{ line.goods.name | empty }}</view>
      </view>
      <view class="sku-code">
        <view class="info-tag">{{ line.goods.inputCode | empty }}</view>
        <view class="info-tag">{{ line.goods.code | empty }}</view>
        <view class="info-tag">{{ line.goods.qpcStr | empty }}</view>
      </view>
      <view class="sku-info">
        <view class="qty">
          <text class="num">{{ line.qpcQty | empty }}</text>
          {{ line.goods.munit | empty }}
        </view>
      </view>
      <view class="sku-note" v-if="line.note">
        <text class="sku-note-label">备注：</text>
        {{ line.note }}
      </view>
    </view>
    <view class="view-more" v-if="showMore">
      <text class="click-btn" @click="viewMore">查看更多</text>
      <text class="arrow"></text>
    </view>
  </view>
</template>
<script lang="ts" src="./SpecGdRegistGoodsList.ts"></script>
<style lang="scss" scoped>
.goods-list-container {
  width: 750rpx;
  padding: 0 $base-padding;
  background: #ffffff;
  box-sizing: border-box;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  .list-title {
    position: relative;
    width: 100%;
    height: 76rpx;
    display: flex;
    box-sizing: border-box;
    align-items: center;
    justify-content: space-between;
    font-size: 26rpx;
    color: $font-color-darklight;
    background: #ffffff;
    .name {
      flex: 2;
    }
    .qty {
      flex: 1.5;
      text-align: right;
    }
  }
  .divide {
    left: 0;
    width: 750rpx;
    position: absolute;
    top: 76rpx;
    border-bottom: $list-border-bottom;
  }
  .title-sticky {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    left: 0;
  }

  .list {
    padding: $base-padding 0;
    border-bottom: $list-border-bottom;
    .sku-goods {
      display: flex;
      margin-bottom: 12rpx;
    }

    .sku-left {
      position: relative;
      width: 120rpx;
      height: 120rpx;
      flex: 0 0 auto;
      margin-right: 16rpx;

      .sku-img {
        width: 120rpx;
        height: 120rpx;
      }
      .info__scale {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 24rpx;
        height: 24rpx;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 8rpx 0px 8rpx 0rpx;
        text-align: center;
        @include flex();
        &-img {
          width: 16rpx;
          height: 16rpx;
        }
      }
    }
    .sku-name {
      height: 100%;
      line-height: 44rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: $color-text-primary;
      font-family: PingFangSC-Medium, PingFang SC;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .sku-code {
      margin-top: 8rpx;
      line-height: 40rpx;
      font-size: 30rpx;

      .info-tag {
        display: inline-block;
        margin-right: 10rpx;
        font-size: 26rpx;
        color: $font-color-darklight;
      }
    }

    .sku-info {
      line-height: 40rpx;
      display: flex;
      margin-top: 10rpx;
      justify-content: flex-end;
      color: #585a5e;
      font-size: 26rpx;
      .code-name {
        flex: 2;
        max-width: 319rpx;
        @include ellipsis();
        color: #585a5e;
      }
      .qty {
        box-sizing: border-box;
        padding-right: 24rpx;
        flex: 1.5;
        text-align: right;
      }
      .num {
        font-size: 32rpx;
        font-weight: bold;
      }
    }

    .sku-note {
      margin-top: 24rpx;
      width: 100%;
      padding: 16rpx 16rpx;
      background: #f5f6f7;
      border-radius: 8rpx;
      box-sizing: border-box;
      line-height: 32rpx;
      color: #333333;
      font-size: 24rpx;
      &-label {
        color: #999999;
      }
    }
  }
  .list-none-border {
    border: none;
  }
  .view-more {
    width: 100%;
    height: 88rpx;
    font-size: 32rpx;
    font-weight: 400;
    color: rgba(88, 90, 94, 1);
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    .arrow {
      width: 14rpx;
      height: 14rpx;
      display: inline-block;
      border-top: 1px solid #94969a;
      border-right: 1px solid #94969a;
      transform: rotate(45deg);
      margin-left: 20rpx;
    }
  }
}
</style>
