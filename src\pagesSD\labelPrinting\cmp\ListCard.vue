<!--
 * @Author: yuzhipi
 * @Date: 2025-03-28 14:03:33
 * @LastEditTime: 2025-04-22 15:02:58
 * @LastEditors: hanwei
 * @Description: 
 * @FilePath: /soa/src/pagesSD/labelPrinting/cmp/ListCard.vue
 * 记得注释
-->
<template>
  <view class="card">
    <view class="common-flex">
      <image :src="'/static/icon/ic_goods.png' | oss" class="card-header-icon"></image>
      <view class="card-title">{{ good.name | empty }}</view>
    </view>
    <view class="common-flex card-text">
      <view class="field-box">
        <text class="normal-color normal-font">条码：</text>
        <text class="normal-color normal-font">{{ good.inputCode | empty }}</text>
      </view>
      <view class="field-box">
        <text class="normal-color normal-font">代码：</text>
        <text class="normal-color normal-font">{{ good.code | empty }}</text>
      </view>
    </view>
    <view class="common-flex card-text">
      <view class="field-box">
        <text class="normal-color normal-font">售价：</text>
        <text class="normal-color normal-font">¥ {{ good.rtlPrc | empty }}/{{ good.munit | empty }}</text>
      </view>
      <view class="field-box">
        <text class="normal-color normal-font">保质期：</text>
        <text class="special-color normal-font">{{ good.validPeriod || 0 }}天</text>
      </view>
    </view>
    <!-- <view class="common-flex card-text">
      <view class="field-box">
        <text class="normal-color normal-font">规格：</text>
        <text class="normal-color normal-font">{{ good.qpcStr | empty }}{{ good.munit | empty }}</text>
      </view>
      <view class="field-box">
        <text class="normal-color normal-font">自定义价格：</text>
        <text class="special-color normal-font">-</text>
        <image class="normal-img" :src="'/static/icon/ic_edit_2x.png' | oss" />
      </view>
    </view> -->
    <view class="common-flex card-text" v-if="otherSorts.memberPriceFilter === 2">
      <view class="field-box">
        <text class="normal-color normal-font">会员价：</text>
        <text class="normal-color normal-font">¥ {{ good.mbrPrc | empty }}/{{ good.munit | empty }}</text>
      </view>
    </view>

    <!-- 修改打印数量输入框 -->
    <view class="template-info">
      <view class="common-template" @click="selectTemplate">
        <view class="template-name">{{ templateName }}</view>
        <image class="image_32" :src="'/static/icon/ic_chevronright_line_grey.png' | oss"></image>
      </view>
      <!-- 修改数量变化事件 -->
      <hd-number-box-test v-model="printNum" @change="doNumberChange" :max="99999999" :min="1" :scale="0"></hd-number-box-test>
    </view>
    <view class="card-main" v-if="validateNotNull">
      <view class="card-main-date normal-box">
        <view class="card-main-date-text normal-box normal-font normal-color">
          <image :src="'/static/icon/ic_date.png' | oss" class="date-icon image_32"></image>
          <text>效期：</text>
        </view>
        <view :class="['card-main-date-start normal-box normal-font', isHasPrdDate ? 'special-color' : 'normal-color']" @click="change('prd')">
          <text v-if="!isHasPrdDate">请选择生产日期</text>
          <text v-else>{{ good.mfgDate }}</text>
        </view>
        <view class="card-main-date-and normal-box">
          <view class="card-main-date-and-content"></view>
        </view>
        <view :class="['card-main-date-end normal-box normal-font', isHasValidDate ? 'special-color' : 'normal-color']" @click="change('valid')">
          <text v-if="!isHasValidDate">请选择到效日期</text>
          <text v-else>{{ good.expDate }}</text>
        </view>
      </view>
    </view>
    <view class="print">
      <hd-button
        shape="circle"
        type="primary"
        size="mini"
        width="152rpx"
        :disabled="!isHasPrdDate || !isHasValidDate || !printNum"
        @click="handlePrint"
      >
        打印预览
      </hd-button>
    </view>
  </view>
</template>

<script lang="ts" src="./ListCard.ts"></script>

<style lang="scss" scoped>
.card {
  margin: 24rpx;
  width: calc(100% - 48rpx);
  position: relative;
  border-bottom: 1px solid #e1e1e1;
  font-family: PingFangSC, PingFang SC;
  &-main {
    border-radius: 8rpx;
    background: #f8f8f8;
    width: 100%;
    margin-bottom: 24rpx;
    &-date {
      padding: 16rpx 20rpx;
      &-text {
        .date-icon {
          margin-right: 8rpx;
        }
      }
      &-and {
        margin: 0 48rpx;
        &-content {
          width: 16rpx;
          height: 2rpx;
          background: #666666;
        }
      }
    }
    &-line {
      height: 1rpx;
      background: #eeeeee;
    }
    &-print {
      padding: 16rpx 20rpx 24rpx;
      @include flex(row, space-between, center);
      &-text {
        margin-right: 16rpx;
        height: 48rpx;
        @include flex(row, flex-start, center);
        font-weight: 400;
        font-size: 26rpx;
        color: #666666;
        font-style: normal;
      }
      &-total {
        height: 48rpx;
        @include flex(row, flex-start, center);
        &-unit {
          font-weight: 500;
          font-size: 28rpx;
          font-style: normal;
          display: inline-block;
          margin-left: 12rpx;
        }
      }
    }
  }

  .print {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 24rpx;
  }

  .normal-box {
    height: 40rpx;
    @include flex(row, flex-start, center);
  }

  .common-flex {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 12rpx;
  }

  .card-header-icon {
    width: 42rpx;
    height: 42rpx;
    margin-right: 4rpx;
  }
  .card-title {
    width: calc(100% - 46rpx);
    height: 42rpx;
    font-size: 30rpx;
    font-weight: 500;
    color: #1a1a1a;
    line-height: 42rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .field-box {
    width: 50%;
    height: 32rpx;
    line-height: 32rpx;
    display: flex;
    align-items: center;
  }

  .template-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 8rpx 0 20rpx 0;
    color: #333333;
    .common-template {
      display: flex;
      align-items: center;
    }
  }

  .template-name {
    max-width: 400rpx;
    flex: 0 0 auto;
    height: 40rpx;
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    line-height: 40rpx;
    font-style: normal;
    margin-right: 4rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .card-text {
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
  }

  .normal-color {
    color: #94969a;
  }

  .special-color {
    color: #333333;
  }

  .normal-img {
    width: 40rpx;
    height: 40rpx;
    margin-left: 4rpx;
  }

  .normal-font {
    font-weight: 400;
    font-size: 24rpx;
    font-style: normal;
  }
}
</style>
