import ReturnApplyReasonAttach from './ReturnApplyReasonAttach'
import ReturnApplyReasonBatch from './ReturnApplyReasonBatch'

export default class ReturnApplyBaseReasonLine {
  // 申请数量
  qty: number = 0
  // 申请金额
  total: number = 0
  // 申请规格数
  qpcQty: number = 0
  // 审批数量
  approvalQty: Nullable<number> = null
  // 审批金额
  approvalTotal: Nullable<number> = null
  // 备注
  note: Nullable<string> = null
  // 退货原因代码
  reasonCode: string = ''
  // 退货原因名称
  reasonName: string = ''
  // 通知限量(为空表示不限制)
  limitQty: Nullable<number> = null
  // 通知规格限量(为空表示不限制)
  limitQpcQty: Nullable<number> = null
  // 最后退仓日期
  lstBckDate: Nullable<Date> = null
  // 收货单单号
  receiptNum: Nullable<string> = null
  // 收货单单据类型
  receiptDatasource: Nullable<string> = null
  // 退货价格
  bckPrice: Nullable<number> = null
  // 退货单价
  bckSinglePrice: Nullable<number> = null
  // 原因效期明细
  reasonBatchs: ReturnApplyReasonBatch[] = []
  // 原因附件明细
  reasonAttaches: ReturnApplyReasonAttach[] = []
}
