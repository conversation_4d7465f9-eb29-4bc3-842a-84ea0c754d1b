/*
 * @Author: AI Assistant
 * @Date: 2025-01-19
 * @LastEditTime: 2025-06-19 14:02:21
 * @LastEditors: shikailei
 * @Description: Android原生网络请求封装
 * @FilePath: \soa\src\network\apiClientAndroid.ts
 * 记得注释
 */

import TokenMgr from '@/common/TokenMgr'
import Config from '@/config'
import store from '@/store'
import DateUtil from '@/utils/DateUtil'
import packageConfig from '../../package.json'

// 获取Android原生网络模块
const NetworkModule = uni.requireNativePlugin('NetMgr') as any

/**
 * Android原生请求配置接口
 */
interface AndroidRequestConfig {
  url: string
  headers?: Record<string, any>
  timeout?: number
  baseURL?: string
}

/**
 * Android原生响应接口
 */
interface AndroidResponse {
  data: string
  headers: string
  status: number
  statusText?: string
}

/**
 * 错误响应接口
 */
interface ErrorResponse {
  msg: string
  response: any
  status?: number
}

/**
 * Android原生API客户端类
 */
class ApiClientAndroid {
  private timeout: number = 35 * 1000
  private defaultHeaders: Record<string, any> = {}

  constructor() {
    this.initDefaultHeaders()
  }

  /**
   * 初始化默认请求头
   */
  private initDefaultHeaders(): void {
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept-Language': 'zh-CN,zh;q=0.9',
      language: 'zh-CN',
      // eslint-disable-next-line prettier/prettier
      'app_version': packageConfig.version
    }
  }

  /**
   * 构建完整的请求头
   */
  private buildHeaders(customHeaders: Record<string, any> = {}): Record<string, any> {
    const headers = { ...this.defaultHeaders, ...customHeaders }

    // 添加trace_id
    headers['trace_id'] = DateUtil.format(new Date().getTime(), 'yyyyMMdd-HHmmss-SSS')

    // 添加租户信息到headers（某些接口可能需要）
    if (store && store.getters && store.getters.isMicro) {
      const tenant = store.state.microInfo && store.state.microInfo.tenantId ? store.state.microInfo.tenantId : ''
      headers['sourceAppId'] = 'uni'
      if (tenant) {
        headers['tenant'] = tenant
      }
    } else {
      const tenant = store.state.currentTenant || Config.tenant
      if (tenant) {
        headers['tenant'] = tenant
      }
    }

    // 添加门店信息
    if (store.state.store) {
      headers.shopId = store.state.store.id
      headers.shop = store.state.store.id
      headers.shopNo = store.state.store.code
      headers.stroeCode = store.state.store.code
      headers.shopName = encodeURI(store.state.store.name || '')
    }

    // 添加用户信息
    if (store && store.state && (store.state.receiptName || (store.state.userInfo && store.state.userInfo.loginId))) {
      headers.openUserId = encodeURI(store.state.receiptName || store.state.userInfo!.loginId!)
      headers.openUserName = encodeURI(store.state.receiptName || store.state.userInfo!.nickName || store.state.userInfo!.loginId!)
    }

    // 添加认证信息
    if (store && store.getters && store.getters.isMicro) {
      // HD-UNI环境下的token处理
      if (store.state.microInfo) {
        // 这里需要调用uni.$hdmp.setToken，但在Android环境下可能需要特殊处理
        // 暂时使用基本的token设置
        if (store.state.userInfo) {
          headers.userCode = store.state.userInfo.loginId
        }
      }
    } else if (store.state.isHdPortal) {
      headers['sourceAppId'] = 'uni'
      // 使用TokenMgr设置token，但这里需要手动处理
      if (store.state.tokenInfo && store.state.tokenInfo.accessToken) {
        headers.Authorization = store.state.tokenInfo.accessToken
      }
    } else {
      if (store.state.userInfo) {
        headers.userCode = store.state.userInfo.loginId
        headers.Authorization = store.state.userInfo.token
      }
    }

    headers.appId = Config.appId

    return headers
  }

  /**
   * 构建完整的URL
   */
  private buildUrl(url: string, baseURL?: string): string {
    // 如果URL已经是完整的HTTP/HTTPS地址，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }

    // 确定基础URL
    let finalBaseURL = baseURL
    if (!finalBaseURL) {
      if (store && store.getters && store.getters.isMicro) {
        finalBaseURL = store.state.microInfo?.serverUrl || Config.baseUrl
      } else {
        finalBaseURL = store.state.isHdPortal ? Config.soaBff : Config.baseUrl
      }
    }

    // 确保finalBaseURL不为空
    if (!finalBaseURL) {
      finalBaseURL = Config.baseUrl
    }

    // 处理租户占位符
    let tenant: string = ''
    if (store && store.getters && store.getters.isMicro) {
      tenant = store.state.microInfo && store.state.microInfo.tenantId ? store.state.microInfo.tenantId : ''
    } else {
      if (store.state.currentTenant) {
        tenant = store.state.currentTenant
      } else {
        tenant = Config.tenant
      }
    }

    const processedUrl = url.replace('{tenant}', tenant)

    // 拼接完整URL
    const separator = finalBaseURL.endsWith('/') || processedUrl.startsWith('/') ? '' : '/'
    return finalBaseURL + separator + processedUrl
  }

  /**
   * 处理响应数据
   */
  private processResponse(response: AndroidResponse): Promise<any> {
    try {
      // 解析响应数据
      let responseData: any
      try {
        responseData = JSON.parse(response.data)
      } catch (e) {
        // 如果解析失败，直接使用原始数据
        responseData = response.data
      }

      // 解析响应头
      let responseHeaders: any = {}
      try {
        responseHeaders = JSON.parse(response.headers)
      } catch (e) {
        console.warn('Failed to parse response headers:', e)
      }

      // 处理token刷新
      this.handleTokenRefresh(responseHeaders)

      // 检查响应状态
      if (response.status >= 200 && response.status < 300) {
        // 检查业务状态码
        if (responseData && typeof responseData === 'object') {
          // 评论模块的接口服务端没有封装code，根据success和echoCode来判断接口是否返回成功
          if (responseData.success && responseData.echoCode === 0) {
            if (!responseData.data && typeof responseData.data !== 'boolean') {
              responseData.data = null
            }
            return Promise.resolve(responseData)
          } else if (responseData.code === 2000 || responseData.code === 0) {
            if (!responseData.data && typeof responseData.data !== 'boolean') {
              responseData.data = null
            }
            return Promise.resolve(responseData)
          } else {
            // 业务错误
            const error: ErrorResponse = {
              msg: responseData.msg || `服务器内部异常-${responseData.code}`,
              response: responseData
            }
            return Promise.reject(error)
          }
        } else {
          // 非JSON响应，直接返回
          return Promise.resolve({ data: responseData })
        }
      } else {
        // HTTP状态码错误
        const error: ErrorResponse = {
          msg: this.getErrorMessage(response.status),
          response: responseData,
          status: response.status
        }
        return Promise.reject(error)
      }
    } catch (e) {
      const error: ErrorResponse = {
        msg: '响应处理失败',
        response: e
      }
      return Promise.reject(error)
    }
  }

  /**
   * 处理token刷新
   */
  private handleTokenRefresh(responseHeaders: any): void {
    if (store && store.state && store.state.isHdPortal) {
      // 处理Portal环境的token刷新
      if (responseHeaders['access-token']) {
        const tokenInfo = store.state.tokenInfo || {}
        tokenInfo.accessToken = responseHeaders['access-token']

        if (responseHeaders['refresh-token']) {
          tokenInfo.refreshToken = responseHeaders['refresh-token']
        }

        if (responseHeaders['server-time']) {
          const currentTime = Math.floor(new Date().getTime())
          tokenInfo.serverDiffTime = responseHeaders['server-time'] - currentTime
        }

        store.dispatch('tokenInfo', tokenInfo)
      }
    } else if (store && store.getters && store.getters.isMicro) {
      // 处理HD-UNI环境的token刷新
      if (store.state.microInfo && uni.$hdmp && uni.$hdmp.refreshToken) {
        const microInfo = uni.$hdmp.refreshToken(responseHeaders, store.state.microInfo)
        if (microInfo) {
          store.dispatch('microInfo', microInfo)
          uni.$hdmp.sendMessage({
            event: 'updateToken',
            data: {
              traceId: DateUtil.format(new Date().getTime(), 'yyyyMMddHHmmssSSS'),
              data: microInfo,
              msg: 'token发生更新'
            }
          })
        }
      }
    }
  }

  /**
   * 获取错误信息
   */
  private getErrorMessage(status: number): string {
    switch (status) {
      case 0:
        return '请检查网络设置'
      case 1:
        return '网络超时!'
      case 401:
        return '登录已过期'
      case 403:
        return '禁止访问!'
      case 404:
        return '服务器无回应!'
      case 500:
        return '服务内部异常!'
      case 502:
        return '服务器暂不可用!'
      case 503:
        return '服务器升级中!'
      default:
        return `未知错误! 状态码: ${status}`
    }
  }

  /**
   * 处理401错误（登录过期）
   */
  private handle401Error(): void {
    if (store && store.getters && store.getters.isMicro) {
      // HD-UNI环境下的处理
      uni.showModal({
        content: '登录已过期',
        showCancel: false,
        success: function (res) {
          if (res.confirm) {
            uni.$hdmp.sendMessage({
              event: 'tokenExpire',
              data: {
                traceId: DateUtil.format(new Date().getTime(), 'yyyyMMddHHmmssSSS'),
                data: plus.runtime.appid,
                msg: '401了'
              },
              complete: () => {
                plus.runtime.quit()
              }
            })
          }
        }
      })
    } else {
      // 普通环境下的处理
      if (store.state.userInfo && store.state.userInfo.token) {
        store.dispatch('clear')
        setTimeout(() => {
          uni.showToast({ title: '登录已过期,请重新登录!', icon: 'none' })
        }, 300)

        const pages = getCurrentPages() as any[]
        if (
          !pages[pages.length - 1].$page ||
          (pages[pages.length - 1].$page &&
            pages[pages.length - 1].$page.fullPath !== (store.state.isHdPortal ? '/pagePortalSub/login/Login' : '/pagesOther/login/Login'))
        ) {
          uni.reLaunch({ url: store.state.isHdPortal ? '/pagePortalSub/login/Login' : '/pagesOther/login/Login' })
        }
      }
    }
  }

  /**
   * GET请求
   */
  public get(url: string, config: Partial<AndroidRequestConfig> = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      const fullUrl = this.buildUrl(url, config.baseURL)
      const headers = this.buildHeaders(config.headers)

      console.log('Android GET请求:', fullUrl, JSON.stringify(headers))

      NetworkModule.getAsync(fullUrl, JSON.stringify(headers), (response: AndroidResponse) => {
        console.log('Android GET响应:', response)

        if (response.status === 401) {
          this.handle401Error()
          reject({
            msg: '登录已过期',
            response: response,
            status: 401
          })
          return
        }

        this.processResponse(response).then(resolve).catch(reject)
      })
    })
  }

  /**
   * POST请求
   */
  public post(url: string, data: any = {}, config: Partial<AndroidRequestConfig> = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      const fullUrl = this.buildUrl(url, config.baseURL)
      const headers = this.buildHeaders(config.headers)
      const requestData = typeof data === 'string' ? data : JSON.stringify(data)

      console.log('Android POST请求:', fullUrl, JSON.stringify(headers), requestData)

      NetworkModule.postJson(fullUrl, JSON.stringify(headers), requestData, (response: AndroidResponse) => {
        console.log('Android POST响应:', response)

        if (response.status === 401) {
          this.handle401Error()
          reject({
            msg: '登录已过期',
            response: response,
            status: 401
          })
          return
        }

        this.processResponse(response).then(resolve).catch(reject)
      })
    })
  }

  /**
   * 设置超时时间
   */
  public setTimeout(timeout: number): void {
    this.timeout = timeout
  }

  /**
   * 设置默认请求头
   */
  public setDefaultHeaders(headers: Record<string, any>): void {
    this.defaultHeaders = { ...this.defaultHeaders, ...headers }
  }

  /**
   * 检查是否为Android环境
   */
  public static isAndroidEnvironment(): boolean {
    let isAndroid = false
    // #ifdef APP-PLUS
    isAndroid = uni.getSystemInfoSync().platform === 'android'
    // #endif
    return isAndroid && !!NetworkModule
  }
}

// 创建并导出实例
const apiClientAndroid = new ApiClientAndroid()

export default apiClientAndroid

// 同时导出类，以便需要时创建新实例
export { ApiClientAndroid }

/**
 * 使用示例：
 *
 * // 在Login.ts中的doLoginAndroid方法可以这样重构：
 *
 * import apiClientAndroid from '@/network/apiClientAndroid'
 *
 * async doLoginAndroid() {
 *   try {
 *     this.startTime = new Date().getTime()
 *     const timestamp = new Date().getTime()
 *     const urlParams = `terminalType=MOBILE&tenant=8211&timestamp=${timestamp}&token=${this.buildToken(timestamp)}`
 *     const url = `https://baas-test.hd123.com/unibff/v1/login-form/login?${urlParams}`
 *
 *     const params = this.buildLoginForm()
 *
 *     // 使用封装的Android客户端
 *     const response = await apiClientAndroid.post(url, params)
 *
 *     this.$hideLoading()
 *     console.log('数据响应 ===>', response)
 *
 *     const data = response.data as LoginResultVo
 *     this.handleLogined(data, true) // 处理登录成功
 *
 *   } catch (error) {
 *     this.$hideLoading()
 *     console.error('登录失败:', error)
 *     this.$showToast({ icon: 'error', title: error.msg || '登录失败' })
 *   }
 * }
 *
 * // 其他方法也可以类似地使用：
 *
 * async getAndroidLastAndLogin(): Promise<CLastLoginVo> {
 *   try {
 *     const url = 'https://baas-test.hd123.com/zl-portal/customerEnd/v1/api/org/getLastAndLogin'
 *     const response = await apiClientAndroid.get(url)
 *     return response.data
 *   } catch (error) {
 *     throw error
 *   }
 * }
 *
 * async getAndroidTenantByAppIds() {
 *   try {
 *     const url = 'https://baas-test.hd123.com/zl-portal/v2/customerEnd/api/appTenant/listByAppIds'
 *     const params = { appIds: ['sop', 'oas-osp', '经营助手'] }
 *     const response = await apiClientAndroid.post(url, params)
 *
 *     // 处理响应数据...
 *     const respData = response.data
 *     // ... 其他逻辑
 *
 *   } catch (error) {
 *     console.error('获取租户信息失败:', error)
 *     throw error
 *   }
 * }
 */
