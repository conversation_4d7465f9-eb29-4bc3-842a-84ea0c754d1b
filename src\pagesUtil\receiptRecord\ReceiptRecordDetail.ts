/*
 * @Author: 徐庆凯
 * @Date: 2020-11-04 13:34:38
 * @LastEditTime: 2025-03-14 11:02:22
 * @LastEditors: yuzhipi
 * @Description: 收货详情
 * @FilePath: \soa\src\pagesUtil\receiptRecord\ReceiptRecordDetail.ts
 * @记得注释
 */
import { Component, Vue } from 'vue-property-decorator'
import ReceiptApi from '@/network/receipt/ReceiptApi'
import ReceiptApiV2 from '@/network/receipt/ReceiptApiV2'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import BIllIdNum from 'model/data/BillIdNum'
import config from '@/config'
import CommonUtil from '@/utils/CommonUtil'
import CommentApi from '@/network/commentApi/CommentApi'
import ModuleFieldRights from '@/model/default/ModuleFieldRights'
import { Mutation, State } from 'vuex-class'
import SysConfigItem from '@/model/sysConfig/SysConfigItem'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'
import QueryRequest from '@/model/receipt/default/QueryRequest'
import AppReceiptBoxGoodsDTO from '@/model/receipt/AppReceiptBoxGoodsDTO'
import ReceiptLine from '@/model/receipt/default/ReceiptLine'
import AppReceiptRecordApi from '@/network/AppReceiptRecordApi/AppReceiptRecordApi'
import ID from '@/model/default/ID'
import AppReceiptRecordDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordDTO'
import AppReceiptRecordLineDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordLineDTO'
import AppReceiptRecordBoxDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxDTO'
import RecordSkuDetailCard from './cmp/RecordSkuDetailCard.vue'
import RecordBoxDetailCard from './cmp/RecordBoxDetailCard.vue'
import AppReceiptRecordBoxGoodsDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxGoodsDTO'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import { StateTransformer } from 'vuex-class/lib/bindings'
import AdjustFeedback from './cmp/AdjustFeedback.vue'
import AppReceiptRecordAttachQueryDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordAttachQueryDTO'

@Component({
  components: { RecordSkuDetailCard, RecordBoxDetailCard, ViewExhibit, AdjustFeedback }
})
export default class ReceiptRecordDetail extends Vue {
  @State('moduleFieldRightsList') moduleFieldRightsList: ModuleFieldRights[] // 获取模块字段权限
  @Mutation('moduleFieldRightsList') setModuleFieldRightsList // 提交模块字段权限
  @State('sysConfig') sysConfig: SysConfigItem // 获取全局配置
  @State('optionList') optionList: ModuleOption[]
  optionListModuleId = ModuleId

  $refs: any
  receiptLines: ReceiptLine[] = [] // 收货商品列表
  boxGoods: AppReceiptBoxGoodsDTO[] = [] // 按箱收货商品列表
  billId: string = '' // 收货记录单号
  receiptBillId: string = '' // 收货单单号
  detail: AppReceiptRecordDTO = new AppReceiptRecordDTO() // 收货记录详情
  // 分页相关
  pageSize: number = 10 // 每页大小
  pageNum: number = 0 // 页码
  finished: boolean = false // 是否加载完成
  isLoading: boolean = false // 是否在加载
  skuList: AppReceiptRecordLineDTO[] = [] // 商品列表
  boxList: AppReceiptRecordBoxDTO[] = [] // 箱码列表
  exhibitGoodsName: string = '' // 货位弹窗名
  exhibitLocation: string = '' // 货位
  images: string[] = [] // 外部传入图片

  // 是否展示散称商品件数
  get showWeightSkuReceivedQty() {
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWWEIGHTSKURECEIVEDQTY)) {
      return true
    }
    return false
  }

  // 获取权限
  get diffPermision() {
    if (PermissionMgr.hasPermission(Permission.receiptDiffView)) {
      return true
    }
    return false
  }

  /**
   * 是否展示配货价配货金额 - 默认展示
   */
  get showAlcPrice() {
    const moduleConfig = (this.moduleFieldRightsList || []).find((option: ModuleFieldRights) => option.moduleId == 'sosReceipt')
    if (moduleConfig && moduleConfig.fieldRights && moduleConfig.fieldRights.price) {
      return moduleConfig.fieldRights.price === '1'
    }
    return true
  }

  // 控制收货记录是否支持上传附件，0-否，1-是
  get uploadRecordAttach() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []

    if (moduleConfig.length > 0) {
      return moduleConfig.findIndex((item) => item.options && item.options.uploadRecordAttach === '1') > -1
    }
    return false
  }

  get showPrice() {
    //店务配置，是否显示配货价
    if (!this.showAlcPrice) {
      return false
    }
    if (PermissionMgr.hasPermission(Permission.receiptShowprice)) {
      return true
    }
    return false
  }

  async onLoad(option) {
    this.billId = option.id
    this.receiptBillId = option.receiptBillId
    try {
      this.$showLoading()
      this.detail = await this.getDetail({ id: this.billId })
      this.detail.receiptWay === 1 ? this.queryBox() : this.queryLine()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast(error.msg)
    }
  }

  // 显示更多商品
  onReachBottom() {
    if (this.finished || this.isLoading || !this.detail || this.detail.receiptWay === null || this.detail.receiptWay === undefined) {
      return
    }
    this.detail.receiptWay === 1 ? this.queryBox() : this.queryLine()
  }

  /**
   * 查询单据线切割
   * @param body
   * @returns
   */
  getDetail(body: ID) {
    return new Promise<AppReceiptRecordDTO>((resolve, reject) => {
      AppReceiptRecordApi.get(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  /**
   * 查询箱码列表
   * @param body
   * @returns
   */
  queryLine() {
    this.isLoading = true
    const body = new QueryRequest()
    body.page = this.pageNum
    body.pageSize = this.pageSize
    body.conditions = [{ operation: 'billId:=', parameters: [this.billId] }]
    return new Promise<AppReceiptRecordLineDTO[]>((resolve, reject) => {
      AppReceiptRecordApi.queryLine(body)
        .then((resp) => {
          this.isLoading = false
          this.pageNum++
          if (!resp.more) {
            this.finished = true
          }
          if (body.page === 0) {
            this.skuList = resp.data
          } else {
            this.skuList.push(...resp.data)
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  async loadSkuList() {
    try {
      this.$showLoading()
      await this.queryLine()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '查询失败',
        icon: 'error'
      })
    }
  }

  /**
   * 查询箱码列表
   * @param body
   * @returns
   */
  queryBox() {
    this.isLoading = true
    const body = new QueryRequest()
    body.page = this.pageNum
    body.pageSize = this.pageSize
    body.conditions = [{ operation: 'billId:=', parameters: [this.billId] }]

    return new Promise<AppReceiptRecordBoxDTO[]>((resolve, reject) => {
      AppReceiptRecordApi.queryBox(body)
        .then((resp) => {
          this.isLoading = false
          this.pageNum++
          if (!resp.more) {
            this.finished = true
          }
          if (body.page === 0) {
            this.boxList = resp.data
          } else {
            this.boxList.push(...resp.data)
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  async loadBoxList() {
    try {
      this.$showLoading()
      await this.queryBox()
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '查询失败',
        icon: 'error'
      })
    }
  }

  /**
   * 跳转到周转箱详情
   * @param box
   */
  handleNavSplitBox(box: AppReceiptRecordBoxDTO) {
    if (box.type === 'split') {
      this.$Router.push({
        name: 'receiptRecordBoxMore',
        params: {
          boxNo: box.boxNo,
          uuid: box.uuid,
          id: this.billId,
          receiptBillId: this.receiptBillId
        }
      })
    }
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info: AppReceiptRecordBoxGoodsDTO) {
    this.exhibitGoodsName = info.goods.name
    this.exhibitLocation = info.displayLocation || ''
    this.$refs.viewExhibit.open()
  }

  viewSkuExhibit(info: AppReceiptRecordLineDTO) {
    this.exhibitGoodsName = info.goods.name
    this.exhibitLocation = info.displayLocation || ''
    this.$refs.viewExhibit.open()
  }

  // 查看附件
  viewFile() {
    this.$showLoading()
    AppReceiptRecordApi.listAttach({ id: this.detail.billId })
      .then((resp) => {
        this.$hideLoading()
        this.images = []
        const data: AppReceiptRecordAttachQueryDTO[] = resp.data || []
        data.forEach((item: AppReceiptRecordAttachQueryDTO) => {
          this.images.push(item.fileUrl)
        })
        this.$refs.feedback.open()
      })
      .catch((error) => {
        this.$hideLoading()
        this.$showToast(error.msg)
      })
  }

  // 查看附件关闭
  doCloseFeedback() {
    this.$refs.feedback.close()
  }
}
