<template>
  <view class="goods-list-item">
    <view class="good-list-detail">
      <view v-if="showGdImage" class="image_view">
        <image lazy-load :src="img" class="image_120" @click="previewImg" mode="aspectFit" />
        <view class="scale-max">
          <image :src="'/static/icon/img_enlarge2.png' | oss" class="scale-max-image" mode="aspectFit"></image>
        </view>
      </view>
      <view class="goods-text">
        <view class="goods-title">
          <template>
            <text class="goods-new" v-if="isHot">爆</text>
            <text class="goods-new limit-color" v-else-if="isLimit">限</text>
            <text class="goods-new" v-else-if="isNewList">新</text>
            <text class="goods-fuliao" v-if="showAccessoryTag">辅料</text>
          </template>

          <text>{{ friendlyStr | empty }}</text>
        </view>
        <view class="all-tags">
          <view class="goods-tag" v-if="isDisp">散称</view>
          <view class="goods-gary" v-if="alcType">{{ alcType }}</view>
          <view class="goods-tag" v-if="recommandType">{{ recommandType }}</view>
          <view class="goods-tag" v-if="hasLiaobao">含料包</view>
        </view>
      </view>
      <view class="header-right" v-if="showNote">
        <image :src="'/static/icon/ic_tips_2x.png' | oss" class="image_32" mode="aspectFit" @click="doNoteShow()" />
        <div class="round" v-if="note"></div>
      </view>
    </view>
    <view class="goods-stock flex-warp">
      <view class="stock ellipsis" v-if="showInputCode">
        条码：
        <text class="stock-number">{{ inputCode | empty }}</text>
      </view>

      <view class="stock">
        <hd-popover>
          <view class="flex-row">
            <text class="stock-label">门店库存</text>
            <image class="tips-icon" :src="'/static/icon/<EMAIL>' | oss"></image>
            ：
            <text v-if="!isDisp">{{ hideInvQty ? '--' : goodsLine.goods.invQpcQtyTitle | empty }}</text>
            <text class="stock-number" v-else>{{ hideInvQty ? '--' : goodsLine.goods.invQty | empty }}{{ goodsLine.goods.minMunit | empty }}</text>
          </view>
          <template #bubble>
            <view style="width: 232rpx">指门店的实物库存</view>
          </template>
        </hd-popover>
      </view>
      <view class="stock" v-if="enableStandardShowTransportationInventory">
        <hd-popover>
          <view class="flex-row">
            <text class="stock-label">在途库存</text>
            <image class="tips-icon" :src="'/static/icon/<EMAIL>' | oss"></image>
            ：
            <text class="stock-number" v-if="!isDisp">{{ goodsLine.goods.inTransitInvQpcQty | empty }}</text>
            <text class="stock-number" v-else>{{ goodsLine.goods.inTransitInvQty | empty }}{{ goodsLine.goods.minMunit | empty }}</text>
          </view>
          <template #bubble>
            <view style="width: 232rpx">指发货方已发出但暂未收货的库存</view>
          </template>
        </hd-popover>
      </view>
      <view class="stock" v-if="enableStandardShowUnshippedInventory">
        <hd-popover>
          <view class="flex-row">
            <text class="stock-label">未发货库存</text>
            <image class="tips-icon" :src="'/static/icon/<EMAIL>' | oss"></image>
            ：
            <text class="stock-number" v-if="!isDisp">{{ goodsLine.goods.inOrderInvQpcQty | empty }}</text>
            <text class="stock-number" v-else>{{ goodsLine.goods.inOrderInvQty | empty }}{{ goodsLine.goods.minMunit | empty }}</text>
          </view>
          <template #bubble>
            <view style="width: 232rpx">指订单已确认但尚未发货的库存</view>
          </template>
        </hd-popover>
      </view>

      <view class="stock ellipsis" v-if="showCode">
        代码：
        <text class="stock-number">{{ code | empty }}</text>
      </view>
      <view class="goods-daily" @click="doAvgShow">
        <text class="daily">平均日销：</text>
        <text class="daily-number">{{ goodsLine.goods.dayAvgSale | empty }}{{ minMunit | empty }}</text>
        <image v-if="showAvgArrow" class="daily-img" :src="'/static/icon/ic_right_grey.png' | oss" style="height: 32rpx; width: 32rpx"></image>
      </view>
      <view class="stock">
        规格：
        <text class="stock-number">{{ qpcStr | empty }}</text>
      </view>
      <view class="stock">
        零售价：
        <text class="stock-number">{{ retailPrice }}</text>
      </view>
      <view class="stock" v-if="yestedayPremission">
        昨日叫货：
        <text class="stock-number">{{ reqQtyDay1 | empty }}{{ minMunit | empty }}</text>
      </view>
      <view class="stock" v-if="isHot">
        报名数：
        <text class="stock-number">{{ signUpQty | empty }}</text>
      </view>
      <view class="stock" v-if="!isHot && isLimit">
        可订数：
        <text class="stock-number">{{ allowQpcQty | empty }}</text>
      </view>
      <block v-if="isLimit && isHot">
        <view class="stock">
          已订数：
          <text class="stock-number">{{ orderQpcQty | empty }}</text>
        </view>
        <view class="stock">
          可订数：
          <text class="stock-number">{{ allowQpcQty | empty }}</text>
        </view>
      </block>

      <view class="stock" v-if="showValidPeriod">
        保质期天数：
        <text class="stock-number">{{ validPeriod | empty }}天</text>
      </view>

      <view class="stock" v-if="showRecentValidDate">
        到效期：
        <text class="stock-number">{{ recentValidDate | date | empty }}</text>
      </view>
      <view class="stock" v-if="showStdShow">
        标准陈列量：
        <text class="stock-number">{{ goodsLine.stdShowQty | empty }}</text>
      </view>
      <view class="stock" v-if="showActiveShowQty">
        活动陈列量：
        <text class="stock-number">{{ goodsLine.activeShowQty | empty }}</text>
      </view>
    </view>

    <view class="wrhNote" v-if="shelveNote && showNote" @click="doShowShelveNote()">上架备注：{{ shelveNote }}</view>

    <!-- 爆品商品 -->
    <view v-if="isHot && allowActQpcQty && !isShopCart" class="hot-good-qpc">
      <view class="hot-good-price">
        <text class="qpc-price" style="font-size: 28rpx">￥</text>
        <text class="qpc-price">{{ +goodsLine.goods.actPrice | empty }}</text>
        <text class="qpc-munit">/{{ munit | empty }}</text>
        <view class="hot-tag">爆品价</view>
      </view>
      <view class="hot-good-order">
        <text class="order-num-label">爆品可订数：</text>
        <text class="order-num-value">{{ allowActQpcQty | empty }}</text>
      </view>
    </view>

    <view class="good-qpc">
      <view>
        <view style="display: flex; align-items: center">
          <text class="qpc-price" style="font-size: 24rpx">￥</text>
          <text class="qpc-price">{{ +price | empty }}</text>
          <text class="qpc-munit">/{{ munit | empty }}</text>
        </view>
        <!-- 爆品订货提示 -->
        <view v-if="isHot && isShopCart" class="hot-active-tip">*此价格为爆品价与正常订货价计算的平均价</view>
      </view>
      <view class="add-num-box">
        <!-- 爆品订货提示 -->
        <view v-if="isHot && isShopCart" class="hot-good-order">
          <text class="order-num-label">爆品可订数：</text>
          <text class="order-num-value">{{ allowActQpcQty | empty }}</text>
        </view>
        <!-- 加购步进器 -->
        <view class="uni-numbox">
          <view
            v-if="showMinus"
            @click="_calcValue('minus')"
            class="uni-numbox__minus"
            :class="{ 'uni-numbox--disabled': goods.qpcQty <= 0 || disabled }"
          >
            <image
              class="operator-icon"
              :src="goods.qpcQty <= 0 || disabled ? '/static/icon/ic_minus_disable.png' : '/static/icon/ic_minus_normal.png' | oss"
            ></image>
          </view>
          <!-- #ifndef MP-ALIPAY -->
          <input
            v-if="showMinus && iosVerison"
            :key="uuid"
            :disabled="disabled"
            @blur="_onBlur"
            @focus="onFocus"
            class="uni-numbox__value uni-input-input"
            type="text"
            v-model="goods.qpcQty"
          />
          <input
            v-if="showMinus && !iosVerison"
            :key="uuid"
            :disabled="disabled"
            @blur="_onBlur"
            @focus="onFocus"
            class="uni-numbox__value uni-input-input"
            type="digit"
            v-model="goods.qpcQty"
          />
          <!-- #endif -->

          <!-- #ifdef MP-ALIPAY -->
          <input
            v-if="showMinus"
            :key="uuid"
            :disabled="disabled"
            @blur="_onBlur"
            @focus="onFocus"
            class="uni-numbox__value uni-input-input"
            type="number"
            v-model="goods.qpcQty"
          />
          <!-- #endif -->
          <view @click="_calcValue('plus')" class="uni-numbox__plus" :class="{ 'uni-numbox--disabled': goods.qpcQty >= max || disabled }">
            <image
              class="operator-icon"
              :src="goods.qpcQty >= max || disabled ? '/static/icon/ic_add_disable.png' : '/static/icon/ic_add_normal.png' | oss"
            ></image>
          </view>
        </view>
      </view>
    </view>

    <view class="good-tip" v-if="warnText">
      <view class="good-tip-tag">
        <image class="good-tip-tag-img" :src="'/static/icon/ic_warning_tip.png' | oss"></image>
        <text class="good-tip-tag-txt">{{ warnText }}</text>
      </view>
    </view>

    <view
      class="good-acclines"
      v-if="goodsLine.accLines && goodsLine.accLines.length && !!Number(goodsLine.qpcQty) && !isShopCart && accessoryManage === 2"
    >
      <accline-item
        v-for="(line, index) in goodsLine.accLines"
        :key="index"
        :line="line"
        :hideInvQty="hideInvQty"
        :showInputCode="showInputCode"
        @qtyChange="(qpcQty) => handleAccChange(qpcQty, index)"
      ></accline-item>
    </view>
  </view>
</template>

<script lang="ts" src="./GoodsListItem.ts"></script>

<style lang="scss" scoped>
.goods-list-item {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 228rpx;
  width: 100%;
  box-sizing: border-box;
  padding-top: 24rpx;
  padding-bottom: 23rpx;
  background: rgba(255, 255, 255, 1);
  .good-list-detail {
    margin: 0 24rpx;
    @include flex(row, flex-start, flex-start);
  }

  .flex-row {
    @include flex(row, flex-start);
  }
  .image_view {
    width: 120rpx;
    height: 120rpx;
    flex: 0 0 auto;
    margin-right: 12rpx;
    position: relative;
    .scale-max {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 24rpx;
      height: 24rpx;
      background: rgba(0, 0, 0, 0.4);
      border-radius: 8rpx 0px 8rpx 0rpx;
      text-align: center;
      @include flex();
      .scale-max-image {
        width: 16rpx;
        height: 16rpx;
      }
    }
  }

  .goods-text {
    text-align: left;
    flex: 1 1 auto;
    @include flex(column, flex-start, flex-start);

    .goods-title {
      width: 100%;
      box-sizing: border-box;
      font-family: $font-medium;
      font-weight: 500;
      color: rgba(40, 44, 52, 1);
      line-height: 40rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      word-break: break-all;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      .goods-new {
        background: linear-gradient(180deg, #fd4f6e 0%, #fa273b 100%);
        border-radius: 0px 0px 14rpx 14rpx;
        font-size: $font-size-xxsmall;
        font-family: PingFangSC-Regular, PingFang SC;
        color: $color-white;
        padding: 2rpx 4rpx;
        margin-right: 6rpx;
        font-weight: 400;
      }
      .goods-fuliao {
        margin-right: 8rpx;
        width: 56rpx;
        height: 32rpx;
        background: rgba(253, 52, 49, 0.1);
        border-radius: 4rpx;
        border: 1rpx solid #fd3431;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24rpx;
        color: #fd3431;
        line-height: 32rpx;
        text-align: center;
      }

      .limit-color {
        background: linear-gradient(141deg, #427eff 0%, #1c64fd 100%);
      }
    }
    .all-tags {
      @include flex(row, flex-start, center);
    }
    .goods-tag {
      margin-top: 8rpx;
      padding: 0rpx 6rpx;
      box-sizing: border-box;
      max-width: 120rpx;
      width: auto;
      height: 32rpx;
      line-height: 32rpx;
      background: #e9f0ff;
      border-radius: 4rpx;
      color: $color-primary;
      font-size: $font-size-xxsmall;
      margin-right: 8rpx;
      text-align: center;
      font-weight: 400;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .goods-gary {
      margin-top: 8rpx;
      height: 32rpx;
      padding: 0rpx 6rpx;
      background: #f5f6f7;
      border-radius: 4rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 20rpx;
      color: #666666;
      text-align: center;
      margin-right: 8rpx;
      line-height: 32rpx;
    }

    .goods-id {
      max-width: 100%;
      padding: 2rpx 6rpx;
      height: 32rpx;
      background: #f3f4f6;
      border-radius: 4rpx;
      box-sizing: border-box;

      margin-top: 8rpx;
      font-size: $font-size-small;
      color: rgba(148, 150, 154, 1);
      // line-height: 37rpx;
    }
  }
  .header-right {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 37rpx;
    flex: 0 0 auto;

    .round {
      z-index: 999;
      top: calc(50% - 25rpx);
      right: 0;
      transform: translate(30%, 30%);
      display: block;
      position: absolute;
      height: 10rpx !important;
      width: 10rpx;
      background: $color-error;
      border-radius: 50%;
      border: 1rpx solid #ffffff;
    }
  }

  .goods-stock {
    max-width: 100%;
    box-sizing: border-box;
    padding: 0 24rpx;

    display: flex;
    align-items: center;
    .stock {
      margin-top: 8rpx;
      width: 50%;
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: rgba(148, 150, 154, 1);
      line-height: 32rpx;

      .stock-label {
        flex: 0 0 auto;
      }

      .stock-number {
        font-size: 24rpx;
        color: rgba(88, 90, 94, 1);
        line-height: 32rpx;
      }

      .tips-icon {
        width: 32rpx;
        height: 32rpx;
        flex: 0 0 auto;
      }
    }
    .goods-daily {
      line-height: 32rpx;
      margin-top: 8rpx;
      width: 50%;
      display: flex;
      align-items: center;
      .daily {
        font-size: 24rpx;
        color: rgba(148, 150, 154, 1);
        line-height: 32rpx;
      }
      .daily-number {
        font-size: 24rpx;
        color: rgba(88, 90, 94, 1);
        line-height: 32rpx;
      }
      .daily-img {
        height: 32rpx;
        width: 32rpx;
      }
    }
  }

  .ellipsis {
    @include ellipsis(2);
  }

  .flex-warp {
    flex-wrap: wrap;
  }
  .wrhNote {
    margin: 16rpx 24rpx 0rpx;
    padding: 12rpx;
    box-sizing: border-box;

    height: 56rpx;
    background: $color-bg-primary;
    border-radius: 8rpx;
    @include ellipsis();

    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #94969a;
  }
  .qpc-detail {
    font-size: 24rpx;
    color: rgba(148, 150, 154, 1);
    line-height: 32rpx;
  }
  .good-qpc {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 24rpx;
    margin-top: 16rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .hot-active-tip {
      margin-top: 16rpx;
      font-size: 24rpx;
      color: #999;
      padding-right: 90rpx;
    }
    .qpc-price {
      font-size: 32rpx;
      font-weight: 600;
      color: rgba(255, 136, 0, 1);
      line-height: 32rpx;
    }
    .qpc-munit {
      margin-left: 12rpx;
      font-size: 24rpx;
      font-weight: 400;
      color: rgba(88, 90, 94, 1);
      line-height: 32rpx;
    }

    $box-height: 48rpx;
    /* #ifdef APP-NVUE */
    $box-line-height: 35px;
    /* #endif */
    $box-line-height: 26px;
    $box-width: 48rpx;
    .add-num-box {
      display: flex;
      align-items: flex-end;
      flex-direction: column;
      .hot-good-order {
        margin-bottom: 24rpx;
      }
    }

    .uni-numbox {
      /* #ifndef APP-NVUE */
      display: flex;
      /* #endif */
      flex-direction: row;
      align-items: flex-end;
      max-width: 246rpx;
      height: 48rpx;
      overflow: hidden;
      border-radius: 24rpx;
      overflow: hidden;
    }

    .operator-icon {
      height: 48rpx;
      width: 48rpx;
    }
    .uni-numbox__value {
      width: 108rpx;
      height: 48rpx;
      background-color: #f4f5f5;
      text-align: center;
      border-width: 1rpx;
      border-style: solid;
      border-color: $uni-border-color;
      border-left-width: 0;
      border-right-width: 0;
      border: none;
      box-sizing: border-box;
      font-size: 24rpx;
    }
    .uni-input-input {
      display: block;
      color: inherit;
      opacity: 1;
      -webkit-text-fill-color: currentcolor;
      font: inherit;
      line-height: inherit;
      letter-spacing: inherit;
      text-align: center;
      text-indent: inherit;
      text-transform: inherit;
      text-shadow: inherit;
      outline: none;
      padding: 0;
      margin: 0;
    }

    .uni-numbox__minus {
      /* #ifndef APP-NVUE */
      display: flex;
      /* #endif */
      align-items: center;
      justify-content: center;
      width: $box-width;
      height: 48rpx;
      // line-height: $box-line-height;
      // text-align: center;
      color: $uni-text-color;
      background-color: $uni-bg-color-grey;
      border-width: 1rpx;
      border-style: solid;
      border-color: $uni-border-color;
      border-radius: 4rpx 0 0 4rpx;
      background-color: #f4f5f5;
      border: none;
      font-size: 40rpx;
      line-height: 0;
      color: $uni-text-color;
      overflow: hidden;
    }

    .uni-numbox__plus {
      /* #ifndef APP-NVUE */
      display: flex;
      /* #endif */
      align-items: center;
      justify-content: center;
      width: $box-width;
      height: 48rpx;
      border-width: 1rpx;
      border-style: solid;
      border-color: $uni-border-color;
      border-left-width: 0;
      background-color: #f4f5f5;
      border-radius: 0 4rpx 4rpx 0;
      border: none;
      font-size: 40rpx;
      line-height: 0;
      color: $uni-text-color;
    }

    .uni-numbox--disabled {
      color: $uni-text-color-disable;
    }
  }
  .hot-good-qpc {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding: 0 24rpx;
    margin-top: 12rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .hot-good-price {
      display: flex;
      align-items: center;
      .qpc-price {
        font-size: 36rpx;
        font-weight: 600;
        color: #fd3431;
        line-height: 36rpx;
      }
      .qpc-munit {
        margin-left: 12rpx;
        font-size: 24rpx;
        font-weight: 400;
        color: #585a5e;
        line-height: 32rpx;
      }
      .hot-tag {
        margin-left: 4rpx;
        width: 68rpx;
        height: 32rpx;
        background: #feeaea;
        border-radius: 4rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #fd3431;
      }
    }
  }
  .hot-good-order {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #333;
    .order-num-label {
      color: #999;
    }
  }
  .good-tip {
    margin-top: 12rpx;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    box-sizing: border-box;
    padding: 0 24rpx;

    &-tag {
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      padding: 4rpx 12rpx;
      min-width: 204rpx;
      height: 40rpx;
      background: #feeaea;
      border-radius: 22rpx;
      font-size: 24rpx;
      font-family: PingFangSC;
      color: $color-error;
      &-img {
        width: 32rpx;
        height: 32rpx;
      }
      &-txt {
        margin-left: 4rpx;
      }
    }
  }

  .good-acclines {
    margin: 24rpx auto 0 auto;
    position: relative;
    box-sizing: border-box;
    width: calc(100% - 48rpx);
    background: #f5f6f7;
    border-radius: 8rpx;
    &::before {
      position: absolute;
      top: 0;
      left: 24rpx;
      transform: translateY(-100%);
      content: ' ';
      width: 0;
      height: 0;
      border-left: 16rpx solid transparent;
      border-right: 16rpx solid transparent;
      border-bottom: 16rpx solid #f5f6f7;
    }
  }
}
.goods-list-item:after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  bottom: 0;
  left: 19rpx;
  width: calc(100% - 38rpx);
  border-bottom: 2rpx solid rgba(227, 228, 232, 1);
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
</style>
