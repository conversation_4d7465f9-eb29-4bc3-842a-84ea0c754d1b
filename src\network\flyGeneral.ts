/*
 * @Author: AI Assistant
 * @Date: 2025-01-19
 * @LastEditTime: 2025-06-20 10:52:56
 * @LastEditors: shikailei
 * @Description: 通用网络请求适配器 - 根据运行环境自动选择Android原生或flyPortal
 * @FilePath: \soa\src\network\flyGeneral.ts
 * 记得注释
 */

import flyPortal from './flyPortal'
import { ApiClientAndroid } from './apiClientAndroid'
import config from '@/config'

/**
 * 检查是否为Android APP环境且支持原生网络请求
 */
function isAndroidNativeSupported(): boolean {
  let isAndroid = false
  let hasNativeModule = false

  // #ifdef APP-PLUS
  try {
    const systemInfo = uni.getSystemInfoSync()
    isAndroid = systemInfo.platform === 'android'

    // 检查是否有原生网络模块
    const NetworkModule = uni.requireNativePlugin('NetMgr')
    hasNativeModule = NetworkModule !== null && NetworkModule !== undefined
  } catch (e) {
    console.warn('检查Android原生支持时出错:', e)
    isAndroid = false
    hasNativeModule = false
  }
  // #endif

  console.log('flyGeneral环境检查:', { isAndroid, hasNativeModule })
  return isAndroid && hasNativeModule
}

/**
 * 创建Android原生网络客户端适配器
 * 将Android原生客户端的接口适配为flyPortal的接口格式
 */
class AndroidNetworkAdapter {
  private androidClient: ApiClientAndroid

  constructor() {
    this.androidClient = new ApiClientAndroid()
  }

  /**
   * GET请求 - 适配flyPortal的接口格式
   */
  get(url: string, params?: any, options?: any): Promise<any> {
    console.log('AndroidNetworkAdapter GET:', url, params, options)

    // 构建完整URL（包含查询参数）
    let fullUrl = url
    if (params && typeof params === 'object') {
      const queryString = this.buildQueryString(params)
      fullUrl = url + (url.includes('?') ? '&' : '?') + queryString
    }

    // 处理baseURL和headers
    const requestConfig: any = {}
    if (options) {
      if (options.baseURL) {
        requestConfig.baseURL = options.baseURL
      }
      if (options.headers) {
        requestConfig.headers = options.headers
      }
    }

    // 应用Portal的URL处理逻辑
    fullUrl = this.applyPortalUrlLogic(fullUrl, requestConfig.baseURL)

    return this.androidClient.get(fullUrl, requestConfig)
  }

  /**
   * POST请求 - 适配flyPortal的接口格式
   */
  post(url: string, data?: any, options?: any): Promise<any> {
    console.log('AndroidNetworkAdapter POST:', url, data, options)

    // 处理baseURL和headers
    const requestConfig: any = {}
    if (options) {
      if (options.baseURL) {
        requestConfig.baseURL = options.baseURL
      }
      if (options.headers) {
        requestConfig.headers = options.headers
      }
    }

    // 应用Portal的URL处理逻辑
    const fullUrl = this.applyPortalUrlLogic(url, requestConfig.baseURL)

    return this.androidClient.post(fullUrl, data, requestConfig)
  }

  /**
   * 构建查询字符串
   */
  private buildQueryString(params: Record<string, any>): string {
    const queryPairs: string[] = []

    Object.keys(params).forEach((key) => {
      if (params[key] !== undefined && params[key] !== null) {
        const encodedKey = encodeURIComponent(key)
        const encodedValue = encodeURIComponent(String(params[key]))
        queryPairs.push(`${encodedKey}=${encodedValue}`)
      }
    })

    return queryPairs.join('&')
  }

  /**
   * 应用Portal的URL处理逻辑
   * 复制flyPortal.ts中的URL处理逻辑
   */
  private applyPortalUrlLogic(url: string, baseURL?: string): string {
    // 如果已经是完整URL，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }

    // 设置baseURL
    let finalUrl = url
    if (!baseURL) {
      baseURL = config.portalBaseUrl || ''
    }

    // 后端分了两个网关，接口里不放网关，自行判断加网关
    if (url.indexOf('/customerEnd/v1') > -1 || url.indexOf('/v2/customerEnd') > -1) {
      // 选组织，查卡片等调下网关接口
      finalUrl = '/zl-portal' + url
    } else if (url.indexOf('/cprint-app') > -1) {
      finalUrl = '' + url
    } else {
      // 绑租户，登录等调下网关
      finalUrl = '/unibff' + url
    }

    // 拼接完整URL
    const separator = baseURL.endsWith('/') || finalUrl.startsWith('/') ? '' : '/'
    return baseURL + separator + finalUrl
  }

  /**
   * 设置配置 - 兼容flyPortal的config属性
   */
  get config() {
    return {
      timeout: 35 * 1000
    }
  }

  /**
   * 设置拦截器 - 兼容flyPortal的interceptors属性
   */
  get interceptors() {
    return {
      request: {
        use: (_fn: any) => {
          console.log('AndroidNetworkAdapter: request interceptor registered (no-op)')
        }
      },
      response: {
        use: (_successFn: any, _errorFn: any) => {
          console.log('AndroidNetworkAdapter: response interceptor registered (no-op)')
        }
      }
    }
  }
}

// 创建适配器实例
const androidAdapter = new AndroidNetworkAdapter()

/**
 * 智能网络请求客户端
 * 根据运行环境自动选择Android原生请求或flyPortal请求
 */
const flyGeneral = (() => {
  const useAndroidNative = isAndroidNativeSupported()

  console.log('flyGeneral初始化:', { useAndroidNative })

  if (useAndroidNative) {
    console.log('使用Android原生网络请求')
    return androidAdapter
  } else {
    console.log('使用flyPortal网络请求')
    return flyPortal
  }
})()

// 导出默认实例
export default flyGeneral

/**
 * 使用示例：
 *
 * 在LoginFormApi.ts中，您只需要替换import语句：
 *
 * // 原来的导入：
 * // import flyPortal from '../flyPortal'
 *
 * // 新的导入：
 * import flyGeneral from '../flyGeneral'
 *
 * // 其他代码完全不需要修改！
 * export default class LoginFormApi {
 *   static load(language?: string): Promise<ResponseVo<LoginFormVo>> {
 *     return flyGeneral.get('/v1/login-form/load', { terminalType: 'MOBILE', tenant: store.state.tenantInfo.id }, { headers: { language } })
 *   }
 *
 *   static login(body: CredentialVo[], timestamp?: number, token?: string, language?: string): Promise<ResponseVo<LoginResultVo>> {
 *     const params = { terminalType: 'MOBILE', tenant: store.state.tenantInfo.id, timestamp, token }
 *     const url = '/v1/login-form/login?' + new URLSearchParams(params).toString()
 *     return flyGeneral.post(url, body, { headers: { language } })
 *   }
 * }
 *
 * flyGeneral会自动检测运行环境：
 * - 如果是Android APP且支持原生网络模块，使用Android原生请求（更快、更稳定）
 * - 否则使用flyPortal的网络请求（兼容所有环境）
 *
 * 这样实现了：
 * 1. 无感替换：只需要修改import语句
 * 2. 自动适配：根据环境自动选择最优的网络请求方式
 * 3. 完全兼容：保持与flyPortal完全一致的API接口
 * 4. 性能优化：在支持的环境下使用原生网络请求
 */
