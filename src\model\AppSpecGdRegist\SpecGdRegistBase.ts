/*
 * @Author: weish<PERSON>
 * @Date: 2025-04-14 10:50:03
 * @LastEditTime: 2025-04-14 10:56:21
 * @LastEditors: weisheng
 * @Description:
 * @FilePath: /soa/src/model/AppSpecGdRegist/SpecGdRegistBase.ts
 * 记得注释
 */
import ShopEntity from './ShopEntity'
import { SpecGdRegistState } from './SpecGdRegistState'

export default class SpecGdRegistBase extends ShopEntity {
  // 数据标识
  billId: string = ''
  // 单号
  num: string = ''
  // 状态,取值范围：initial-未提交，submitted-已提交，approved-已批准，rejected-已拒绝
  state: Nullable<SpecGdRegistState> = null
  // 品项数
  recCnt: number = 0
  // 类型：0-登记，1-取消登记
  type: number = 0
  // 库存分类ID
  invCatId: number = 0
  // 库存分类名称
  invCatName: string = ''
  // 申请数
  qty: number = 0
  // 申请规格数
  qpcQty: number = 0
  // 批准数
  approveQty: Nullable<number> = null
  // 批准规格数
  approveQpcQty: Nullable<number> = null
  // 提交时间
  submitTime: Nullable<Date> = null
  // 提交人代码
  submitterId: Nullable<string> = null
  // 提交人名称
  submitterName: Nullable<string> = null
  // 审批时间
  auditTime: Nullable<Date> = null
  // 审批人代码
  auditorId: Nullable<string> = null
  // 审批人名称
  auditorName: Nullable<string> = null
  // 备注
  note: Nullable<string> = null
}
