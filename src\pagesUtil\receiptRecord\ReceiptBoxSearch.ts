import { Component } from 'vue-property-decorator'
import ReceiptApi from '@/network/receipt/ReceiptApi'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import { mixins } from 'vue-class-component'
import { Getter, State } from 'vuex-class'
import ModuleOption from '@/model/default/ModuleOption'
import BroadCast from '@/common/ScanMixin/broadCastMixin'
import QueryRequest from '@/model/base/QueryRequest'
import BoxEditCard from './cmp/BoxEditCard.vue'
import UserInfo from '@/model/user/UserInfo'
import AppReceiptBoxDTO from '@/model/receipt/AppReceiptBoxDTO'
import BoxEditCardDialog from './cmp/BoxEditCardDialog.vue'
import AppReceiptRecordApi from '@/network/AppReceiptRecordApi/AppReceiptRecordApi'
import AppReceiptRecordBoxDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxDTO'
import AppReceiptRecordBoxCheckDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxCheckDTO'
import ReceiptRecordBoxSum from '@/model/AppReceiptRecord/default/ReceiptRecordBoxSum'
import AppReceiptRecordBoxCheckGoodsDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxCheckGoodsDTO'
import Slot from '@/model/data/Slot'
import AppReceiptRecordBoxGoodsDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxGoodsDTO'
import GoodsSlotBinder from '@/model/data/GoodsSlotBinder'
import SelectExhibit from '@/components/select-exhibit/SelectExhibit.vue'
import ViewExhibit from '@/components/view-exhibit/ViewExhibit.vue'
import ResetExhibit from '@/components/select-exhibit/ResetExhibit.vue'
import AppReceiptMultipleRcvRecordQueryer from '@/model/receipt/AppReceiptMultipleRcvRecordQueryer'
import AppReceiptMultipleRcvGdRecordDTO from '@/model/receipt/AppReceiptMultipleRcvGdRecordDTO'
import SkuRecordDialog from '@/pages/cmp/SkuRecordDialog.vue'
import AppReceiptRecordDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordDTO'

@Component({
  components: { BoxEditCard, BoxEditCardDialog, SelectExhibit, ViewExhibit, ResetExhibit, SkuRecordDialog }
})
export default class ReceiptBoxSearch extends mixins(BroadCast) {
  @State('optionList') optionList: ModuleOption[]
  @State('userInfo') userInfo: Nullable<UserInfo> // 用户信息
  @Getter('qtyScale') qtyScale: number

  $refs: any
  keyWord: string = '' // 搜索关键字
  boxList: AppReceiptRecordBoxDTO[] = [] // 箱子列表 // 草稿明细列表
  billId: string = '' // 单据id
  receiptBillId: string = '' // 收货单单号

  // 分页相关
  pageSize: number = 10 // 每页大小
  pageNum: number = 0 // 页码
  finished: boolean = false // 是否加载完成
  isLoading: boolean = false // 是否在加载

  showEmpty: boolean = false // 是否展示空状态

  hasSelelcExhibit: Slot = new Slot() // 已选中的陈列位置
  viewExhibitInfo: AppReceiptRecordBoxGoodsDTO = new AppReceiptRecordBoxGoodsDTO() // 陈列位置弹窗数据
  exhibitIndex: number = 0 // 陈列位置下标

  get showPrice() {
    if (PermissionMgr.hasPermission(Permission.receiptShowprice)) {
      return true
    }
    return false
  }

  async onLoad(option) {
    if (option && option.id) {
      this.billId = option.id
    }

    this.receiptBillId = option.receiptBillId
    if (option && option.keyWord) {
      try {
        this.keyWord = decodeURIComponent(option.keyWord)
      } catch (error) {
        this.keyWord = option.keyWord
      }
      this.handleSearch()
    }

    if (option && option.searchword) {
      try {
        this.keyWord = decodeURIComponent(option.searchword)
      } catch (error) {
        this.keyWord = option.searchword
      }
      this.handleSearch(false)
    }

    uni.$off('searchQuery')
    // 重新监听
    uni.$on('searchQuery', () => {
      this.doResetPage()
      this.loadBoxList()
    })
    uni.$off('refreshExhibit')
    uni.$on('refreshExhibit', () => {
      this.$refs.exhibit.refresh()
      this.$refs.resetExhibit.refresh()
    })
  }

  onUnload() {
    uni.$off('searchQuery')
    uni.$off('refreshExhibit')
  }

  /**
   * 拆零箱点击
   * @param box
   */
  handleCardClick(box: AppReceiptRecordBoxDTO) {
    if (box.type === 'split') {
      this.$Router.push({
        name: 'receiptRecordBoxLineEdit',
        params: {
          boxNo: box.boxNo,
          id: this.billId,
          uuid: box.uuid,
          receiptBillId: this.receiptBillId
        }
      })
    }
  }

  /**
   * 确认收货前置
   * @param box
   */
  handleBeforeConfirm(box: AppReceiptRecordBoxDTO) {
    this.$showModal({
      title: '确定已完成该商品收货？',
      success: async (action) => {
        if (action.confirm) {
          this.handleConfirm(box)
        }
      }
    })
  }

  /**
   * 待收货的箱子确认收货
   * @param box
   */
  async handleConfirm(box: AppReceiptRecordBoxDTO) {
    try {
      this.$showLoading()
      const boxGoodss: AppReceiptRecordBoxCheckGoodsDTO[] = box.boxGoodss.map((item) => {
        return {
          uuid: item.uuid,
          gdUuid: item.goods.uuid,
          receiptQty: item.receiptQty || 0,
          receiptQpcQty: item.receiptQpcQty || '0',
          receiptTotal: item.goods.isDisp
            ? Number(item.receiptQty).multiply(item.goods.price).divide(item.goods.qpc).scale(2)
            : box.receiptQty.multiply(item.goods.price).scale(2),
          bindingSlots: null
        }
      })
      const body: AppReceiptRecordBoxCheckDTO = {
        confirmed: true,
        billId: this.billId,
        boxGoodss: boxGoodss,
        boxNo: box.boxNo,
        receiptQty: box.receiptQty
      }
      const summary = await this.modifyBox(body)
      this.$hideLoading()
      this.doResetPage()
      this.loadBoxList()
      uni.$emit('boxQuery')
      // 更新待收数量
      uni.$emit('update-un-confirm', { unConfirmPack: summary.unConfirmPack, confirmedPack: summary.confirmedPack })
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败',
        icon: 'error'
      })
    }
  }

  /**
   * 编辑箱
   * @param box
   */
  handleEdit(box: AppReceiptBoxDTO) {
    this.$refs.edit.open(box)
  }

  handleEditReason(box: AppReceiptBoxDTO) {
    this.$refs.reason.open(box)
  }

  async loadBoxList(isScan: boolean = false) {
    try {
      this.$showLoading()
      await this.queryBox(isScan)
      this.$hideLoading()
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '查询失败',
        icon: 'error'
      })
    }
  }

  // 显示更多商品
  onReachBottom() {
    if (this.finished || this.isLoading) {
      return
    }
    this.loadBoxList(false)
  }

  /**
   * 查询箱码列表
   * @param body
   * @returns
   */
  queryBox(isScan: boolean) {
    this.isLoading = true
    const body = new QueryRequest()
    body.page = this.pageNum
    body.pageSize = this.pageSize
    body.sorts = [
      { asc: false, field: 'type' },
      { asc: true, field: 'lineNo' }
    ]
    body.conditions = [{ operation: 'billId:=', parameters: [this.billId] }]
    if (this.keyWord) {
      body.conditions.push({ operation: 'keyword:%=%', parameters: [this.keyWord] })
    }
    if (isScan) {
      body.conditions.push({ operation: 'isScan:=', parameters: ['true'] })
    } else {
      body.conditions.push({ operation: 'isScan:=', parameters: ['false'] })
    }

    return new Promise<AppReceiptRecordBoxDTO[]>((resolve, reject) => {
      AppReceiptRecordApi.queryBox(body)
        .then((resp) => {
          if (!this.showEmpty) {
            this.showEmpty = true
          }
          this.pageNum++
          if (!resp.more) {
            this.finished = true
          }
          if (body.page === 0) {
            this.boxList = resp.data
          } else {
            this.boxList.push(...resp.data)
          }
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
        .finally(() => {
          this.isLoading = false
        })
    })
  }

  /**
   * 重置分页参数
   */
  doResetPage() {
    this.pageNum = 0
    this.isLoading = false
    this.finished = false
  }

  // 搜索框清空事件
  doClear() {
    this.keyWord = ''
    this.doResetPage()
    this.boxList = []
  }

  doCancel() {
    this.$Router.back(1)
  }

  /**
   * 编辑箱码
   * @param body
   * @returns
   */
  modifyBox(body: AppReceiptRecordBoxCheckDTO) {
    return new Promise<AppReceiptRecordDTO>((resolve, reject) => {
      AppReceiptRecordApi.boxCheckLine(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  // 搜索框搜索事件
  async doSearch() {
    if (this.keyWord.trim() === '') {
      this.$nextTick(() => {
        uni.pageScrollTo({
          scrollTop: 0
        })
      })
    } else {
      this.handleSearch(false)
    }
  }

  /**
   * PDA扫码回调事件
   * @param scanWord 扫码文字
   * @param isScan 是否扫码
   * @returns
   */
  async doScanAfter(scanWord: string) {
    if (this.isLoading) {
      this.$showToast({ title: '正在加载，请稍后重试~' })
      return
    }
    this.keyWord = scanWord
    this.handleSearch()
  }

  async handleSearch(isScan: boolean = true) {
    this.doResetPage()
    await this.loadBoxList(isScan)
    this.$nextTick(() => {
      uni.pageScrollTo({
        scrollTop: 0
      })
    })
  }

  /**
   * 绑定陈列位置
   */
  bindExhibit(info: AppReceiptRecordBoxGoodsDTO, index: number) {
    this.exhibitIndex = index ? index : this.exhibitIndex
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.open(this.hasSelelcExhibit, slotGoods, 'bindExhibit')
  }

  /**
   * 陈列位置调整
   */
  resetExhibit(info: AppReceiptRecordBoxGoodsDTO, index: number) {
    this.hasSelelcExhibit = new Slot()
    this.exhibitIndex = index ? index : this.exhibitIndex
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.$refs.resetExhibit.open(slotGoods)
  }

  /**
   * 获取该商品信息
   */
  getRequestBody(info: AppReceiptRecordBoxGoodsDTO) {
    const slotGoods = new GoodsSlotBinder()
    // 来源货位
    slotGoods.sourceSlotCode = info.displayLocation
    // 商品数据标识
    slotGoods.bingdingGoods.uuid = info.goods.uuid
    // 商品代码
    slotGoods.bingdingGoods.code = info.goods.code
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    // 商品条码
    slotGoods.bingdingGoods.inputCode = info.goods.inputCode
    this.inputExhibit(info.exhibitValue)
    this.$refs.exhibit.success(this.hasSelelcExhibit, slotGoods)
  }

  /**
   * 输入框的值组装
   */
  inputExhibit(code) {
    this.hasSelelcExhibit.code = code
    this.hasSelelcExhibit.name = code
    this.hasSelelcExhibit.uuid = ''
  }

  /**
   * 陈列位置绑定成功
   */
  async confirmExhibit() {
    this.doResetPage()
    await this.loadBoxList()
    this.$refs.edit.open(this.boxList[this.exhibitIndex] || [], false)
    this.hasSelelcExhibit = new Slot()
  }

  /**
   * 添加陈列位置
   */
  addExhibit() {
    this.$Router.push({
      name: 'addExhibitLocation'
    })
  }

  /**
   * 关闭弹窗
   */
  closeViewExhibit() {
    this.$refs.viewExhibit.close()
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(info: AppReceiptRecordBoxGoodsDTO) {
    this.viewExhibitInfo = { ...info }
    this.$refs.viewExhibit.open()
  }

  /**
   * 查看商品行收货记录
   * @param box 箱
   */
  async handleViewDetail(box: AppReceiptRecordBoxDTO) {
    try {
      this.$showLoading()
      const recordList = await this.listMultipleRcvRecord({
        billId: this.receiptBillId,
        gdInputCode: box.boxGoodss[0].goods.inputCode
      })
      this.$hideLoading()

      if (recordList.length === 0) {
        this.$showToast({
          title: '暂无收货记录'
        })
      } else {
        this.$refs.recordDialog.open(recordList)
      }
    } catch (error) {
      this.$hideLoading()
      this.$showToast({
        title: error.msg || '操作失败'
      })
    }
  }

  /**
   * 查询商品行多次收货记录
   * @param body
   * @returns
   */
  listMultipleRcvRecord(body: AppReceiptMultipleRcvRecordQueryer) {
    return new Promise<AppReceiptMultipleRcvGdRecordDTO[]>((resolve, reject) => {
      ReceiptApi.listMultipleRcvRecord(body)
        .then((resp) => {
          resolve(resp.data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
}
