/*
 * @Author: 刘湘
 * @Date: 2021-05-06 16:22:21
 * @LastEditTime: 2025-03-18 13:58:56
 * @LastEditors: hanwei
 * @Description:
 * @FilePath: \soa\src\pagesShopManage\receipt\cmp\SaleListBox.ts
 * 记得注释
 */
import { Vue, Component, Prop } from 'vue-property-decorator'
import { State } from 'vuex-class'
import ModuleOption from '@/model/default/ModuleOption'
import { ModuleId } from '@/model/common/OptionListModuleId'
import AppReceiptBoxGoodsDTO from '@/model/receipt/AppReceiptBoxGoodsDTO'
import CommonUtil from '@/utils/CommonUtil'
import config from '@/config'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'

@Component({
  components: {}
})
export default class SaleListBox extends Vue {
  @Prop({ type: Array, default: () => [] }) lines: AppReceiptBoxGoodsDTO[] // 商品行
  @Prop({ type: Boolean, default: true }) showMore: boolean // 显示查看更多按钮
  @Prop({ type: Boolean, default: false }) isSticky: boolean // 是否标题吸顶
  @Prop({ type: String, default: '0' }) top: string // 标题吸顶位置
  @Prop({ type: Boolean, default: false }) showPrice: boolean // 显示配货价
  @Prop({ type: Boolean, default: false }) showWeightSkuReceivedQty: boolean // 是否展示散称商品件数
  @Prop({ type: Boolean, default: false }) enableMultipleReceipt: boolean // 是否启用多次收货

  @State('optionList') optionList: ModuleOption[]
  optionListModuleId = ModuleId

  // 商品图片
  get img() {
    return (sku: AppReceiptBoxGoodsDTO) => {
      return sku && sku.goods.images && sku.goods.images.length && CommonUtil.isImageUrl(sku.goods.images[0])
        ? `${sku.goods.images[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
        : `${config.sourceUrl}icon/pic_goods.png`
    }
  }

  get imageList() {
    return (sku: AppReceiptBoxGoodsDTO) => {
      return sku && sku.goods.images && sku.goods.images.filter((item) => CommonUtil.isImageUrl(item)).length
        ? sku.goods.images.filter((item) => CommonUtil.isImageUrl(item))
        : [`${config.sourceUrl}icon/pic_goods.png`]
    }
  }

  //获取字段展示权限
  get showMaster() {
    const masterPermission = {
      showDisplayLocation: false // 陈列位置
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDISPLAYLOCATION)) {
      masterPermission.showDisplayLocation = true
    }
    return masterPermission
  }

  viewMore() {
    this.$emit('total')
  }

  /**
   * 查看指定商品收货明细
   * @param line
   */
  handleViewDetail(line: AppReceiptBoxGoodsDTO) {
    this.$emit('detail', line.goods.inputCode)
  }

  /**
   * 是否有多个陈列位置
   */
  get hasMutiple() {
    return (item) => {
      return item && item.split(',').length > 1
    }
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit(line: AppReceiptBoxGoodsDTO) {
    this.$emit('viewExhibit', line)
  }

  /**
   * 预览图片
   */
  handlePreviewImg(sku: AppReceiptBoxGoodsDTO) {
    uni.previewImage({
      current: String(0),
      urls: this.imageList(sku)
    })
  }
}
