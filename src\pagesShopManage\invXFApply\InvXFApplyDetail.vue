<template>
  <view :class="['inv-xf-apply-detail', showAbortBtn ? 'abort' : '']">
    <hd-toast></hd-toast>
    <hd-modal></hd-modal>
    <hd-loading></hd-loading>
    <hd-water-mark></hd-water-mark>
    <!-- 头部状态 -->
    <view class="top-header">
      <view class="title" @click="doShowProcess">
        <text>{{ data.state | flowName }}</text>
        <image v-if="processAble" class="title-arrow" :src="'/static/icon_Flat/ic_noticebar_right_white.png' | oss" mode="aspectFill"></image>
      </view>
    </view>
    <!-- 头部汇总数据 -->
    <view class="top-summary">
      <view class="transfer-shop">{{ data.type === InvXFApplyType.increase ? data.counterShopName : data.shopName }}</view>
      <image class="transfer-icon" :src="'/static/icon/ic_transferin.png' | oss" mode="aspectFill"></image>
      <view class="transfer-shop">{{ data.type === InvXFApplyType.increase ? data.shopName : data.counterShopName }}</view>
    </view>
    <!-- tab切换 -->
    <view class="inv-xf-tab" v-if="isHasPack">
      <view class="inv-xf-goods" @click="getTab('good')">
        <text :class="[tabType === 'good' ? 'inv-xf-goods-txt' : 'inv-xf-goods-txt-sub']">商品明细</text>
        <view v-if="tabType === 'good'" class="inv-xf-goods-btn"></view>
      </view>
      <view class="inv-xf-goods" @click="getTab('package')">
        <text :class="[tabType === 'package' ? 'inv-xf-goods-txt' : 'inv-xf-goods-txt-sub']">包材明细</text>
        <view v-if="tabType === 'package'" class="inv-xf-goods-btn"></view>
      </view>
    </view>
    <!--列表-->
    <sale-list
      v-if="tabType === 'good'"
      :lines="data.lines.slice(0, 4)"
      :showMore="showMore"
      :showPrice="showPrice"
      :showRtlPrice="showRtlPrice"
      :enableValidDate="enableValidDate"
      @total="doViewMore"
    ></sale-list>

    <package-list v-if="tabType === 'package'" :state="data.state" :lines="data.packLines"></package-list>

    <view class="bottom-info">
      <block v-if="!isHasPack">
        <view class="field" v-if="showPrice">
          <view class="label">商品总额</view>
          <view class="data total">￥{{ data.total | empty }}</view>
        </view>
        <view class="field" v-if="showRtlPrice">
          <view class="label">售价总额</view>
          <view class="data total">￥{{ data.rtlTotal | empty }}</view>
        </view>
      </block>
      <block v-else>
        <view class="field">
          <view class="label">调拨金额</view>
          <view class="data total">￥{{ aggAllTotal | empty }}</view>
        </view>
        <view class="field">
          <view class="label">货品调拨金额</view>
          <view class="data total">￥{{ goodsTotal | empty }}</view>
        </view>
        <view class="field">
          <view class="label">包材调拨金额</view>
          <view class="data total">￥{{ packTotal | empty }}</view>
        </view>
      </block>
      <view class="field">
        <view class="label">{{ counterShop }}门店</view>
        <view class="data">{{ data.counterShopName | empty }}</view>
      </view>
      <view class="field">
        <view class="label">备注</view>
        <view class="remark">{{ data.note | empty }}</view>
      </view>
      <view class="field" v-if="infDeliveryTypeShow">
        <view class="label">配送模式</view>
        <view class="remark">{{ deliveryType | empty }}</view>
      </view>
      <!-- <view class="field">
        <view class="label">申请单号</view>
        <view class="data">{{ data.num | empty }}</view>
      </view> -->
      <view class="field">
        <view class="label">提交人</view>
        <view class="data">{{ data.creatorName | empty }}</view>
      </view>
      <view class="field">
        <view class="label">提交时间</view>
        <view class="data">{{ data.submitTime | date('yyyy-MM-dd HH:mm:ss') | empty }}</view>
      </view>
    </view>
    <view v-if="showAbortBtn" class="footer">
      <hd-button type="primary" @click="doAbort">作废</hd-button>
    </view>
    <uni-popup type="bottom" ref="flowDialog">
      <flow-dialog></flow-dialog>
    </uni-popup>
  </view>
</template>
<script lang="ts" src="./InvXFApplyDetail.ts"></script>

<style lang="scss" scoped>
.inv-xf-apply-detail {
  height: 100vh;
  background: $list-bg-color-lx;
  overflow-x: hidden;
  position: relative;
  .top-header {
    width: 750rpx;
    height: 120rpx;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    justify-content: space-between;
    padding: 20rpx $base-padding 32rpx;
    background: $color-primary;
    .title {
      display: flex;
      align-items: center;
      font-size: 44rpx;
      height: 48rpx;
      line-height: 48rpx;
      font-weight: 500;
      color: $uni-bg-color;
      .title-arrow {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .top-summary {
    margin-top: -12rpx;
    border-radius: 12rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 16rpx;
    padding: 16rpx 24rpx;
    background-color: $color-localbg;
    box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.1);

    .transfer-icon {
      width: 122rpx;
      height: 32rpx;
    }

    .transfer-shop {
      height: 40rpx;
      width: 270rpx;
      text-align: center;
      color: #282c34;
      font-size: $font-size-default;
      font-weight: 500;
      line-height: 40rpx;
      @include ellipsis();
    }
  }

  .inv-xf-tab {
    width: 100%;
    height: 100rpx;
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: #ffffff;
    border-bottom: 2rpx solid #eeeeee;

    .inv-xf-goods {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      margin-right: 16rpx;

      .inv-xf-goods-txt {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32rpx;
        color: #1a1a1a;
        text-align: center;
        font-style: normal;
        margin-top: 22rpx;
      }

      .inv-xf-goods-txt-sub {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        text-align: center;
        font-style: normal;
        margin-top: 24rpx;
      }

      .inv-xf-goods-btn {
        width: 64rpx;
        height: 7rpx;
        background: #1c64fd;
        border-radius: 4rpx;
      }
    }
  }

  .bottom-info {
    border-radius: 12rpx;
    overflow: hidden;
    background-color: #ffffff;
    margin: 16rpx 0;
    padding: 24rpx $base-padding;
    box-sizing: border-box;
    .field {
      width: 100%;
      display: flex;
      font-size: 28rpx;
      .label {
        flex: 0 0 auto;
        min-width: 140rpx;
        color: $font-color-darklight;
        height: 40rpx;
      }
      .data {
        word-break: break-all;
        color: $color-text-primary;
        margin-left: 15rpx;
        flex: 1 1 auto;
        flex-wrap: wrap;
        min-height: 40rpx;
        text-align: right;
        @include ellipsis();
      }
      .remark {
        word-break: break-all;
        color: $color-text-primary;
        margin-left: 15rpx;
        flex: 1 1 auto;
        flex-wrap: wrap;
        min-height: 40rpx;
        text-align: right;
      }
      .total {
        font-weight: 500;
      }
      & + .field {
        margin-top: 24rpx;
      }
    }
  }
}
.footer {
  width: 100%;
  height: 100rpx;
  position: fixed;
  bottom: 0;
  background: #1c64fd;
}
.abort {
  height: calc(100vh - 100rpx);
  padding-bottom: 100rpx;
}
</style>
