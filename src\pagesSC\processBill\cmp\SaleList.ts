/*
 * @LastEditors: hanwei
 */
import { Vue, Component, Prop } from 'vue-property-decorator'
import ProcessBillLine from '@/model/processBill/ProcessBillLine'
import { ProcessGoodsState } from '@/model/processBill/ProcessGoodsState'
import { Filter } from '@/pagesSC/processBill/cmd/Filter'
import config from '@/config'
import CommonUtil from '@/utils/CommonUtil'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'

@Component({
  components: {},
  filters: Filter
})
export default class SaleList extends Vue {
  @Prop({ default: ProcessGoodsState.material }) type: ProcessGoodsState.material // 标题
  @Prop({ type: Array, default: () => [] }) lines: ProcessBillLine[] // 商品行
  @Prop({ type: Boolean, default: false }) isSticky: boolean // 是否标题吸顶

  // 价格描述
  get describe() {
    if (this.type === ProcessGoodsState.material) {
      return '原料'
    } else if (this.type === ProcessGoodsState.product) {
      return '成品'
    } else {
      return ''
    }
  }

  // 价格描述
  get total() {
    if (this.type === ProcessGoodsState.material) {
      return '成本'
    } else if (this.type === ProcessGoodsState.product) {
      return '零售'
    } else {
      return ''
    }
  }

  // 展示金额
  get showPrice() {
    if (this.type === ProcessGoodsState.material) {
      return !PermissionMgr.hasPermission(Permission.globalPriceView)
    } else {
      return true
    }
  }

  // title描述
  get title() {
    if (this.type === ProcessGoodsState.material) {
      return '成本价'
    } else if (this.type === ProcessGoodsState.product) {
      return '售价'
    } else {
      return ''
    }
  }

  // 商品图片
  get skuImg() {
    return (sku: ProcessBillLine) => {
      return sku && sku.goodsImages && sku.goodsImages.length && CommonUtil.isImageUrl(sku.goodsImages[0])
        ? `${sku.goodsImages[0]}?x-oss-process=image/resize,l_${uni.upx2px(120)}`
        : `${config.sourceUrl}icon/pic_goods.png`
    }
  }

  get imageList() {
    return (sku: ProcessBillLine) => {
      return sku && sku.goodsImages && sku.goodsImages.filter((item) => CommonUtil.isImageUrl(item)).length
        ? sku.goodsImages.filter((item) => CommonUtil.isImageUrl(item))
        : [`${config.sourceUrl}icon/pic_goods.png`]
    }
  }

  /**
   * 预览图片
   */
  handlePreviewImg(sku: ProcessBillLine) {
    uni.previewImage({
      current: String(0),
      urls: this.imageList(sku)
    })
  }
}
