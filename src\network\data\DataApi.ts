import fly from '@/network/fly'
import BaseResponse from '@/model/base/BaseResponse'
import Weather from '@/model/data/Weather'
import Shop from '@/model/data/Shop'
import QueryRequest from '@/model/base/QueryRequest'
import GoodsCategory from '@/model/data/GoodsCategory'
import Bill from '@/model/data/Bill'
import CategoryQueryRequest from '@/model/default/CategoryQueryRequest'
import TabCategory from '@/model/default/TabCategory'
import Slot from '@/model/data/Slot'
import GoodsSlotBinder from '@/model/data/GoodsSlotBinder'
import SlotQueryRequest from '@/model/data/SlotQueryRequest'
import InvCat from '@/model/base/InvCat'
import Sort from '@/model/default/Sort'

export default class DataApi {
  /**
   * 查询天气预报
   *
   * @param body 查询日期
   */
  static listWeather(dates: string[]): Promise<BaseResponse<Nullable<Weather[]>>> {
    return fly
      .post(
        'sos/v1/{tenant}/mdata/weather/list',
        {
          dates: dates
        },
        {}
      )
      .then((res) => {
        return res
      })
  }

  /**
   * 查询门店
   *
   * @param body 查询条件
   */
  static queryShop(body: QueryRequest): Promise<BaseResponse<Nullable<Shop[]>>> {
    return fly.post('sos/v1/{tenant}/mdata/store/query', body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询存在商品的商品分类（两级）
   *
   * @param body 查询定义
   * @param tenant 租户标识
   */
  static queryCategoryExistsGoods(body: CategoryQueryRequest): Promise<BaseResponse<TabCategory[]>> {
    return fly.post(`sos/v1/{tenant}/mdata/goodscategory/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询商品分类
   *
   * @param body 查询条件
   */
  static sortList(): Promise<BaseResponse<Nullable<GoodsCategory[]>>> {
    return fly.post('sos/v1/{tenant}/mdata/category/list', {}, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询单据
   *
   * @param num 单号
   * @param fetchParts 需要返回的部分信息，多个以逗号分隔
   */
  static getBill(num: string): Promise<BaseResponse<Nullable<Bill[]>>> {
    return fly.get(`sos/v1/{tenant}/commons/list/${num}`).then((res) => {
      return res
    })
  }

  /**
   * 创建货架位
   *
   * @param body 货架位
   * @param tenant 租户标识
   */
  static createSlot(body: Slot): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/mdata/slot/create`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 商品绑定货架位
   *
   * @param body 商品货架位绑定对象
   * @param tenant 租户标识
   */
  static goodsBindSlot(body: GoodsSlotBinder[]): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/mdata/goods/binding/slot`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 查询货架位
   *
   * @param body 货架位查询定义
   * @param tenant 租户标识
   */
  static querySlot(body: SlotQueryRequest): Promise<BaseResponse<Slot[]>> {
    return fly.post(`sos/v1/{tenant}/mdata/slot/query`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 删除货架位
   *
   * @param body 货架位查询定义
   * @param tenant 租户标识
   */
  static removeSlot(body: Slot[]): Promise<BaseResponse<void>> {
    return fly.post(`sos/v1/{tenant}/mdata/slot/remove`, body, {}).then((res) => {
      return res
    })
  }

  /**
   * 库存分类查询
   *
   * @param tenant 租户标识
   */
  static listInvCat(): Promise<BaseResponse<InvCat[]>> {
    return fly.post(`sos/v1/{tenant}/mdata/invcat/list`, {}, {}).then((res) => {
      return res
    })
  }

  /**
   * 获取商品主分类
   *
   * @param tenant 租户标识
   */
  static listSort(): Promise<BaseResponse<GoodsCategory[]>> {
    return fly.post(`sos/v1/{tenant}/mdata/sort/list`, {}, {}).then((res) => {
      return res
    })
  }
}
