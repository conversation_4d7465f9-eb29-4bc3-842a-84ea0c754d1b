import Ucn from 'model/base/Ucn'
import Category from 'model/data/Category'
import Sort from 'model/data/Sort'

export default class ProcessBillGoods {
  brand: Nullable<Ucn> = null // 品牌
  code: string = '' // 代码
  firstCategory: Nullable<Category> = null // 一级面板分类
  inputCode: string = '' // 输入码
  minMunit: Nullable<string> = null // 最小规格单位
  munit: string = '' // 包装单位
  name: string = '' // 名称
  price: number = 0 // 规格价
  qpc: number = 0 // 规格
  qpcStr: string = '' // 包装规格
  secondCategory: Nullable<Category> = null // 二级面板分类
  singlePrice: number = 0 // 单价
  sort: Nullable<Sort> = null // 类别
  uuid: string = '' // 商品id
  qty: number = 0 // 数量
  qpcQty: number = 0 // 规格数量
  total: number = 0 // 金额
  // 售价
  rtlPrc: Nullable<number> = 0
  images: string[] = [] // 图片
}
