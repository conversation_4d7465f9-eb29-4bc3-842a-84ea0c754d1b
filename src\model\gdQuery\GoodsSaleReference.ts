import GoodsSaleGoodsInfo from './GoodsSaleGoodsInfo'
import GoodsSaleRefDtl from './GoodsSaleRefDtl'
import Weather from './Weather'

export default class GoodsSaleReference {
  // 商品资料
  goods: GoodsSaleGoodsInfo = new GoodsSaleGoodsInfo()
  // 今日销量（单品数量）
  saleQty: Nullable<number> = null
  // 月销量（单品数量）
  monthSaleQty: Nullable<number> = null
  // 最后更新时间
  lastModified: Nullable<Date> = null
  // 商品销售数据日期明细
  details: GoodsSaleRefDtl[] = []
  // 天气数据
  weathers: Weather[] = []
}
