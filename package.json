{"name": "soa", "version": "2.56.1", "private": true, "scripts": {"deploy:mkh": "build/deploy_mkh.sh", "deploy:cdh": "build/deploy_cdh.sh", "job": "node build/buildjob.js", "dev": "cross-env NODE_ENV=development uniapp-cli custom mp-dingtalk", "dev:h5-dingtalk": "cross-env NODE_ENV=development uniapp-cli custom h5-dingtalk", "serve": "npm run dev:h5", "build:hd-uni": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 uniapp-cli custom hd-uni", "build": "cross-env NODE_ENV=production uniapp-cli custom mp-dingtalk", "build:h5-alipay": "cross-env NODE_ENV=production uniapp-cli custom h5-dingtalk", "build:app-bl": "cross-env NODE_ENV=production uniapp-cli custom app-bl", "build:app-cs": "cross-env NODE_ENV=production uniapp-cli custom app-cs", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build --minimize", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:h5-alipay": "cross-env NODE_ENV=development uniapp-cli custom h5-dingtalk", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:app-cs": "cross-env NODE_ENV=development uniapp-cli custom app-cs", "dev:app-bl": "cross-env NODE_ENV=development uniapp-cli custom app-bl", "dev:hd-uni": "cross-env NODE_ENV=development uniapp-cli custom hd-uni", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "lint": "vue-cli-service lint --fixed", "prepare": "husky install", "commit": "git-cz", "release-major": "standard-version --release-as major", "release-minor": "standard-version --release-as minor", "release-patch": "standard-version --release-as patch", "release": "standard-version", "prerelease": "standard-version --prerelease", "upload:mp-dingtalk": "cross-env NODE_ENV=production uniapp-cli custom mp-dingtalk  && minici --platform dd", "upload:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build --minimize  && minici --platform weixin"}, "config": {"commitizen": {"path": "git-cz"}}, "dependencies": {"@dcloudio/uni-app-plus": "2.0.1-34720220422002", "@dcloudio/uni-h5": "2.0.1-34720220422002", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-i18n": "2.0.1-34720220422002", "@dcloudio/uni-mp-360": "2.0.1-34720220422002", "@dcloudio/uni-mp-alipay": "2.0.1-34720220422002", "@dcloudio/uni-mp-baidu": "2.0.1-34720220422002", "@dcloudio/uni-mp-jd": "2.0.1-34720220422002", "@dcloudio/uni-mp-kuaishou": "2.0.1-34720220422002", "@dcloudio/uni-mp-lark": "2.0.1-34720220422002", "@dcloudio/uni-mp-qq": "2.0.1-34720220422002", "@dcloudio/uni-mp-toutiao": "2.0.1-34720220422002", "@dcloudio/uni-mp-vue": "2.0.1-34720220422002", "@dcloudio/uni-mp-weixin": "2.0.1-34720220422002", "@dcloudio/uni-mp-xhs": "2.0.1-34720220422002", "@dcloudio/uni-quickapp-native": "2.0.1-34720220422002", "@dcloudio/uni-quickapp-webview": "2.0.1-34720220422002", "@dcloudio/uni-stat": "2.0.1-34720220422002", "@vue/shared": "^3.0.0", "core-js": "^3.6.5", "dayjs": "1.10.7", "dingtalk-h5-remote-debug": "^0.1.3", "dingtalk-jsapi": "^3.0.38", "flyio": "^0.6.2", "jsencrypt": "^3.3.2", "ts-debounce": "2.0.1", "vue": "^2.6.11", "vue-class-component": "^6.3.2", "vue-property-decorator": "^8.0.0", "vuex": "^3.2.0", "vuex-class": "0.3.2"}, "devDependencies": {"@babel/plugin-syntax-typescript": "^7.2.0", "@commitlint/cli": "16.0.1", "@commitlint/config-conventional": "15.0.0", "@dcloudio/types": "^2.5.13", "@dcloudio/uni-automator": "2.0.1-34720220422002", "@dcloudio/uni-cli-i18n": "2.0.1-34720220422002", "@dcloudio/uni-cli-shared": "2.0.1-34720220422002", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-migration": "2.0.1-34720220422002", "@dcloudio/uni-template-compiler": "2.0.1-34720220422002", "@dcloudio/vue-cli-plugin-hbuilderx": "2.0.1-34720220422002", "@dcloudio/vue-cli-plugin-uni": "2.0.1-34720220422002", "@dcloudio/vue-cli-plugin-uni-optimize": "2.0.1-34720220422002", "@dcloudio/webpack-uni-mp-loader": "2.0.1-34720220422002", "@dcloudio/webpack-uni-pages-loader": "2.0.1-34720220422002", "@typescript-eslint/eslint-plugin": "2.33.0", "@typescript-eslint/parser": "2.33.0", "@vant/area-data": "^2.0.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "^4.5.0", "@vue/cli-plugin-typescript": "~4.5.19", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "babel-plugin-import": "^1.11.0", "big.js": "^6.2.1", "commitizen": "4.2.4", "cross-env": "^7.0.2", "cz-conventional-changelog": "3.3.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-vue": "^6.2.2", "git-cz": "4.8.0", "husky": "7.0.4", "jest": "^25.4.0", "js-base64": "3.7.5", "js-md5": "0.7.3", "jwt-decode": "3.1.2", "lint-staged": "11.2.6", "postcss-comment": "^2.0.0", "prettier": "2.7.1", "sass": "1.77.6", "sass-loader": "^8.0.2", "standard-version": "9.3.2", "typescript": "^3.9.3", "uni-read-pages": "1.0.5", "uni-simple-router": "2.0.7", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "4.7.0"}, "browserslist": ["Android >= 4", "ios >= 8"], "lint-staged": {"*.{js,ts,vue}": "vue-cli-service lint"}, "uni-app": {"scripts": {"mp-dingtalk": {"title": "钉钉小程序", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-DINGTALK": true, "ACTIVATION": false}}, "h5-dingtalk": {"title": "钉钉h5发布", "env": {"UNI_PLATFORM": "h5"}, "define": {"H5-DINGTALK": true}}, "app-cs": {"title": "CS", "env": {"UNI_PLATFORM": "app-plus"}, "define": {"APP-CS": true, "ACTIVATION": true}}, "app-bl": {"title": "BL", "env": {"UNI_PLATFORM": "app-plus"}, "define": {"APP-BL": true, "ACTIVATION": true}}, "hd-uni": {"title": "HDUNI", "env": {"UNI_PLATFORM": "app-plus"}, "define": {"HD-UNI": true, "ACTIVATION": false}}}}}