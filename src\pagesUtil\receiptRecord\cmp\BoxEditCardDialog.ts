/*
 * @Author: 刘湘
 * @Date: 2021-05-06 16:22:21
 * @LastEditTime: 2025-05-08 17:04:47
 * @LastEditors: yuzhipi
 * @Description:
 * @FilePath: /soa/src/pagesUtil/receiptRecord/cmp/BoxEditCardDialog.ts
 * 记得注释
 */
import { Vue, Component } from 'vue-property-decorator'
import AppReceiptBoxDTO from '@/model/receipt/AppReceiptBoxDTO'
import CommonUtil from '@/utils/CommonUtil'
import ModuleOption from '@/model/default/ModuleOption'
import { Getter, State } from 'vuex-class'
import { ModuleId } from '@/model/common/OptionListModuleId'
import AppReceiptRecordBoxDTO from '@/model/AppReceiptRecord/dto/app/AppReceiptRecordBoxDTO'
import PermissionMgr from '@/mgr/PermissionMgr'
import { Permission } from '@/model/user/Permission'
import FieldsConfigMgr from '@/mgr/FieldsConfigMgr'
import { FieldsConfig } from '@/model/user/FieldsConfig'

// eslint-disable-next-line @typescript-eslint/ban-ts-ignore
// @ts-ignore
@Component({
  components: {},
  options: {
    virtualHost: true
  }
})
export default class BoxEditCardDialog extends Vue {
  @State('optionList') optionList: ModuleOption[]
  @Getter('qtyScale') qtyScale: number

  $refs: any
  box: AppReceiptRecordBoxDTO = new AppReceiptRecordBoxDTO() // 箱信息
  oldBox: AppReceiptRecordBoxDTO = new AppReceiptRecordBoxDTO() // 箱信息
  exhibitValue: string = '' // 陈列位置输入框的值

  /**
   * 是否展示陈列位置按钮
   */
  get isShowExhibitBtn() {
    return PermissionMgr.hasPermission(Permission.receiptDisplayLocationBind)
  }

  //获取字段展示权限
  get showMaster() {
    const masterPermission = {
      showDisplayLocation: false // 陈列位置
    }
    if (FieldsConfigMgr.hasFieldsConfig(FieldsConfig.SHOWDISPLAYLOCATION)) {
      masterPermission.showDisplayLocation = true
    }
    return masterPermission
  }

  /**
   * 是否允许一品多货位
   */
  get allowOneGoodsMultipleSlot() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.allowOneGoodsMultipleSlot) {
      return moduleConfig[0].options.allowOneGoodsMultipleSlot === 'true'
    }
    return false
  }

  // 整件箱商品允许按最小规格收货：0-否；1-是
  get wholeBoxGoodsReceiptByMinQpc() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.wholeBoxGoodsReceiptByMinQpc == '1'
    }
    return false
  }

  /**
   * 数据来源：0，H6，不显示陈列位置相关；1，鼎力云，显示
   */
  get slotSource() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.slotSource) {
      return moduleConfig[0].options.slotSource === '1'
    }
    return false
  }

  /**
   * 陈列位置长度
   */
  get slotSegmentCount() {
    const moduleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosGlobal
        })
      : []
    if (moduleConfig.length > 0 && moduleConfig[0].options && moduleConfig[0].options.slotSegmentCount) {
      return Number(moduleConfig[0].options.slotSegmentCount)
    }
    return 4
  }

  // 散称双计量是否需要录入件数（包装数）
  get doubleMeasureGoodsEnterQpcQty() {
    const receiptModuleConfig = this.optionList
      ? this.optionList.filter((option: ModuleOption) => {
          return option.moduleId == ModuleId.sosReceipt
        })
      : []
    if (receiptModuleConfig.length > 0 && receiptModuleConfig[0].options) {
      return receiptModuleConfig[0].options.doubleMeasureGoodsEnterQpcQty == '1'
    }
    return false
  }

  /**
   * 是否有多个陈列位置
   */
  get hasMutiple() {
    return this.box.boxGoodss[0] && this.box.boxGoodss[0].displayLocation && this.box.boxGoodss[0].displayLocation.split(',').length > 1
  }

  open(box: AppReceiptRecordBoxDTO, isOpen: boolean = true) {
    this.oldBox = CommonUtil.deepClone(box)
    this.box = CommonUtil.deepClone(box)
    this.exhibitValue = ''
    if (this.box.boxGoodss[0] && this.box.boxGoodss[0].goods.isDisp) {
      this.box.boxGoodss[0].receiptQpcQty = Number(this.box.boxGoodss[0].receiptQpcQty) as any
    }
    if (isOpen) {
      this.$refs.edit.open()
    }
  }

  close() {
    this.$refs.edit.close()
    setTimeout(() => {
      this.box = new AppReceiptRecordBoxDTO()
      this.oldBox = new AppReceiptRecordBoxDTO()
      this.exhibitValue = ''
    }, 300)
  }

  /**
   * 取消
   */
  handleCancel() {
    this.$emit('cancel')
    this.close()
  }

  handleConfirm() {
    const box: AppReceiptRecordBoxDTO = CommonUtil.deepClone(this.box)

    if (box.boxGoodss[0].goods.isDisp) {
      box.receiptQty = this.doubleMeasureGoodsEnterQpcQty ? Number(box.boxGoodss[0].receiptQpcQty) : box.qty
      if (!this.doubleMeasureGoodsEnterQpcQty) {
        box.boxGoodss[0].receiptQpcQty = box.boxGoodss[0].qpcQty
      }
    } else {
      if (!this.wholeBoxGoodsReceiptByMinQpc) {
        box.boxGoodss[0].receiptQty = box.receiptQty.multiply(box.boxGoodss[0].goods.qpc).scale(this.qtyScale)
        box.boxGoodss[0].receiptQpcQty = `${box.receiptQty.scale(this.qtyScale)}`
      }
    }
    this.$emit('confirm', box)
    this.close()
  }

  handleClick() {
    this.$emit('click', this.box)
  }

  handleWholeQtyChange(qty: number) {
    this.box.boxGoodss[0].receiptQpcQty = (qty || 0) as any
    this.box.boxGoodss[0].receiptQty = Math.min(Number(this.box.boxGoodss[0].qty), (qty * this.box.boxGoodss[0].goods.qpc).scale(this.qtyScale))
  }

  handleSplitQtyChange(qty: number) {
    if (this.box.boxGoodss[0].goods.qpcStr === '1*1' && Number(this.box.boxGoodss[0].receiptQpcQty) !== qty) {
      this.box.boxGoodss[0].receiptQpcQty = (qty || 0) as any
    }
  }

  // 开启了最小规格处理，编辑收货数量（需要把规格数量处理成m+n）
  handleMinQpcQtyChange(qty: number) {
    const qpc: number = this.box.boxGoodss[0].goods.qpc || 1 // 商品规格
    const wholeNum = Math.floor(Number(qty) / qpc)
    const singleNum = Number(qty) % qpc
    this.box.boxGoodss[0].receiptQpcQty = String(wholeNum) + '+' + singleNum
  }

  /**
   * 获取该商品信息
   */
  getGoodsInfo(e) {
    const value = e.detail.value
    if (!value && this.allowOneGoodsMultipleSlot) return
    if (!value && !this.allowOneGoodsMultipleSlot) {
      this.$showModal({
        title: '',
        content: '是否确定当前陈列位置置为空？',
        showCancel: true,
        confirmText: '确定',
        success: (action) => {
          if (action.confirm) {
            this.inputComfirm(value)
          }
        }
      })
      return
    }
    if (value) {
      this.inputComfirm(value)
    }
  }

  /**
   * 陈列位置确认
   */
  inputComfirm(value) {
    const regex = /^[A-Z0-9]+(-[A-Z0-9]+)*$/
    if (!regex.test(value) && value) {
      this.exhibitValue = ''
      this.$showToast({ title: '需要按照XX-XX-XX-XX，且只能为大写字母和数字的形式填写', icon: 'none' })
      return
    }
    if (regex.test(value) && value) {
      const list = value.split('-')
      if (list.length > this.slotSegmentCount) {
        this.exhibitValue = ''
        this.$showToast({ title: `陈列位置最大长度为${this.slotSegmentCount}级`, icon: 'none' })
        return
      } else {
        const idx = list.findIndex((item) => item.length > 4)
        if (idx > -1) {
          this.exhibitValue = ''
          this.$showToast({ title: `陈列位置每级最大长度为4`, icon: 'none' })
          return
        }
      }
    }
    this.box.boxGoodss[0].exhibitValue = value || ''
    this.$emit('getRequestBody', this.box.boxGoodss[0])
  }

  /**
   * 扫码陈列位置
   */
  doScanExhibit(info) {
    uni.scanCode({
      success: (res) => {
        this.inputComfirm(res.result || '')
      }
    })
  }

  /**
   * 绑定陈列位置
   */
  bindExhibit() {
    this.$emit('bindExhibit', this.box.boxGoodss[0])
  }

  /**
   * 陈列位置调整
   */
  resetExhibit() {
    this.$emit('resetExhibit', this.box.boxGoodss[0])
  }

  /**
   * 打开陈列位置弹窗
   */
  viewExhibit() {
    this.$emit('viewExhibit', this.box.boxGoodss[0])
  }
}
