import aseGoodsQueryDTO from '../check/checkV2/aseGoodsQueryDTO'

export default class AppRequireApplyAddGoods extends aseGoodsQueryDTO {
  // 规格价
  price: number = 0
  // 单价
  singlePrice: number = 0
  // 门店管理生产日期
  storeUseMfg: number = 0
  // 门店管理到效期
  storeUseExp: number = 0
  // 是否限量
  isLimit: Nullable<number> = null
  // 限量数
  limitQty: Nullable<number> = null
  // 已定数量
  orderQty: Nullable<number> = null
  // 可定数量
  allowQty: Nullable<number> = null
  // 上架备注
  shelveNote: Nullable<string> = null
  // 活动标识
  activityId: Nullable<string> = null
  // 商品类型：0-普通商品，4-特价商品(爆品)
  goodsType: Nullable<number> = null
  // 方案编码
  schemeNo: Nullable<string> = null
}
